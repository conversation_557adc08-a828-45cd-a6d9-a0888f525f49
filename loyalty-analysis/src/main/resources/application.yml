server:
  port: 8080
  servlet:
    context-path: /loyalty-analysis/v1
spring:
  application:
    name: loyalty-analysis
  redis:
    host: ${redis.host}
    port: ${redis.port}
    password: ${redis.password}
    lettuce:
      pool:
        max-active: ${lettuce.pool.max-active:8}
        max-idle: ${lettuce.pool.max-idle:8}
        min-idle: ${lettuce.pool.min-idle:1}
        max-wait: ${lettuce.pool.max-wait:-1ms}
        time-between-eviction-runs: 30000ms
  cloud:
    function:
      definition: members;expirePoints;delayPoints;expireGrade
    stream:
      bindings:
        members-in-0:
          destination: LOYALTY_REMINDS_MEMBERS_${system.environment}
          group: loyalty-analysis-${system.environment}
          contentType: application/json
          consumer:
            concurrency: ${LOYALTY_REMINDS.concurrency:10}
        members-out-0:
          destination: LOYALTY_REMINDS_MEMBERS_${system.environment}
        expirePoints-in-0:
          destination: LOYALTY_REMINDS_EXPIRE_POINT_${system.environment}
          group: loyalty-analysis-${system.environment}
          contentType: application/json
          consumer:
            concurrency: ${LOYALTY_REMINDS.concurrency:10}
        expirePoints-out-0:
          destination: LOYALTY_REMINDS_EXPIRE_POINT_${system.environment}
        delayPoints-in-0:
          destination: LOYALTY_REMINDS_DELAY_POINT_${system.environment}
          group: loyalty-analysis-${system.environment}
          contentType: application/json
          consumer:
            concurrency: ${LOYALTY_REMINDS.concurrency:10}
        delayPoints-out-0:
          destination: LOYALTY_REMINDS_DELAY_POINT_${system.environment}
        expireGrade-in-0:
          destination: LOYALTY_REMINDS_EXPIRE_GRADE_${system.environment}
          group: loyalty-analysis-${system.environment}
          contentType: application/json
          consumer:
            concurrency: ${LOYALTY_REMINDS.concurrency:10}
        expireGrade-out-0:
          destination: LOYALTY_REMINDS_EXPIRE_GRADE_${system.environment}
      kafka:
        binder:
          brokers: ${kafka.brokers:127.0.0.1:9092}
          consumer-properties:
            session.timeout.ms: ${kafka.session.timeout.ms:60000}
            heartbeat.interval.ms: ${kafka.heartbeat.interval.ms:3000}
            max.poll.interval.ms: ${kafka.max.poll.interval.ms:600000}
            max.poll.records: ${kafka.max.poll.records:1}
            auto.offset.reset: latest
          auto-create-topics: true
          auto-add-partitions: true
          min-partition-count: ${kafka.min.partition.num:10}
          replication-factor: ${kafka.replication.factor:1}


management:
  endpoints:
    web:
      base-path: /system
  endpoint:
    health.show-details: always