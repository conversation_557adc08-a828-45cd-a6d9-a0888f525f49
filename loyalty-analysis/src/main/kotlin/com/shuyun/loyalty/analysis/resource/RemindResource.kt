package com.shuyun.loyalty.analysis.resource

import com.shuyun.loyalty.analysis.database.RemindConfig
import com.shuyun.loyalty.analysis.database.list
import com.shuyun.loyalty.analysis.handler.RemindHandler
import com.shuyun.loyalty.analysis.scheduler.RemindJob
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController
import kotlin.concurrent.thread

@RestController
class RemindResource {

    private val logger = LoggerFactory.getLogger(RemindJob::class.java)

    @Autowired
    private lateinit var remindHandler: RemindHandler

    @GetMapping("/test-trigger")
    fun testTrigger() {
        val remindConfigs = RemindConfig.list()
        if (remindConfigs.isEmpty()) {
            logger.info("没有发现任何提醒配置")
            return
        }
        thread { remindHandler.dispatchMember(remindConfigs, updateCompleteTime = false, ignoreTriggerInterval = true) }
    }


    @PostMapping("/trigger")
    fun trigger(@RequestBody remindRequest: RemindRequest) {
        logger.info("接口触发提醒 {}", remindRequest)
        val remindConfigs = RemindConfig.list().filter {
            it.id == remindRequest.remindId
        }
        if (remindConfigs.isEmpty()) {
            logger.info("没有发现提醒配置 id: {}", remindRequest.remindId)
            return
        }
        thread { remindHandler.dispatchMember(remindConfigs, remindRequest.triggerByInterval, remindRequest.triggerByInterval) }
    }

    data class RemindRequest(var triggerByInterval: Boolean = true, var remindId: Long)
}