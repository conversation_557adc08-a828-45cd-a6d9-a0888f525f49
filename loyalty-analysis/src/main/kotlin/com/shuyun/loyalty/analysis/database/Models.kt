package com.shuyun.loyalty.analysis.database

import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.time.ZonedDateTime

//data class ValidPoints(
//    var memberId: String,
//    var memberPointId: String,
//    var point: BigDecimal,
//    var overdueDate: ZonedDateTime?,
//)

//data class GainPoints(
//    var memberId: String,
//    var memberPointId: String,
//    var point: BigDecimal,
//    var effectiveDate: ZonedDateTime?,
//)

data class ApproachingExpirationPointsRecord(
    var id: String?,
    var remindId: Long?,
    var name: String?,
    var planId: Long?,
    var subjectId: Long?,
    var subjectFqn: String?,
    var pointPlanId: Long?,
    var pointPlanName: String?,
    var member: String?,
    var memberId: String?,
    var effectiveDate: ZonedDateTime?,
    var overdueDate: ZonedDateTime?,
    var point: BigDecimal,
    var created: ZonedDateTime = ZonedDateTime.now(),
    var desc: String = ""
)



data class UpcomingEffectivePointsRecord(
    var id: String?,
    var remindId: Long?,
    var name: String?,
    var planId: Long?,
    var pointPlanId: Long?,
    var pointPlanName: String?,
    var subjectId: Long?,
    var subjectFqn: String?,
    var memberId: String?,
    var member: String?,
    var effectiveDate: ZonedDateTime?,
    var overdueDate: ZonedDateTime?,
    var point: BigDecimal,
    var created: ZonedDateTime = ZonedDateTime.now(),
    var desc: String = "",
)


data class ApproachingExpirationGradeRecord(
    var id: String?,
    var remindId: Long?,
    var name: String?,
    var planId: Long?,
    var subjectFqn: String?,
    var gradeHierarchyId: Long?,
    var gradeHierarchyName: String?,
    var member: String?,
    var memberId: String?,
    var currentGradeId: Long?,
    var currentGradeName: String?,
    var currentEffectDate: ZonedDateTime?,
    var currentOverdueDate: ZonedDateTime?,
    var desc: String? = "",
    var created: ZonedDateTime = ZonedDateTime.now(),
)

enum class RemindType { POINT_EXPIRE, POINT_DELAY, GRADE_EXPIRE }
enum class IntervalUnit {
    DAY,
    WEEK,
    MONTH,
}
enum class TimeType {
    RELATIVE_TIME,              //相对时间
    ABSOLUTE_TIME,              //绝对时间

    RELATIVE_TIME_DAY,          //相对时间天
    RELATIVE_TIME_YEAR,         //相对时间年
    RELATIVE_TIME_YEAR_MONTH,   //相对时间年月
}
data class RemindConfig(
    var versionId: Long?,
    var name: String?,
    var planVersionId: Long?,
    var subjectVersionId: Long?,
    var id: Long?,
    var parentId: Long?,
    var parentVersionId: Long?,
    var remindType: RemindType?,
    var timeType: TimeType?,
    var timeDay: Int?,
    var timeMonth: Int?,
    var timeYear: Int?,
    var timeSign: String?,
    var timeDate: ZonedDateTime?,
    var cycleDay: Int?,
    var intervalUnit: IntervalUnit?,
    var intervalValue: Int?,
    var sort: Int?,
    var status: String?,
    var disabled: Boolean,
    var subjectFqn: String?,
    var remindTableName: String?,
    var completeTime: ZonedDateTime?,
    var updateTime: ZonedDateTime?,
    // 下面非数据库模型字段
    var planId : Long? = null,
    var planName: String? = null,
    var subjectId: Long? = null,
    var subjectName : String? = null,
    var hierarchyId: Long? = null,
    var hierarchyName: String? = null,
) {
    companion object {
        val logger: Logger = LoggerFactory.getLogger(RemindConfig::class.java)
    }
}
