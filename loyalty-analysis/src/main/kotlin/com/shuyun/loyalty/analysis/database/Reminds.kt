package com.shuyun.loyalty.analysis.database

import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.analysis.database.RemindType.*
import com.shuyun.loyalty.sdk.Dataapi.fetch
import com.shuyun.loyalty.sdk.Dataapi.sdk
import com.shuyun.loyalty.sdk.Uuid
import com.shuyun.loyalty.sdk.localDate
import com.shuyun.loyalty.sdk.utcStr
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.concurrent.ConcurrentHashMap
import kotlin.concurrent.thread

object Reminds {


    fun batchInsertApproachingExpirationGradeRecord(hierarchyId: Long, records: List<ApproachingExpirationGradeRecord>) {
        if (records.isEmpty()) return
        var sql = """
            INSERT INTO data.loyalty.member.hierarchy.GradeExpireRemindRecord${hierarchyId} 
            (
                `id`,`planId`,`subjectFqn`,`memberId`,`member`,
                `gradeHierarchyId`,`gradeHierarchyName`,`currentGradeId`,`currentGradeName`,
                `currentEffectDate`,`currentOverdueDate`,`remindId`,`name`,
                `desc`,`created`
            ) 
            VALUES 
        """.trimIndent()
        for (item in records) {
            sql += """
            (
            ${Uuid.uuid.s}, ${item.planId}, ${item.subjectFqn.s}, ${item.memberId.s}, ${item.memberId.s}, 
            ${item.gradeHierarchyId}, ${item.gradeHierarchyName.s}, ${item.currentGradeId}, ${item.currentGradeName.s}, 
            ${item.currentEffectDate.s}, ${item.currentOverdueDate.s}, ${item.remindId}, ${item.name.s},
             ${item.desc.s}, ${item.created.s}
            ),
            """.trimIndent()
        }
        sql = sql.trim().dropLast(1)
        sdk.use { x -> x.execute(sql, emptyMap()) }
    }


    fun batchInsertApproachingExpirationPointsRecord(hierarchyId: Long, records: List<ApproachingExpirationPointsRecord>) {
        if (records.isEmpty()) return
        var sql = """
            INSERT INTO data.loyalty.member.account.PointExpireRemindRecord${hierarchyId} 
            (
                `id`,`planId`,`subjectId`,`subjectFqn`,`memberId`,`member`, 
                `pointPlanId`,`pointPlanName`,`effectiveDate`,`overdueDate`,
                `point`,`desc`,`remindId`,`created`,`name`
            ) 
            VALUES 
        """.trimIndent()
        for (item in records) {
            sql += """
            (
            ${Uuid.uuid.s}, ${item.planId}, ${item.subjectId}, ${item.subjectFqn.s}, ${item.memberId.s}, ${item.memberId.s}, 
            ${item.pointPlanId}, ${item.pointPlanName.s}, ${item.effectiveDate.s}, ${item.overdueDate.s}, 
            ${item.point}, ${item.desc.s}, ${item.remindId}, ${item.created.s},${item.name.s}
            ),
            """.trimIndent()
        }
        sql = sql.trim().dropLast(1)
        sdk.use { x -> x.execute(sql, emptyMap()) }
    }


    fun batchInsertUpcomingEffectivePointsRecord(hierarchyId: Long, records: List<UpcomingEffectivePointsRecord>) {
        if (records.isEmpty()) return
        var sql = """
            INSERT INTO data.loyalty.member.account.PointDelayRemindRecord${hierarchyId} 
            (
                `id`,`planId`,`subjectId`,`subjectFqn`,`memberId`,`member`, 
                `pointPlanId`,`pointPlanName`,`effectiveDate`,`overdueDate`,
                `point`,`desc`,`remindId`,`name`,`created`
            ) 
            VALUES 
        """.trimIndent()
        for (item in records) {
            sql += """
            (
            ${Uuid.uuid.s}, ${item.planId}, ${item.subjectId}, ${item.subjectFqn.s}, ${item.memberId.s}, ${item.memberId.s}, 
            ${item.pointPlanId}, ${item.pointPlanName.s}, ${item.effectiveDate.s}, ${item.overdueDate.s}, 
            ${item.point}, ${item.desc.s}, ${item.remindId}, ${item.name.s}, ${item.created.s}
            ),
            """.trimIndent()
        }
        sql = sql.trim().dropLast(1)
        sdk.use { x -> x.execute(sql, emptyMap()) }
    }


    private const val NULL = "NULL"
    private val String?.s: String get() = if (this == null) NULL else "'${this.replace("'", "\\'")}'"
    private val ZonedDateTime?.s: String get() = this?.utcStr()?.s ?: NULL
}


private data class Plan(val id: Long, val planVersionId: Long, val name: String)
private data class Subject(val id: Long, val subjectVersionId: Long, val name: String, val fqn: String)
private data class Hierarchy(val id: Long, val name: String)



fun RemindConfig.updateCompleteTime() {
    sdk.use {
        val sql = "UPDATE data.loyalty.manager.remindConfig SET completeTime = :completeTime where id = :id"
        it.execute(sql, mapOf("completeTime" to ZonedDateTime.now(), "id" to this.id))
    }
}

fun RemindConfig.updateTime() {
    sdk.use {
        val sql = "UPDATE data.loyalty.manager.remindConfig SET updateTime = :updateTime where id = :id"
        it.execute(sql, mapOf("updateTime" to ZonedDateTime.now(), "id" to this.id))
    }
}


@Suppress("UNCHECKED_CAST")
fun RemindConfig.Companion.list(): List<RemindConfig> {
    val plans = findPlans()
    val planVersionIds = plans.map { it.planVersionId }.toSet()
    if (planVersionIds.isEmpty()) return emptyList()
    val subjectVersionIds = findSubjectsByPlanVersionIds(planVersionIds).map { it.subjectVersionId }.toSet()

    val sql = """
        SELECT 
            `versionId`,`id`,`name`,`planVersionId`,`subjectVersionId`,`parentId`,`parentVersionId`,
            `remindType`,`timeSign`,`timeType`,`timeYear`,`timeMonth`,`timeDay`,`timeDate`,`cycleDay`, `intervalUnit`,`intervalValue`,
            `sort`,`remindTableName`,`completeTime`,`status`,`disabled`,`subjectFqn`,`updateTime`
        FROM data.loyalty.manager.remindConfig
        WHERE planVersionId IN (:planVersionIds) AND subjectVersionId IN (:subjectVersionIds) AND status = 'ENABLED' AND disabled = false
    """.trimIndent()
    val params = mapOf("planVersionIds" to planVersionIds, "subjectVersionIds" to subjectVersionIds)
    val response = sdk.use { sdk -> sdk.execute(sql, params) }
    val remindConfigs = response.data.map {
        val row = it as Map<String, Any?>
        val subjectVersionId = row["subjectVersionId"].toString().toLong()
        val remindType = row["remindType"]?.toString()
        val hierarchyId = row["parentId"].toString().toLong()

        val plan = plans.first { x -> x.planVersionId == row["planVersionId"].toString().toLong() }
        val subject = findSubjectByVersionId(subjectVersionId)
        val hierarchy = findHierarchyByHierarchyId(hierarchyId, subjectVersionId, remindType)
        val cd = row["cycleDay"]?.toString()?.toInt()
        val iu = row["intervalUnit"]?.toString()?.let { x -> IntervalUnit.valueOf(x) } ?: IntervalUnit.DAY
        val iv = row["intervalValue"]?.toString()?.toInt() ?: if (cd == null || cd <= 0) 0 else cd

        RemindConfig(
            versionId = row["versionId"]?.toString()?.toLong(),
            id = row["id"]?.toString()?.toLong(),
            name = row["name"]?.toString(),
            planVersionId = row["planVersionId"]?.toString()?.toLong(),
            subjectVersionId = row["subjectVersionId"]?.toString()?.toLong(),
            parentId = row["parentId"]?.toString()?.toLong(),
            parentVersionId = row["parentVersionId"]?.toString()?.toLong(),
            remindType = row["remindType"]?.toString()?.let { x -> RemindType.valueOf(x)  },
            timeSign = row["timeSign"]?.toString(),
            timeType = row["timeType"]?.toString()?.let { x -> TimeType.valueOf(x)  },
            timeYear = row["timeYear"]?.toString()?.toInt(),
            timeMonth = row["timeMonth"]?.toString()?.toInt(),
            timeDay = row["timeDay"]?.toString()?.toInt(),
            timeDate = row["timeDate"]?.toString()?.let { x -> ZonedDateTime.parse(x) },
            cycleDay = cd,
            intervalUnit = iu,
            intervalValue = iv,
            sort = row["sort"]?.toString()?.toInt(),
            remindTableName = row["remindTableName"]?.toString(),
            completeTime = row["completeTime"]?.toString()?.let { x -> ZonedDateTime.parse(x) },
            status = row["status"]?.toString(),
            disabled = row["disabled"]?.toString()?.toBoolean() ?: false,
            subjectFqn = row["subjectFqn"]?.toString(),
            updateTime = row["updateTime"]?.toString()?.let { x -> ZonedDateTime.parse(x) },
            planId = plan.id,
            planName = plan.name,
            subjectId = subject?.id,
            subjectName = subject?.name,
            hierarchyId = hierarchyId,
            hierarchyName = hierarchy?.name,
        )
    }
    return remindConfigs
}


// 当前绝对时间只有[早于等于] 如：失效时间早于等于2024-01-01 00:00:00
fun RemindConfig.isTimeToTrigger(now: LocalDate = LocalDate.now()): Boolean {
    val lastTriggerDate = this.updateTime?.localDate()
    val date = if (timeType == TimeType.ABSOLUTE_TIME) timeDate!!.localDate() else now
    if (date < now) return false
    if (lastTriggerDate == null) return true // 首次触发
    return when (intervalUnit!!) {
        IntervalUnit.DAY -> lastTriggerDate.plusDays(intervalValue!!.toLong()) <= date
        IntervalUnit.WEEK -> now.dayOfWeek.value == intervalValue
        IntervalUnit.MONTH -> if (intervalValue == -1) now.dayOfMonth == now.lengthOfMonth() else now.dayOfMonth == intervalValue
    }
}


@Suppress("UNCHECKED_CAST")
private fun RemindConfig.Companion.findPlans(): List<Plan> = sdk.use { sdk ->
    val planSql = """
        SELECT id, versionId, name
        FROM data.loyalty.manager.plan 
        WHERE disabled = 0 AND status='PUBLISHED'
    """.trimIndent()
    val response = sdk.execute(planSql, mapOf())
    val plans = response.data.map {
        val row = it as Map<String, Any?>
        Plan(
            id = row["id"].toString().toLong(),
            planVersionId = row["versionId"].toString().toLong(),
            name = (row["name"] as? String) ?: "",
        )
    }
    plans
}


@Suppress("UNCHECKED_CAST")
private fun RemindConfig.Companion.findSubjectsByPlanVersionIds(planVersionIds: Set<Long>): List<Subject> {
    val sql = """
        SELECT versionId, id, name, dataType as fqn 
        FROM data.loyalty.manager.subject 
        WHERE planVersionId in (:planVersionIds) 
    """.trimIndent()
    val response = sdk.use { sdk -> sdk.execute(sql, mapOf("planVersionIds" to planVersionIds)) }
    if (response.data.isEmpty()) return emptyList()
    return response.data.map {
        val row = it as Map<String, Any?>
        Subject(
            id = row["id"].toString().toLong(),
            subjectVersionId = row["versionId"].toString().toLong(),
            name = (row["name"] as? String) ?: "",
            fqn = (row["fqn"] as? String) ?: "",
        )
    }
}


@Suppress("UNCHECKED_CAST")
private fun RemindConfig.Companion.findSubjectByVersionId(versionId: Long): Subject? {
    val sql = """
        SELECT id, versionId,name, dataType as fqn 
        FROM data.loyalty.manager.subject 
        WHERE versionId = :versionId 
    """.trimIndent()
    val response = sdk.use { sdk -> sdk.execute(sql, mapOf("versionId" to versionId)) }
    if (response.data.isEmpty()) return null
    val row = response.data.first() as Map<String, Any?>
    return Subject(
        id = row["id"].toString().toLong(),
        subjectVersionId = row["versionId"].toString().toLong(),
        name = (row["name"] as? String) ?: "",
        fqn = (row["fqn"] as? String) ?: "",
    )
}


@Suppress("UNCHECKED_CAST")
private fun RemindConfig.Companion.findHierarchyByHierarchyId(hierarchyId: Long, subjectVersionId: Long, remindType: String?): Hierarchy? {
    val fqn = when (remindType) {
        POINT_EXPIRE.name, POINT_DELAY.name -> "data.loyalty.manager.pointAccountType"
        GRADE_EXPIRE.name -> "data.loyalty.manager.gradeHierarchy"
        else -> return null
    }
    val gradeHierarchySql = """
        SELECT id, name
        FROM $fqn
        WHERE  id = :id AND subjectVersionId = :subjectVersionId
    """.trimIndent()
    val response = sdk.use { sdk ->
        sdk.execute(gradeHierarchySql, mapOf("id" to hierarchyId, "subjectVersionId" to subjectVersionId))
    }
    if (response.data.isEmpty()) return null
    val row = response.data.first() as Map<String, Any?>
    return Hierarchy(
        id = row["id"].toString().toLong(),
        name = (row["name"] as? String) ?: "",
    )
}

private fun RemindConfig.Companion.pointAccountTypeIds() = typeIds("data.loyalty.manager.pointAccountType")

private fun RemindConfig.Companion.gradeHierarchyIds() = typeIds("data.loyalty.manager.gradeHierarchy")


@Suppress("UNCHECKED_CAST")
private fun RemindConfig.Companion.typeIds(fqn: String): List<Long> {
    val sql = "SELECT distinct id FROM $fqn"
    val response = sdk.use { sdk ->
        sdk.execute(sql, mapOf())
    }
    return response.data.map {
        val row = it as Map<String, Any?>
        row["id"].toString().toLong()
    }
}

private fun RemindConfig.Companion.deleteRemindRecord(
    threads: MutableList<Thread>,
    failedIds: ConcurrentHashMap.KeySetView<Long, Boolean>,
    typeIds: List<Long>, fqnPrefix: String
) {
    typeIds.forEach { typeId ->
        val fqn = fqnPrefix + "$typeId"
        val t = thread(name = "d-$fqn") {
            var count = 0
            logger.info("删除提醒旧数据开始 模型: {}", fqn)
            try {
                fetch("select `id` from $fqn") {
                    val ids = it.map { x -> x["id"]?.toString() }
                    if (ids.isEmpty()) return@fetch
                    val del = "delete from $fqn where `id` in (:ids)"
                    sdk.use { x -> x.execute(del, mapOf("ids" to ids)) }
                    count += ids.size
                }
                logger.info("删除提醒旧数据完成 模型: {} 删除行数: {}", fqn, count)
            } catch (e: SdkException) {
                if (e.error_code == "152201") {
                    // 模型不存在
                    logger.info("删除提醒旧数据完成 模型不存在 模型: {}", fqn)
                    failedIds.add(typeId)
                    return@thread
                }
            }
        }
        threads.add(t)
    }
}


fun RemindConfig.Companion.deleteRemindRecord(): Set<Long> {
    val s = System.currentTimeMillis()
    val threads = ArrayList<Thread>()
    val failedRemindIds = ConcurrentHashMap.newKeySet<Long>()
    deleteRemindRecord(threads, failedRemindIds, pointAccountTypeIds(), "data.loyalty.member.account.PointDelayRemindRecord")
    deleteRemindRecord(threads, failedRemindIds, pointAccountTypeIds(), "data.loyalty.member.account.PointExpireRemindRecord")
    deleteRemindRecord(threads, failedRemindIds, gradeHierarchyIds(), "data.loyalty.member.hierarchy.GradeExpireRemindRecord")
    threads.forEach { it.join() }
    logger.info("全部提醒数据删除完成 耗时: {}s", (System.currentTimeMillis() - s) / 1000)
    return failedRemindIds
}