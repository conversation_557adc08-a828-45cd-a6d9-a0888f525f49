package com.shuyun.loyalty.analysis.scheduler

import com.shuyun.lite.context.GlobalContext
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.provider.redis.spring.RedisLockProvider
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.redis.connection.RedisConnectionFactory
import org.springframework.scheduling.annotation.EnableScheduling


@Configuration
@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = "PT1H")
class Config {

    companion object {
        private val ENV = GlobalContext.serviceName() + "_" + GlobalContext.defTenantId()
    }

    @Bean
    fun lockProvider(connectionFactory: RedisConnectionFactory): LockProvider {
        return RedisLockProvider(connectionFactory, ENV)
    }

    @Bean
    fun lettuceClientConfigurationBuilderCustomizer (): LettuceClientConfigurationBuilderCustomizer {
        return LettuceClientConfigurationBuilderCustomizer { builder ->
            val cf = builder.build()
            if (cf.isUseSsl) {
                builder.useSsl().disablePeerVerification()
            }
        }
    }

}