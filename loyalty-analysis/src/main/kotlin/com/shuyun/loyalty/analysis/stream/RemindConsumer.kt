package com.shuyun.loyalty.analysis.stream

import com.shuyun.loyalty.analysis.handler.RemindHandler
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Lazy
import org.springframework.messaging.Message
import java.util.function.Consumer

@Configuration
class RemindConsumer(@Lazy val handler: RemindHandler) {

    private val logger = org.slf4j.LoggerFactory.getLogger(RemindConsumer::class.java)

    @Bean("members")
    fun members(): Consumer<RemindMessage> {
        return Consumer { message ->
            logger.info("Received message: {}", message)
            handler.handleMember(
                rcs = message.rcs,
                id = message.id,
                memberId = message.memberId,
                date = message.date
            )
        }
    }


    @Bean("expirePoints")
    fun expirePoints(): Consumer<Message<ApproachingExpirationPointsRecordMessage>> {
        return Consumer {
            handler.handleExpirePoint(
                hierarchyId = it.payload.hierarchyId,
                records = it.payload.records
            )
        }
    }


    @Bean("delayPoints")
    fun delayPoints(): Consumer<Message<UpcomingEffectivePointsRecordMessage>> {
        return Consumer {
            handler.handleDelayPoint(
                hierarchyId = it.payload.hierarchyId,
                records = it.payload.records
            )
        }
    }


    @Bean("expireGrade")
    fun expireGrade(): Consumer<Message<ApproachingExpirationGradeRecordMessage>> {
        return Consumer {
            handler.handleExpireGrade(
                hierarchyId = it.payload.hierarchyId,
                records = it.payload.records
            )
        }
    }
}