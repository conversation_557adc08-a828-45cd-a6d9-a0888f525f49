package com.shuyun.loyalty.analysis.scheduler

import com.shuyun.loyalty.analysis.database.RemindConfig
import com.shuyun.loyalty.analysis.database.list
import com.shuyun.loyalty.analysis.handler.RemindHandler
import com.shuyun.loyalty.sdk.Property
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class RemindJob(private val remindHandler: RemindHandler) {

    private val logger = LoggerFactory.getLogger(RemindJob::class.java)

    // 每天0点执行一次
    @Scheduled(cron = "0 0 0 */1 * ?")
    @SchedulerLock(name = "remindJob", lockAtMostFor = "30m", lockAtLeastFor = "5m")
    fun executeInternal() {
        try {
            val enabled = Property.getSysOrEnv("loyalty.analysis.scheduler.remind.enabled", true)
            if (!enabled) {
                logger.info("提醒任务已经被关闭 , 配置参数: loyalty.analysis.scheduler.remind.enabled")
                return
            }
            val remindConfigs = RemindConfig.list()
            if (remindConfigs.isEmpty()) {
                logger.info("没有配置提醒")
                return
            }
            remindHandler.dispatchMember(remindConfigs)
        } catch (e: Throwable) {
            logger.error("执行提醒任务失败", e)
        }
    }

}