package com.shuyun.loyalty.analysis.handler

import com.shuyun.loyalty.analysis.database.*
import com.shuyun.loyalty.analysis.database.RemindType.*
import com.shuyun.loyalty.analysis.database.TimeType.*
import com.shuyun.loyalty.analysis.stream.ApproachingExpirationGradeRecordMessage
import com.shuyun.loyalty.analysis.stream.ApproachingExpirationPointsRecordMessage
import com.shuyun.loyalty.analysis.stream.RemindMessage
import com.shuyun.loyalty.analysis.stream.UpcomingEffectivePointsRecordMessage
import com.shuyun.loyalty.sdk.Dataapi.fetch
import com.shuyun.loyalty.sdk.Dataapi.sdk
import com.shuyun.loyalty.sdk.localDate
import com.shuyun.loyalty.sdk.utcStr
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.sdk.Uuid
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cloud.stream.function.StreamBridge
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.concurrent.atomic.AtomicInteger
import kotlin.concurrent.thread

@Component
class RemindHandler {

    @Autowired
    private lateinit var streamBridge: StreamBridge

    companion object {
        private val logger = LoggerFactory.getLogger(RemindHandler::class.java)
        private val ASIA_SHANGHAI_ZONE_ID = ZoneId.of("Asia/Shanghai")
        private const val REMIND_EXPIRE_POINT_RECORD_CHANNEL = "expirePoints-out-0"
        private const val REMIND_EXPIRE_GRADE_RECORD_CHANNEL = "expireGrade-out-0"
        private const val REMIND_UPCOMING_POINT_RECORD_CHANNEL = "delayPoints-out-0"
        private const val REMIND_MEMBER_OUT_CHANNEL = "members-out-0"
    }


    fun dispatchMember(
        remindConfigs: List<RemindConfig>,
        updateCompleteTime: Boolean = true,
        ignoreTriggerInterval: Boolean = false
    ) {
        val now = ZonedDateTime.now()
        val deleteFailedRemindIds = RemindConfig.deleteRemindRecord()
        val configs = HashMap<String, MutableList<RemindConfig>>()
        for (rc in remindConfigs) {
            try {
                if (rc.id!! in deleteFailedRemindIds) {
                    continue
                }
                val fqn = when (rc.remindType) {
                    POINT_EXPIRE, POINT_DELAY -> "data.loyalty.member.account.Point${rc.parentId}"
                    GRADE_EXPIRE -> "data.loyalty.member.hierarchy.Grade${rc.parentId}"
                    else -> continue
                }
                if (rc.status != "ENABLED" || rc.disabled) {
                    logger.info("提醒配置未启用: {} {} {} {}", rc.id, rc.parentId, rc.name, rc.remindTableName)
                    continue
                }
                if (!ignoreTriggerInterval && !rc.isTimeToTrigger(now.localDate())) {
                    logger.info(
                        "提醒配置未到达触发时间: {} {} {} {}",
                        rc.id,
                        rc.parentId,
                        rc.name,
                        rc.remindTableName
                    )
                    continue
                }
                if (!configs.containsKey(fqn)) {
                    configs[fqn] = ArrayList()
                }
                configs[fqn]!!.add(rc)
            } catch (e: Throwable) {
                logger.error("发送提醒消息异常 {}", rc, e)
            }
        }

        val threads = ArrayList<Thread>()
        for ((fqn, rcs) in configs) {
            val t = thread {
                try {
                    rcs.forEach { it.updateTime() }
                    val count = AtomicInteger(0)
                    logger.info("发送主体账户消息开始: configIds=[{}] typeIds=[{}] configNames=[{}] remindTableNames=[{}]", (rcs.map { it.id }), (rcs.map { it.parentId }), (rcs.map { it.name }), (rcs.map { it.remindTableName }))
                    val fetchSize = Property.getSysOrEnv("task.member.fetchSize", 1000)
                    val sql = "select `id`, memberId from $fqn order by memberId asc"
                    fetch(sql, size = fetchSize) {
                        it.distinctBy { x -> x["memberId"].toString() }.forEach { x ->
                            streamBridge.send(
                                REMIND_MEMBER_OUT_CHANNEL,
                                RemindMessage(rcs, x["id"].toString(), now, x["memberId"].toString())
                            )
                            count.incrementAndGet()
                        }
                    }
                    if (updateCompleteTime) {
                        rcs.forEach { it.updateCompleteTime() }
                    }
                    logger.info(
                        "发送主体账户消息完成: configIds=[{}] configParentIds=[{}] configNames=[{}] remindTableNames=[{}] count={}",
                        (rcs.map { it.id }),
                        (rcs.map { it.parentId }),
                        (rcs.map { it.name }),
                        (rcs.map { it.remindTableName }),
                        count.get()
                    )
                } catch (e: Throwable) {
                    logger.error("发送主体账户消息异常 {}", fqn, e)
                }
            }
            threads.add(t)
        }
        threads.forEach { it.join() }
    }



    // 筛选会员的通知记录
    fun handleMember(rcs: List<RemindConfig>, id: String, memberId: String?, date: ZonedDateTime) {
        for (rc in rcs) {
            when (rc.remindType) {
                POINT_EXPIRE -> processApproachingExpirationPoints(rc, id, memberId, date)
                POINT_DELAY -> processUpcomingEffectivePoints(rc, id, memberId, date)
                GRADE_EXPIRE -> processApproachingExpirationGrades(rc, id, date)
                else -> {
                    logger.error("不支持的提醒类型 {} id: {} name: {}", rc.remindType, rc.id, rc.name)
                }
            }
        }
    }


    // 批量保存即将过期积分通知记录
    fun handleExpirePoint(hierarchyId: Long, records: List<ApproachingExpirationPointsRecord>) {
        val s = System.currentTimeMillis()
        Reminds.batchInsertApproachingExpirationPointsRecord(hierarchyId, records)
        logger.info("会员即将过期有效积分保存到模型: {} {} 耗时{}ms", hierarchyId,  records.first().member, System.currentTimeMillis() - s)
    }


    // 批量保存待生效积分通知记录
    fun handleDelayPoint(hierarchyId: Long, records: List<UpcomingEffectivePointsRecord>) {
        Reminds.batchInsertUpcomingEffectivePointsRecord(hierarchyId, records)
    }


    // 批量保存即将过期等级通知记录
    fun handleExpireGrade(hierarchyId: Long, records: List<ApproachingExpirationGradeRecord>) {
        Reminds.batchInsertApproachingExpirationGradeRecord(hierarchyId, records)
    }


    // 积分到期提醒
    @Suppress("UNCHECKED_CAST")
    private fun processApproachingExpirationPoints(rc: RemindConfig, memberPointId: String, memberId: String?, date: ZonedDateTime) {
        logger.info("收到会员信息: 类型ID={} 会员积分账户ID={} 会员ID={} 执行时间={}", rc.parentId, memberPointId, memberId, date)
        val start = System.currentTimeMillis()
        val fqn = "data.loyalty.member.account.ValidStatement${rc.parentId}"
        var sql = "SELECT sum(point) as point, min(overdueDate) as overdueDate FROM $fqn where memberPointId = :memberPointId and fromStatus not in('OPEN_FROZE', 'SPECIAL_FROZE') and overdueDate > :now "
        val params = mutableMapOf<String, Any?>().also {
            it["memberPointId"] = memberPointId
            it["now"] = date.utcStr()
        }

        val remindDate = getRemindDate(rc, date) ?: return
        when (rc.timeSign!!) {
            "=","between" -> {
                sql += " and (overdueDate >= :overdueDate1 and overdueDate <= :overdueDate2) "
                params["overdueDate1"] = ZonedDateTime.of(remindDate, LocalTime.MIN, ASIA_SHANGHAI_ZONE_ID)
                params["overdueDate2"] = ZonedDateTime.of(remindDate, LocalTime.MAX, ASIA_SHANGHAI_ZONE_ID)
            }
            "<" -> {
                sql += " and overdueDate < :overdueDate "
                params["overdueDate"] = ZonedDateTime.of(remindDate, LocalTime.MAX, ASIA_SHANGHAI_ZONE_ID)
            }
            "<=" -> {
                sql += " and overdueDate <= :overdueDate "
                params["overdueDate"] = ZonedDateTime.of(remindDate, LocalTime.MAX, ASIA_SHANGHAI_ZONE_ID)
            }
            else -> {
                logger.error("积分到期提醒不支持的时间类型 {} id: {} name: {}", rc.timeSign, rc.id, rc.name)
                return
            }
        }
        val records = ArrayList<ApproachingExpirationPointsRecord>()
        val response = sdk.use { x -> x.execute(sql, params) }
        val rows = response.data
        val map = rows[0] as Map<String, Any?>
        val p = map["point"]?.toString()?.let { BigDecimal(it) } ?: return
        val od = map["overdueDate"]?.let { ZonedDateTime.parse(it.toString()) }
        logger.info("会员有效积分查询完毕: {} {} {} {} 耗时: {}ms", rc.parentId, memberPointId, memberId, p, System.currentTimeMillis() - start)
        if (p > BigDecimal.ZERO) {
            val record = ApproachingExpirationPointsRecord(
                id = Uuid.uuid,
                remindId = rc.id,
                name = rc.name,
                planId = rc.planId,
                subjectId = rc.subjectId,
                subjectFqn = rc.subjectFqn,
                pointPlanId = rc.hierarchyId,
                pointPlanName = rc.hierarchyName,
                member = memberPointId,
                memberId = memberId,
                effectiveDate = null,
                overdueDate = null,
                point = p,
            ).also {
                if (rc.timeSign == "=" || rc.timeSign == "between") {
                    it.overdueDate = od
                }
            }
            records.add(record)
        }
        if (records.isNotEmpty()) {
            val message = ApproachingExpirationPointsRecordMessage(rc.parentId!!, records)
            streamBridge.send(REMIND_EXPIRE_POINT_RECORD_CHANNEL, message)
            logger.info("会员即将过期有效积分推送到队列: {} {} {}", rc.parentId, memberPointId, memberId)
        }
    }


    // 待生效积分提醒
    @Suppress("UNCHECKED_CAST")
    private fun processUpcomingEffectivePoints(rc: RemindConfig, memberPointId: String, memberId: String?, date: ZonedDateTime) {

        var sql = "select sum(point) as point, min(effectiveDate) as effectiveDate from data.loyalty.member.account.GainStatement${rc.parentId} where memberPointId = :memberPointId and status = 'DELAY_SEND' and effectiveDate > :now "
        val params = mutableMapOf<String, Any?>().also {
            it["memberPointId"] = memberPointId
            it["now"] = date.utcStr()
        }

        val remindDate = getRemindDate(rc, date) ?: return
        when (rc.timeSign!!) {
            "=","between" -> {
                sql += " and (effectiveDate >= :effectiveDate1 and effectiveDate <= :effectiveDate2) "
                params["effectiveDate1"] = ZonedDateTime.of(remindDate, LocalTime.MIN, ASIA_SHANGHAI_ZONE_ID)
                params["effectiveDate2"] = ZonedDateTime.of(remindDate, LocalTime.MAX, ASIA_SHANGHAI_ZONE_ID)
            }
            "<" -> {
                sql += " and effectiveDate < :effectiveDate "
                params["effectiveDate"] = ZonedDateTime.of(remindDate, LocalTime.MAX, ASIA_SHANGHAI_ZONE_ID)
            }
            "<=" -> {
                sql += " and effectiveDate <= :effectiveDate "
                params["effectiveDate"] = ZonedDateTime.of(remindDate, LocalTime.MAX, ASIA_SHANGHAI_ZONE_ID)
            }
            else -> {
                logger.error("待生效积分提醒不支持的时间类型 {} id: {} name: {}", rc.timeSign, rc.id, rc.name)
                return
            }
        }

        val records = ArrayList<UpcomingEffectivePointsRecord>()
        val response = sdk.use { x -> x.execute(sql, params) }
        val rows = response.data
        val map = rows.first() as Map<String, Any?>
        val p = map["point"]?.toString()?.let { BigDecimal(it) } ?: return
        val ed = map["effectiveDate"]?.let { ZonedDateTime.parse(it.toString()) }
        val record = UpcomingEffectivePointsRecord(
            id = Uuid.uuid,
            remindId = rc.id,
            name = rc.name,
            planId = rc.planId,
            subjectId = rc.subjectId,
            subjectFqn = rc.subjectFqn,
            pointPlanId = rc.hierarchyId,
            pointPlanName = rc.hierarchyName,
            member = memberPointId,
            memberId = memberId,
            effectiveDate = null,
            overdueDate = null,
            point = p,
        ).also {
            if (rc.timeSign == "=" || rc.timeSign == "between") {
                it.effectiveDate = ed
            }
        }
        records.add(record)
        if (records.isNotEmpty()) {
            val message = UpcomingEffectivePointsRecordMessage(rc.parentId!!, records)
            streamBridge.send(REMIND_UPCOMING_POINT_RECORD_CHANNEL, message)
        }
    }


    // 等级到期提醒
    @Suppress("UNCHECKED_CAST")
    private fun processApproachingExpirationGrades(rc: RemindConfig, memberGradeId: String, date: ZonedDateTime) {
        val fqn = "data.loyalty.member.hierarchy.Grade${rc.parentId}"
        var sql = "SELECT id, memberId, currentGradeDefinitionId, currentGradeName, overdueDate FROM $fqn where id = :id and overdueDate > :now "
        val params = mutableMapOf<String, Any?>().also {
            it["id"] = memberGradeId
            it["now"] = date.utcStr()
        }

        val remindDate = getRemindDate(rc, date) ?: return
        when (rc.timeSign!!) {
            "=","between" -> {
                sql += " and (overdueDate >= :overdueDate1 and overdueDate <= :overdueDate2) limit 3000 "
                params["overdueDate1"] = ZonedDateTime.of(remindDate, LocalTime.MIN, ASIA_SHANGHAI_ZONE_ID)
                params["overdueDate2"] = ZonedDateTime.of(remindDate, LocalTime.MAX, ASIA_SHANGHAI_ZONE_ID)
            }
            "<", "<=" -> {
                sql += " and overdueDate ${rc.timeSign} :overdueDate limit 3000 "
                params["overdueDate"] = ZonedDateTime.of(remindDate, LocalTime.MAX, ASIA_SHANGHAI_ZONE_ID)
            }
            else -> {
                logger.error("等级到期提醒不支持的时间类型 {} id: {} name: {}", rc.timeSign, rc.id, rc.name)
                return
            }
        }
        val records = ArrayList<ApproachingExpirationGradeRecord>()
        val response = sdk.use { it.execute(sql, params) } ?: return
        val grades = response.data ?: emptyList()
        for (row in grades) {
            val map = row as Map<String, Any?>
            val memberId = map["memberId"].toString()
            val currentGradeDefinitionId = map["currentGradeDefinitionId"].toString()
            val currentGradeName = map["currentGradeName"].toString()
            val overdueDate = map["overdueDate"]?.let { x -> ZonedDateTime.parse(x.toString()) }

            val record = ApproachingExpirationGradeRecord(
                id = Uuid.uuid,
                remindId = rc.id,
                name = rc.name,
                planId = rc.planId,
                subjectFqn = rc.subjectFqn,
                gradeHierarchyId = rc.parentId,
                gradeHierarchyName = rc.hierarchyName,
                member = memberGradeId,
                memberId = memberId,
                currentGradeId = currentGradeDefinitionId.toLong(),
                currentGradeName = currentGradeName,
                currentEffectDate = null,
                currentOverdueDate = overdueDate,
            )

            records.add(record)
        }

        if (records.isNotEmpty()) {
            val message = ApproachingExpirationGradeRecordMessage(rc.parentId!!, records)
            streamBridge.send(REMIND_EXPIRE_GRADE_RECORD_CHANNEL, message)
        }
    }




    /**
     * 绝对时间 <早于等于> 具体时间年月日
     *
     * 待生效积分相对时间提醒
     * 距离生效时间 <等于|小于|小于等于> <x天> 的积分
     * 生效时间  <等于|小于|小于等于> 当年 <月/日> 的积分
     * 生效时间  <等于|小于|小于等于> 当年 当月 <1-31-周后一天> 的积分
     *
     * 积分到期相对时间提醒
     * 距离失效时间 <等于|小于|小于等于> <x天> 的积分
     * 失效时间  <等于|小于|小于等于> 当年 <月/日> 的积分
     * 失效时间  <等于|小于|小于等于> 当年 当月 <1-31-周后一天> 的积分
     *
     */
    private fun getRemindDate(rc: RemindConfig, date: ZonedDateTime): LocalDate? {
        val d = date.withZoneSameInstant(ZoneId.of("Asia/Shanghai"))
        // 绝对时间提醒
        if (rc.timeType == ABSOLUTE_TIME) {
            return rc.timeDate!!.localDate()
        }
        return when (rc.timeType) {
            RELATIVE_TIME_DAY -> {    // <距离生效/失效时间> <等于|小于|小于等于> <x天> 的积分/等级
                d.plusDays(rc.timeDay!!.toLong()).localDate()
            }
            RELATIVE_TIME_YEAR -> {   // 相对年 绝对月日
                val year = when (rc.timeYear) {
                    1 -> d.year + 1 // 下一年
                    else -> d.year // 当年
                }
                val monthOfYear = rc.timeMonth!!
                val dayOfMonth = rc.timeDay!!
                LocalDate.of(year, monthOfYear, dayOfMonth)
            }
            RELATIVE_TIME_YEAR_MONTH -> { // 相对年相对月 绝对天
                val year = when (rc.timeYear) {
                    1 -> d.year + 1 // 下一年
                    else -> d.year // 当年
                }
                val month = when(rc.timeMonth) {
                    1 -> d.monthValue + 1 // 下一月
                    2 -> d.monthValue + 2 // 下下个月
                    6 -> d.monthValue + 6 // 第六个月
                    else -> d.monthValue // 当月
                }
                // 获取month月的最后一天
                val lastDayOfMonth = LocalDate.of(year, month, 1).lengthOfMonth()
                // 当前仅支持最后一天 小于等于0都算作最后一天 最后一天前端会传值 -1
                if (rc.timeSign!! == "=" && rc.timeDay!! > lastDayOfMonth) {
                    logger.warn("没有计算出正确的提醒时间: {}", Json.toJson(rc))
                    null
                } else {
                    val dayOfMonth = if (rc.timeDay!! <= 0 || rc.timeDay!! > lastDayOfMonth) lastDayOfMonth else rc.timeDay!!
                    LocalDate.of(year, month, dayOfMonth)
                }
            }
            else -> {
                throw RuntimeException("不支持的时间类型${rc.timeType}")
            }
        }
    }
}