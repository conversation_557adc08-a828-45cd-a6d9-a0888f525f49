package com.shuyun.loyalty.analysis.stream

import com.shuyun.loyalty.analysis.database.ApproachingExpirationGradeRecord
import com.shuyun.loyalty.analysis.database.ApproachingExpirationPointsRecord
import com.shuyun.loyalty.analysis.database.RemindConfig
import com.shuyun.loyalty.analysis.database.UpcomingEffectivePointsRecord
import java.time.ZonedDateTime

data class RemindMessage(val rcs: List<RemindConfig>, val id: String, val date: ZonedDateTime, val memberId: String? = null)

data class ApproachingExpirationPointsRecordMessage(val hierarchyId: Long, val records: List<ApproachingExpirationPointsRecord>)
data class ApproachingExpirationGradeRecordMessage(val hierarchyId: Long, val records: List<ApproachingExpirationGradeRecord>)
data class UpcomingEffectivePointsRecordMessage(val hierarchyId: Long, val records: List<UpcomingEffectivePointsRecord>)
