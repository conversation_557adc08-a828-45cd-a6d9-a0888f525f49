package com.shuyun.loyalty

import com.shuyun.lite.client.ConfigurationManagerHolder
import com.shuyun.lite.client.PassportClientFactory
import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.sdk.Property
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication

@SpringBootApplication(proxyBeanMethods = false)
class AnalysisApplication

private val logger = LoggerFactory.getLogger(AnalysisApplication::class.java)

fun main(args: Array<String>) {
    logger.info("服务启动中...")
    runApplication<AnalysisApplication>(*args) {
        ConfigurationManagerHolder.init()
        val passportBuilder = PassportClientFactory.passportBuilder().appKey(GlobalContext.serviceName())
        val passportClient = passportBuilder.build()
        passportClient.registerClient(GlobalContext.serviceName(), GlobalContext.serviceName())
        System.setProperty("database.driverClass", Property.getSysOrEnv("database.driverClass","com.mysql.cj.jdbc.Driver"))
        System.setProperty("spring.datasource.driver-class-name", Property.getSysOrEnv("database.driverClass","com.mysql.cj.jdbc.Driver"))
    }
    logger.info("服务启动完成！")
}
