FROM hub.shuyun.com/base/java:zulu-jdk11

WORKDIR /loyalty-analysis
COPY target/loyalty-analysis.jar /loyalty-analysis/loyalty-analysis.jar

# 将脚本文件复制到项目中
COPY src/scripts/rename-lib.sh /usr/local/bin/rename-lib.sh

# 执行脚本文件，对原始文件中的依赖包进行重命名
RUN sh /usr/local/bin/rename-lib.sh /loyalty-analysis/loyalty-analysis.jar

EXPOSE 8080

RUN groupadd -g 1324 shuyunuser && useradd -m -u 1324 -g shuyunuser shuyunuser
RUN chown shuyunuser:shuyunuser -R /loyalty-analysis

ENTRYPOINT ["java", "-jar", "loyalty-analysis.jar"]