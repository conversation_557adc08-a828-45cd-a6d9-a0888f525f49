kind: "Service"
specVersion: v4
metadata:
  name: loyalty-stream
  apiVersion: "v1"
  accessPoint:
    container: basecrm/loyalty-stream
  middleware:
    mysql:
      requireSchemas: loyalty_manager
containers:
  - name: basecrm/loyalty-stream
    ports:
      - name: web
        protocol: tcp
        targetPort: 0
        containerPort: 8080
profiles:
  - name: default
    cpu: 1
    mem: 2048
    replicas: 1
    containers:
      - name: basecrm/loyalty-stream
        cpu: 1
        mem: 2048