#FROM hub.shuyun.com/base/java:eclipse-temurin-11.0.25_9-jdk-noble
#ENV TZ=Asia/Shanghai TIMEZONE=Asia/Shanghai LANG=en_US.UTF-8 LANGUAGE=en_US.UTF-8:zh:en_US:en LC_ALL=en_US.UTF-8
FROM hub.shuyun.com/base/java:zulu-jdk11
WORKDIR /loyalty-stream
COPY target/loyalty-stream.jar /loyalty-stream/loyalty-stream.jar


# 将脚本文件复制到项目中
COPY src/scripts/rename-lib.sh /usr/local/bin/rename-lib.sh

# 执行脚本文件，对原始文件中的依赖包进行重命名
RUN sh /usr/local/bin/rename-lib.sh /loyalty-stream/loyalty-stream.jar

EXPOSE 8080

RUN groupadd -g 1324 shuyunuser && useradd -m -u 1324 -g shuyunuser shuyunuser
RUN chown shuyunuser:shuyunuser -R /loyalty-stream

ENTRYPOINT ["java", "-jar", "loyalty-stream.jar"]