server:
  port: 8080
  servlet:
    context-path: /loyalty-stream/v1

spring:
  profiles:
    active: schedule, pip-common, pip-feign
  application:
    name: loyalty-stream
  aop:
    proxy-target-class: true

  cloud:
    stream:
      kafka:
        binder:
          brokers: ${kafka.brokers:127.0.0.1:9092}
          configuration:
            auto.offset.reset: latest
          auto-add-partitions: true
          auto-create-topics: true
          min-partition-count: ${kafka.min.partition.num:60}
          consumer-properties:
            session.timeout.ms: ${kafka.session.timeout.ms:60000}
            heartbeat.interval.ms: ${kafka.heartbeat.interval.ms:3000}
            max.poll.records: ${kafka.max.poll.records:1}
            max.poll.interval.ms: ${kafka.max.poll.interval.ms:600000}
          required-acks: ${kafka.binder.requiredAcks:1}
          producer-properties:
            send.buffer.bytes: ${kafka.producer.sendBufferBytes:1048576}
            batch.size: ${kafka.producer.batchSize:327680}
            linger.ms: ${kafka.linger.ms:1}
          replication-factor: ${kafka.replication.factor:1}
        bindings:
          LOYALTY_HTTP_REQUEST_ASYNC_PROCESS_INPUT:
            consumer:
              ack-mode: manual
          NORMAL_INPUT:
            consumer:
              ack-mode: manual
          GREEN_CHANNEL_INPUT:
            consumer:
              ack-mode: manual
          LOYALTY_EVENT_CHANNEL_INPUT:
            consumer:
              ack-mode: manual
              enable-dlq: true
              dlq-name: ${system.environment}_LOYALTY_EVENT_CHANNEL_DLQ
              dlq-partitions: 1
              auto-commit-on-error: true
          PARKING_LOT_INPUT:
            consumer:
              ack-mode: manual
          POINT_MODIFY_INPUT:
            consumer:
              ack-mode: manual
          GRADE_MODIFY_INPUT:
            consumer:
              ack-mode: manual
          MEDAL_MODIFY_INPUT:
            consumer:
              ack-mode: manual
          LOYALTY_LP_EXPIRE_INPUT:
            consumer:
              ack-mode: manual
          LOYALTY_LP_EFFECT_INPUT:
            consumer:
              ack-mode: manual
      bindings:
        #用于消费接口任务
        LOYALTY_HTTP_REQUEST_ASYNC_PROCESS_OUTPUT:
          destination: ${system.environment}_LOYALTY_HTTP_REQUEST_ASYNC_PROCESS
          contentType: application/json
          producer:
            partition-key-expression: headers['memberId']
        LOYALTY_HTTP_REQUEST_ASYNC_PROCESS_INPUT:
          destination: ${system.environment}_LOYALTY_HTTP_REQUEST_ASYNC_PROCESS
          group: loyalty4-group_LOYALTY_HTTP_REQUEST_ASYNC_PROCESS_${system.environment}
          contentType: application/json
          consumer:
            concurrency: ${LOYALTY_HTTP.concurrency:5}
        #用于可以积压的任务处理
        NORMAL_OUTPUT:
          destination: LOYALTY_NORMAL_${system.environment}
          contentType: application/json
        NORMAL_INPUT:
          destination: LOYALTY_NORMAL_${system.environment}
          group: loyalty4-group_LOYALTY_NORMAL_${system.environment}
          contentType: application/json
          consumer:
            concurrency: ${LOYALTY_NORMAL.concurrency:5}
        #用于不可以积压，需要及时处理的消息，它们在资源分配上会有所不同
        GREEN_CHANNEL_OUTPUT:
          destination: LOYALTY_GREEN_CHANNEL_${system.environment}
          contentType: application/json
        GREEN_CHANNEL_INPUT:
          destination: LOYALTY_GREEN_CHANNEL_${system.environment}
          group: loyalty4-group_LOYALTY_GREEN_CHANNEL_${system.environment}
          contentType: application/json
          consumer:
            concurrency: ${LOYALTY_GREEN_CHANNEL.concurrency:5}
        LOYALTY_EVENT_CHANNEL_OUTPUT:
          destination: LOYALTY_EVENT_CHANNEL_${system.environment}
          contentType: application/json
          producer:
            partition-key-expression: headers['partitionKey']
        LOYALTY_EVENT_CHANNEL_INPUT:
          destination: LOYALTY_EVENT_CHANNEL_${system.environment}
          group: loyalty4-group_LOYALTY_EVENT_CHANNEL_${system.environment}
          contentType: application/json
          consumer:
            concurrency: ${LOYALTY_EVENT_CHANNEL.concurrency:5}
            header-mode: headers
            max-attempts: 2
        #用于存放处理失败的消息记录
        PARKING_LOT_OUTPUT:
          destination: LOYALTY_PARKING_LOT_${system.environment}
          contentType: application/json
          producer:
            partition-key-expression: headers['memberId']
        PARKING_LOT_INPUT:
          destination: LOYALTY_PARKING_LOT_${system.environment}
          group: loyalty4-group_LOYALTY_PARKING_LOT_${system.environment}
          contentType: application/json
          consumer:
            concurrency: ${LOYALTY_PARKING_LOT.concurrency:5}
        #等级变更通知
        GRADE_NOTIFY_OUTPUT:
          destination: LOYALTY_GRADE_NOTIFY_${system.environment}
          contentType: application/json
          producer:
            partition-key-expression: headers['memberId']
        #积分变更通知
        POINT_NOTIFY_OUTPUT:
          destination: LOYALTY_POINT_NOTIFY_${system.environment}
          contentType: application/json
          producer:
            partition-key-expression: headers['memberId']
        #变更记录通知
        RECORD_NOTIFY_OUTPUT:
          destination: LOYALTY_RECORD_NOTIFY_${system.environment}
          contentType: application/json
          producer:
            partition-key-expression: headers['memberId']
        #时机已计算通知
        LOYALTY_EVENT_NOTIFY_OUTPUT:
          destination: LOYALTY_EVENT_NOTIFY_${system.environment}
          contentType: application/json
          producer:
            partition-key-expression: headers['partitionKey']
        LOYALTY_ASYNC_NOTIFY_OUTPUT:
          destination: LOYALTY_ASYNC_NOTIFY_${system.environment}
          contentType: application/json
          producer:
            partition-count: 1
            partition-key-expression: headers['partitionKey']
        #对外接口积分变更
        POINT_MODIFY_INPUT:
          destination: POINT_MODIFY_CHANNEL_${system.environment}
          group: loyalty4-group_POINT_MODIFY_CHANNEL_${system.environment}
          contentType: application/json
          consumer:
            concurrency: ${POINT_MODIFY_CHANNEL.concurrency:5}
        #对外接口等级变更
        GRADE_MODIFY_INPUT:
          destination: GRADE_MODIFY_CHANNEL_${system.environment}
          group: loyalty4-group_GRADE_MODIFY_CHANNEL_${system.environment}
          contentType: application/json
          consumer:
            concurrency: ${GRADE_MODIFY_CHANNEL.concurrency:5}
        #对外接口勋章变更
        MEDAL_MODIFY_INPUT:
          destination: MEDAL_MODIFY_CHANNEL_${system.environment}
          group: loyalty4-group_MEDAL_MODIFY_CHANNEL_${system.environment}
          contentType: application/json
          consumer:
            concurrency: ${MEDAL_MODIFY_CHANNEL.concurrency:5}
        #用于处理过期积分
        LOYALTY_LP_EXPIRE_OUTPUT:
          destination: ${system.environment}_LOYALTY_LP_EXPIRE
          contentType: application/json
          producer:
            partition-key-expression: headers['memberId']
        LOYALTY_LP_EXPIRE_INPUT:
          destination: ${system.environment}_LOYALTY_LP_EXPIRE
          group: loyalty4-group_LOYALTY_LP_EXPIRE_${system.environment}
          contentType: application/json
          consumer:
            concurrency: ${LOYALTY_EXPIRE.concurrency:5}
        #用于处理生效积分
        LOYALTY_LP_EFFECT_OUTPUT:
          destination: ${system.environment}_LOYALTY_LP_EFFECT
          contentType: application/json
          producer:
            partition-key-expression: headers['memberId']
        LOYALTY_LP_EFFECT_INPUT:
          destination: ${system.environment}_LOYALTY_LP_EFFECT
          group: loyalty4-group_LOYALTY_LP_EFFECT_${system.environment}
          contentType: application/json
          consumer:
            concurrency: ${LOYALTY_EFFECT.concurrency:5}
      binders:
        bind:
          type: kafka

  datasource:
    url: ${database.url}
    username: ${database.username}
    password: ${database.password}
  redis:
    host: ${redis.host}
    port: ${redis.port}
    password: ${redis.password}
    lettuce:
      pool:
        max-active: ${lettuce.pool.max-active:64}
        max-idle: ${lettuce.pool.max-idle:64}
        min-idle: ${lettuce.pool.min-idle:1}
        max-wait: ${lettuce.pool.max-wait:-1ms}
        time-between-eviction-runs: 30000ms

management:
  endpoints:
    web:
      base-path: /system
      exposure:
        include: health,info,metrics,prometheus,loggers
  endpoint:
    health.show-details: always