#禁用配置中心默认将配置配置上传功能
spectrum.disableAutoUploadProperties=true

#kafka

#jpa
spring.datasource.druid.initial-size=5
spring.datasource.druid.max-active=100
spring.datasource.druid.validation-query=select 1
spring.datasource.druid.test-on-borrow=true
spring.datasource.druid.test-on-return=true
spring.datasource.druid.test-while-idle=true
spring.jpa.generate-ddl=false
spring.jpa.hibernate.ddl-auto=none
spring.jpa.properties.org.hibernate.flushMode=MANUAL
spring.jpa.properties.hibernate.show_sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.show-sql=false
spring.jpa.open-in-view=false

#druid
spring.datasource.druid.filter.stat.logSlowSql=true
spring.datasource.druid.filter.stat.slowSqlMillis=1000
spring.datasource.druid.filter.stat.merge-sql=true

spring.messages.basename=i18n/messages,fx/messages

#logging
logging.format=${system.logging.format:[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%-5level] [%class{0}:%M:%L] [%t] [%X{loyalty_trace_id}] [-[%msg]-] %n}
logging.file.path=${system.logging.dir:/var/log}

#ribbon
ribbon.ReadTimeout=120000

#异常码
exception.error.response.module=loyalty
exception.error.response.module.code=06