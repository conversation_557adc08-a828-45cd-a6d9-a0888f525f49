1001=plan filed, plan name :{0}
1002=plan does not exist, plan id:{0}
1003=duplicate plan name
1004=the body already exists and is not allowed to be added repeatedly
1005=principal for dataType repeat
1006=the corresponding body is not enabled
1007=principal {0} is being referenced on the business side and cannot be disabled. To disable, go to the business side to terminate the rule
1008=the current principal is being referenced on the business side and cannot be disabled. To disable, go to the business side to terminate the rule
1009=opportunity {0} is being referenced by the business side, cannot be disabled. To disable, go to the business side to terminate the rule
1010=the current time is being referenced by the business side and cannot be disabled. To disable, go to the business side to terminate the rule
1011=the timing object does not exist
1012=the body object does not exist
1013=the timing property {0} is being referenced on the business side and cannot be disabled. To disable, go to the business side to terminate the rule
1014=principal property {0} is being referenced on the business side and cannot be disabled. To disable, go to the business side to terminate the rule
1015=the current property template is being referenced on the business side and cannot be disabled. To disable, go to the business side to terminate the rule
1016=the current property is being referenced on the business side and cannot be disabled. To disable, go to the business side to terminate the rule
1017=credit account type {0} is being referenced by the business side and cannot be archived. If you want to archive, please go to the business side to terminate the relevant rules
1018=the class rule group name already exists
1019=the current version does not exist and cannot be modified
1020=previous version not found
1021=the subject does not exist
1022=the rule group does not exist
1023=the integral rule group name already exists
1024=configuration does not exist
1025=archived plans do not allow you to return to the last release
1026=unreleased plans do not allow you to return to the previous release
1027=the release schedule does not allow you to return to the previous release
1028=failed release plans are not allowed to return to the previous release
1029=only released or failed plans can be republished
1030=publish only when edited
1031=archived plans are not allowed to be published
1032=plans in release are not allowed to be released
1033=failed release plans are not allowed to be released
1034=archiving is not allowed for unpublished plans
1035=archived plans do not allow archiving
1036=plans in release do not allow archiving
1037=failed release plans do not allow archiving
1038=the hierarchy of archived published plans, the status can only be archived
1039=the current plan is being referenced on the business side and cannot be archived. To archive, go to the business end to terminate the rule
1040=the current credit account type is being referenced by the business side, so this operation cannot be carried out. If you want to archive, please go to the business side to terminate the relevant rules
1041=this rule group does not allow archiving
1042=there is a hierarchy rule running under the current hierarchy system, so this operation cannot be carried out. If you need to file, please terminate the relevant rules at the business end before the operation
1043=the time when the undrafted state is not allowed to be deleted for published plans
1044=published plans do not allow the deletion of undrafted account types
1045=published plans do not allow the removal of properties in undrafted state
1046=off-draft version is not allowed to be deleted
1047=published plans do not allow the removal of subjects in undrafted state
1048=this rule group is not allowed to be deleted
1049=the current level is running, so this operation cannot be carried out. If it needs to be deleted, please terminate the relevant rules at the business end before the operation
1050=user exists at the current level and cannot be deleted
1051=published plans do not allow the removal of undrafted status hierarchies
1052=deleted from published plans is not allowed
1053=archived plans do not allow deletion
1054=deletion is not allowed in the release schedule
1055=failed plans are not allowed to be deleted
1056=a hierarchy of undrafted states of published plans, which can only be published or archived
1057=historical version is not allowed to change
1058=the configuration of archived plans is not allowed to be modified
1059=archived plans do not allow editing
1060=plan cannot be edited in publishing
1061=plan failed to publish cannot be edited
1062=there are level rules running under the level system {0} and they cannot be archived. If you need to archive, please terminate the relevant rules at the business side first and then operate
1063=level {1} under level system {0} has a level rule running or a member, which cannot be deleted
1064=the published plan information is incomplete. The plan contains at least one entity, each entity contains at least one opportunity and any one of the credit account types or rating systems, and each opportunity or entity it belongs to contains at least one attribute.
1065=duplicate principal attribute template name
1066=duplicate account type name
1067=duplicate opportunity name
1068=event flow repeat corresponding to time
1069=duplicate grade system name
1070=only one level of rules can be saved at a time
1071=rule group does not allow start time less than current time
1072=rule group does not allow start time greater than end time
1073=the start time is not allowed to be less than the rule group time
1074=the start time is not allowed to be less than the end time during the validity period of the integral
1075=configuration changed to historical version or deleted
1076=change status to new not allowed
1077=enable status does not allow transition to disable draft
1078=enable draft not allowed to be disabled
1079=disable status does not allow transition to enable draft
1080=disable draft not allowed to be enabled
1081=enable failed. The principal involved is not enabled, the current operation is invalid
1082=no plan released
1083=illegal grade validity
1084=level rule group list is empty1101=no user scores against the account
1102=account not available
1103=the integral has expired
1104=the record with ID {1} under the credit account {0} does not exist
1105=unsupported operation type: {0}
1106=the integral value is not allowed to be less than or equal to 0
1107=the data corresponding to the original single cannot be found
1108=points account is not allowed to deduct
1109=deduction failed, insufficient available balance
1110=the current integral deduction failed, the deduction may not be allowed or there is no valid integral
1111=points account is not allowed to be abandoned
1112=points account is not allowed to be frozen
1113=points account is not allowed to be thawed
1114=the corresponding valid integral has expired
1115=the corresponding frozen integral has expired
1116=the corresponding integral state has changed, and the operation will continue after refreshing
1117=credit account initialization failed
1118=the integral account is deducted negative, after setting the switch, the integral account limit is less than the deduction value
1119=the expiry date is not allowed to be less than the start date
1120=is not allowed to be less than or equal to 0
1121=deduction failed, insufficient available balance
1122=frozen failed, insufficient available balance
1123=Beyond {0} manually point limit
1124=Beyond manually point limit, Does it need to be submitted for approval?
1125=failed to deduct point segment
1126=failed to plus point segment
1127=failed to create point segment
1201=no hierarchy exists
1202=no target level, unable to initialize the member level object
1203=illegal type
1204=expired time before the current time
1205=rank system {0} cannot find member :{1} original rank
1206=class rule time parameter type mismatch
1301=member does not exist
1302={0} failed, member is in blacklist
1401=unknown exception
1402=not found
1403=request exception
1404=validation parameter failed
1409=limiting processing
1501=X - Business - Token required
1502=this x-business-token has been executed
1503=channelType illegally
1504=the number of submissions cannot exceed {0}
1505=Sub card member cannot be found or sub card member has no points
1506=closing in progress. No other operations are allowed
1507=Please add the card - closing mapping model at the implementation end before the card - closing operation
1508=The change mode does not match
1510=The original label does not allow repeated operation of the same type
1509=The thawing and consuming integral do not match
1601=the timing does not match the subject
1602=the rule corresponding to the level cannot be found
1701=error writing to file
1702=error closing file
1703=Mysql anomalies
1704=Failed to open data service connection
1705=The transaction type is not supported, {0}
1706=Whether the transaction is closed, {0}
1707=File export error
1708=File upload failed, please check file service configuration
1709=error operating data service
1801=account type
1802=change value
1803=effective time of points
1804=integral failure time
1805=remarks
1806=change
1807=change of credit account name
1808=effective immediately
1809=permanent
1810=bonus points
1811=offline exchange
1812=hierarchy
1813=grade after change
1814=grade failure time
1815=change level system name
1816=VIP
1817=consumption upgrade
1818=senior member
1819=Double 11 order reward
1820=failure record
1821=row number resolver does not match
1822=integral format error
1823=effective date cannot be empty
1824=the expiration time cannot be less than the current time
1825=expiration date cannot be empty
1826=invalid date error
1827=effective time cannot be less than current time
1828=effective time cannot be greater than or equal to expiration time
1829=date must be empty when points are deducted
1830=the expiration time cannot be less than the current time
1831=account type name mismatch
1832=level name mismatch
1833=remark cannot be greater than 200 words
1834=hierarchy name mismatch
1835=from line {0}, hierarchy does not exist
1836=from line {0}, plan filed
1837=from line {0}, account not available
1838=in line {0}, import interrupt occurred
1839=error message
1840=save error file, unknown exception
1841=from line {0}, unable to process file, import failed
1842=calculation error
1843=plan
1844=shopId
1091=no hierarchy exists

GRADE_UPGRADE=grade upgrade
GRADE_HOLD_OR_DEGRADE=grade hold or degrade
GRADE_RECALCULATE=grade recalculate
GRADE_HOLD=grade hold
GRADE_DEGRADE=grade degrade
POINT_SEND=point send
POINT_RECALCULATE=point recalculate
POINT_FREEZE=point freeze
POINT_UNFREEZE=point unfreeze
POINT_DEDUCT_BY_USE=point deduct by use
POINT_DEDUCT_BY_ABOLISH=point deduct by abolish
POINT_FREEZE_RECALCULATE=point freeze recalculate
POINT_DEDUCT_RECYCLE=point deduct recycle
POINT_SEND_RECYCLE=point send recycle
MODIFY_MEMBER_GRADE=modify member grade
MODIFY_MEMBER_MEDAL=modify member medal
SINGLE=single
TOTAL=total
MEDAL_OBTAIN=medal obtain
MEDAL_KEEP=medal keep
MEDAL_RECYCLE=medal recycle