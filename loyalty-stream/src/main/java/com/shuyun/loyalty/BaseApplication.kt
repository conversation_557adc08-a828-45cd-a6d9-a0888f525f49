package com.shuyun.loyalty

import com.fasterxml.jackson.core.json.JsonReadFeature
import com.shuyun.lite.client.ConfigurationManagerHolder
import com.shuyun.lite.client.PassportClientFactory
import com.shuyun.lite.context.GlobalContext
import com.shuyun.lite.util.Common
import com.shuyun.loyalty.service.infrastructure.es.EventClient
import com.shuyun.pip.component.json.JsonUtils
import org.springframework.boot.SpringApplication
import org.springframework.context.ConfigurableApplicationContext

open class BaseApplication(vararg primarySources: Class<*>?) : SpringApplication(*primarySources) {

    override fun run(vararg args: String?): ConfigurableApplicationContext {
        System.setProperty("CryptTools.crypt.not", "true")
        System.setProperty("hystrix.command.default.requestLog.enabled", "false")
        System.setProperty("log.warn.time", "500")
        System.setProperty("druid.mysql.usePingMethod", "false")
        System.setProperty("tenant.tables", "derivation_event_metas,error_event,grade_rule,grade_rule_group,grade_transfer_task,import_point_or_grade,open_batch_log,point_deduct_rule,point_deduct_rule_operate,point_rule_group,point_send_rule,t_s_sequence_event,tmp_table,transfer_point_or_grade,point_send_limit_rule")
        ConfigurationManagerHolder.init()
        System.setProperty("database.driverClass", Common.getSysOrEnv("database.driverClass","com.mysql.cj.jdbc.Driver"))
        System.setProperty("spring.datasource.driver-class-name", Common.getSysOrEnv("database.driverClass","com.mysql.cj.jdbc.Driver"))
        registerService()
        defaultExcludeAutoconfigure()
        // 解决json中有换行符的问题
        val mapper = JsonUtils.objectMapper()
        mapper.enable(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature())
        return super.run(*args)
    }

    private fun registerService() {
        val passportBuilder = PassportClientFactory.passportBuilder().appKey(GlobalContext.serviceName())
        val passportClient = passportBuilder.build()
        passportClient.registerClient(GlobalContext.serviceName(), GlobalContext.serviceName())
        EventClient.registerClient()
    }

    private fun defaultExcludeAutoconfigure() {
        val excludeClassName = "org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration,org.springframework.cloud.openfeign.ribbon.FeignRibbonClientAutoConfiguration"
        val excludeEnv = Common.getSysOrEnv("spring.autoconfigure.exclude")
        val newExcludeClassNames = if (excludeEnv.isNullOrEmpty()) excludeClassName else "$excludeEnv,$excludeClassName"
        System.setProperty("spring.autoconfigure.exclude", newExcludeClassNames)
    }

}