package com.shuyun.loyalty

import com.shuyun.fx.configuration.EnableFx
import com.shuyun.loyalty.stream.kafka.sink.KafkaSource
import org.apache.logging.log4j.LogManager
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.cloud.stream.annotation.EnableBinding
import org.springframework.data.jpa.repository.config.EnableJpaRepositories


/**
 * 1、开启死信队列，将所有处理异常的消息，放到列信队列中。
 * 2、监听所有死信队列，进行通知消息推送
 * 3、开启kafka transaction，需要参见spring kafka机制
 * 4、使用partition解决系统内部并发partitions = instance count * concurrency
 * 5、动态指定target topic (Using Dynamically Bound Destinations) https://docs.spring.io/spring-cloud-stream/docs/current/reference/html/_configuration_options.html#binding-properties
 */
@EnableFx
@SpringBootApplication
@EnableJpaRepositories
@EnableBinding(KafkaSource::class)
class StreamApplication: BaseApplication(StreamApplication::class.java)

private val logger = LogManager.getLogger(StreamApplication::class.java)

fun main(args: Array<String>) {
    StreamApplication().run(*args)
    logger.info("服务启动完成！")
}

