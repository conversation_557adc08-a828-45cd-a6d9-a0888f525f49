package com.shuyun.loyalty.stream.sequence

import org.slf4j.LoggerFactory
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.data.redis.core.script.DefaultRedisScript
import org.springframework.stereotype.Component
import java.time.Duration


@Component
class MessageCounter(val redisTemplate: RedisTemplate<String, Any?>) {

    enum class K(val key: String) {
        EFFECT_POINT_MESSAGE_COUNT("effect_point_message_count_key_"),
        EXPIRE_POINT_MESSAGE_COUNT("expire_point_message_count_key_"),
        EXPIRE_GRADE_MESSAGE_COUNT("expire_grade_message_count_key_"),
        EXPIRE_MEDAL_MESSAGE_COUNT("expire_medal_message_count_key_"),
    }

    companion object {
        // Lua script for atomic INCR and EXPIRE
        // KEYS[1] = counterKey
        // ARGV[1] = ttl_seconds
        private val incrScript = DefaultRedisScript<Long>().apply {
            setScriptText("""
                local key = KEYS[1]
                local ttl_seconds = ARGV[1]
                local new_value = redis.call('INCR', key)
                redis.call('EXPIRE', key, ttl_seconds)
                return new_value
            """.trimIndent())
            resultType = Long::class.java
        }

        private val decrScript = DefaultRedisScript<Long>().apply {
            setScriptText("""
                local key = KEYS[1]
                local new_value = redis.call('DECR', key)
                if new_value <= 0 then
                    redis.call('SET', key, 0)
                end
                return new_value
            """.trimIndent())
            resultType = Long::class.java
        }

        private val maxExpireTime = Duration.ofDays(2)
        private const val DEFAULT_MESSAGE_COUNT_LIMIT = 3000L
        private val logger = LoggerFactory.getLogger(MessageCounter::class.java)
    }

    fun initPointCounter(id: Set<Long>) {
        id.forEach {
            redisTemplate.opsForValue().setIfAbsent(K.EFFECT_POINT_MESSAGE_COUNT.key + it, 0, maxExpireTime)
            redisTemplate.opsForValue().setIfAbsent(K.EXPIRE_POINT_MESSAGE_COUNT.key + it, 0, maxExpireTime)
        }
    }

    fun initGradeCounter(id: Set<Long>) {
        id.forEach {
            redisTemplate.opsForValue().setIfAbsent(K.EXPIRE_GRADE_MESSAGE_COUNT.key + it, 0, maxExpireTime)
        }
    }

    fun initMedalCounter(id: Set<Long>) {
        id.forEach {
            redisTemplate.opsForValue().setIfAbsent(K.EXPIRE_MEDAL_MESSAGE_COUNT.key + it, 0, maxExpireTime)
        }
    }

    fun increment(id: Long, k: K) {
        try {
            redisTemplate.execute(incrScript, listOf(k.key + id), maxExpireTime.seconds)
        } catch (e: Exception) {
            logger.error("Error incrementing message count for key: ${k.key + id}", e)
        }
    }

    fun decrement(id: Long, k: K) {
        try {
            redisTemplate.execute(decrScript, listOf(k.key + id))
        } catch (e: Exception) {
            logger.error("Error decrementing message count for key: ${k.key + id}", e)
        }
    }

    fun get(id: Long, k: K): Long {
        try {
            return redisTemplate.opsForValue().get(k.key + id)?.toString()?.toLongOrNull() ?: 0L
        } catch (e: Exception) {
            logger.error("Error getting message count for key: ${k.key + id}", e)
        }
        return 0L
    }

    fun canProduce(id: Long, k: K): Boolean {
        val currentCount = get(id, k)
        return currentCount < DEFAULT_MESSAGE_COUNT_LIMIT
    }

}