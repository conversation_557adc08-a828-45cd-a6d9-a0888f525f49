package com.shuyun.loyalty.stream.handler.point.process

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.entity.enums.NegativeStrategyEnum
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.service.datamodel.MemberPoint
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.loyalty.service.transfer.points.MemberPointInvalidTransfer
import com.shuyun.loyalty.service.transfer.points.PointEventType
import com.shuyun.loyalty.stream.handler.point.MemberPointDeductCalculate
import com.shuyun.loyalty.stream.handler.point.ReturnOrderContext
import com.shuyun.loyalty.stream.message.PointCalcMessage
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class MemberPointDiscardProcess : MemberPointDeductCalculate() {

    private val log = LogManager.getLogger(MemberPointDiscardProcess::class.java)

    @Autowired
    private lateinit var memberPointInvalidTransfer: MemberPointInvalidTransfer

    override fun type() = EventOperationEnum.DISCARD


    override fun process(memberPoint: MemberPoint, message: PointCalcMessage, group: PointRuleGroup) {
        log.info("废弃积分计算 规则组：{} 积分账户类型({} {})", group.groupName, message.pointAccountType.id, message.pointAccountType.name)

        if (message.pointAccountType.negativeStrategy == NegativeStrategyEnum.NOT_ALLOWED) {
            log.info("积分配置不允许扣减，废弃积分结束")
            return
        }

        // 当前时机
        val invalidE = message.eventType

        val sets = HashSet<String>()

        //一个规则组下满足条件规则
        val passedRule = filterRule(message.subject.dataType!!, message.event, group.deductRuleList, invalidE) ?: return

        val lp = message.toLoyaltyPoints(memberPoint, group, passedRule)
        memberPointInvalidTransfer.checkSpecialList(lp, ForbiddenOperation.POINT_DEDUCT_BY_ABOLISH, ForbiddenPort.CALC_EVENT)
        // 解冻的关联时机是冻结， 冻结的关联的时机是发放
        for (relatedE in invalidE.relatedEventTypes!!) {
            if (!message.checkReturnOrder(invalidE.id!!, relatedE.id!!)) {
                continue
            }
            // 作废积分关联的时机只能是发放或者冻结
            if (relatedE.operation == EventOperationEnum.FREEZE) {
                for (sendE in relatedE.relatedEventTypes!!) {
                    if (!sets.add("${sendE.id}-${passedRule.quotaType}")) {
                        continue
                    }
                    // 查询是否冻结的记录
                    val gss = gainService.findByTraceIdList(
                        lp.hierarchy.id,
                        sendE.id!!,
                        lp.attr.traceId,
                        lp.member.id
                    )
                    if (gss.isEmpty()) continue
                    for (gs in gss) {
                        if (gs.status == FSMPointEvent.ABOLISH) {
                            log.info("该冻结的积分已经作废中，无需重复作废")
                            continue
                        }
                        val frozenPoint = frozenPointService.findFrozenPointByGainStatementId(gs.id!!, gs.pointPlanId!!)
                        if (frozenPoint == null) {
                            val ttl = invalidE.ttl(message.event.getOccurrenceTs())
                            if (ttl > 0) {
                                ReturnOrderContext.add(message.traceId!!, invalidE.id!!, relatedE.id!!, ttl, message.rawEvent)
                                continue
                            }
                            log.debug("作废关联的冻结记录不存在, 作废冻结结束 原单ID: {}", lp.attr.traceId)
                            continue
                        }

                        // 设置时机
                        val sendEOccurrenceTs = gs.occurrenceTs?.toLong() ?: continue
                        lp.eventType = initPointEventType(invalidE, sendE, sendEOccurrenceTs, message)
                        lp.pointValue = frozenPoint.point.abs()
                        lp.attr.businessId = gs.id
                        lp.member.point = lp.afterTotalPoints

                        memberPointInvalidTransfer.invalidFrozen(lp, gs, frozenPoint)

                        log.info(
                            "积分作废冻结完成 计划名称：{} 积分体系名称：{} 时机名称：{} 会员ID：{} 追溯ID：{} 规则组名称：{}, 规则名称：{}, 积分状态：{}, 作废积分：{} ",
                            lp.plan.name,
                            lp.hierarchy.name,
                            lp.eventType!!.name,
                            lp.member.memberId,
                            lp.attr.traceId,
                            group.groupName,
                            passedRule.ruleName,
                            frozenPoint.fromStatus,
                            frozenPoint.point
                        )
                    }
                    val oldGSS = Json.copy(gss)
                    val newGSS = oldGSS.map {
                        val g = Json.copy(it)
                        g.point = BigDecimal.ZERO
                        g
                    }
                    memberPointInvalidTransfer.releaseLimit(lp, oldGSS, newGSS, relatedE.id!!)
                }
            }
            else if (relatedE.operation == EventOperationEnum.SEND) {
                if (!sets.add("${relatedE.id}-${passedRule.quotaType}")) {
                    continue
                }
                val matchingTimeMillis = findSendMatchingTimeMillis(message.pointAccountType.id!!, lp.member.id, lp.member.memberId,relatedE, lp.attr.traceId)
                if (matchingTimeMillis == null) {
                    // 检验是否配置了等待原单
                    val ttl = invalidE.ttl(message.event.getOccurrenceTs())
                    if (ttl > 0) {
                        ReturnOrderContext.add(message.traceId!!, invalidE.id!!, relatedE.id!!, ttl, message.rawEvent)
                        continue
                    }
                    log.warn("原单不存在, 作废发放结束 原单ID: {}", lp.attr.traceId)
                    continue
                }
                // 查找作废关联的发放原单获取记录
                val gss = gainService.findByTraceIdList(
                    message.pointAccountType.id!!,
                    relatedE.id!!,
                    lp.attr.traceId,
                    lp.member.id
                )

                if (gss.isEmpty()) {
                    continue
                }

                // 设置时机
                lp.eventType = PointEventType(
                    invalidE.id!!,
                    invalidE.name!!,
                    message.event.getOccurrenceTs(),
                    message.eventType.operation!!,
                    PointEventType(relatedE.id!!, relatedE.name!!, matchingTimeMillis, relatedE.operation!!)
                )
                val oldGSS = Json.copy(gss)
                for (gs in gss) {
                    if (gs.overdueDate.shDate() < lp.date.shDate() || gs.status == FSMPointEvent.EXPIRE || gs.status == FSMPointEvent.FREEZE) {
                        continue
                    }
                    if (gs.status == FSMPointEvent.ABOLISH) {
                        log.debug("积分已被作废, 无需重复作废 原单ID: {}", lp.attr.traceId)
                        continue
                    }
                    val frozenPoint = frozenPointService.findFrozenPointByGainStatementId(gs.id!!, gs.pointPlanId!!)
                    if (frozenPoint != null) {
                        log.debug("积分已被冻结, 作废冻结结束 原单ID: {}", lp.attr.traceId)
                        continue
                    }
                    lp.pointValue = gs.point.abs()
                    lp.attr.businessId = gs.id
                    lp.member.point = lp.afterTotalPoints

                    memberPointInvalidTransfer.invalidSend(lp, gs)

                    log.info(
                        "积分作废完成 计划名称：{} 积分体系名称：{} 时机名称：{} 会员ID：{} 追溯ID：{} 规则组名称：{}, 规则名称：{}, 积分状态：{}, 作废积分：{} ",
                        lp.plan.name,
                        lp.hierarchy.name,
                        lp.eventType!!.name,
                        lp.member.memberId,
                        lp.attr.traceId,
                        group.groupName,
                        passedRule.ruleName,
                        gs.status,
                        lp.afterPoints
                    )
                }
                val newGSS = oldGSS.map {
                    val g = Json.copy(it)
                    g.point = BigDecimal.ZERO
                    g
                }
                memberPointInvalidTransfer.releaseLimit(lp, oldGSS, newGSS, relatedE.id!!)
            }
            else continue
        }

    }
}