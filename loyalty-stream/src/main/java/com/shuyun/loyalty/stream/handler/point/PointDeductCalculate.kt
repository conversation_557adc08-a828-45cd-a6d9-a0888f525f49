package com.shuyun.loyalty.stream.handler.point

import com.shuyun.loyalty.service.datamodel.MemberPoint
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.loyalty.stream.message.PointCalcMessage

interface PointDeductCalculate {

    fun type(): EventOperationEnum

    fun calc(memberPoint: MemberPoint, message: PointCalcMessage, group: PointRuleGroup)
}