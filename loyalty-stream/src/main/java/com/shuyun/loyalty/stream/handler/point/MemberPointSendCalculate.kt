package com.shuyun.loyalty.stream.handler.point


import com.pip.shuyun.pool.transaction.DmTransaction
import com.pip.shuyun.pool.transaction.TransactionInfoHolder
import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.entity.dto.MemberPointMessage
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.entity.enums.ProcessRecordTypeEnum
import com.shuyun.loyalty.service.datamodel.MemberPoint
import com.shuyun.loyalty.service.datamodel.save
import com.shuyun.loyalty.service.extension.isSdkDuplicate
import com.shuyun.loyalty.service.kafka.IPointSyncProducer
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.metrics.EventDuplicatedMetrics
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.loyalty.service.service.*
import com.shuyun.loyalty.service.transfer.points.MemberPointSendTransfer
import com.shuyun.loyalty.service.transfer.points.PointEventType
import com.shuyun.loyalty.service.transfer.points.PointGroup
import com.shuyun.loyalty.service.transfer.points.PointIssuanceRule
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.ProcessRecordUtil
import com.shuyun.loyalty.service.util.SendLimitUtils
import com.shuyun.loyalty.stream.handler.point.process.PointDeductFactory
import com.shuyun.loyalty.stream.message.PointCalcMessage
import com.shuyun.loyalty.stream.service.ReturnOrderService
import com.shuyun.pip.ApplicationContextHolder
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime

@Component
class MemberPointSendCalculate {

    private val logger = LogManager.getLogger(MemberPointSendCalculate::class.java)

    @Autowired
    private lateinit var memberPointSendTransfer: MemberPointSendTransfer

    @Autowired
    private lateinit var pointRuleGroupBaseService: PointRuleGroupBaseService

    @Autowired
    private lateinit var pointSendRuleBaseService: PointSendRuleBaseService

    @Autowired
    private lateinit var pointDeductRuleBaseService: PointDeductRuleBaseService

    @Autowired
    private lateinit var pointDeductRuleOperateBaseService: PointDeductRuleOperateBaseService

    @Autowired
    private lateinit var specialListService: SpecialListService

    @Autowired
    private lateinit var returnOrderService: ReturnOrderService

    @Autowired
    private lateinit var eventDuplicatedMetrics: EventDuplicatedMetrics

    @Autowired
    private lateinit var memberPointService: MemberPointService

    @DmTransaction
    fun calc(message: PointCalcMessage) {

        val memberPoint = memberPointService.getOrCreate(message.pointAccountType.id!!, message.memberId)

        logger.info("事件计算-查询或创建会员积分账户 {} {}", memberPoint.pointPlanId, memberPoint.memberId)

        if (!message.skipIdempotentCheck && ProcessRecordUtil.isProcessRecord(message.pointAccountType.id.toString(), message.event.getKey(), ProcessRecordTypeEnum.POINT)) {
            logger.warn("积分计算-事件重复提交 key: {} {}", message.event.getKey(), message.event)
            eventDuplicatedMetrics.increment()
            return
        }

        val pointRuleGroups = pointRuleGroupBaseService.findPointRuleGroup(
            message.plan.id!!,
            message.pointAccountType.id!!,
            message.eventType.id!!,
            message.eventType.operation!!,
            message.matchingTime
        ).filter { it.fileStatus != true }
        if (pointRuleGroups.isEmpty()) {
            logger.info(
                "没有配置任何积分规则组 类型: {} eventFQN: {} matchingTime: {} accountTypeId: {} accountTypeName: {}",
                message.eventType.operation,
                message.eventType.eventStream,
                ZonedDateTime.ofInstant(Instant.ofEpochMilli(message.matchingTime.time), ZoneId.systemDefault()),
                message.pointAccountType.id,
                message.pointAccountType.name
            )
            return
        }
        try {
            logger.info("积分计算-计算之前 积分账号ID: {} 剩余积分: {}", memberPoint.pointPlanId, memberPoint.point)
            if (message.eventType.operation!! == EventOperationEnum.SEND) {
                calcSending(memberPoint, message, pointRuleGroups)
            } else {
                calcAnother(memberPoint, message, pointRuleGroups)
            }
            logger.info("积分计算-计算之后 积分账号ID: {} 剩余积分: {}", memberPoint.pointPlanId, memberPoint.point)
            returnOrderService.save(message.pointAccountType.id!!, message.memberId, message.traceId!!, message.eventType, ReturnOrderContext.get())
            TransactionInfoHolder.afterCommit {
                ApplicationContextHolder.getBean(IPointSyncProducer::class.java).send(
                    MemberPointMessage(
                        message.pointAccountType.id!!,
                        message.memberId,
                        memberPoint.id!!
                    )
                )
            }
        } finally {
            SendLimitUtils.clear()
            ReturnOrderContext.remove()
        }
    }


    private fun calcSending(memberPoint: MemberPoint, message: PointCalcMessage, groups: List<PointRuleGroup>) {
        val list = ArrayList<PointRuleGroup>()
        for (group in groups) {
            if (group.scoreType != EventOperationEnum.SEND) {
                continue
            }
            val rules = pointSendRuleBaseService.findByRuleGroupIdAndDisabled(group.id!!)
            if (rules.isEmpty()) {
                continue
            }
            group.sendRuleList = rules
            list.add(group)
        }
        if (list.isEmpty()) {
            logger.info(
                "规则组下无有效规则 类型: {} eventFQN: {} accountTypeId: {} accountTypeName: {}",
                message.eventType.operation,
                message.eventType.eventStream,
                message.pointAccountType.id,
                message.pointAccountType.name
            )
            return
        }
        val isBlackList = specialListService.check(
            message.subject.dataType!!,
            message.memberId,
            ForbiddenOperation.POINT_SEND,
            ForbiddenPort.CALC_EVENT,
            message.forbiddenConfigs
        )
        if (isBlackList){
            logger.warn("积分计算被特殊名单拦截")
            return
        }
        list.forEach {
            try {
                sendPoint(memberPoint, message, it)
            } catch (e: Throwable) {
                logger.error("规则组积分计算异常：规则组{}", it.groupName, e)
            }
        }
        saveMemberPointEvent(message)
    }


    private fun calcAnother(memberPoint: MemberPoint, message: PointCalcMessage, groups: List<PointRuleGroup>) {
        val list = ArrayList<PointRuleGroup>()
        for (group in groups) {
            if (group.scoreType != message.eventType.operation) {
                continue
            }
            val deductRuleList = pointDeductRuleBaseService.findPointRuleList(group.id!!)
            if (deductRuleList.isEmpty()) continue
            for (pointDeductRule in deductRuleList) {
                pointDeductRule.pointDeductRuleOperateList = pointDeductRuleOperateBaseService.findPointRuleOperateList(pointDeductRule.id!!)
                if (pointDeductRule.pointDeductRuleOperateList.isNullOrEmpty()) continue
            }
            group.deductRuleList = deductRuleList
            list.add(group)
        }
        if (list.isEmpty()) {
            logger.info(
                "规则组下无有效规则 类型: {} eventFQN: {} accountTypeId: {} accountTypeName: {}",
                message.eventType.operation,
                message.eventType.eventStream,
                message.pointAccountType.id,
                message.pointAccountType.name
            )
            return
        }
        // 关联的发放时机
        val sendEventTypes = ArrayList<EventType>()
        message.setRelativeEventTypes(message.eventType, EventOperationEnum.SEND, sendEventTypes)

        if (sendEventTypes.isEmpty() || message.eventType.relatedEventTypes.isNullOrEmpty()) {
            logger.debug("没有找到关联的时机")
            return
        }
        list.forEach {
            try {
                //积分类型 冻结:FREEZE, 解冻:UNFREEZE, 废弃:DISCARD, 重新计算:RECALCULATE
                PointDeductFactory.factory(message.eventType.operation!!).calc(memberPoint, message, it)
            } catch (e: Throwable) {
                logger.error("规则组积分计算异常：规则组{}", it.groupName, e)
            }
        }

        saveMemberPointEvent(message)
    }


    private fun saveMemberPointEvent(message: PointCalcMessage) {
        try {
            if (message.skipIdempotentCheck) return
            message.toMemberPointEvent().save()
        } catch (e: Throwable) {
            val ex = when {
                e is SdkException -> e
                e.cause != null && e.cause is SdkException -> e.cause!!
                else -> e
            }
            if (ex is SdkException) {
                if (e.isSdkDuplicate() || ex.message?.contains("duplicate entry", ignoreCase = true) == true) {
                    logger.debug("积分订单事件重复推送 key:{} event: {} message: {} ", message.event.getKey(), message.event, ex.message)
                    return
                } else {
                    throw ex
                }
            } else {
                throw ex
            }
        }
    }



    fun sendPoint(memberPoint: MemberPoint, message: PointCalcMessage, group: PointRuleGroup) {
        logger.info(
            "规则组计算开始({}) 计划名称：{} 积分体系名称：{} 时机名称：{} 会员ID：{} 追溯ID：{}",
            group.groupName,
            message.plan.name,
            message.pointAccountType.name,
            message.eventType.name,
            message.memberId,
            message.traceId
        )

        val lp = message.toLoyaltyPoints(memberPoint)

        lp.eventType = PointEventType(
            message.eventType.id!!,
            message.eventType.name!!,
            message.matchingTime.time,
            EventOperationEnum.SEND,
            sendCycleTimePath = message.eventType.pointSendCycleTimePath
        )

        val gss = memberPointSendTransfer.calculate(lp, group)

        if (gss.isEmpty()) {
            logger.info(
                "规则组计算完成({})，不满足发放规则。 计划名称：{} 积分体系名称：{} 时机名称：{} 会员ID：{} 追溯ID：{} ",
                group.groupName,
                lp.plan.name,
                lp.hierarchy.name,
                lp.eventType?.name,
                lp.member.memberId,
                lp.attr.traceId
            )
            return
        }

        for (gs in gss) {
            gs.eventTypeId = message.eventType.id
            gs.eventTypeName = message.eventType.name
            gs.occurrenceTs = message.matchingTime.time.toString()
            lp.attr.businessId = gs.id
            lp.attr.desc = gs.desc
            lp.attr.effectiveDate = gs.effectiveDate
            lp.attr.overdueDate = gs.overdueDate
            lp.pointRuleGroup = PointGroup(gs.ruleGroupId!!, gs.ruleGroupName!!, gs.enabledGlobalGroupLimit)
            lp.sendRule = PointIssuanceRule(gs.ruleId!!, gs.ruleName!!)
            lp.pointValue = gs.point
            lp.afterPoints = gs.point
            if (gs.point <= BigDecimal.ZERO) {
                logger.info(
                    "积分值小于等于0，不发放积分。 计划名称：{} 积分体系名称：{} 时机名称：{} 会员ID：{} 追溯ID：{} 规则组名称：{}, 规则名称：{}",
                    lp.plan.name,
                    lp.hierarchy.name,
                    lp.eventType?.name,
                    lp.member.memberId,
                    lp.attr.traceId,
                    lp.pointRuleGroup?.name,
                    lp.sendRule!!.name
                )
                continue
            }

            val journalKey = lp.attr.uniqueId + "-"  + gs.id
            val msg = memberPointSendTransfer.send(lp, gs.point, ForbiddenPort.CALC_EVENT, journalKey)
            if (msg != null) {
                logger.warn(msg)
                if (lp.afterPoints <= ConstantValue.defaultZeroLine) {
                    continue
                }
            }

            gs.point = lp.afterPoints
            gs.save(lp.hierarchy.id)

            lp.member.point = lp.afterTotalPoints
            logger.info(
                "积分总账发放完成 计划名称：{} 积分体系名称：{} 时机名称：{} 会员ID：{} 追溯ID：{} 规则组名称：{}, 规则名称：{}, 到账积分：{} 生效时间：{} 过期时间：{}",
                lp.plan.name,
                lp.hierarchy.name,
                lp.eventType!!.name,
                lp.member.memberId,
                lp.attr.traceId,
                lp.pointRuleGroup?.name,
                lp.sendRule?.name,
                lp.afterPoints,
                gs.effectiveDate,
                gs.overdueDate
            )
        }
    }

}