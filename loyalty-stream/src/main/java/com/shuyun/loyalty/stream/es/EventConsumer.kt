package com.shuyun.loyalty.stream.es

import com.shuyun.air.es.CommitPolicy
import com.shuyun.air.es.SubscribeOptions
import com.shuyun.air.es.event.EventBody.*
import com.shuyun.air.es.ser.BodySerializer
import com.shuyun.kylin.es.sdk.EsFactory
import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.service.LoyaltyPrograms
import com.shuyun.loyalty.stream.processor.EventPreprocessor
import com.shuyun.pip.util.EnvUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.ZonedDateTime
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import kotlin.concurrent.thread

@Service
class EventConsumer: InitializingBean {

    private val logger = LogManager.getLogger(EventConsumer::class)

    @Autowired
    private lateinit var eventPreprocessor: EventPreprocessor

    private val factory by lazy { EsFactory.create() }

    private val startedFQNS = ConcurrentHashMap.newKeySet<String>()

    fun start() {
        thread(isDaemon = true, name = "es-consumer") {
            while (true) {
                try {
                    TimeUnit.SECONDS.sleep(5)
                    val publishedESFQNs = LoyaltyPrograms.getEsFqnList()
                    if (publishedESFQNs.isEmpty()) {
                        continue
                    }
                    val list = publishedESFQNs.filter { it !in startedFQNS }
                    if (list.isNotEmpty()) {
                        logger.info("订阅的事件模型列表: {}", list)
                        consume(list)
                    }
                } catch (e: InterruptedException) {
                    return@thread
                } catch (e: Throwable) {
                    logger.warn("")
                }
            }
        }
    }


    private fun consume(publishedESFQNs: List<String>) {
        for (fqn in publishedESFQNs) {
            if (startedFQNS.contains(fqn)) {
                continue
            }
            startedFQNS.add(fqn)
            thread(isDaemon = true, name = "es-consumer-$fqn") {
                try {
                    consume(fqn)
                } catch (e: InterruptedException) {
                    return@thread
                } catch (e: Exception) {
                    logger.warn("consume error, fqn: {}", fqn, e)
                }
            }
        }
    }


    private fun consume(fqn: String) {
        val client = factory.createConsumerClient()
        try {
            val options = SubscribeOptions.builder()
                .topic(fqn)
                .commitPolicy(CommitPolicy.AUTO)
                .maxRetryCountOnError(0)
                .serializer(BodySerializer.map())
                .group(GlobalContext.defTenantId() + "-" + GlobalContext.serviceName())
                .build()
            client.subscribe(options)
            while (true) {
                val records: List<com.shuyun.air.es.event.Event<Map<String, Any?>>> = client.poll(options, Duration.ofSeconds(5))
                for (record in records) {
                    val payload = record.body.payload
                    val event = Event().apply {
                        for ((k, v) in payload) {
                            when (k) {
                                PROPERTY_OBJECT_ID -> setKey(v.toString())
                                PROPERTY_DETECTION_DT -> setDetectionTs(ZonedDateTime.parse(v.toString()).toInstant().toEpochMilli())
                                PROPERTY_OCCURRENCE_DT -> setOccurrenceTs(ZonedDateTime.parse(v.toString()).toInstant().toEpochMilli())
                                else -> put(k, v)
                            }
                        }
                    }
                    logger.info("收到事件服务消息：{}", event)
                    eventPreprocessor.process(event, ZonedDateTime.now())
                }
            }
        } finally {
            runCatching {
                startedFQNS.remove(fqn)
                client.close()
            }
        }
    }

    override fun afterPropertiesSet()  {
        if (EnvUtils.isDevMode) {
            logger.info("当前环境为开发环境,不启动ES消费")
            return
        }
        start()
    }
}