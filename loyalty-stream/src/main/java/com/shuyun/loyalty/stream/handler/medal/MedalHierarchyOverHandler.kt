package com.shuyun.loyalty.stream.handler.medal

import com.pip.shuyun.pool.transaction.DmTransaction
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.entity.enums.ProcessRecordTypeEnum
import com.shuyun.loyalty.service.extension.getIfPresent
import com.shuyun.loyalty.service.message.MessageHandlerType
import com.shuyun.loyalty.service.message.medal.MedalHierarchyKeepOrRecycleMessage
import com.shuyun.loyalty.service.message.medal.MedalHierarchyOverMessage
import com.shuyun.loyalty.service.meta.MedalRuleGroupTypeEnum
import com.shuyun.loyalty.service.meta.RuleGroupStatusEnum
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.loyalty.service.model.MedalHierarchy
import com.shuyun.loyalty.service.service.*
import com.shuyun.loyalty.service.util.DateUtils
import com.shuyun.loyalty.service.util.ProcessRecordUtil
import com.shuyun.loyalty.stream.handler.AbstractEventHandler
import com.shuyun.loyalty.stream.sequence.MessageCounter
import com.shuyun.loyalty.stream.sequence.MessageCounter.K.EXPIRE_MEDAL_MESSAGE_COUNT
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.pip.component.name.Name
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.lang.Boolean.FALSE
import java.time.ZonedDateTime
import java.util.*

@Name
@Component
class MedalHierarchyOverHandler: AbstractEventHandler<MedalHierarchyOverMessage>() {
    private val log = LogManager.getLogger(MedalHierarchyOverHandler::class.java)

    @Autowired
    private lateinit var locker: Locker

    @Autowired
    private lateinit var medalHierarchyBaseService: MedalHierarchyBaseService

    @Autowired
    private lateinit var medalDefinitionBaseService: MedalDefinitionBaseService

    @Autowired
    private lateinit var memberMedalBaseService: MemberMedalBaseService

    @Autowired
    private lateinit var medalRuleGroupBaseService: MedalRuleGroupBaseService

    @Autowired
    private lateinit var eventTypeBaseService: EventTypeBaseService

    @Autowired
    private lateinit var memberMedalCalculateService: MemberMedalCalculateService

    @Autowired
    private lateinit var messageCounter: MessageCounter

    override fun handle(message: MedalHierarchyOverMessage) {
        val hierarchyId = message.medalHierarchyId
        val memberId = message.memberId()
        val medalDefinitionId = message.medalDefinitionId
        message.forbiddenPort = ForbiddenPort.TIME_EVENT
        messageCounter.decrement(hierarchyId, EXPIRE_MEDAL_MESSAGE_COUNT)
        log.debug("触发内置勋章过期开始,勋章体系:{},会员ID:{},勋章:{}", hierarchyId, memberId, medalDefinitionId)

        val lock = locker.getLock("medal_over_${hierarchyId}_${memberId}")
        lock.lock()
        try {
            ApplicationContextHolder.getBean(MedalHierarchyOverHandler::class.java).dispatcher(hierarchyId, medalDefinitionId, message)
        } finally {
            lock.unlock()
        }
        log.debug("触发内置勋章过期结束,勋章体系:{},会员ID:{},勋章:{}", hierarchyId, memberId, medalDefinitionId)
    }

    @DmTransaction
    fun dispatcher(hierarchyId: Long, @Suppress("unused") medalDefinitionId: Long, message: MedalHierarchyOverMessage) {
        val hierarchy = medalHierarchyBaseService.getEffectiveOneCache(hierarchyId,
            DateUtils.formatMinutesCacheKey(ZonedDateTime.now()))
        if (!hierarchy.checkHierarchyPlanPublishedOne()) {
            log.debug("勋章体系当前状态不可用")
            return
        }

        if (ProcessRecordUtil.isProcessRecord(
                message.medalHierarchyId.toString(), message.event().getKey(),
                ProcessRecordTypeEnum.MEDAL
            )
        ) {
            log.info(
                "勋章体系下已处理过改事件,事件ID:{},勋章体系ID:{}",
                message.event().getKey(),
                message.medalHierarchyId
            )
            return
        }

        hierarchy.medalDefinitions = medalDefinitionBaseService.findEnabledMedalHierarchyByVersionId(hierarchy.versionId!!)
        doDispatcher(hierarchy, message)
    }

    private fun doDispatcher(hierarchy: MedalHierarchy, message: MedalHierarchyOverMessage) {
        val memberId = message.memberId
        val medalDefinitionId = message.medalDefinitionId
        val memberMedal = memberMedalBaseService.findMemberMedal(memberId, hierarchy.id!!, medalDefinitionId, FALSE).getIfPresent()
        if (memberMedal == null) {
            log.debug("勋章过期事件无效会员勋章不存在,会员ID:$memberId,勋章体系ID:${hierarchy.id},勋章ID:$medalDefinitionId")
            return
        }
        if (memberMedal.overdueDate == null || memberMedal.overdueDate?.isAfter(ZonedDateTime.now()) == true) {
            log.debug(
                "本次勋章过期事件无效,勋章ID:{},过期时间:{},过期会员ID:{}",
                medalDefinitionId,
                memberMedal.overdueDate,
                memberId
            )
            return
        }
        val ruleGroupList = medalRuleGroupBaseService.findGroupByDefinition(medalDefinitionId)
        val occurDate = DateUtils.dateToZonedDateTime(Date(message.event().getOccurrenceTs()))
        val runKeepRuleGroupList = ruleGroupList.filter {
            it.calculateStatusByDate(occurDate) == RuleGroupStatusEnum.RUNNING && it.groupType == MedalRuleGroupTypeEnum.KEEP
                && it.medalHierarchyId == hierarchy.id && it.medalDefinitionId == medalDefinitionId
        }.filter {
            val eventType = eventTypeBaseService.getEffectiveOne(it.eventTypeId!!, message.event().getOccurrenceZonedDateTime())
            eventType.eventStream == MessageHandlerType.MEDAL_OVER_HANDLER.fqn && eventType.eventStream == message.event().getFqn()
        }
        val medalHierarchyKeepOrRecycleMessage = MedalHierarchyKeepOrRecycleMessage(
            message.plan,
            memberId,
            medalDefinitionId,
            message.medalHierarchy,
            ForbiddenPort.TIME_EVENT
        )
        medalHierarchyKeepOrRecycleMessage.setEvent(message.event())
        medalHierarchyKeepOrRecycleMessage.traceId=message.event().getKey()
        //过期时机下，如果该勋章没有重算规则，则直接回收
        if (runKeepRuleGroupList.isNotEmpty()) {
            val medalOverEventType = EventType().apply {
                this.id = runKeepRuleGroupList.first().eventTypeId
                this.name = runKeepRuleGroupList.first().eventTypeName
                this.eventStream = message.event().getFqn()
            }
            memberMedalCalculateService.matchRule(
                medalHierarchyKeepOrRecycleMessage,
                runKeepRuleGroupList,
                hierarchy,
                medalOverEventType
            )
        } else {
            memberMedalCalculateService.medalRecycle(medalHierarchyKeepOrRecycleMessage, hierarchy)
        }
    }

}