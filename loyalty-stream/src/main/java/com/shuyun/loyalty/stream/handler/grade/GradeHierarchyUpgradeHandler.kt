package com.shuyun.loyalty.stream.handler.grade

import com.pip.shuyun.pool.transaction.DmTransaction
import com.shuyun.loyalty.entity.api.constants.ChangeMode
import com.shuyun.loyalty.entity.api.constants.GradeRecordType
import com.shuyun.loyalty.entity.enums.ProcessRecordTypeEnum
import com.shuyun.loyalty.service.datamodel.MemberGrade
import com.shuyun.loyalty.service.datamodel.MemberGradeRecord
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.exception.GradeException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.extension.getIfPresent
import com.shuyun.loyalty.service.message.grade.GradeHierarchyEventMessage
import com.shuyun.loyalty.service.meta.GradeRuleGroupTypeEnum
import com.shuyun.loyalty.service.model.*
import com.shuyun.loyalty.service.model.GradeRuleGroup.Companion.filterRunningGradeRuleGroupsByType
import com.shuyun.loyalty.service.repository.MemberGradeRecordRepository
import com.shuyun.loyalty.service.repository.MemberGradeRepository
import com.shuyun.loyalty.service.service.*
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.ProcessRecordUtil
import com.shuyun.loyalty.service.util.SpecialCheckerUtils
import com.shuyun.loyalty.stream.handler.AbstractEventHandler
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.component.name.Name
import org.apache.commons.lang3.StringUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.*
import kotlin.concurrent.withLock

/**
 * 升级
 * */
@Name
@Component
class GradeHierarchyUpgradeHandler : AbstractEventHandler<GradeHierarchyEventMessage>() {

    private val logger = LogManager.getLogger(GradeHierarchyUpgradeHandler::class.java)

    @Autowired
    private lateinit var gradeHierarchyBaseService: GradeHierarchyBaseService

    @Autowired
    private lateinit var gradeDefinitionBaseService: GradeDefinitionBaseService

    @Autowired
    private lateinit var eventTypeBaseService: EventTypeBaseService

    @Autowired
    private lateinit var gradeRuleGroupBaseService: GradeRuleGroupBaseService

    @Autowired
    private lateinit var notifyBaseService: NotifyBaseService

    @Autowired
    private lateinit var locker: Locker

    @Autowired
    private lateinit var memberGradeRepository: MemberGradeRepository
    @Autowired
    private lateinit var memberGradeRecordRepository: MemberGradeRecordRepository

    override fun handle(message: GradeHierarchyEventMessage) {
        val gradeLock = locker.getLock("grade_calculate_key_${message.event.getKey()}")
        gradeLock.withLock {
            val traceId = message.eventTraceId()
            if (StringUtils.isEmpty(traceId)) {
                logger.debug("等级体系所属主体未定义该时机，等级体系ID：{}", message.gradeHierarchyId)
                return
            }
            val memberId = message.memberId()
            logger.debug("traceId:{}.会员{}在等级体系{}的等级升级计算开始", traceId, memberId, message.gradeHierarchyId)
            val gradeHierarchy = findEffectGradeHierarchy(message)

            val checklistIn = SpecialCheckerUtils.checkMemberInSpecialGrade(
                memberId,
                message.gradeHierarchyId,
                ForbiddenOperation.GRADE_UPGRADE,
                message.forbiddenPort
            )
            if(checklistIn){
                logger.info("客户在升级黑名单中!,eventKey:${message.event.getKey()}")
                return
            }
            val lock = locker.getLock("grade_calculate_${gradeHierarchy.id}-$memberId")
            lock.lock()
            try {
                ApplicationContextHolder.getBean(GradeHierarchyUpgradeHandler::class.java).saveGrade(memberId, message, gradeHierarchy)
            } catch (e: Exception){
                logger.error("事件处理失败，事件ID：${message.event.getKey()},等级体系id:${message.gradeHierarchyId}",e)
                throw e
            } finally {
                lock.unlock()
            }
        }
    }

    @DmTransaction
    fun saveGrade(memberId: String, message: GradeHierarchyEventMessage, gradeHierarchy: GradeHierarchy) {
        val streamProcessed = ProcessRecordUtil.isProcessRecord(message.gradeHierarchyId.toString(),message.event.getKey(), ProcessRecordTypeEnum.GRADE)
        if(streamProcessed){
            logger.info("等级体系下已处理过该事件，事件ID：${message.event.getKey()},等级体系id:${message.gradeHierarchyId}")
            return
        }

        val originalMemberGrade = memberGradeRepository.findOneByFilter(
            """{"${"$"}and":[{"memberId": "$memberId" }, {"gradeHierarchyId": ${message.gradeHierarchyId}}]}""",
            message.gradeHierarchyId.toString()
        )
        if (originalMemberGrade.isPresent) {
            logger.debug("初始等级为{},{}", {memberId}, {JsonUtils.toJson(originalMemberGrade.get())})
        }
        val originalGrade = findGradeDefinition(originalMemberGrade, gradeHierarchy)
        val targetGrade = calculateGrade(gradeHierarchy, originalGrade, message)
        logger.debug("目标等级为{}", {JsonUtils.toJson(targetGrade)})
        if (targetGrade.targetGradeDefinition is GradeDefinition) {
            val memberGrade = targetGrade.initMemberGrade(originalMemberGrade.getIfPresent(), message)
            val memberGradeId = memberGradeRepository.saveOrUpdate(memberGrade, message.gradeHierarchyId.toString())
            val memberGradeRecord = MemberGradeRecord.init(
                originalMemberGrade.getIfPresent(),
                memberGradeId,
                GradeRecordType.UPGRADE,
                message.event.getKey(),
                targetGrade.effectRuleName,
                targetGrade.channelType ?: ConstantValue.DEFAULT_CHANNEL_TYPE,
                ChangeMode.AUTO_CALC,
                message.eventTraceId(),
                targetGrade.effectEventTypeName,
                targetGrade.ruleId,
                targetGrade.ruleName
            )
            memberGradeRecordRepository.saveOrUpdate(memberGradeRecord, message.gradeHierarchyId.toString())
            notifyBaseService.postIntoBus(memberGradeRecord)
        }
    }

    private fun findEffectEventType(event: Event, eventTypeId : Long): EventType {
        return try {
            eventTypeBaseService.getEffectiveOne(eventTypeId, event.getOccurrenceZonedDateTime())
        } catch (_: Exception) {
            throw GradeException(LoyaltyExceptionCode.GRADE_HIERARCHY_NOT_FOUND)
        }
    }

    private fun findEffectGradeHierarchy(message: GradeHierarchyEventMessage): GradeHierarchy {
        val gradeHierarchyId = message.gradeHierarchyId
        val eventTime = message.event.getOccurrenceZonedDateTime()
        try {
            val hierarchy = gradeHierarchyBaseService.getEffectiveOneCache(gradeHierarchyId, eventTime)
            hierarchy.gradeDefinitions = gradeDefinitionBaseService.findByEnabledHierarchyByVersionId(hierarchy.versionId!!)
            return hierarchy
        } catch (_: Exception) {
            throw GradeException(LoyaltyExceptionCode.GRADE_HIERARCHY_NOT_FOUND)
        }
    }

    private fun findGradeDefinition(
        memberGrade: Optional<MemberGrade>,
        gradeHierarchy: GradeHierarchy
    ): GradeDefinition? {
        val gradeDefinitionId = if (memberGrade.isPresent) {
            memberGrade.get().currentGradeDefinitionId
        } else {
            null
        }
        return gradeHierarchy.findGradeDefinitionById(gradeDefinitionId)
    }

    private fun calculateGrade(gradeHierarchy: GradeHierarchy, originalGrade: GradeDefinition?, message: GradeHierarchyEventMessage): TargetGrade {
        var matched = false
        val targetGradeInfo = TargetGrade()
        val memberGrade = memberGradeRepository.findMemberGrade(message.memberId(), message.gradeHierarchyId.toString())
            .getIfPresent()
        gradeHierarchy.findGradeDefinitionsGreaterThanSpecified(originalGrade).forEach { gradeDefinition ->
            if (!matched) {
                val ruleGroupList = gradeRuleGroupBaseService.findGroup(gradeDefinition.id!!, message.matchingTime)
                filterRunningGradeRuleGroupsByType(
                    ruleGroupList,
                    GradeRuleGroupTypeEnum.UPGRADE,
                    message.matchingTime
                ).filter { gradeRuleGroup ->
                    val eventType = findEffectEventType(message.event, gradeRuleGroup.eventTypeId!!)
                    eventType.eventStream == message.event.getFqn()
                }.forEach { gradeRuleGroup ->
                    val eventType = findEffectEventType(message.event, gradeRuleGroup.eventTypeId!!)
                    gradeRuleGroup.ruleList!!.forEach { gradeRule ->
                        if (!targetGradeInfo.effectForever) {
                            logger.debug(
                                "待匹配的规则为{},规则组名称为{}",
                                { JsonUtils.toJson(gradeRule) },
                                { gradeRuleGroup.name })
                            matched = targetGradeInfo.updateTargetGradeEffectTimeWhenMatched(
                                eventType.id!!,
                                gradeRule,
                                gradeDefinition,
                                gradeRuleGroup.name!!,
                                eventType.name!!,
                                message,
                                memberGrade = memberGrade
                            )
                            if (matched) {
                                targetGradeInfo.ruleId = gradeRule.id
                                targetGradeInfo.ruleName = gradeRuleGroup.name // rule.name 没有值
                                logger.debug("规则匹配成功，目标等级为：{}", { JsonUtils.toJson(targetGradeInfo) })
                            } else {
                                logger.debug("规则匹配不成功")
                            }
                        }
                    }
                }
            }
        }
        return targetGradeInfo
    }
}
