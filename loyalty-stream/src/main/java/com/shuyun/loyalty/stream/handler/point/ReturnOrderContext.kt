package com.shuyun.loyalty.stream.handler.point

import com.shuyun.loyalty.service.datamodel.ReturnOrder
import com.shuyun.loyalty.service.event.Event

object ReturnOrderContext {

    private val threadLocal = ThreadLocal<List<ReturnOrder>>()

    private fun add(returnOrder: ReturnOrder) {
        val list = threadLocal.get() ?: emptyList()
        threadLocal.set(list + returnOrder)
    }

    fun add(traceId: String, currentEventTypeId: Long, relativeEventTypeId: Long,  ttl: Long, event: Event) {
        if (event.get(Event.RETURN_ORDER_EVENT_TYPE) != null) {
            return
        }
        val returnOrder = ReturnOrder().build(traceId, currentEventTypeId, ttl, relativeEventTypeId.toString(), event)
        add(returnOrder)
    }

    fun get(): List<ReturnOrder> {
        val orders =  threadLocal.get() ?: emptyList()
        return orders.distinctBy { "${it.currentEventTypeId}_${it.eventTypeId}" }
    }

    fun remove() {
        threadLocal.remove()
    }
}