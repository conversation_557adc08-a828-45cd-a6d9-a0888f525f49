package com.shuyun.loyalty.stream.service

import com.shuyun.loyalty.entity.dto.MemberMessage
import com.shuyun.loyalty.entity.dto.MemberPointEffectMessage
import com.shuyun.loyalty.entity.dto.MemberPointExpireMessage
import com.shuyun.loyalty.entity.dto.MemberPointMessage
import com.shuyun.loyalty.service.kafka.IPointSyncProducer
import com.shuyun.loyalty.service.util.MDCUtils
import com.shuyun.loyalty.stream.kafka.sink.KafkaSource
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.messaging.support.MessageBuilder
import org.springframework.stereotype.Component

@Component
class PointSyncProducer : IPointSyncProducer {

    private val log = LogManager.getLogger(PointSyncProducer::class.java)

    @Autowired
    private lateinit var kafkaSource: KafkaSource


    override fun send(memberMessage: MemberMessage): Boolean {
        try {
            return when (memberMessage) {
                is MemberPointMessage -> {
                    val message = MessageBuilder.withPayload(JsonUtils.toJson(memberMessage))
                        .setHeader(IPointSyncProducer.MEMBER_ID, memberMessage.memberId)
                        .setHeader(MDCUtils.LOYALTY_TRACE_ID, MDCUtils.getTraceId()).build()
                    kafkaSource.requestSyncProcessOutput().send(message)
                }
                is MemberPointExpireMessage -> {
                    val message = MessageBuilder.withPayload(JsonUtils.toJson(memberMessage))
                        .setHeader(IPointSyncProducer.MEMBER_ID, memberMessage.memberId)
                        .setHeader(MDCUtils.LOYALTY_TRACE_ID, MDCUtils.getTraceId()).build()
                    kafkaSource.lpExpireOutput().send(message)
                }
                is MemberPointEffectMessage -> {
                    val message = MessageBuilder.withPayload(JsonUtils.toJson(memberMessage))
                        .setHeader(IPointSyncProducer.MEMBER_ID, memberMessage.memberId)
                        .setHeader(MDCUtils.LOYALTY_TRACE_ID, MDCUtils.getTraceId()).build()
                    kafkaSource.lpEffectOutput().send(message)
                }
                else -> false
            }
        } catch (e: Exception) {
            log.error("发送积分事件消息异常", e)
            return false
        }
    }

}