package com.shuyun.loyalty.stream.handler.point.process

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.entity.enums.NegativeStrategyEnum
import com.shuyun.loyalty.service.datamodel.MemberPoint
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.model.PointDeductRuleOperate
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.loyalty.service.transfer.points.MemberPointFreezeTransfer
import com.shuyun.loyalty.service.transfer.points.PointEventType
import com.shuyun.loyalty.service.util.sendNotify
import com.shuyun.loyalty.stream.handler.point.MemberPointDeductCalculate
import com.shuyun.loyalty.stream.handler.point.ReturnOrderContext
import com.shuyun.loyalty.stream.message.PointCalcMessage
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class MemberPointFreezeProcess : MemberPointDeductCalculate() {

    private val log = LogManager.getLogger(MemberPointFreezeProcess::class.java)

    @Autowired
    private lateinit var memberPointFreezeTransfer: MemberPointFreezeTransfer


    override fun type() = EventOperationEnum.FREEZE


    override fun process(memberPoint: MemberPoint, message: PointCalcMessage, group: PointRuleGroup) {
        log.info("冻结积分计算 规则组：{} 积分账户类型({} {})", group.groupName, message.pointAccountType.id, message.pointAccountType.name)
        if (message.pointAccountType.negativeStrategy == NegativeStrategyEnum.NOT_ALLOWED) {
            log.info("积分冻结因扣减策略被禁止")
            return
        }
        val sets = HashSet<String>()
        //一个规则组下满足条件规则
        val passedRule = filterRule(message.subject.dataType!!, message.event, group.deductRuleList, message.eventType) ?: return

        val lp = message.toLoyaltyPoints(memberPoint, group, passedRule)
        memberPointFreezeTransfer.checkSpecialList(lp, ForbiddenOperation.POINT_FREEZE, ForbiddenPort.CALC_EVENT)
        // 冻结的关联时机是发放
        for (sendE in message.eventType.relatedEventTypes!!) {
            if (!sets.add("${sendE.id}-${passedRule.quotaType}")) {
                continue
            }

            if (!message.checkReturnOrder(message.eventType.id!!, sendE.id!!)) {
                continue
            }

            val matchingTimeMillis = findSendMatchingTimeMillis(lp.hierarchy.id, lp.member.id, lp.member.memberId,sendE, lp.attr.traceId)
            if (matchingTimeMillis == null) {
                // 检验是否配置了等待原单
                val ttl = message.eventType.ttl(lp.event!!.occurrenceTs)
                if (ttl > 0) {
                    ReturnOrderContext.add(message.traceId!!, message.eventType.id!!, sendE.id!!, ttl, message.rawEvent)
                    continue
                }
                log.warn("原单不存在,冻结计算结束 原单ID: {}", lp.attr.traceId)
                continue
            }

            // 设置时机
            lp.eventType = PointEventType(
                message.eventType.id!!,
                message.eventType.name!!,
                lp.event!!.occurrenceTs,
                EventOperationEnum.FREEZE,
                PointEventType(sendE.id!!, sendE.name!!, matchingTimeMillis, sendE.operation!!)
            )

            // 查询冻结关联的发放的原单获取记录
            val gss = gainService.findByTraceIdList(
                lp.hierarchy.id,
                sendE.id!!,
                lp.attr.traceId,
                lp.member.id
            )

            for (gs in gss) {
                // 只能处理 发放、待发放、扣减、其他冻结状态的gain
                val freezeCanUsed = setOf(
                    FSMPointEvent.SEND,
                    FSMPointEvent.DELAY_SEND,
                    FSMPointEvent.DEDUCT,
                    FSMPointEvent.OPEN_FREEZE,
                    FSMPointEvent.SPECIAL_FREEZE
                )

                if (gs.status !in freezeCanUsed) {
                    log.info("积分不能被扣除，当前积分状态: ${gs.status}")
                    continue
                }

                if (gs.point <= BigDecimal.ZERO) {
                    log.info("原单小于0, 不允许冻结")
                    continue
                }

                // 冻结积分已经存在，不再进行冻结操作
                if (gs.findFrozenPoint(lp.hierarchy.id) != null) {
                    log.debug("冻结积分已经存在，不再进行冻结")
                    continue
                }

                if (passedRule.quotaType == PointDeductRuleOperate.QuotaTypeEnum.ZERO) {
                    val gcStatus = if (gs.status == FSMPointEvent.DELAY_SEND) PCStatus.DELAY_FROZEN else PCStatus.FROZEN
                    log.info("积分冻结规则扣减额度为0")
                    val zeroRecord = memberPointFreezeTransfer.initPointRecord(
                        lp, Uuid.uuid, FSMPointEvent.ABOLISH,
                        points = BigDecimal.ZERO,
                        changePoints = BigDecimal.ZERO,
                        totalPoints = lp.member.point,
                        pcStatus = gcStatus
                    ).apply {
                        this.recordDetail = "积分冻结规则扣减配额为0"
                    }
                    zeroRecord.insert(lp.hierarchy.id)
                    listOf(zeroRecord).sendNotify()
                    continue
                }

                val journalKey = lp.attr.uniqueId + "-"  + gs.id
                lp.pointValue = gs.point
                lp.attr.businessId = gs.id
                lp.afterPoints = if (gs.status == FSMPointEvent.DELAY_SEND) BigDecimal.ZERO else gs.point

                memberPointFreezeTransfer.freeze(
                    lp,
                    lp.afterPoints,
                    ForbiddenPort.CALC_EVENT,
                    ForbiddenOperation.POINT_FREEZE,
                    journalKey
                )

                val frozenP = if (gs.status == FSMPointEvent.DELAY_SEND) gs.point else lp.afterPoints

                if (frozenP.compareTo(BigDecimal.ZERO) == 0) {
                    log.debug("无剩余积分可冻结")
                    continue
                }

                val frozenPoint = memberPointFreezeTransfer.initMemberFrozenPoint(
                    lp,
                    Uuid.uuid,
                    frozenP,
                    gs.status,
                    gs.id!!
                )

                frozenPoint.save(lp.hierarchy.id)

                lp.member.point = lp.afterTotalPoints

                log.info(
                    "积分冻结完成 计划名称：{} 积分体系名称：{} 时机名称：{} 会员ID：{} 追溯ID：{} 规则组名称：{}, 规则名称：{}, 积分状态：{}, 冻结积分：{} ",
                    lp.plan.name,
                    lp.hierarchy.name,
                    lp.eventType!!.name,
                    lp.member.memberId,
                    lp.attr.traceId,
                    group.groupName,
                    passedRule.ruleName,
                    gs.status,
                    frozenP
                )
            }
        }
    }

}