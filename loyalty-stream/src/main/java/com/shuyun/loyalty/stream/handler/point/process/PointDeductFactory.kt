package com.shuyun.loyalty.stream.handler.point.process

import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.stream.handler.point.PointDeductCalculate
import com.shuyun.pip.ApplicationContextHolder

object PointDeductFactory {
    private val handlerMap by lazy {
        ApplicationContextHolder.getApplicationContext().getBeansOfType(PointDeductCalculate::class.java)
            .map { it.value }.associateBy { it.type() }
    }

    fun factory(type: EventOperationEnum): PointDeductCalculate {
        return handlerMap[type] ?: error("未找到相应积分类型处理器:$type")
    }
}