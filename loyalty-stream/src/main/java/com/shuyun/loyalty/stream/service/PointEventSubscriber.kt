package com.shuyun.loyalty.stream.service

import com.google.common.eventbus.AllowConcurrentEvents
import com.google.common.eventbus.Subscribe
import com.shuyun.loyalty.service.datamodel.MemberGradeRecord
import com.shuyun.loyalty.service.datamodel.MemberPointRecord
import com.shuyun.loyalty.service.event.notify.GradeModifiedNotify
import com.shuyun.loyalty.service.event.notify.LoyaltyNotify
import com.shuyun.loyalty.service.event.notify.PointModifiedNotify
import com.shuyun.loyalty.service.event.notify.RecordNotify
import com.shuyun.loyalty.service.model.DefaultSubscriber
import com.shuyun.loyalty.stream.kafka.sink.KafkaSource
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.messaging.MessageChannel
import org.springframework.messaging.support.MessageBuilder
import org.springframework.stereotype.Component

@Component
@Suppress("unused")
class PointEventSubscriber : DefaultSubscriber {
    private val logger = LogManager.getLogger(PointEventSubscriber::class)

    @Autowired
    private lateinit var kafkaSource: KafkaSource

    @Subscribe
    @AllowConcurrentEvents
    fun subscribe(event : MemberGradeRecord){
        try {
            val message = GradeModifiedNotify().initByRecord(event)
            notify(kafkaSource.gradeNotifyOutput(), message)
            notify(kafkaSource.recordNotifyOutput(), RecordNotify().initByRecord(event))
        } catch (e: Throwable) {
            throw e
        }
    }

    @Subscribe
    @AllowConcurrentEvents
    fun subscribe(event : MemberPointRecord){
        try {
            val message = PointModifiedNotify().initByRecord(event)
            notify(kafkaSource.pointNotifyOutput(), message)
            notify(kafkaSource.recordNotifyOutput(), RecordNotify().initByRecord(event))
        } catch (e: Throwable) {
            throw e
        }
    }

    fun notify(messageChannel: MessageChannel, message: LoyaltyNotify) {
        val msg = MessageBuilder.withPayload(message)
            .setHeader("memberId", message.partitionKey() ?: "-")
            .build()
        messageChannel.send(msg)
    }
}