package com.shuyun.loyalty.stream.handler.medal

import com.pip.shuyun.pool.transaction.DmTransaction
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.entity.enums.ProcessRecordTypeEnum.MEDAL_KEEP
import com.shuyun.loyalty.service.message.MessageHandlerType
import com.shuyun.loyalty.service.message.medal.MedalHierarchyKeepOrRecycleMessage
import com.shuyun.loyalty.service.message.medal.MedalHierarchyRecalculateMessage
import com.shuyun.loyalty.service.meta.MedalRuleGroupTypeEnum
import com.shuyun.loyalty.service.meta.RuleGroupStatusEnum
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.loyalty.service.service.*
import com.shuyun.loyalty.service.util.DateUtils
import com.shuyun.loyalty.service.util.ProcessRecordUtil
import com.shuyun.loyalty.stream.handler.AbstractEventHandler
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.component.name.Name
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.time.ZonedDateTime
import java.util.*

@Name
@Component
class MedalHierarchyRecalculateHandler: AbstractEventHandler<MedalHierarchyRecalculateMessage>() {
    private val log = LogManager.getLogger(MedalHierarchyRecalculateHandler::class.java)

    @Autowired
    private lateinit var locker: Locker

    @Autowired
    private lateinit var memberMedalBaseService: MemberMedalBaseService

    @Autowired
    private lateinit var medalHierarchyBaseService: MedalHierarchyBaseService

    @Autowired
    private lateinit var medalDefinitionBaseService: MedalDefinitionBaseService

    @Autowired
    private lateinit var medalRuleGroupBaseService: MedalRuleGroupBaseService

    @Autowired
    private lateinit var eventTypeBaseService: EventTypeBaseService

    @Autowired
    private lateinit var memberMedalCalculateService: MemberMedalCalculateService

    override fun handle(message: MedalHierarchyRecalculateMessage) {
        // 目前bu用实现
        val hierarchyId = message.medalHierarchyId
        val traceId = message.eventTraceId()
        if (traceId.isEmpty()) {
            log.info("勋章体系所属主体未定义该时机,勋章体系ID:{}", hierarchyId)
            return
        }

        val memberId = message.memberId()
        val medalDefinitionIds = memberMedalBaseService.currentMedalDefinitionIds(memberId, hierarchyId)
        log.debug("触发勋章重算开始,勋章体系:{},会员ID:{},会员原勋章:{}", hierarchyId, memberId, medalDefinitionIds)

        val lock = locker.getLock("medal_keep_$hierarchyId-$memberId")
        lock.lock()
        try {
            ApplicationContextHolder.getBean(MedalHierarchyRecalculateHandler::class.java).saveMedal(memberId, hierarchyId,
                medalDefinitionIds, message)
        } finally {
            lock.unlock()
        }
        log.debug("触发勋章保持结束,勋章体系:{},会员ID:{},会员原勋章:{}", hierarchyId, memberId, medalDefinitionIds)
    }

    @DmTransaction
    private fun saveMedal(memberId: String, hierarchyId: Long, medalDefinitionIds: List<Long?>,
                          message: MedalHierarchyRecalculateMessage ) {
        if (ProcessRecordUtil.isProcessRecord(hierarchyId.toString(), message.event.getKey(), MEDAL_KEEP)) {
            log.info("勋章体系下已处理过该事件,事件ID:${message.event.getKey()},等级体系ID:$hierarchyId")
            return
        }

        val hierarchy = medalHierarchyBaseService.getEffectiveOneCache(hierarchyId,
            DateUtils.formatMinutesCacheKey(ZonedDateTime.now()))
        if (!hierarchy.checkHierarchyPlanPublishedOne()) {
            log.debug("勋章体系当前状态不可用")
            return
        }
        hierarchy.medalDefinitions = medalDefinitionBaseService.findEnabledMedalHierarchyByVersionId(hierarchy.versionId!!)

        val ruleGroupList = medalRuleGroupBaseService.findByHierarchyId(hierarchyId)
        val occurDate = Date(message.event.getOccurrenceTs())
        val runRuleGroupList = ruleGroupList.filter { it.groupType == MedalRuleGroupTypeEnum.KEEP }.filter {
            it.calculateStatusByDate(DateUtils.dateToZonedDateTime(occurDate)) == RuleGroupStatusEnum.RUNNING
        }
        log.debug("勋章保持规则组:hierarchyId:{},groupList:{},runningGroupList:{}", hierarchyId,
            JsonUtils.toJson(ruleGroupList), JsonUtils.toJson(runRuleGroupList))
        val runKeepRuleGroupMedals =  ArrayList<Long>()//用于记录哪些需要重算的勋章id
        var recalEventType = EventType()
        val runKeepRuleGroupList = runRuleGroupList.filter {
            val eventType =
                eventTypeBaseService.getEffectiveOne(it.eventTypeId!!, message.event.getOccurrenceZonedDateTime())
            val flag = eventType.eventStream != MessageHandlerType.MEDAL_OVER_HANDLER.fqn
            val fqnEq = eventType.eventStream == message.event.getFqn()
            val fqnmd = it.medalDefinitionId in medalDefinitionIds

            if (!flag || !fqnEq)
                log.debug(
                    "勋章保持规则组-过滤:groupEventTypeId:{},effectiveFqn:{},inputFn:{}", it.eventTypeId,
                    message.event.getFqn(), eventType.eventStream
                )
            if (!fqnmd) {
                log.debug(
                    "勋章保持规则组-过滤-该勋章无重算规则:groupEventTypeId:{},effectiveFqn:{},inputFn:{},medalDefinitionId:{}",
                    it.eventTypeId,message.event.getFqn(), eventType.eventStream,it.medalDefinitionId
                )
            }
            if (flag && fqnEq && fqnmd) {
                recalEventType = eventType
                runKeepRuleGroupMedals.add(it.medalDefinitionId!!)//这部分勋章是需要进行重算的
            }
            flag && fqnEq && fqnmd
        }
        if (runKeepRuleGroupList.isEmpty()) {
            log.debug("勋章保持规则组-过滤-该勋章过滤后无重算规则")
            return
        }
        //自定义重算时机下  会员所拥有的勋章必须有重算规则 才进行保持或者回收变动
        medalDefinitionIds.filter { it in runKeepRuleGroupMedals }.forEach {
            val medalHierarchyKeepOrRecycleMessage = MedalHierarchyKeepOrRecycleMessage(
                message.plan,
                memberId,
                it!!,
                message.medalHierarchy,
                ForbiddenPort.CALC_EVENT
            )
            medalHierarchyKeepOrRecycleMessage.setEvent(message.event())
            medalHierarchyKeepOrRecycleMessage.traceId=message.eventTraceId()
            memberMedalCalculateService.matchRule(medalHierarchyKeepOrRecycleMessage,runKeepRuleGroupList, hierarchy,recalEventType
            )
        }
    }

}