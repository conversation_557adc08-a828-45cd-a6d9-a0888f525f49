package com.shuyun.loyalty.stream.resource

import com.shuyun.epassport.sdk.register.RequiresPermissions
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.infrastructure.es.EventClient
import com.shuyun.loyalty.service.model.Plan
import com.shuyun.loyalty.service.service.LoyaltyPrograms
import com.shuyun.loyalty.service.util.MDCUtils
import com.shuyun.loyalty.stream.kafka.sink.KafkaSource
import com.shuyun.pip.component.json.JsonUtils
import io.swagger.v3.oas.annotations.Operation
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.messaging.support.MessageBuilder
import org.springframework.web.bind.annotation.*
import java.time.ZonedDateTime
import java.util.regex.Pattern
import javax.servlet.http.HttpServletResponse

@RestController
@RequestMapping("/delivers")
@RequiresPermissions(allowAuthenticated = true)
class EventDeliverResource {
    private val log = LogManager.getLogger(EventDeliverResource::class.java)

    @Autowired private lateinit var kafkaSource: KafkaSource


    @RequestMapping(method = [RequestMethod.POST])
    fun sendMessage(@RequestBody event: Event) {
        try {
            kafkaSource.greenOutput().send(
                MessageBuilder.withPayload(event)
                    .setHeader(MDCUtils.LOYALTY_TRACE_ID, MDCUtils.getTraceId()).build())
            log.debug("发送MQ消息: {}", { JsonUtils.toJson(event) })
        } catch (e: Exception) {
            log.error("push event error:{}", event, e)
        }
    }


    @PostMapping("/es")
    fun sendESMessage(@RequestBody event: Map<String, Any?>, httpServletResponse: HttpServletResponse) {
        try {
            val fqn = event["fqn"]?.toString() ?: throw IllegalArgumentException("fqn is required")
            if (!LoyaltyPrograms.getEsFqnList().contains(fqn)) {
                throw IllegalArgumentException("已发布的计划方案中不包含该fqn($fqn)")
            }
            EventClient.sendEvent(fqn, event)
        } catch (e: Exception) {
            log.warn("push event error:{}", event, e)
            httpServletResponse.status = 500
            val errorMessage = e.message ?: "push event error"
            val matcher = Pattern.compile(""".*(?<error>\{.+?}).*""").matcher(errorMessage)
            if (matcher.matches()) {
                httpServletResponse.contentType = "application/json;charset=UTF-8"
                httpServletResponse.writer.use { it.print(matcher.group("error")) }
            } else {
                httpServletResponse.contentType = "text/plain;charset=UTF-8"
                httpServletResponse.writer.use { it.print(e.message) }
            }
        }
    }

    @Operation(summary = "查询通过积分账号ID计划配置", tags = ["实施工具"])
    @GetMapping("/queryPlanByAccountTypeId")
    fun queryPlanByAccountTypeId(@RequestParam("accountTypeId") accountTypeId: Long): Plan? {
        return LoyaltyPrograms.findPlanByAccountTypeId(accountTypeId)
    }


    @Operation(summary = "查询通过FQN计划配置", tags = ["实施工具"])
    @GetMapping("/queryPlansByFQN")
    fun queryPlansByFQN(@RequestParam("fqn") fqn: String, @RequestParam(value = "dateTime", required = false) dateTime: String?): List<Plan> {
        val date = dateTime?.let { ZonedDateTime.parse(it) } ?: ZonedDateTime.now()
        return LoyaltyPrograms.findPlansByFqn(fqn, date)
    }

    @Operation(summary = "查询所有积分账号类型ID", tags = ["实施工具"])
    @GetMapping("/findAllPointHierarchyIds")
    fun findAllPointHierarchyIds(): Set<Long> {
        return LoyaltyPrograms.findAllHierarchyIds(type = LoyaltyPrograms.HierarchyType.POINT)
    }

}