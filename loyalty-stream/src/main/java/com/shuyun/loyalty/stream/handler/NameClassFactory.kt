package com.shuyun.loyalty.stream.handler

import com.google.common.collect.Maps
import com.shuyun.loyalty.service.message.EventMessage
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.name.NameClassFinder

/**
 * handler工厂类
 */
object NameClassFactory {
    private val handleMap = Maps.newConcurrentMap<String, Any>()

    /** 获取相应的处理器  */
    fun <T: EventHandler<EventMessage>> getHandlerOrProcessor(name: String): T {
        @Suppress("UNCHECKED_CAST")
        return handleMap.computeIfAbsent(name) { s -> createHandler(s) } as T
    }

    private fun <T> createHandler(name: String): T {
        val clazz = findNameClass<T>(name)
        return ApplicationContextHolder.getBean(clazz)
    }

    /** 获取相应的扩展元数据类型信息  */
    private fun <T> findNameClass(name: String): Class<T> {
        return NameClassFinder.getInstance().get(name)
    }
}