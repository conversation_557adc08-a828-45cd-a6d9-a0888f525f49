package com.shuyun.loyalty.stream.handler.point.process

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.service.datamodel.MemberPoint
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.loyalty.service.transfer.points.MemberPointUnfreezeTransfer
import com.shuyun.loyalty.stream.handler.point.MemberPointDeductCalculate
import com.shuyun.loyalty.stream.handler.point.ReturnOrderContext
import com.shuyun.loyalty.stream.message.PointCalcMessage
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
class MemberPointUnFreezeProcess : MemberPointDeductCalculate() {

    private val log = LogManager.getLogger(MemberPointUnFreezeProcess::class.java)

    @Autowired
    private lateinit var memberPointUnfreezeTransfer: MemberPointUnfreezeTransfer


    override fun type() = EventOperationEnum.UNFREEZE


    override fun process(memberPoint: MemberPoint, message: PointCalcMessage, group: PointRuleGroup) {
        log.info("解冻积分计算 规则组：{} 积分账户类型({} {})", group.groupName, message.pointAccountType.id, message.pointAccountType.name)

        val sets = HashSet<String>()
        //一个规则组下满足条件规则
        val passedRule = filterRule(message.subject.dataType!!, message.event, group.deductRuleList, message.eventType) ?: return

        val lp = message.toLoyaltyPoints(memberPoint, group, passedRule)
        memberPointUnfreezeTransfer.checkSpecialList(lp, ForbiddenOperation.POINT_UNFREEZE, ForbiddenPort.CALC_EVENT)

        // 解冻的关联时机是冻结， 冻结的关联的时机是发放
        for (freezeE in message.eventType.relatedEventTypes!!) {
            val sendEventTypes = freezeE.relatedEventTypes ?: continue
            for (sendE in sendEventTypes) {
                if (!sets.add("${sendE.id}-${passedRule.quotaType}")) {
                    continue
                }
                if (!message.checkReturnOrder(message.eventType.id!!, freezeE.id!!)) {
                    continue
                }
                // 查询是否冻结的记录
                val gss = gainService.findByTraceIdList(
                    lp.hierarchy.id,
                    sendE.id!!,
                    lp.attr.traceId,
                    lp.member.id
                )

                if (gss.isEmpty()) continue

                for (gs in gss) {
                    if (gs.status == FSMPointEvent.ABOLISH) {
                        log.debug("该积分已经作废，无法解冻结 TraceId: {}", gs.traceId)
                        continue
                    }
                    val frozenPoint = frozenPointService.findFrozenPointByGainStatementId(gs.id!!, gs.pointPlanId!!)
                    if (frozenPoint == null) {
                        val ttl = message.eventType.ttl(message.event.getOccurrenceTs())
                        if (ttl > 0) {
                            ReturnOrderContext.add(message.traceId!!, message.eventType.id!!, sendE.id!!, ttl, message.rawEvent)
                            continue
                        }
                        log.debug("解冻关联的冻结记录不存在无法解冻 TraceId: {} 原单获取记录Id: {}", lp.attr.traceId, gs.id)
                        continue
                    }

                    if (frozenPoint.point <= BigDecimal.ZERO) {
                        log.info("该冻结的积分已经解冻中，无需重复解冻")
                        continue
                    }
                    // 设置时机
                    val sendEOccurrenceTs = gs.occurrenceTs?.toLong() ?: continue
                    lp.eventType = initPointEventType(message.eventType, sendE, sendEOccurrenceTs, message)
                    lp.pointValue = frozenPoint.point.abs()
                    lp.attr.businessId = gs.id

                    val journalKey = lp.attr.uniqueId + "-"  + gs.id

                    memberPointUnfreezeTransfer.unfreeze(
                        lp,
                        frozenPoint.point,
                        ForbiddenPort.CALC_EVENT,
                        ForbiddenOperation.POINT_UNFREEZE,
                        journalKey
                    )

                    lp.member.point = lp.afterTotalPoints

                    log.info(
                        "积分冻结完成 计划名称：{} 积分体系名称：{} 时机名称：{} 会员ID：{} 追溯ID：{} 规则组名称：{}, 规则名称：{}, 积分状态：{}, 冻结积分：{} ",
                        lp.plan.name,
                        lp.hierarchy.name,
                        lp.eventType!!.name,
                        lp.member.memberId,
                        lp.attr.traceId,
                        group.groupName,
                        passedRule.ruleName,
                        frozenPoint.fromStatus,
                        frozenPoint.point
                    )
                }
            }
        }
    }

}