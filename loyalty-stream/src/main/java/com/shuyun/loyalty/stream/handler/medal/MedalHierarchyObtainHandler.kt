package com.shuyun.loyalty.stream.handler.medal

import com.shuyun.loyalty.entity.api.constants.MedalChangeMode
import com.shuyun.loyalty.entity.api.constants.MedalRecordType
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.entity.enums.ProcessRecordTypeEnum
import com.shuyun.loyalty.service.datamodel.MemberMedalRecord
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.exception.MedalException
import com.shuyun.loyalty.service.extension.getIfPresent
import com.shuyun.loyalty.service.message.medal.MedalHierarchyEventMessage
import com.shuyun.loyalty.service.meta.MedalRuleGroupTypeEnum
import com.shuyun.loyalty.service.meta.RuleGroupStatusEnum
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.model.MatchMedal
import com.shuyun.loyalty.service.model.MedalHierarchy
import com.shuyun.loyalty.service.service.*
import com.shuyun.loyalty.service.util.DateUtils
import com.shuyun.loyalty.service.util.ProcessRecordUtil
import com.shuyun.loyalty.service.util.SpecialCheckerUtils
import com.shuyun.loyalty.stream.handler.AbstractEventHandler
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.component.name.Name
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.time.ZoneId
import kotlin.concurrent.withLock

@Name
@Component
class MedalHierarchyObtainHandler: AbstractEventHandler<MedalHierarchyEventMessage>() {
    private val log = LogManager.getLogger(MedalHierarchyObtainHandler::class.java)

    @Autowired
    private lateinit var medalHierarchyBaseService: MedalHierarchyBaseService

    @Autowired
    private lateinit var medalDefinitionBaseService: MedalDefinitionBaseService

    @Autowired
    private lateinit var eventTypeBaseService: EventTypeBaseService

    @Autowired
    private lateinit var medalRuleGroupBaseService: MedalRuleGroupBaseService

    @Autowired
    private lateinit var notifyBaseService: NotifyBaseService

    @Autowired
    private lateinit var memberMedalBaseService: MemberMedalBaseService

    @Autowired
    private lateinit var memberMedalRecordBaseService: MemberMedalRecordBaseService

    @Autowired
    private lateinit var locker: Locker

    override fun handle(message: MedalHierarchyEventMessage) {
        val medalLock = locker.getLock("medal_obtain_key_${message.event.getKey()}")
        medalLock.withLock {
            val traceId = message.eventTraceId()
            val medalHierarchyId = message.medalHierarchyId
            message.forbiddenPort= ForbiddenPort.CALC_EVENT
            if (traceId.isEmpty()) {
                log.debug("勋章体系所属主体未定义该时机，勋章体系ID：{}", JsonUtils.toJson(message.medalHierarchy))
                return
            }

            val memberId = message.memberId()
            log.debug("traceId:$traceId,会员${memberId}在勋章体系:${medalHierarchyId}的勋章获取计算开始")
            if (SpecialCheckerUtils.checkMemberInSpecialMedal(
                    memberId,
                    medalHierarchyId,
                    ForbiddenOperation.MEDAL_OBTAIN,
                    ForbiddenPort.CALC_EVENT
                )
            ) {
                log.info("客户在获取勋章黑名单中! eventKey:{}", message.event.getKey())
                return
            }

            val medalHierarchy = findEffectMedalHierarchy(message)
            val lock = locker.getLock("medal_obtain_${medalHierarchyId}-$memberId")
            lock.lock()
            try {
                ApplicationContextHolder.getBean(MedalHierarchyObtainHandler::class.java).doHandle(message, medalHierarchy)
            } catch (e: Exception) {
                log.error("事件处理失败,事件ID:{},勋章体系ID:{}", message.event.getKey(), medalHierarchyId, e)
                throw e
            } finally {
                lock.unlock()
            }
        }
    }

    fun doHandle(message: MedalHierarchyEventMessage, medalHierarchy: MedalHierarchy) {
        val medalHierarchyId = medalHierarchy.id!!
        if (ProcessRecordUtil.isProcessRecord(medalHierarchyId.toString(), message.event.getKey(), ProcessRecordTypeEnum.MEDAL)) {
            log.info("勋章体系下已处理过该事件,事件ID:{},勋章体系ID:{}", message.event.getKey(), medalHierarchyId)
            return
        }
        var matchList = calculateMedal(medalHierarchy, message)
        log.debug("匹配的勋章为:{}", JsonUtils.toJson(matchList))
        val matchMap = mutableMapOf<Long, MatchMedal>()
        matchList.map {
            val medalId = it.matchMedal?.id ?: return
            if (it.moreThan(matchMap[medalId]))
                matchMap[medalId] = it
        }
        matchList = matchMap.values.toMutableList()
        log.debug("去除相同勋章后为:{}", JsonUtils.toJson(matchList))
        matchList.forEach {
            val originMemberMedal = memberMedalBaseService.findMemberMedal(message.memberId(),
                medalHierarchyId, it.matchMedal!!.id!!, null).getIfPresent()
            if (originMemberMedal != null && originMemberMedal.disabled == false) {
                // 如果当前勋章为永久，不触发获取勋章
                if (originMemberMedal.overdueDate == null) return@forEach
                // 如果当前勋章比获取勋章到期时间晚，不触发获取勋章
                if (!it.effectForever &&
                    originMemberMedal.overdueDate!!.isAfter(it.overdueTime!!.atZone(ZoneId.systemDefault()))
                ) return@forEach
            }
            if (!it.effectForever && it.effectDate!!.isAfter(it.overdueTime!!.atZone(ZoneId.systemDefault()))) {
                log.error("勋章生效时间大于失效时间,请检查规则:${it.effectRuleName}")
                return@forEach
            }
            val memberMedal = it.initMemberMedal(originMemberMedal, message).saveOrUpdate(medalHierarchyId)
            val record = MemberMedalRecord.init(memberMedal, MedalChangeMode.OBTAIN, MedalRecordType.OBTAIN, it.effectRuleName)
            record.apply {
                this.traceId = message.eventTraceId()
                this.triggerId = message.event().getKey()
                this.originalEffectDate = originMemberMedal?.effectDate
                this.originalOverdueDate = originMemberMedal?.effectDate
                this.ruleId = it.effectRuleId
                this.ruleName = it.effectRuleName
                this.eventTypeId = it.effectEventTypeId
                this.eventTypeName = it.effectEventTypeName
                this.eventFqn = message.event().getFqn()
            }
            memberMedalRecordBaseService.saveOrUpdate(record, medalHierarchyId)
            notifyBaseService.postIntoBus(record)
        }
    }

    private fun calculateMedal(medalHierarchy: MedalHierarchy, message: MedalHierarchyEventMessage): List<MatchMedal> {
        val matchList = mutableListOf<MatchMedal>()
        val occurDate = DateUtils.dateToZonedDateTime(message.matchingTime)
        medalHierarchy.medalDefinitions!!.forEach { medalDefinition ->
            val ruleGroupList = medalRuleGroupBaseService.findGroupByDefinition(medalDefinition.id!!)
            ruleGroupList.filter {
                it.groupType == MedalRuleGroupTypeEnum.OBTAIN
                && it.calculateStatusByDate(occurDate) == RuleGroupStatusEnum.RUNNING
                && findEffectEventType(message.event, it.eventTypeId!!).eventStream == message.event.getFqn()
            }.forEach { medalRuleGroup ->
                val eventType = findEffectEventType(message.event, medalRuleGroup.eventTypeId!!)
                medalRuleGroup.ruleList!!.forEach { medalRule ->
                    log.debug("待匹配规则为:{},规则组名称为:{}", JsonUtils.toJson(medalRule), medalRuleGroup.name)
                    MatchMedal().matchMedalRule(eventType, medalRule, medalDefinition, medalRuleGroup.name!!, message)?.let {
                        matchList.add(it)
                        log.debug("规则匹配成功,匹配勋章为:{}", medalDefinition.name)
                    } ?: log.debug("规则匹配不成功")
                }
            }
        }
        return matchList
    }

    private fun findEffectEventType(event: Event, eventTypeId: Long): EventType {
        return try {
            eventTypeBaseService.getEffectiveOne(eventTypeId, event.getOccurrenceZonedDateTime())
        } catch (_: Exception) {
            throw MedalException(LoyaltyExceptionCode.MEDAL_HIERARCHY_NOT_FOUND)
        }
    }

    private fun findEffectMedalHierarchy(message: MedalHierarchyEventMessage): MedalHierarchy {
        val medalHierarchyId = message.medalHierarchyId
        val eventTime = message.event.getOccurrenceZonedDateTime()
        try {
            val medalHierarchy = medalHierarchyBaseService.getEffectiveOneCache(medalHierarchyId, eventTime)
            medalHierarchy.medalDefinitions = medalDefinitionBaseService.findEnabledMedalHierarchyByVersionId(medalHierarchy.versionId!!)
            return medalHierarchy
        } catch (_: Exception) {
            throw MedalException(LoyaltyExceptionCode.MEDAL_HIERARCHY_NOT_FOUND)
        }
    }

}