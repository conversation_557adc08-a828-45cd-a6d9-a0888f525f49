package com.shuyun.loyalty.stream.configurer

import org.redisson.Redisson
import org.redisson.api.RedissonClient
import org.redisson.config.RedissonConfigFactory
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer
import org.springframework.boot.autoconfigure.data.redis.RedisProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class RedisConfig {

    @Bean
    fun redissonClient(redisProperties: RedisProperties): RedissonClient {
        val config = RedissonConfigFactory.createConfig(redisProperties)
        return Redisson.create(config)
    }

    @Bean
    fun lettuceClientConfigurationBuilderCustomizer (): LettuceClientConfigurationBuilderCustomizer {
        return LettuceClientConfigurationBuilderCustomizer { builder ->
            val config = builder.build()
            if (config.isUseSsl) {
                builder.useSsl().disablePeerVerification()
            }
//            config.clientOptions.map {
//                val tcpUserTimeout = it.socketOptions.tcpUserTimeout.mutate().enable()
//                    .tcpUserTimeout(Duration.ofSeconds(30))
//                    .build()
//                val socketOptions = it.socketOptions.mutate().tcpUserTimeout(tcpUserTimeout).build()
//                it.mutate().socketOptions(socketOptions).build()
//            }.ifPresent {
//                builder.clientOptions(it)
//            }
        }
    }
}