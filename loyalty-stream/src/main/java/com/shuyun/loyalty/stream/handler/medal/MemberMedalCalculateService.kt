package com.shuyun.loyalty.stream.handler.medal

import com.shuyun.loyalty.entity.api.constants.MedalChangeMode
import com.shuyun.loyalty.entity.api.constants.MedalRecordType
import com.shuyun.loyalty.entity.api.request.MemberMedalModifyRequest
import com.shuyun.loyalty.service.datamodel.MemberMedal
import com.shuyun.loyalty.service.datamodel.MemberMedalRecord
import com.shuyun.loyalty.service.extension.getIfPresent
import com.shuyun.loyalty.service.message.MessageHandlerType
import com.shuyun.loyalty.service.message.medal.MedalHierarchyKeepOrRecycleMessage
import com.shuyun.loyalty.service.model.*
import com.shuyun.loyalty.service.service.MedalDefinitionBaseService
import com.shuyun.loyalty.service.service.MemberMedalBaseService
import com.shuyun.loyalty.service.service.MemberMedalRecordBaseService
import com.shuyun.loyalty.service.service.NotifyBaseService
import com.shuyun.loyalty.service.util.SpecialCheckerUtils
import com.shuyun.loyalty.service.util.VisitorInfoUtil
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.lang.Boolean.FALSE
import java.time.LocalDateTime
import java.time.ZonedDateTime

@Service
class MemberMedalCalculateService {
    private val log = LogManager.getLogger(MemberMedalCalculateService::class.java)

    @Autowired
    private lateinit var memberMedalBaseService: MemberMedalBaseService

    @Autowired
    private lateinit var medalDefinitionBaseService: MedalDefinitionBaseService

    @Autowired
    private lateinit var notifyBaseService: NotifyBaseService

    @Autowired
    private lateinit var memberMedalRecordBaseService: MemberMedalRecordBaseService

        fun matchRule(message: MedalHierarchyKeepOrRecycleMessage, ruleGroupList: List<MedalRuleGroup>,
                  hierarchy: MedalHierarchy,medalEventType: EventType) {
        log.debug("开始计算规则:{}", JsonUtils.toJson(ruleGroupList))
        val memberId = message.memberId
        val medalHierarchyId = message.medalHierarchyId
        val medalDefinitionId = message.medalDefinitionId

        val memberMedal = memberMedalBaseService.findMemberMedal(memberId, medalHierarchyId, medalDefinitionId,
            FALSE).getIfPresent()
        if (memberMedal == null) {
            log.info("会员勋章不存在,会员ID:$memberId,勋章体系ID:${hierarchy.id},勋章ID:$medalDefinitionId")
            return
        }

        val medalDefinition = medalDefinitionBaseService.getEffectiveOne(medalDefinitionId, ZonedDateTime.now())
        val matchMedal = MatchMedal()
        ruleGroupList.filter { it.medalDefinitionId == medalDefinitionId }.forEach { medalRuleGroup ->
            medalRuleGroup.ruleList!!.forEach { rule ->
                log.debug("待匹配的保持规则为:${JsonUtils.toJson(rule)},规则组名称为:${medalRuleGroup.name}")
                val eventType = EventType().apply {
                    this.id = medalRuleGroup.eventTypeId
                    this.name = medalRuleGroup.eventTypeName
                }
                matchMedal.matchMedalRule(eventType, rule, medalDefinition, medalRuleGroup.name!!, message,memberMedal)?.let {
                    log.debug("规则匹配成功,匹配勋章为:{}", medalDefinition.name)
                } ?: log.debug("规则匹配不成功")
            }
        }
        if (matchMedal.matchMedal == null)
            medalRecycle(message, hierarchy,medalEventType)
        else if (!matchMedal.effectForever && matchMedal.overdueTime!!.isBefore(LocalDateTime.now()) && message.event()
                .getFqn() == MessageHandlerType.MEDAL_OVER_HANDLER.fqn)//如果计算出来的时间小于当前时间，并且是过期时机，才直接回收
            medalRecycle(message, hierarchy,medalEventType)
        else
            medalKeep(matchMedal, message, memberMedal,medalEventType)
        log.debug("保持计算结束")
    }


    private fun medalKeep(matchMedal: MatchMedal, message: MedalHierarchyKeepOrRecycleMessage,
                                originalMemberMedal: MemberMedal,eventType: EventType) {
        val memberId = message.memberId
        val medalHierarchyId = message.medalHierarchyId
        val medalDefinitionId = message.medalDefinitionId
        log.debug("开始进行勋章保持,会员id:${memberId},勋章ID:${medalDefinitionId},匹配规则:${matchMedal.effectRuleName}")
        if (SpecialCheckerUtils.checkMemberInSpecialMedal(
                memberId,
                medalHierarchyId,
                ForbiddenOperation.MEDAL_KEEP,
                message.forbiddenPort
            )
        ) {
            log.info("客户在保持黑名单中!")
            return
        }
        var memberMedal = matchMedal.initMemberMedal(originalMemberMedal, message)
        memberMedal.effectDate = matchMedal.effectDate ?: ZonedDateTime.now()
        if (!matchMedal.effectForever && memberMedal.effectDate!!.isAfter(memberMedal.overdueDate)) {
            log.error("勋章生效时间大于失效时间,请检查规则:${matchMedal.effectRuleName}")
            return
        }
        memberMedal = memberMedalBaseService.saveOrUpdate(memberMedal, medalHierarchyId)
        val record = MemberMedalRecord.init(memberMedal, MedalChangeMode.KEEP, MedalRecordType.KEEP,
            matchMedal.effectRuleName)
        record.apply {
            this.traceId = message.traceId
            this.triggerId = message.event().getKey()
            this.key= message.event().getKey()
            this.ruleId=matchMedal.effectRuleId
            this.ruleName = matchMedal.effectRuleName
            this.eventTypeId=eventType.id
            this.eventFqn=eventType.eventStream
            this.originalEffectDate = originalMemberMedal.effectDate
            this.originalOverdueDate = originalMemberMedal.overdueDate
            this.eventTypeName = eventType.name
        }
        memberMedalRecordBaseService.saveOrUpdate(record, record.medalHierarchyId!!)
        notifyBaseService.postIntoBus(record)
    }

    fun medalRecycle(message: MedalHierarchyKeepOrRecycleMessage, hierarchy: MedalHierarchy,eventType: EventType?=null) {
        val memberId = message.memberId
        val medalHierarchyId = message.medalHierarchyId
        val medalDefinitionId = message.medalDefinitionId
        log.debug("开始进行勋章回收,会员ID:${memberId},勋章ID:${medalDefinitionId}")
/*        if (SpecialCheckerUtils.checkMemberInSpecialMedal(
                memberId,
                medalHierarchyId,
                ForbiddenOperation.MEDAL_RECYCLE,
                message.forbiddenPort
            )
        ) {
            log.info("客户在回收黑名单中!")
            return
        }*/

        val memberMedal = memberMedalBaseService.findMemberMedal(memberId, medalHierarchyId, medalDefinitionId,
            FALSE).getIfPresent()
        if (memberMedal == null) {
            log.info("会员勋章不存在,会员ID:$memberId,勋章体系ID:${hierarchy.id},勋章ID:$medalDefinitionId")
            return
        }
        memberMedal.disabled = true
        memberMedalBaseService.saveOrUpdate(memberMedal, medalHierarchyId)
        val record = MemberMedalRecord.init(memberMedal, MedalChangeMode.KEEP, MedalRecordType.RECYCLE, "")
        record.apply {
            this.triggerId = message.event().getKey()
            this.key = message.event().getKey()
            this.traceId = message.traceId
            this.originalEffectDate = memberMedal.effectDate
            this.originalOverdueDate = memberMedal.overdueDate
            this.currentEffectDate = memberMedal.effectDate
            if (eventType !=null) {
                this.eventTypeId = eventType.id
                this.eventTypeName = eventType.name
                this.eventFqn = eventType.eventStream
            }
            this.currentOverdueDate = memberMedal.overdueDate

        }
        memberMedalRecordBaseService.saveOrUpdate(record, medalHierarchyId)
        notifyBaseService.postIntoBus(record)
    }

    fun saveMedal(originMemberMedal: MemberMedal, newMemberMedal: MemberMedal, request: MemberMedalModifyRequest,
                  recordType: MedalRecordType) {
        memberMedalBaseService.saveOrUpdate(newMemberMedal, newMemberMedal.medalHierarchyId!!)
        val operator = VisitorInfoUtil.username.get()
        val record = MemberMedalRecord.init(newMemberMedal, request.changeWayType!!, recordType, request.description)
        record.apply {
            this.triggerId = request.triggerId
            this.operator = operator
            this.originalOverdueDate = originMemberMedal.overdueDate
            this.originalEffectDate = originMemberMedal.effectDate
            this.KZZD1 = request.KZZD1
            this.KZZD2 = request.KZZD2
            this.KZZD3 = request.KZZD3
        }
        memberMedalRecordBaseService.saveOrUpdate(record, request.medalHierarchyId!!)
        notifyBaseService.postIntoBus(record)
    }

}