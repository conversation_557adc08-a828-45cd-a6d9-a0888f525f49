package com.shuyun.loyalty.stream.sequence

import com.pip.mybatisplus.toolkit.DataapiHttpFactory
import com.shuyun.dm.api.vo.FetchStartRequest
import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.entity.dto.*
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.extension.utcStr
import com.shuyun.loyalty.service.kafka.IPointSyncProducer
import com.shuyun.loyalty.service.message.MessageHandlerType
import com.shuyun.loyalty.service.model.Plan
import com.shuyun.loyalty.service.service.LoyaltyPrograms
import com.shuyun.loyalty.service.service.LoyaltyPrograms.HierarchyType
import com.shuyun.loyalty.service.util.PropertyUtils
import com.shuyun.loyalty.service.util.RedisConstantValue
import com.shuyun.loyalty.service.util.RedisUtils
import com.shuyun.loyalty.stream.kafka.sink.KafkaSource
import com.shuyun.loyalty.stream.message.output.GradeHierarchyDegradeOutMessage
import com.shuyun.loyalty.stream.message.output.MedalHierarchyOverOutMessage
import com.shuyun.loyalty.stream.sequence.MessageCounter.K.*
import com.shuyun.loyalty.stream.service.PointTimedTaskService
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.spectrum.discovery.api.DiscoveryException
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.messaging.support.MessageBuilder
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import java.io.IOException
import java.time.ZonedDateTime
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import kotlin.concurrent.thread


@Component
class MessageTaskScheduler {

    companion object {
        private val log = LogManager.getLogger(MessageTaskScheduler::class.java)
        private const val CHUNKED_SIZE = 10
        private const val POINT_RETRY_BEFORE_DAYS_K = "loyalty.point.retry.before.days"
        private const val POINT_EFFECT_BEFORE_DAYS_K = "loyalty.point.effect.before.days"
        private const val POINT_EXPIRE_BEFORE_DAYS_K = "loyalty.point.expire.before.days"
        private const val GRADE_EXPIRE_BEFORE_DAYS_K = "loyalty.grade.expire.before.days"
        private const val MEDAL_EXPIRE_BEFORE_DAYS_K = "loyalty.medal.expire.before.days"
        val shouldIgnoredIds = HashSet<Long>()
    }

    @Autowired
    private lateinit var pointSyncProducer: IPointSyncProducer

    @Autowired
    private lateinit var counter: MessageCounter

    @Autowired
    private lateinit var kafkaSource: KafkaSource

    @Autowired
    private lateinit var redisUtils: RedisUtils

    @Autowired
    private lateinit var pointTimedTaskService: PointTimedTaskService

    @Autowired
    private lateinit var locker: Locker


    // 每两小时执行一次
    @Scheduled(cron =  "0 0 */2 * * ?")
    @SchedulerLock(name = "checkRetry", lockAtMostFor = "1h",lockAtLeastFor = "10m")
    fun checkRetry() {
        if(!PropertyUtils.getScheduledRetryPoint()) {
            log.debug("积分重试任务已经关闭 , 配置参数: scheduled.retry.point.enabled")
            return
        }
        val now = ZonedDateTime.now()
        val hierarchyIds = LoyaltyPrograms.findAllHierarchyIds(type = HierarchyType.POINT)
        val list = hierarchyIds.chunked(CHUNKED_SIZE)
        val beforeStr = now.minusDays(Property.getSysOrEnv(POINT_RETRY_BEFORE_DAYS_K, 7L)).utcStr()
        for (ids in list) {
            val threads = ArrayList<Thread>()
            for (accountTypeId in ids) {
                val t = thread {
                    try {
                        log.debug("检查是否存在重试任务：体系ID $accountTypeId")
                        val sql = """
                            SELECT distinct `memberId`, `memberPointId` 
                            FROM data.loyalty.member.PointCalculateJournal$accountTypeId 
                            WHERE status = 'NEW' and created > '${beforeStr}' and created < '${now.utcStr()}'
                            LIMIT 3000
                        """.trimIndent()
                        fetch(sql) { map ->
                            val memberId = map["memberId"] as String
                            val memberPointId = map["memberPointId"] as String
                            val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT, accountTypeId, memberId))
                            val b = lock.tryLock(100, TimeUnit.MILLISECONDS)
                            if (!b) return@fetch
                            try {
                                pointTimedTaskService.retry(accountTypeId, memberPointId, memberId)
                            } finally {
                                lock.unlock()
                            }
                        }
                    } catch (ex: Throwable) {
                        ex.addIgnoreSetIfModelNotFound(accountTypeId)
                        log.error("检查待执行任务出错：积分体系ID={} {}", accountTypeId, ex.message)
                    }
                }
                threads.add(t)
            }
            for (thread in threads) thread.join()
        }
    }


    // 每10分钟执行一次
    @Scheduled(fixedRate = 60000 * 10, initialDelay = 60000)
    @SchedulerLock(name = "checkDelayedPoints", lockAtMostFor = "1h", lockAtLeastFor = "1m")
    fun checkDelayedPoints() {
        if (!PropertyUtils.getScheduledEffectPoint()) {
            log.debug("生效积分任务已经关闭 , 配置参数: scheduled.effect.point.enabled")
            return
        }
        val hierarchyIds = LoyaltyPrograms.findAllHierarchyIds(type = HierarchyType.POINT)
        counter.initPointCounter(hierarchyIds.toSet())
        val map = ConcurrentHashMap<Long, Pair<Long, Long>>()
        val now = ZonedDateTime.now()
        val list = hierarchyIds.chunked(CHUNKED_SIZE)
        for (ids in list) {
            val threads = ArrayList<Thread>()
            for (accountTypeId in ids) {
                if (!EFFECT_POINT_MESSAGE_COUNT.canProduce(accountTypeId)) {
                    continue
                }
                val t = thread {
                    try {
                        val s = System.currentTimeMillis()
                        var c = 0L
                        val beforeStr = now.minusDays(Property.getSysOrEnv(POINT_EFFECT_BEFORE_DAYS_K, 7L)).utcStr()
                        val sql = """
                            SELECT distinct memberId, memberPointId  
                            FROM data.loyalty.member.account.GainStatement${accountTypeId} 
                            WHERE status = 'DELAY_SEND' AND effectiveDate > '${beforeStr}' AND effectiveDate < '${now.utcStr()}'
                        """.trimIndent()
                        fetch(sql) { map ->
                            val memberPointId = map["memberPointId"] as String
                            val memberId = map["memberId"] as String
                            val msg = MemberPointEffectMessage(accountTypeId, memberId, memberPointId)
                            msg.send()
                            c++
                        }
                        map[accountTypeId] = Pair(c, (System.currentTimeMillis() - s))
                    } catch (ex: Throwable) {
                        ex.addIgnoreSetIfModelNotFound(accountTypeId)
                        log.error("检查生效积分任务出错：积分体系ID={} {}", accountTypeId, ex.message)
                    }
                }
                threads.add(t)
            }
            for (thread in threads) thread.join()
            log.debug("检查积分生效任务完成 {}", buildLog(map))
        }
    }


    @Scheduled(fixedRate = 60000 * 10, initialDelay = 60000)
    @SchedulerLock(name = "checkExpiredPoints", lockAtMostFor = "1h", lockAtLeastFor = "1m")
    fun checkExpiredPoints() {
        if(!PropertyUtils.getScheduledExpirePoint()) {
            log.debug("过期积分任务已经关闭 , 配置参数: scheduled.expire.point.enabled")
            return
        }
        val map = ConcurrentHashMap<Long, Pair<Long, Long>>()
        val now = ZonedDateTime.now()
        val hierarchyIds = LoyaltyPrograms.findAllHierarchyIds(type = HierarchyType.POINT)
        counter.initPointCounter(hierarchyIds)
        val list = hierarchyIds.chunked(CHUNKED_SIZE)
        for (ids in list) {
            val threads = ArrayList<Thread>()
            for (accountTypeId in ids) {
                if (!EXPIRE_POINT_MESSAGE_COUNT.canProduce(accountTypeId)) {
                    continue
                }
                val t = thread {
                    try {
                        val s = System.currentTimeMillis()
                        var c = 0L
                        val beforeStr = now.minusDays(Property.getSysOrEnv(POINT_EXPIRE_BEFORE_DAYS_K, 7L)).utcStr()
                        val sql = """
                            SELECT distinct memberId, memberPointId  
                            FROM data.loyalty.member.account.ValidStatement$accountTypeId 
                            WHERE overdueDate > '${beforeStr}' AND overdueDate < '${now.utcStr()}' 
                                AND fromStatus NOT IN ('OPEN_FROZE', 'SPECIAL_FROZE')
                        """.trimIndent()
                        fetch(sql) { map ->
                            val memberPointId = map["memberPointId"] as String
                            val memberId = map["memberId"] as String
                            val msg = MemberPointExpireMessage(accountTypeId, memberId, memberPointId)
                            log.debug("发送存在过期积分的会员积分账户消息：{}", msg)
                            msg.send()
                            c++
                        }
                        map[accountTypeId] = Pair(c, (System.currentTimeMillis() - s))
                    } catch (ex: Throwable) {
                        ex.addIgnoreSetIfModelNotFound(accountTypeId)
                        log.error("检查重试任务出错：积分体系ID={} {}", accountTypeId,ex.message)
                    }
                }
                threads.add(t)
            }
            for (thread in threads) thread.join()
            log.debug("检查积分过期任务完成 {}", buildLog(map))
        }
    }


    @Scheduled(fixedRate = 60000 * 10, initialDelay = 60000)
    @SchedulerLock(name = "checkExpiredGrade", lockAtMostFor = "1h", lockAtLeastFor = "1m")
    fun  checkExpiredGrade() {
        if(!PropertyUtils.getScheduledExpireGrade()) {
            log.debug("过期等级任务已经关闭 , 配置参数: scheduled.expire.grade.enabled")
            return
        }
        val now = ZonedDateTime.now()
        val hierarchyIds = LoyaltyPrograms.findAllHierarchyIds(type = HierarchyType.GRADE)
        counter.initGradeCounter(hierarchyIds)
        val list = hierarchyIds.chunked(CHUNKED_SIZE)
        for (ids in list) {
            val threads = ArrayList<Thread>()
            for (hierarchyId in ids) {
                if (!EXPIRE_GRADE_MESSAGE_COUNT.canProduce(hierarchyId)) {
                    log.info("队列存在未处理的过期等级消息，跳过处理：体系ID $hierarchyId")
                    continue
                }
                val gradeHierarchy = LoyaltyPrograms.findGradeHierarchy(hierarchyId) ?: continue
                val t = thread {
                    try {
                        var c = 0L
                        val beforeStr = now.minusDays(Property.getSysOrEnv(GRADE_EXPIRE_BEFORE_DAYS_K, 60L)).utcStr()
                        val sql = """
                            SELECT planId, planName, memberId, currentGradeDefinitionId, overdueDate 
                            FROM data.loyalty.member.hierarchy.Grade$hierarchyId 
                            WHERE overdueDate > '${beforeStr}' AND overdueDate <= '${now.utcStr()}'
                        """.trimIndent()
                        fetch(sql) { map ->
                            val planId = map["planId"].toString().toLong()
                            val planName = map["planName"].toString()
                            val gradeDefinitionId = map["currentGradeDefinitionId"].toString().toLong()
                            val memberId = map["memberId"].toString()
                            val overdueDate = ZonedDateTime.parse(map["overdueDate"].toString())
                            val message = GradeHierarchyDegradeOutMessage(Plan(planId, planName), memberId, gradeDefinitionId, overdueDate, gradeHierarchy)
                            message.send()
                            c++
                        }
                    } catch (ex: Throwable) {
                        ex.addIgnoreSetIfModelNotFound(hierarchyId)
                        log.error("检查等级过期任务出错：等级体系ID={} {}", hierarchyId, ex.message)
                    }
                }
                threads.add(t)
            }
            for (thread in threads) thread.join()
        }
    }



    @Scheduled(fixedRate = 60000 * 10,  initialDelay = 60000)
    @SchedulerLock(name = "checkExpiredMedal", lockAtMostFor = "1h", lockAtLeastFor = "1m")
    fun  checkExpiredMedal() {
        if(!PropertyUtils.getScheduledExpireMedal()) {
            log.debug("过期勋章任务已经关闭 , 配置参数: scheduled.expire.medal.enabled")
            return
        }
        val activated = redisUtils.getString(RedisConstantValue.MEDAL_ACTIVATED_KEY)
        if (activated?.toBoolean() != false) {
            return
        }

        val now = ZonedDateTime.now()
        val nowStr = now.utcStr()
        val hierarchyIds = LoyaltyPrograms.findAllHierarchyIds(type = HierarchyType.MEDAL)
        counter.initMedalCounter(hierarchyIds)
        val list = hierarchyIds.chunked(CHUNKED_SIZE)
        for (ids in list) {
            val threads = ArrayList<Thread>()
            for (hierarchyId in ids) {
                if (!EXPIRE_MEDAL_MESSAGE_COUNT.canProduce(hierarchyId)) {
                    log.info("队列存在未处理的过期勋章消息，跳过处理：体系ID $hierarchyId")
                    continue
                }
                val medalHierarchy = LoyaltyPrograms.findMedalHierarchy(hierarchyId) ?: continue
                val t = thread {
                    try {
                        var c = 0L
                        val beforeStr = now.minusDays(Property.getSysOrEnv(MEDAL_EXPIRE_BEFORE_DAYS_K, 60L)).utcStr()
                        val sql = "select planId, planName, memberId, medalDefinitionId, overdueDate from data.loyalty.member.hierarchy.Medal${hierarchyId} where disabled = 0 overdueDate > '${beforeStr}' and overdueDate <= '${nowStr}' "
                        fetch(sql) { map ->
                            val planId = map["planId"].toString().toLong()
                            val planName = map["planName"].toString()
                            val medalDefinitionId = map["medalDefinitionId"].toString().toLong()
                            val memberId = map["memberId"].toString()
                            val overdueDate = ZonedDateTime.parse(map["overdueDate"].toString())
                            val message = MedalHierarchyOverOutMessage(Plan(planId, planName), memberId, medalDefinitionId, overdueDate, medalHierarchy)
                            message.send()
                            c++
                        }
                    } catch (ex: Throwable) {
                        ex.addIgnoreSetIfModelNotFound(hierarchyId)
                        log.error("检查勋章过期任务出错：等级体系ID={} {}", hierarchyId, ex.message)
                    }
                }
                threads.add(t)
            }
            for (thread in threads) thread.join()
        }
    }

    private fun buildLog(map: Map<Long, Pair<Long, Long>>): String {
        val msg = StringBuilder("\n")
        map.forEach { (k, v) ->
            msg.append("账户体系ID $k 数据 ${v.first} 条, 耗时 ${v.second} ms\n")
        }
        return msg.toString()
    }


    private fun fetch(sql: String, callback: (Map<String, Any?>) -> Unit) {
        val request = FetchStartRequest(sql, mapOf(),2000)
        DataapiHttpFactory.httpSdk().fetch(request).use { c ->
            while (true) {
                val data = c.next().data
                if (data.isEmpty()) break
                data.forEach { map ->
                    try {
                        callback(map)
                    } catch (e: Exception) {
                        if (e is IOException || e is DiscoveryException) {
                            log.warn("Fetch数据异常: {} {}", Json.toJson(map), e.message)
                        } else {
                            log.error("Fetch数据异常: {}", Json.toJson(map), e)
                        }
                    }
                }
            }
        }
    }

    private fun Throwable.addIgnoreSetIfModelNotFound(id: Long) {
        if (this is SdkException) {
            if (this.error_code == "152201" || this.error_code == "151147") {
                shouldIgnoredIds.add(id)
                return
            }
        }
    }

    private fun MessageCounter.K.canProduce(hierarchyId: Long): Boolean {
        val b = counter.canProduce(hierarchyId, EXPIRE_MEDAL_MESSAGE_COUNT)
        if (!b) {
            log.info("队列存在未处理的生效积分消息，跳过处理：k={}", this.key + hierarchyId)
        }
        return b
    }

    private fun MemberMessage.send() {
        val b = pointSyncProducer.send(this)
        if (b) {
            val accountTypeId = when (this) {
                is MemberPointMessage -> this.pointAccountTypeId
                is MemberPointExpireMessage -> this.pointAccountTypeId
                is MemberPointEffectMessage -> this.pointAccountTypeId
                else -> null
            }
            if (accountTypeId != null) {
                counter.increment(accountTypeId, EXPIRE_POINT_MESSAGE_COUNT)
            }
        }
    }

    private fun GradeHierarchyDegradeOutMessage.send() {
        val self = this
        val event = Json.convert<Event>(this).apply {
            this.setFqn(MessageHandlerType.GRADE_OVER_HANDLER.fqn)
            this.setOccurrenceTs(self.overdueDate.toInstant().toEpochMilli())
            this.setDetectionTs(System.currentTimeMillis())
            this.setKey(self.toKey())
        }
        log.debug("发送存在过期等级的会员账户消息：hierarchyId={} memberId={}", gradeHierarchy.id, memberId)
        val b = kafkaSource.normalOutput().send(MessageBuilder.withPayload(event).build())
        if (b) {
            counter.increment(gradeHierarchy.id!!, EXPIRE_GRADE_MESSAGE_COUNT)
        }
    }

    private fun MedalHierarchyOverOutMessage.send() {
        val self = this
        val event = Json.convert<Event>(this).apply {
            this.setFqn(MessageHandlerType.MEDAL_OVER_HANDLER.fqn)
            this.setOccurrenceTs(self.overdueDate.toInstant().toEpochMilli())
            this.setDetectionTs(System.currentTimeMillis())
            this.setKey(self.toKey())
        }
        log.debug("发送存在过期勋章的会员账户消息：hierarchyId={} memberId={}", medalHierarchy.id!!, memberId)
        val b = kafkaSource.normalOutput().send(MessageBuilder.withPayload(event).build())
        if (b) {
            counter.increment(medalHierarchy.id!!, EXPIRE_MEDAL_MESSAGE_COUNT)
        }
    }

}

