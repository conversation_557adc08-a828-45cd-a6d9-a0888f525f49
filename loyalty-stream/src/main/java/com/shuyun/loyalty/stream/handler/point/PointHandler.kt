package com.shuyun.loyalty.stream.handler.point

import com.shuyun.loyalty.stream.handler.Handler
import com.shuyun.loyalty.stream.message.PointCalcMessage
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class PointHandler: Handler<PointCalcMessage> {

    private val logger = LoggerFactory.getLogger(PointHandler::class.java)



    @Autowired
    private lateinit var memberPointSendCalculate: MemberPointSendCalculate


    override fun handle(message: PointCalcMessage) {
        if (message.traceId.isNullOrEmpty()) {
            logger.info("积分事件未计算 原单ID不存在 fqn: {} path: {} event: {}", message.eventType.eventStream, message.eventType.originalOrderPath, message.event)
            return
        }

        memberPointSendCalculate.calc(message)
    }

}