package com.shuyun.loyalty.stream.processor

import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.infrastructure.eventStreamMeta.LocalEventMetasRepository
import com.shuyun.loyalty.service.message.point.LoyaltyEventMessage
import com.shuyun.loyalty.service.service.LoyaltyPrograms
import com.shuyun.loyalty.service.util.MDCUtils
import com.shuyun.loyalty.stream.kafka.sink.KafkaSource
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.annotation.Order
import org.springframework.messaging.Message
import org.springframework.messaging.support.MessageBuilder
import org.springframework.stereotype.Component
import java.time.ZonedDateTime

@Component
@Order
class EventPreprocessor {

    private val logger = LogManager.getLogger(EventPreprocessor::class.java)

    @Autowired
    private lateinit var metasRepository: LocalEventMetasRepository

    @Autowired
    private lateinit var kafkaSource: KafkaSource

    @Autowired
    private lateinit var messageProcessor: MessageProcessor


    fun process(event: Event, dateTime: ZonedDateTime) {
        if (!validate(event)) {
            logger.warn("事件计算-基础格式不合法: {}", event)
            return
        }

        if (messageProcessor.match(event.getFqn())) {
            messageProcessor.process(event)
            return
        }

        val plans = LoyaltyPrograms.findPlansByFqn(event.getFqn(), event.getOccurrenceZonedDateTime())
        if (plans.isEmpty()) {
            logger.warn("事件计算-事件处理结束 通过事件FQN({})和时间发生时间({})没有匹配到任何忠诚度方案", event.getFqn(), event.getOccurrenceZonedDateTime())
            return
        }
        val outs = ArrayList<Message<LoyaltyEventMessage>>()
        pl@for (plan in plans) {
            for (subject in plan.subjectList!!) {
                for (it in subject.eventTypeList!!) {
                    if (it.eventStreamMetas == null) {
                        val metas = metasRepository.findById(it.eventStream!!)
                        if (metas == null) {
                            val logTemplate = "事件计算-计划：({} {}) 主体：({} {}) 时机({} {}) 未找到时机FQN(${it.eventStream})关联的元数据"
                            logger.warn(logTemplate, plan.id, plan.name, subject.id, subject.dataType, it.id, it.name)
                            continue@pl
                        }
                        it.eventStreamMetas = metas
                    }
                }
            }
            val eventMessage = LoyaltyEventMessage(plan, event, dateTime)
            val message = MessageBuilder.withPayload(eventMessage)
                .setHeader(LoyaltyEventMessage.PARTITION_KEY, eventMessage.messagePartitionKey())
                .setHeader(MDCUtils.LOYALTY_TRACE_ID, MDCUtils.getTraceId()).build()
            outs.add(message)
        }
        outs.forEach { kafkaSource.eventChannelOutput().send(it) }
    }

    private fun validate(event: Event): Boolean {
        return !(event.all().isEmpty() || !event.validate())
    }
}