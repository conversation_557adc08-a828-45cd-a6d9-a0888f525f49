package com.shuyun.loyalty.stream.service

import com.pip.shuyun.pool.transaction.DmTransaction
import com.pip.shuyun.pool.transaction.TransactionInfoHolder.afterCommit
import com.shuyun.loyalty.entity.dto.MemberPointExpireMessage
import com.shuyun.loyalty.service.datamodel.MemberPointCalculateTask
import com.shuyun.loyalty.service.datamodel.PointAction
import com.shuyun.loyalty.service.datamodel.PointStatus
import com.shuyun.loyalty.service.datamodel.update
import com.shuyun.loyalty.service.kafka.IPointSyncProducer
import com.shuyun.loyalty.service.message.point.LoyaltyEventMessage.Companion.PARTITION_KEY
import com.shuyun.loyalty.service.metrics.EventReceiveErrorMetrics
import com.shuyun.loyalty.service.transfer.points.LoyaltyPoints
import com.shuyun.loyalty.service.transfer.points.LoyaltyRequestType
import com.shuyun.loyalty.service.transfer.points.OutNotification
import com.shuyun.loyalty.service.util.MDCUtils
import com.shuyun.loyalty.service.util.MDCUtils.LOYALTY_TRACE_ID
import com.shuyun.loyalty.stream.kafka.sink.KafkaSource
import com.shuyun.pip.ApplicationContextHolder
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.messaging.support.MessageBuilder
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class PointTransferService {

    @Autowired
    private lateinit var pointMetricService: PointMetricService

    @Autowired
    private lateinit var eventReceiveErrorMetrics: EventReceiveErrorMetrics

    @Autowired
    private lateinit var returnOrderService: ReturnOrderService

    @Autowired
    private lateinit var kafkaSource: KafkaSource


    @DmTransaction
    fun process(task: MemberPointCalculateTask) {
        val lp = task.lp!!
        try {
            task.action.transfer(task)
            task.comment = ""
            task.status = PointStatus.COMPLETED
            task.update()
            afterCommit {
                pointMetricService.incr(lp, task.action)
                returnOrderService.returnOrder(lp.type, lp.eventType, lp.member.memberId, lp.attr.traceId)
                if (task.isAsyncAction()) {
                    sendExpireMessage(lp)
                    var out = lp.out
                    if (task.action != PointAction.UNFREEZE && out == null) {
                        out = OutNotification(
                            requestId = lp.attr.uniqueId,
                            type = task.action,
                            memberId = lp.member.memberId,
                            accountTypeId = lp.hierarchy.id,
                            expectPoints = lp.pointValue,
                            actualPoints = BigDecimal.ZERO,
                        )
                    }
                    out?.let {
                        val msg = MessageBuilder.withPayload(it)
                            .setHeader(PARTITION_KEY, lp.member.memberId)
                            .setHeader(LOYALTY_TRACE_ID, MDCUtils.getTraceId()).build()
                        kafkaSource.asyncNotifyOutput().send(msg)
                    }
                }
            }
        } catch (e: Exception) {
            if (lp.type == LoyaltyRequestType.EVENT) eventReceiveErrorMetrics.increment()
            val error = if (e.message != null) e.message ?: e.cause?.message ?: e.stackTraceToString() else e.stackTraceToString()
            task.comment = if (error.length > 400) error.take(400) + "..." else error
            throw e
        }
    }

    private fun sendExpireMessage(lp: LoyaltyPoints) {
        ApplicationContextHolder.getBean(IPointSyncProducer::class.java)
            .send(
                MemberPointExpireMessage(
                    lp.hierarchy.id,
                    lp.member.memberId,
                    lp.member.id
                )
            )
    }
}