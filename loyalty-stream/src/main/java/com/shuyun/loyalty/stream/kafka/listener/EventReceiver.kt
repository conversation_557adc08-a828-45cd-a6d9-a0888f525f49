package com.shuyun.loyalty.stream.kafka.listener


import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.entity.dto.MemberPointEffectMessage
import com.shuyun.loyalty.entity.dto.MemberPointExpireMessage
import com.shuyun.loyalty.entity.dto.MemberPointMessage
import com.shuyun.loyalty.entity.dto.MemberRequest
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.service.calculate.MemberPointAsyncModifyService
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.kafka.GradeModifyMessage
import com.shuyun.loyalty.service.kafka.MedalModifyMessage
import com.shuyun.loyalty.service.kafka.PointModifyMessage
import com.shuyun.loyalty.service.message.point.LoyaltyEventMessage
import com.shuyun.loyalty.service.metrics.EventGreenReceiveMetrics
import com.shuyun.loyalty.service.metrics.EventNormalReceiveMetrics
import com.shuyun.loyalty.service.metrics.LoyaltyEventReceiveMetrics
import com.shuyun.loyalty.service.util.ConstantValue.TENANT_ID
import com.shuyun.loyalty.service.util.MDCUtils
import com.shuyun.loyalty.service.util.MDCUtils.LOYALTY_TRACE_ID
import com.shuyun.loyalty.stream.handler.medal.MemberMedalCalculateService
import com.shuyun.loyalty.stream.handler.point.MemberGradeCalculate
import com.shuyun.loyalty.stream.kafka.sink.KafkaSink
import com.shuyun.loyalty.stream.message.BizMessage
import com.shuyun.loyalty.stream.processor.EventPostprocessor
import com.shuyun.loyalty.stream.processor.EventPreprocessor
import com.shuyun.loyalty.stream.sequence.MessageCounter
import com.shuyun.loyalty.stream.service.PointItemTaskService
import com.shuyun.loyalty.stream.service.PointTimedTaskService
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.pip.frameworkext.filter.VisitTenantInfoHolder
import com.shuyun.pip.thread.ThreadLocalCleanCallbacks
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cloud.stream.annotation.EnableBinding
import org.springframework.cloud.stream.annotation.StreamListener
import org.springframework.kafka.support.Acknowledgment
import org.springframework.kafka.support.KafkaHeaders
import org.springframework.messaging.Message
import java.io.IOException
import java.nio.charset.StandardCharsets
import java.time.ZonedDateTime

@EnableBinding(KafkaSink::class)
class EventReceiver(
    val eventNormalReceiveMetrics: EventNormalReceiveMetrics,
    val eventGreenReceiveMetrics: EventGreenReceiveMetrics,
    val loyaltyEventReceiveMetrics: LoyaltyEventReceiveMetrics) {
    @Autowired
    private lateinit var pointTimedTaskService: PointTimedTaskService

    @Autowired
    private lateinit var memberGradeCalculate: MemberGradeCalculate

    @Autowired
    private lateinit var memberMedalCalculateService: MemberMedalCalculateService

    @Autowired
    private lateinit var memberPointAsyncModifyService: MemberPointAsyncModifyService

    @Autowired
    private lateinit var messageCounter: MessageCounter

    @Autowired
    private lateinit var locker: Locker

    companion object {
        private const val MAX_DELIVERY_ATTEMPT = 2
        private val log = LogManager.getLogger(EventReceiver::class.java)
    }


    @StreamListener(value = KafkaSink.NORMAL_INPUT)
    fun normal(message: Message<Event>) {
        message.exec {
            eventNormalReceiveMetrics.increment()
            ApplicationContextHolder.getBean(EventPreprocessor::class.java).process(it, ZonedDateTime.now())
        }
    }

    @StreamListener(value = KafkaSink.GREEN_INPUT)
    fun green(message: Message<Event>) {
        message.exec {
            eventGreenReceiveMetrics.increment()
            ApplicationContextHolder.getBean(EventPreprocessor::class.java).process(it, ZonedDateTime.now())
        }
    }


    @StreamListener(value = KafkaSink.LOYALTY_EVENT_CHANNEL_INPUT)
    fun loyaltyEventChannel(message: Message<LoyaltyEventMessage>) {
        message.exec(onErrorThrows = true) {
            loyaltyEventReceiveMetrics.increment()
            ApplicationContextHolder.getBean(EventPostprocessor::class.java).process(it)
        }
    }


    @StreamListener(value = KafkaSink.POINT_MODIFY_INPUT)
    fun pointModify(message: Message<PointModifyMessage>){
        message.exec {
            memberPointAsyncModifyService.pointModify(it)
        }
    }


    @StreamListener(value = KafkaSink.GRADE_MODIFY_INPUT)
    fun gradeModify(message: Message<GradeModifyMessage>) {
        message.exec {
            memberGradeCalculate.saveGrade(it.memberGrade!!, it.memberGradeModifyRequest!!, it.originalMemberGrade!!, it.recordType!!)
        }
    }


    @StreamListener(value = KafkaSink.MEDAL_MODIFY_INPUT)
    fun medalModify(message: Message<MedalModifyMessage>) {
        message.exec {
            memberMedalCalculateService.saveMedal(
                it.originMemberMedal!!,
                it.newMemberMedal!!,
                it.request!!,
                it.recordType!!
            )
        }
    }


    @StreamListener(value = KafkaSink.LOYALTY_HTTP_REQUEST_ASYNC_PROCESS_INPUT)
    fun processMessage(message: Message<MemberPointMessage>) {
        message.exec {
            val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT, it.pointAccountTypeId, it.memberId))
            try {
                lock.lock()
                ApplicationContextHolder.getBean(PointItemTaskService::class.java).process(it)
            } finally {
                lock.unlock()
            }
        }
    }


    @StreamListener(value = KafkaSink.LOYALTY_LP_EXPIRE_INPUT)
    fun expire(message: Message<MemberPointExpireMessage>) {
        message.exec {
            val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT, it.pointAccountTypeId, it.memberId))
            try {
                lock.lock()
                pointTimedTaskService.expire(it.pointAccountTypeId, it.memberPointId, it.memberId)
            } finally {
                lock.unlock()
                messageCounter.decrement(it.pointAccountTypeId, MessageCounter.K.EXPIRE_POINT_MESSAGE_COUNT)
            }
        }
    }


    @StreamListener(value = KafkaSink.LOYALTY_LP_EFFECT_INPUT)
    fun effect(message: Message<MemberPointEffectMessage>) {
        message.exec {
            val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT, it.pointAccountTypeId, it.memberId))
            try {
                lock.lock()
                pointTimedTaskService.effect(it.pointAccountTypeId, it.memberPointId, it.memberId)
            } finally {
                lock.unlock()
                messageCounter.decrement(it.pointAccountTypeId, MessageCounter.K.EFFECT_POINT_MESSAGE_COUNT)
            }
        }
    }


    @StreamListener(value = KafkaSink.PARKING_LOT_INPUT)
    fun parkingLot(message: Message<BizMessage>) {
        message.exec {
            // ------
        }
    }


    private fun <T> Message<T>.exec(onErrorThrows: Boolean = false, block: (payload: T) -> Unit) {
        val acknowledgment = headers.get(KafkaHeaders.ACKNOWLEDGMENT, Acknowledgment::class.java)
        val topic = headers[KafkaHeaders.RECEIVED_TOPIC]
        val partition = headers[KafkaHeaders.RECEIVED_PARTITION_ID]
        val offset = headers[KafkaHeaders.OFFSET]
        val deliveryAttempt = headers["deliveryAttempt"]?.toString()?.toInt() ?: 1
        if (deliveryAttempt > MAX_DELIVERY_ATTEMPT) {
            return
        }
        try {
            headers[LOYALTY_TRACE_ID]?.let {
                if (it is ByteArray) {
                    MDCUtils.getAndSetTraceId(it.decodeToString())
                } else {
                    MDCUtils.getAndSetTraceId(it.toString())
                }
            }
            headers[TENANT_ID]?.let {
                if (it is ByteArray) {
                    VisitTenantInfoHolder.setTenantId(it.toString(StandardCharsets.UTF_8))
                } else {
                    VisitTenantInfoHolder.setTenantId(it.toString())
                }
            }
            val p = Json.toJson(payload)
            log.info("开始处理事件 topic: {} partition: {} offset: {} deliveryAttempt: {} event: {}", topic, partition, offset, deliveryAttempt, p)
            val start = System.currentTimeMillis()
            block(payload)
            log.info("处理事件完成 topic: {} partition: {} offset: {} deliveryAttempt: {}  耗时: {}ms ", topic, partition, offset, deliveryAttempt, (System.currentTimeMillis() - start))
            acknowledgment?.acknowledge()
        } catch (e: Exception) {
            if (e !is IOException && e !is SdkException) {
                if (onErrorThrows) {
                    if (deliveryAttempt >= MAX_DELIVERY_ATTEMPT) {
                        log.error("处理事件失败 topic: {} partition: {} offset: {} event: {}", topic, partition, offset, Json.toJson(payload), e)
                    }
                    throw e
                } else {
                    log.error("处理事件失败 topic: {} partition: {} offset: {} event: {}", topic, partition, offset, Json.toJson(payload), e)
                    acknowledgment?.acknowledge()
                }
            } else {
                log.error("处理事件异常 topic: {} partition: {} offset: {} event: {}", topic, partition, offset, Json.toJson(payload), e)
            }
        } finally {
            ThreadLocalCleanCallbacks.callbacks()
            MDCUtils.clear()
        }
    }
}
