package com.shuyun.loyalty.stream.handler.point.process

import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.service.datamodel.MemberPoint
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.loyalty.service.transfer.points.MemberPointUnfreezeRecalibrateTransfer
import com.shuyun.loyalty.stream.handler.point.MemberPointDeductCalculate
import com.shuyun.loyalty.stream.handler.point.ReturnOrderContext
import com.shuyun.loyalty.stream.message.PointCalcMessage
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class MemberPointFreezeRecalculateProcess : MemberPointDeductCalculate() {

    private val log = LogManager.getLogger(MemberPointFreezeRecalculateProcess::class.java)

    @Autowired
    private lateinit var memberPointUnfreezeRecalibrateTransfer: MemberPointUnfreezeRecalibrateTransfer

    override fun type() = EventOperationEnum.FREEZE_RECALCULATE


    override fun process(memberPoint: MemberPoint, message: PointCalcMessage, group: PointRuleGroup) {
        log.info("积分解冻重算 规则组：{} 积分账户类型({} {})", group.groupName, message.pointAccountType.id, message.pointAccountType.name)
        val sets = HashSet<String>()
        //一个规则组下满足条件规则
        val passedRule = filterRule(message.subject.dataType!!, message.event, group.deductRuleList, message.eventType) ?: return

        val lp = message.toLoyaltyPoints(memberPoint, group, passedRule)
         //解冻黑名单校验
        if (memberPointUnfreezeRecalibrateTransfer.checkSpecial(lp, ForbiddenOperation.POINT_UNFREEZE, ForbiddenPort.CALC_EVENT)) {
            log.warn("会员在积分解冻特殊名单组里")
            return
        }
        // 解冻重算的关联时机是冻结， 冻结的关联的时机是发放
        for (freezeE in message.eventType.relatedEventTypes!!) {
            for (sendE in freezeE.relatedEventTypes!!) {
                if (!sets.add("${sendE.id}-${passedRule.quotaType}")) {
                    continue
                }

                if (!message.checkReturnOrder(message.eventType.id!!, freezeE.id!!)) {
                    continue
                }

                val matchingTimeMillis = findSendMatchingTimeMillis(message.pointAccountType.id!!, lp.member.id, lp.member.memberId, sendE, lp.attr.traceId) ?: continue

                // 设置时机
                lp.eventType = initPointEventType(message.eventType, sendE, matchingTimeMillis, message)

                // FQN 替换成关联的发放的时机的FQN
                val rawEvent = message.event.clone()
                rawEvent.setFqn(sendE.eventStream!!)
                lp.rawEvent = rawEvent.apply { setOccurrenceTs(matchingTimeMillis) }


                val gss = gainService.findByTraceIdList(lp.hierarchy.id, sendE.id!!, lp.attr.traceId, lp.member.id)

                if (gss.isEmpty()) {
                    log.trace("原单记录不存在无法解冻重算 traceId: {}", lp.attr.traceId)
                    continue
                }

                // 原单关联的冻结的积分
                val fpGroup = frozenPointService
                    .findFrozenPointByGainStatementIds(lp.hierarchy.id, gss.map { it.id!! })
                    .associateBy { it.gainStatementId }

                if (fpGroup.isEmpty()) {
                    val ttl = message.eventType.ttl(message.event.getOccurrenceTs())
                    if (ttl > 0) {
                        ReturnOrderContext.add(message.traceId!!, message.eventType.id!!, sendE.id!!, ttl, message.rawEvent)
                        continue
                    }
                    log.debug("事件冻结记录不存在无法解冻重算 traceId: {}", lp.attr.traceId)
                    continue
                }

                if (fpGroup.keys.size != gss.size) {
                    log.error("事件冻结记录数量与原单数量不匹配 traceId: {}", lp.attr.traceId)
                    continue
                }

                memberPointUnfreezeRecalibrateTransfer.recalibrate(lp)
            }
        }
    }

}