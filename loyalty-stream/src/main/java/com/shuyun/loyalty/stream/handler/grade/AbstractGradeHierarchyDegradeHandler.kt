package com.shuyun.loyalty.stream.handler.grade

import com.pip.shuyun.pool.transaction.DmTransaction
import com.shuyun.loyalty.entity.enums.ProcessRecordTypeEnum
import com.shuyun.loyalty.service.message.MessageHandlerType
import com.shuyun.loyalty.service.message.grade.GradeHierarchyDegradeEventMessage
import com.shuyun.loyalty.service.message.grade.GradeHierarchyMergeMessage
import com.shuyun.loyalty.service.model.GradeHierarchy
import com.shuyun.loyalty.service.service.*
import com.shuyun.loyalty.service.util.DateUtils
import com.shuyun.loyalty.service.util.ProcessRecordUtil
import com.shuyun.loyalty.stream.handler.AbstractEventHandler
import com.shuyun.loyalty.stream.handler.point.MemberGradeCalculate
import com.shuyun.loyalty.stream.sequence.MessageCounter
import com.shuyun.loyalty.stream.sequence.MessageCounter.K.EXPIRE_GRADE_MESSAGE_COUNT
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import java.time.ZonedDateTime

abstract class AbstractGradeHierarchyDegradeHandler<T: GradeHierarchyDegradeEventMessage> : AbstractEventHandler<T>() {
    protected val logger = LogManager.getLogger(GradeHierarchyOverDegradeHandler::class.java)

    @Autowired
    protected open lateinit var gradeDefinitionBaseService: GradeDefinitionBaseService

    @Autowired
    protected open lateinit var gradeHierarchyBaseService: GradeHierarchyBaseService

    @Autowired
    protected open lateinit var gradeRuleGroupBaseService: GradeRuleGroupBaseService

    @Autowired
    protected open lateinit var eventTypeBaseService: EventTypeBaseService

    @Autowired
    protected open lateinit var memberGradeService: MemberGradeCalculate

    @Autowired
    protected open lateinit var locker: Locker

    @Autowired
    protected open lateinit var messageCounter: MessageCounter

    override fun handle(message: T) {
        val hierarchyId = message.gradeHierarchyId
        val memberId = message.memberId
        val gradeDefinitionId = message.gradeDefinitionId
        if (message.event().getFqn() == MessageHandlerType.GRADE_OVER_HANDLER.fqn) {
            messageCounter.decrement(hierarchyId, EXPIRE_GRADE_MESSAGE_COUNT)
        }
        logger.debug("触发内置等级计算开始，等级体系：{}，会员ID：{}，原等级：{}", hierarchyId, memberId, gradeDefinitionId)
        val lock = locker.getLock("grade_calculate_${hierarchyId}-$memberId")
        lock.lock()
        try {
            if (message.event().getFqn() == MessageHandlerType.GRADE_MERGE_HANDLER.fqn) {
                if (message.gradeHierarchy.id == null) {
                    val plan = LoyaltyPrograms.findPlanByGradeHierarchyId(hierarchyId)
                        ?: throw IllegalArgumentException("未找到等级体系: $hierarchyId")
                    val subject = plan.subjectList!!.first()
                    val gradeHierarchy = subject.gradeHierarchyList!!.first()
                    message.gradeHierarchy = gradeHierarchy
                }
                ApplicationContextHolder.getBean(GradeHierarchyMergeDegradeHandler::class.java).saveGrade(hierarchyId, gradeDefinitionId, message as GradeHierarchyMergeMessage)
            }else {
                ApplicationContextHolder.getBean(GradeHierarchyOverDegradeHandler::class.java).saveGrade(hierarchyId, gradeDefinitionId, message)
            }
        } finally {
            lock.unlock()
        }
        logger.debug("触发内置等级计算结束，等级体系：{}，会员ID：{}，原等级：{}", hierarchyId, memberId, gradeDefinitionId)
    }

    @DmTransaction
    open fun saveGrade(hierarchyId: Long, gradeDefinitionId: Long, message: T) {
        val hierarchy = gradeHierarchyBaseService.getEffectiveOneCache(hierarchyId, DateUtils.formatMinutesCacheKey(ZonedDateTime.now()))
        if (!hierarchy.checkHierarchyPlanPublishedOne()) {
            logger.debug("等级体系当前状态不可用")
            return
        }

        if (message.event().getFqn() != MessageHandlerType.GRADE_MERGE_HANDLER.fqn) {
            val streamProcessed = ProcessRecordUtil.isProcessRecord(message.gradeHierarchyId.toString(), message.event().getKey(), ProcessRecordTypeEnum.GRADE)
            if (streamProcessed) {
                logger.info("等级体系下已处理过该事件，事件ID：${message.event().getKey()},等级体系id:${message.gradeHierarchyId}")
                return
            }
        }

        hierarchy.gradeDefinitions = gradeDefinitionBaseService.findByEnabledHierarchyByVersionId(hierarchy.versionId!!)
        doSaveGrade(hierarchy, message)
    }

    abstract fun doSaveGrade(hierarchy: GradeHierarchy, message: T)
}