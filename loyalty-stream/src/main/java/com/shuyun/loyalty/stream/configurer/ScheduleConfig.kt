package com.shuyun.loyalty.stream.configurer

import com.google.common.util.concurrent.ThreadFactoryBuilder
import com.shuyun.lite.context.GlobalContext
import com.shuyun.lite.util.Common
import net.javacrumbs.shedlock.core.LockProvider
import net.javacrumbs.shedlock.provider.redis.spring.RedisLockProvider
import net.javacrumbs.shedlock.spring.annotation.EnableSchedulerLock
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.redis.connection.RedisConnectionFactory
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.scheduling.annotation.SchedulingConfigurer
import org.springframework.scheduling.config.ScheduledTaskRegistrar
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService


@Configuration
@EnableScheduling
@EnableSchedulerLock(defaultLockAtMostFor = "PT1H")
class ScheduleConfig: SchedulingConfigurer {

    companion object {
        private val ENV = GlobalContext.serviceName() + "_" + GlobalContext.defTenantId()
    }

    override fun configureTasks(taskRegistrar: ScheduledTaskRegistrar) {
        taskRegistrar.setScheduler(scheduleExecutorService())
    }

    @Bean(destroyMethod = "shutdown")
    fun scheduleExecutorService(): ScheduledExecutorService {
        val threadFactoryBuilder = ThreadFactoryBuilder().setDaemon(true).setNameFormat("stream-schedule:%d").build()
        return Executors.newScheduledThreadPool(Common.getInteger("schedule.pool.size", 8), threadFactoryBuilder)
    }

    @Bean
    fun lockProvider(connectionFactory: RedisConnectionFactory): LockProvider {
        return RedisLockProvider(connectionFactory, ENV)
    }
}