package com.shuyun.loyalty.stream.handler.point.process

import com.shuyun.loyalty.service.datamodel.MemberPoint
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.loyalty.service.transfer.points.MemberPointRecalibrateTransfer
import com.shuyun.loyalty.stream.handler.point.MemberPointDeductCalculate
import com.shuyun.loyalty.stream.handler.point.ReturnOrderContext
import com.shuyun.loyalty.stream.message.PointCalcMessage
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class MemberPointRecalculateProcess : MemberPointDeductCalculate() {

    private val log = LogManager.getLogger(MemberPointRecalculateProcess::class.java)

    @Autowired
    private lateinit var memberPointRecalibrateTransfer: MemberPointRecalibrateTransfer


    override fun type() = EventOperationEnum.RECALCULATE


    override fun process(memberPoint: MemberPoint, message: PointCalcMessage, group: PointRuleGroup) {
        log.info("积分重算 规则组：{} 积分账户类型({} {})", group.groupName, message.pointAccountType.id, message.pointAccountType.name)
        val sets = HashSet<String>()
        //一个规则组下满足条件规则
        val passedRule = filterRule(message.subject.dataType!!, message.event, group.deductRuleList, message.eventType) ?: return

        val lp = message.toLoyaltyPoints(memberPoint, group, passedRule)

        // 重算的关联时机是发放
        for (sendE in message.eventType.relatedEventTypes!!) {
            if (!sets.add("${sendE.id}-${passedRule.quotaType}")) {
                continue
            }
            if (!message.checkReturnOrder(message.eventType.id!!, sendE.id!!)) {
                continue
            }
            val matchingTimeMillis = findSendMatchingTimeMillis(message.pointAccountType.id!!, lp.member.id, lp.member.memberId, sendE, lp.attr.traceId)
            if (matchingTimeMillis == null) {
                // 检验是否配置了等待原单
                val ttl = message.eventType.ttl(message.event.getOccurrenceTs())
                if (ttl > 0) {
                    ReturnOrderContext.add(message.traceId!!, message.eventType.id!!, sendE.id!!, ttl, message.rawEvent)
                    continue
                }
                log.warn("原单不存在,重算结束 原单ID: {}", lp.attr.traceId)
                continue
            }

            // 设置时机
            lp.eventType = initPointEventType(message.eventType, sendE, matchingTimeMillis, message)

            // FQN 替换成关联的发放的时机的FQN
            val rawEvent = message.event.clone()
            rawEvent.setFqn(sendE.eventStream!!)
            lp.rawEvent = rawEvent.apply { setOccurrenceTs(matchingTimeMillis) }
            memberPointRecalibrateTransfer.recalibrate(lp)
        }
    }

}