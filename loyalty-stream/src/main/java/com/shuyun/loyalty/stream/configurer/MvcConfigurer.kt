package com.shuyun.loyalty.stream.configurer

import com.shuyun.loyalty.service.interceptor.LogMdcInterceptor
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.servlet.config.annotation.InterceptorRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer

@Configuration
class MvcConfigurer : WebMvcConfigurer {
    @Bean
    fun logMdcInterceptor(): LogMdcInterceptor {
        return LogMdcInterceptor()
    }


    override fun addInterceptors(registry: InterceptorRegistry) {
        registry.addInterceptor(logMdcInterceptor())
    }

}
