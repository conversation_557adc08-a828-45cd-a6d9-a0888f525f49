package com.shuyun.loyalty.stream.service

import com.shuyun.loyalty.entity.dto.MemberPointMessage
import com.shuyun.loyalty.entity.dto.MemberRequest.Companion.MEMBER_OPERATOR_LOCK_KEY_FORMAT
import com.shuyun.loyalty.service.datamodel.MemberPointCalculateTask
import com.shuyun.loyalty.service.datamodel.findNewList
import com.shuyun.loyalty.service.datamodel.updateComment
import com.shuyun.loyalty.service.service.MemberPointService
import com.shuyun.loyalty.service.service.PointMigrationService
import com.shuyun.loyalty.service.transfer.points.MemberPointMigrateTransfer
import com.shuyun.loyalty.service.util.ConstantValue.MIGRATION_MAGIC_MEMBER_ID_PREFIX
import com.shuyun.pip.component.concurrent.lock.Locker
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 */
@Component
class PointItemTaskService {

    private val logger = LogManager.getLogger(PointItemTaskService::class.java)

    @Autowired
    private lateinit var locker: Locker

    @Autowired
    private lateinit var pointTransferService: PointTransferService

    @Autowired
    private lateinit var memberPointService: MemberPointService

    @Autowired
    private lateinit var memberPointMigrateTransfer: MemberPointMigrateTransfer

    @Autowired
    private lateinit var pointMigrationService: PointMigrationService

    fun process(message: MemberPointMessage) {
        logger.debug("开始检查积分任务 积分类型ID: {} 会员ID: {} 会员积分账户ID: {}", message.pointAccountTypeId, message.memberId, message.memberPointId)
        if (message.memberPointId.startsWith(MIGRATION_MAGIC_MEMBER_ID_PREFIX)) {
            // 积分迁移导入处理
            migratePoint(message)
            return
        }
        memberPointService.getByMemberPointId(message.pointAccountTypeId, message.memberPointId, refreshSegmentPoints = false) ?: return
        var c = 0
        while (c < 20) {
            val limit = 200
            val tasks = MemberPointCalculateTask.findNewList(message.pointAccountTypeId, message.memberPointId, limit = limit)
            if (tasks.isEmpty()) {
                break
            }
            for (task in tasks) {
                try {
                    logger.info("开始处理积分明细计算 待计算任务 积分类型ID: {} 会员ID: {} 类型: {} JID: {}", message.pointAccountTypeId, message.memberId, task.action, task.id)
                    process(task)
                } catch (e: Exception) {
                    if (e is ConcurrentModificationException) {
                        break
                    }
                    task.updateComment()
                    logger.error("处理积分明细计算出错 id: {} data: {}", task.id, task.data, e)
                    return
                }
            }
            c++
        }
        logger.info("完成处理积分明细计算 积分类型ID: {} 会员ID: {} 会员积分账户ID: {}", message.pointAccountTypeId, message.memberId, message.memberPointId)
    }


    private fun process(task: MemberPointCalculateTask) {
        if (!task.isAsyncAction()) {
            pointTransferService.process(task)
        } else {
            val lock = locker.getLock(String.format(MEMBER_OPERATOR_LOCK_KEY_FORMAT, task.hierarchyId, task.memberId))
            try {
                lock.lock()
                pointTransferService.process(task)
            } finally {
                lock.unlock()
            }
        }
    }


    private fun migratePoint(es: MemberPointMessage) {
        val lockKey = String.format(MEMBER_OPERATOR_LOCK_KEY_FORMAT, es.pointAccountTypeId, es.memberId)
        val lock = locker.getLock(lockKey)
        if (!lock.tryLock()) {
            logger.info("开始处理接口积分明细计算(没有获取到锁) payload: {} 等待重试", es)
            return
        }
        val migMemberId = es.memberPointId.substring(MIGRATION_MAGIC_MEMBER_ID_PREFIX.length)
        try {
            logger.info("会员积分数据迁移开始  积分类型ID: {} 会员ID: {}", es.pointAccountTypeId, es.memberId)
            memberPointMigrateTransfer.transfer(es.pointAccountTypeId, es.memberId, migMemberId)
            logger.info("会员积分数据迁移完成  积分类型ID: {} 会员ID: {}", es.pointAccountTypeId, es.memberId)
        } catch (e: Exception) {
            // 错误消息最长200个字符
            logger.error("会员积分迁移失败 {}", es, e)
            val error = e.message?.take(150) ?: "会员积分迁移失败"
            pointMigrationService.updateMigMemberStatusByIds(listOf(migMemberId), "FAILURE", error)
            return
        } finally {
            lock.unlock()
        }
    }
}