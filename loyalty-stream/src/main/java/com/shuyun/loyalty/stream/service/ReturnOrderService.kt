package com.shuyun.loyalty.stream.service

import com.shuyun.loyalty.service.datamodel.ReturnOrder
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.loyalty.service.repository.ReturnOrderRepository
import com.shuyun.loyalty.service.transfer.points.LoyaltyRequestType
import com.shuyun.loyalty.service.transfer.points.PointEventType
import com.shuyun.loyalty.stream.processor.EventPreprocessor
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.time.ZonedDateTime

@Component
class ReturnOrderService {

    private val log = LogManager.getLogger(ReturnOrderService::class.java)

    @Autowired
    private lateinit var returnOrderRepository: ReturnOrderRepository

    @Autowired
    private lateinit var eventPreprocessor: EventPreprocessor

    fun save(pointAccountTypeId: Long, memberId: String, traceId: String, eventType: EventType, returnOrders: List<ReturnOrder>) {
        if (returnOrders.isNotEmpty()) {
            log.info("暂存原单不存在的订单: pointAccountTypeId: {} memberId={} traceId={} eventTypeId={} eventName={} eventStream={}", pointAccountTypeId, memberId, traceId, eventType.id, eventType.name, eventType.eventStream)
            returnOrderRepository.saveList(returnOrders)
        }
    }

    fun returnOrder(requestType: LoyaltyRequestType, eventType: PointEventType?, memberId: String, traceId: String) {
        if (requestType != LoyaltyRequestType.EVENT) return
        when (eventType?.eventOperation) {
            EventOperationEnum.SEND, EventOperationEnum.FREEZE, EventOperationEnum.UNFREEZE -> {
                find(traceId, eventType.id).forEach {
                    val event = JsonUtils.objectMapper().readValue(it.event!!, Event::class.java)
                    val r = ReturnOrderEventType(traceId, memberId, it.currentEventTypeId!!.toLong(), it.eventTypeId!!.toLong())
                    log.info("存在等待计算的订单 {}", r)
                    event.put(Event.RETURN_ORDER_EVENT_TYPE, JsonUtils.toJson(r))
                    eventPreprocessor.process(event, it.created!!)
                    returnOrderRepository.delete(it.id!!)
                }
            }
            else -> {}
        }
    }

    private fun find(traceId: String, eventTypeId: Long): List<ReturnOrder> {
        val params = mutableMapOf(
            "traceId" to traceId,
            "ttl" to mapOf("\$ge" to ZonedDateTime.now().toInstant().toEpochMilli()),
            "eventTypeId" to eventTypeId
        )
        val orders = returnOrderRepository.findListByFilter(JsonUtils.toJson(params))
        return orders.sortedBy { it.occurrenceTs!! }
    }
}

data class ReturnOrderEventType(
    val traceId: String,
    val memberId: String,
    val currentEventTypeId: Long,
    val relatedEventTypeId: Long,
)