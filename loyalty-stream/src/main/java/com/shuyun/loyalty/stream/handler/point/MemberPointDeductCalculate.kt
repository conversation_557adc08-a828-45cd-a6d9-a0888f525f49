package com.shuyun.loyalty.stream.handler.point

import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.shuyun.loyalty.service.datamodel.MemberPoint
import com.shuyun.loyalty.service.datamodel.MemberPointEvent
import com.shuyun.loyalty.service.datamodel.find
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.fx.ExtendFxVariableProvider
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.loyalty.service.model.PointDeductRule
import com.shuyun.loyalty.service.model.PointDeductRuleOperate
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.loyalty.service.service.MemberFrozenPointService
import com.shuyun.loyalty.service.service.MemberPointGainStatementService
import com.shuyun.loyalty.service.transfer.points.PointEventType
import com.shuyun.loyalty.service.util.ExpressionIdentUtil
import com.shuyun.loyalty.stream.message.PointCalcMessage
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.exception.AbstractPipException
import com.shuyun.pip.i18n.LocaleI18nContextHolder
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired

abstract class MemberPointDeductCalculate : PointDeductCalculate {

    private val logger = LogManager.getLogger(MemberPointDeductCalculate::class.java)

    @Autowired
    protected lateinit var gainService: MemberPointGainStatementService

    @Autowired
    protected lateinit var frozenPointService: MemberFrozenPointService


    abstract fun process(memberPoint: MemberPoint, message: PointCalcMessage, group: PointRuleGroup)


    override fun calc(memberPoint: MemberPoint, message: PointCalcMessage, group: PointRuleGroup) {
        ApplicationContextHolder.getBean(this::class.java).process(memberPoint, message, group)
    }

    protected fun filterRule(subjectDataType: String, event: Event, deductRuleList: List<PointDeductRule>?, currentEventType: EventType): PointDeductRule? {
        var quotaType: PointDeductRuleOperate.QuotaTypeEnum? = null
        val passedRules = deductRuleList?.filter { dr ->
            dr.pointDeductRuleOperateList?.any {
                // 解冻关联的是冻结
                // 冻结关联的是发放
                val sets = currentEventType.relatedEventTypes?.map { x -> x.id!! }?.toSet() ?: emptySet()
                val b = it.eventTypeId in sets
                if (b) {
                    quotaType = it.quotaType!!
                }
                b
            } ?: false
        }?.filter {
            try {
                ExpressionIdentUtil.eppById(
                    it.conditionExpressionFxId!!,
                    event,
                    JsonNodeFactory.instance.objectNode().put("subject", subjectDataType)
                ).toString().toBoolean()
            } catch (e: Exception) {
                var msg = e.message
                if (e is AbstractPipException) {
                    msg = LocaleI18nContextHolder.getMessage(e.trackErrorCode, e.args)
                }
                val fxVariableProvider = ApplicationContextHolder.getBean(ExtendFxVariableProvider::class.java)
                val fx = fxVariableProvider.findById(it.conditionExpressionFxId!!.toLong())
                logger.error("表达式计算过程中出错：规则组ID：{}, 规则名称：{}, 错误描述：[{}] 表达式：{}", it.ruleGroupId, it.ruleName, msg, JsonUtils.toJson(fx), e)
                false
            }
        }
        if (passedRules.isNullOrEmpty()) {
            return null
        }
        val r = passedRules.sortedBy { it.id }.first()
        r.quotaType = quotaType
        return r
    }


    // 查找发放事件的发生时间
    protected fun findSendMatchingTimeMillis(hierarchyId: Long, memberPointId: String, memberId: String, sendE: EventType, traceId: String): Long? {
        val sendEvent = MemberPointEvent.find(hierarchyId, sendE.id!!, traceId, sendE.operation!!)
        var occurrenceTs = sendEvent?.occurrenceTs
        if (occurrenceTs == null) {
            // 再次检查原单记录，兼容历史数据
            val gss = gainService.findByTraceIdList(
                hierarchyId,
                sendE.id!!,
                traceId,
                memberPointId
            )
            if (gss.isNotEmpty()) {
                occurrenceTs = gss.firstOrNull()?.occurrenceTs?.toLong()
            } else {
                logger.info("原始积分获取计算记录不存在，pointAccountTypeId：{}, sendEventTypeId：{}, sendEventTypeName：{}, memberPointId：{} memberId：{} traceId：{}",
                    hierarchyId, sendE.id, sendE.name,memberPointId, memberId, traceId)
            }
        }
        return occurrenceTs
    }



    protected fun initPointEventType(
        currentEventType: EventType,
        sendE: EventType,
        sendEOccurrenceTs: Long,
        message: PointCalcMessage
    ): PointEventType {
        return PointEventType(
            currentEventType.id!!,
            currentEventType.name!!,
            message.event.getOccurrenceTs(),
            message.eventType.operation!!,
            PointEventType(
                sendE.id!!,
                sendE.name!!,
                sendEOccurrenceTs,
                sendE.operation!!,
                sendCycleTimePath = sendE.pointSendCycleTimePath
            )
        )
    }
}