package com.shuyun.loyalty.stream.message

import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.loyalty.service.model.MedalHierarchy
import com.shuyun.loyalty.service.model.Plan
import com.shuyun.loyalty.service.model.Subject
import com.shuyun.loyalty.stream.handler.medal.MedalHandler
import com.shuyun.pip.ApplicationContextHolder
import org.apache.logging.log4j.LogManager

class MedalCalcMessage(
    plan: Plan,
    subject: Subject,
    eventType: EventType,
    event: Event,
    val medalHierarchy: MedalHierarchy
) : BaseMessage(plan, subject, eventType, event, medalHierarchy.executeOrder!!) {

    companion object {
        private val logger = LogManager.getLogger(MedalCalcMessage::class.java)
    }

    private val matchingTimePath by lazy {
        eventType.matchingTimePathForMedalRule ?: Event.OCCURRENCE_TS
    }

    val matchingTime by lazy {
        val matchingTimeValue = findEventValueByPath(matchingTimePath)
        if (matchingTimeValue.isNullOrEmpty()) {
            throw IllegalArgumentException("事件消息中缺少属性值(${matchingTimePath})")
        }

        val date = convertMatchTimeToDate(matchingTimePath, matchingTimeValue)
            ?: throw IllegalArgumentException("事件消息中属性值格式不正确(path: $matchingTimePath value: ${matchingTimeValue})")
        date
    }


    override fun calc() {
        val s = System.currentTimeMillis()
        logger.info("事件计算-勋章计算 计划:{}, 主体:{}, 时机:({} {}) 勋章体系:({} {}) event:{}", plan.name, subject.name, eventType.name, eventType.eventStream, medalHierarchy.id, medalHierarchy.name, event)
        ApplicationContextHolder.getBean(MedalHandler::class.java).handle(this)
        logger.info("事件计算-勋章计算耗时:{}ms 计划:{}, 主体:{}, 时机:({} {}) 勋章体系:({} {}) event:{}", (System.currentTimeMillis() - s), plan.name, subject.name, eventType.name, eventType.eventStream, medalHierarchy.id, medalHierarchy.name, event)
    }
}