package com.shuyun.loyalty.stream.message

import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.loyalty.service.model.GradeHierarchy
import com.shuyun.loyalty.service.model.Plan
import com.shuyun.loyalty.service.model.Subject
import com.shuyun.loyalty.stream.handler.grade.GradeHandler
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import org.apache.logging.log4j.LogManager

class GradeCalcMessage(
    plan: Plan,
    subject: Subject,
    eventType: EventType,
    event: Event,
    val gradeHierarchy: GradeHierarchy,
) : BaseMessage(plan, subject, eventType, event, gradeHierarchy.executeOrder!!) {

    companion object {
        private val logger = LogManager.getLogger(GradeCalcMessage::class.java)
        private const val MEMBER_OPERATOR_LOCK_KEY_FORMAT = "grade_calculate_key_%s"
    }

    private val matchingTimePath by lazy {
        eventType.matchingTimePathForGradeRule ?: Event.OCCURRENCE_TS
    }

    val matchingTime by lazy {
        val matchingTimeValue = findEventValueByPath(matchingTimePath)
        if (matchingTimeValue.isNullOrEmpty()) {
            throw IllegalArgumentException("事件消息中缺少属性值(${matchingTimePath})")
        }

        val date = convertMatchTimeToDate(matchingTimePath, matchingTimeValue)
            ?: throw IllegalArgumentException("事件消息中属性值格式不正确(path: $matchingTimePath value: ${matchingTimeValue})")
        date
    }




    override fun calc() {
        val s = System.currentTimeMillis()
        logger.info("事件计算-等级计算 计划:{}, 主体:{}, 时机:({} {}) 等级体系:({} {}) event:{}", plan.name, subject.name, eventType.name, eventType.eventStream, gradeHierarchy.id, gradeHierarchy.name, event)
        val locker = ApplicationContextHolder.getBean(Locker::class.java)
        val lock = locker.getLock(String.format(MEMBER_OPERATOR_LOCK_KEY_FORMAT, event.getKey()))
        try {
            lock.lock()
            ApplicationContextHolder.getBean(GradeHandler::class.java).handle(this)
        } finally {
            lock.unlock()
            logger.info("事件计算-等级计算耗时:{}ms  计划:{}, 主体:{}, 时机:({} {}) 等级体系:({} {}) event:{}", (System.currentTimeMillis() - s), plan.name, subject.name, eventType.name, eventType.eventStream, gradeHierarchy.id, gradeHierarchy.name, event)
        }
    }
}