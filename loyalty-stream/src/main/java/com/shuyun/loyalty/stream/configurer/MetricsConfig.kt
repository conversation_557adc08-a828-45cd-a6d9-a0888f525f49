package com.shuyun.loyalty.stream.configurer

import io.prometheus.client.exporter.MetricsServlet
import org.springframework.boot.web.servlet.ServletRegistrationBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration


@Configuration
class MetricsConfig {

    @Bean
    fun servletRegistrationBean(): ServletRegistrationBean<*> {
        //定义监控接口暴露路径
        return ServletRegistrationBean(MetricsServlet(), "/monitor/metrics")
    }

}