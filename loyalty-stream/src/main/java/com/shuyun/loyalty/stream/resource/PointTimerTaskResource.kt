package com.shuyun.loyalty.stream.resource

import com.shuyun.loyalty.entity.dto.MemberRequest
import com.shuyun.loyalty.service.service.MemberPointService
import com.shuyun.loyalty.stream.sequence.MessageTaskScheduler
import com.shuyun.loyalty.stream.service.PointTimedTaskService
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import java.util.concurrent.TimeUnit
import kotlin.concurrent.thread

@RestController
@RequestMapping("/point-timer")
class PointTimerTaskResource {

    private val log = LogManager.getLogger(PointTimerTaskResource::class.java)

    @Autowired
    private lateinit var memberPointService: MemberPointService

    @Autowired
    private lateinit var pointTimedTaskService: PointTimedTaskService

    @Autowired
    private lateinit var locker: Locker

    @PostMapping("/retry")
    fun retry(accountTypeId: Long, memberPointId: String, memberId: String) {
        val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT, accountTypeId, memberId))
        val b = lock.tryLock(100, TimeUnit.MILLISECONDS)
        if (!b) {
            log.warn("获取锁失败, accountTypeId: {}, memberId: {}", accountTypeId, memberId)
            return
        }
        try {
            pointTimedTaskService.retry(accountTypeId, memberPointId, memberId)
        } finally {
            lock.unlock()
        }
    }

    @PostMapping("/effect")
    fun effect(accountTypeId: Long, memberPointId: String, memberId: String) {
        val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT, accountTypeId, memberId))
        val b = lock.tryLock(100, TimeUnit.MILLISECONDS)
        if (!b) {
            log.warn("获取锁失败, accountTypeId: {}, memberId: {}", accountTypeId, memberId)
            return
        }
        try {
            lock.lock()
            pointTimedTaskService.effect(accountTypeId, memberPointId, memberId)
        } finally {
            lock.unlock()
        }
    }

    @PostMapping("/expire")
    fun expire(accountTypeId: Long, memberPointId: String, memberId: String) {
        val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT, accountTypeId, memberId))
        val b = lock.tryLock(100, TimeUnit.MILLISECONDS)
        if (!b) {
            log.warn("获取锁失败, accountTypeId: {}, memberId: {}", accountTypeId, memberId)
            return
        }
        try {
            pointTimedTaskService.expire(accountTypeId, memberPointId, memberId)
        } finally {
            lock.unlock()
        }
    }

    @PostMapping("/mgrtSgmt")
    fun migrateSegment(accountTypeId: Long, memberPointId: String) {
        val memberPoint = memberPointService.getByMemberPointId(accountTypeId, memberPointId, refreshSegmentPoints = false) ?: return
        val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT, accountTypeId, memberPoint.memberId))
        try {
            lock.lock()
            pointTimedTaskService.migrateSegment(accountTypeId, memberPoint.memberId)
        } finally {
            lock.unlock()
        }
    }

    @GetMapping("/triggerScheduler")
    fun triggerScheduler(
        @RequestParam("taskName") taskName: String,
        @RequestParam("accountTypeId") accountTypeId: Long? = null,
        @RequestParam("memberId") memberId: String? = null) {
        thread {
            log.info("触发积分检测任务开始 {} {} {}", taskName, (accountTypeId ?: ""), (memberId ?: ""))
            if (accountTypeId != null && memberId != null) {
                val memberPoint = memberPointService.getByMemberId(accountTypeId, memberId, refreshSegmentPoints = false) ?: return@thread
                val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT, accountTypeId, memberId))
                try {
                    lock.lock()
                    when (taskName) {
                        "expire" -> pointTimedTaskService.expire(accountTypeId, memberPoint.id!!, memberId)
                        "effect" -> pointTimedTaskService.effect(accountTypeId, memberPoint.id!!, memberId)
                        "retry" -> pointTimedTaskService.retry(accountTypeId, memberPoint.id!!, memberId)
                        "mgrtSgmt" -> pointTimedTaskService.migrateSegment(accountTypeId, memberId)
                        else -> {}
                    }
                } finally {
                    lock.unlock()
                }
            } else {
                val scheduler = ApplicationContextHolder.getBean(MessageTaskScheduler::class.java)
                when (taskName) {
                    "expire" -> scheduler.checkExpiredPoints()
                    "effect" -> scheduler.checkDelayedPoints()
                    "retry" -> scheduler.checkRetry()
                    else -> {}
                }
            }
            log.info("触发积分检测任务结束  {} {} {}", taskName, (accountTypeId ?: ""), (memberId ?: ""))
        }
    }

}