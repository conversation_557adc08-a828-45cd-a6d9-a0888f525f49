package com.shuyun.loyalty.stream.kafka.sink

import org.springframework.cloud.stream.annotation.Input
import org.springframework.cloud.stream.annotation.Output
import org.springframework.messaging.MessageChannel
import org.springframework.messaging.SubscribableChannel

interface KafkaSink {

    @Input(NORMAL_INPUT)
    fun normalInput(): SubscribableChannel

    @Input(GREEN_INPUT)
    fun greenInput(): SubscribableChannel

    @Input(LOYALTY_EVENT_CHANNEL_INPUT)
    fun eventChannelInput(): SubscribableChannel

    @Input(PARKING_LOT_INPUT)
    fun parkingLotInput(): SubscribableChannel

    @Input(POINT_MODIFY_INPUT)
    fun pointModifyInput(): SubscribableChannel

    @Input(GRADE_MODIFY_INPUT)
    fun gradeModifyInput(): SubscribableChannel

    @Input(MEDAL_MODIFY_INPUT)
    fun medalModifyInput(): SubscribableChannel

    @Input(LOYALTY_HTTP_REQUEST_ASYNC_PROCESS_INPUT)
    fun openPointInput(): SubscribableChannel

    @Input(LOYALTY_LP_EXPIRE_INPUT)
    fun lpExpireInput(): SubscribableChannel

    @Input(LOYALTY_LP_EFFECT_INPUT)
    fun lpEffectInput(): SubscribableChannel


    companion object {
        const val NORMAL_INPUT = "NORMAL_INPUT"
        const val GREEN_INPUT = "GREEN_CHANNEL_INPUT"
        const val LOYALTY_EVENT_CHANNEL_INPUT = "LOYALTY_EVENT_CHANNEL_INPUT"
        const val PARKING_LOT_INPUT = "PARKING_LOT_INPUT"
        const val POINT_MODIFY_INPUT = "POINT_MODIFY_INPUT"
        const val GRADE_MODIFY_INPUT = "GRADE_MODIFY_INPUT"
        const val MEDAL_MODIFY_INPUT = "MEDAL_MODIFY_INPUT"
        const val LOYALTY_HTTP_REQUEST_ASYNC_PROCESS_INPUT = "LOYALTY_HTTP_REQUEST_ASYNC_PROCESS_INPUT"
        const val LOYALTY_LP_EXPIRE_INPUT = "LOYALTY_LP_EXPIRE_INPUT"
        const val LOYALTY_LP_EFFECT_INPUT = "LOYALTY_LP_EFFECT_INPUT"
    }
}

interface KafkaSource {
    @Output(NORMAL_OUTPUT)
    fun normalOutput(): MessageChannel

    @Output(GREEN_OUTPUT)
    fun greenOutput(): MessageChannel

    @Output(LOYALTY_EVENT_CHANNEL_OUTPUT)
    fun eventChannelOutput(): MessageChannel

    @Output(LOYALTY_EVENT_NOTIFY_OUTPUT)
    fun eventNotifyOutput(): MessageChannel

    @Output(LOYALTY_ASYNC_NOTIFY_OUTPUT)
    fun asyncNotifyOutput(): MessageChannel

    @Output(PARKING_LOT_OUTPUT)
    fun parkingLotOutput(): MessageChannel

    @Output(GRADE_NOTIFY_OUTPUT)
    fun gradeNotifyOutput(): MessageChannel

    @Output(POINT_NOTIFY_OUTPUT)
    fun pointNotifyOutput(): MessageChannel

    @Output(RECORD_NOTIFY_OUTPUT)
    fun recordNotifyOutput(): MessageChannel

    @Output(LOYALTY_LP_EXPIRE_OUTPUT)
    fun lpExpireOutput(): MessageChannel

    @Output(LOYALTY_LP_EFFECT_OUTPUT)
    fun lpEffectOutput(): MessageChannel

    @Output(LOYALTY_HTTP_REQUEST_ASYNC_PROCESS_OUTPUT)
    fun requestSyncProcessOutput(): MessageChannel

    companion object {
        const val NORMAL_OUTPUT = "NORMAL_OUTPUT"
        const val GREEN_OUTPUT = "GREEN_CHANNEL_OUTPUT"
        const val LOYALTY_EVENT_CHANNEL_OUTPUT = "LOYALTY_EVENT_CHANNEL_OUTPUT"
        const val LOYALTY_EVENT_NOTIFY_OUTPUT = "LOYALTY_EVENT_NOTIFY_OUTPUT"
        const val LOYALTY_ASYNC_NOTIFY_OUTPUT = "LOYALTY_ASYNC_NOTIFY_OUTPUT"
        const val PARKING_LOT_OUTPUT = "PARKING_LOT_OUTPUT"
        const val GRADE_NOTIFY_OUTPUT = "GRADE_NOTIFY_OUTPUT"
        const val POINT_NOTIFY_OUTPUT = "POINT_NOTIFY_OUTPUT"
        const val RECORD_NOTIFY_OUTPUT = "RECORD_NOTIFY_OUTPUT"
        const val LOYALTY_HTTP_REQUEST_ASYNC_PROCESS_OUTPUT = "LOYALTY_HTTP_REQUEST_ASYNC_PROCESS_OUTPUT"
        const val LOYALTY_LP_EXPIRE_OUTPUT = "LOYALTY_LP_EXPIRE_OUTPUT"
        const val LOYALTY_LP_EFFECT_OUTPUT = "LOYALTY_LP_EFFECT_OUTPUT"
    }
}