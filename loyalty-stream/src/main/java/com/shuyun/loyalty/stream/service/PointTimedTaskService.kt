package com.shuyun.loyalty.stream.service

import com.github.f4b6a3.tsid.Tsid
import com.pip.mybatisplus.pools.DmPoolFactory
import com.pip.shuyun.pool.transaction.DmTransaction
import com.pip.shuyun.pool.transaction.TransactionInfoHolder
import com.shuyun.loyalty.entity.api.constants.ChangeMode
import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.dto.MemberPointMessage
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.sdk.Segments
import com.shuyun.loyalty.sdk.Uuid
import com.shuyun.loyalty.service.datamodel.MemberPoint
import com.shuyun.loyalty.service.datamodel.MemberPointGainStatement
import com.shuyun.loyalty.service.datamodel.MemberPointRecord
import com.shuyun.loyalty.service.datamodel.PointRecordItem
import com.shuyun.loyalty.service.exception.MemberException
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.meta.TypeEnum
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.model.Plan
import com.shuyun.loyalty.service.model.PointAccountType
import com.shuyun.loyalty.service.model.Subject
import com.shuyun.loyalty.service.service.*
import com.shuyun.loyalty.service.transfer.points.LoyaltyPoints
import com.shuyun.loyalty.service.transfer.points.LoyaltyRequestType
import com.shuyun.loyalty.service.transfer.points.MemberPointEffectTransfer
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.sendNotify
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import kotlin.jvm.optionals.getOrNull

@Service
class PointTimedTaskService {

    @Autowired
    private lateinit var memberPointService: MemberPointService

    @Autowired
    private lateinit var memberPointEffectTransfer: MemberPointEffectTransfer

    @Autowired
    private lateinit var pointItemTaskService: PointItemTaskService

    @Autowired
    private lateinit var specialListService: SpecialListService

    @Autowired
    private lateinit var validService: MemberPointValidStatementService

    @Autowired
    private lateinit var gainService: MemberPointGainStatementService

    @Autowired
    private lateinit var segmentService: PointSegmentService

    @Autowired
    private lateinit var recordService: MemberPointRecordService

    @Autowired
    private lateinit var itemService: PointRecordItemService


    private val logger = LoggerFactory.getLogger(PointTimedTaskService::class.java)

    @DmTransaction
    fun retry(accountTypeId: Long, memberPointId: String, memberId: String) {
        pointItemTaskService.process(MemberPointMessage(accountTypeId, memberId, memberPointId))
    }

    @DmTransaction
    fun effect(accountTypeId: Long, memberPointId: String, memberId: String) {
        logger.info("准备处理待生效积分 accountTypeId: {} memberId: {}", accountTypeId, memberId)
        val s = System.currentTimeMillis()
        val now = ZonedDateTime.now()
        val memberPoint = memberPointService.getByMemberPointId(accountTypeId, memberPointId, now.shDate()) ?: return
        val lp = LoyaltyPoints.of(
            memberPoint,
            type = LoyaltyRequestType.AUTO,
            point = BigDecimal.ZERO,
            date = now,
            changeMode = ChangeMode.AUTO_CALC.name,
            channel = ConstantValue.DEFAULT_CHANNEL_TYPE,
            traceId = Tsid.fast().toString(),
            uniqueId = Tsid.fast().toString()
        )
        val inSpecialList = checkSpecialList(
            subjectId = memberPoint.planId!!,
            subjectFqn = memberPoint.subjectFqn,
            accountTypeId = accountTypeId,
            memberId = memberPoint.memberId,
            forbiddenOperation = ForbiddenOperation.POINT_EFFECT
        )
        if (inSpecialList) {
            return //在黑名单中直接返回
        }
        memberPointEffectTransfer.effect(lp)
        logger.info("处理待生效积分完成 accountTypeId: {} memberId: {} 耗时：{}", accountTypeId, memberId, System.currentTimeMillis() - s)
    }



    @DmTransaction
    fun expire(accountTypeId: Long, memberPointId: String, memberId: String) {
        logger.info("开始处理过期积分 accountTypeId: {} memberId: {}", accountTypeId, memberId)
        val now = ZonedDateTime.now()
        val (plan, subject, hierarchy, memberPoint) = cf(
            accountTypeId = accountTypeId,
            memberPointId = memberPointId,
            now = now
        ) ?: return

        val inSpecialList = checkSpecialList(
            subjectId = subject.id!!,
            subjectFqn = memberPoint.subjectFqn,
            accountTypeId = accountTypeId,
            memberId = memberPoint.memberId,
            forbiddenOperation = ForbiddenOperation.POINT_EXPIRE
        )
        if (inSpecialList) return
        val vss = validService.findExpiredPoints(accountTypeId, memberPointId, now, limit = 10000)
        if (vss.isEmpty()) {
            return
        }

        memberPoint.update()
        memberPoint.saveLog()

        val vssIds = vss.map { it.id!! }.sorted()
        val gssIds = vss.map { it.gainStatementId }.sorted()
        val gss = gainService.findByIds(gssIds, accountTypeId).let {
            val map = HashMap<String, MemberPointGainStatement>()
            it.forEach { x -> map[x.id!!] = x }
            map
        }
        var totalPoint = memberPoint.point + vss.sumOf { it.point }
        val b = Property.getSysOrEnv("loyalty.point.expire.kzzd.ignore", false)
        val records = ArrayList<MemberPointRecord>()
        val items = ArrayList<PointRecordItem>()
        for ((i, vs) in vss.withIndex()) {
            if (vs.point <= BigDecimal.ZERO) {
                continue
            }
            val gs = gss[vs.gainStatementId]
            totalPoint -= vs.point
            val record = MemberPointRecord().apply {
                this.id = Uuid.uuid
                this.planId = plan.id
                this.planName = plan.name
                this.pointPlanId = hierarchy.id
                this.pointPlanName = hierarchy.name
                this.memberPointId = memberPoint.id
                this.memberId = memberPoint.memberId
                this.subjectFqn = memberPoint.subjectFqn
                this.traceId = gs?.traceId ?: "-"
                this.key = vs.id
                this.changeMode = ChangeMode.AUTO_CALC.name
                this.channel = ConstantValue.DEFAULT_CHANNEL_TYPE
                this.desc = "过期"
                this.shopId = vs.shopId
                if (!b) {
                    this.KZZD1 = vs.kzzd1
                    this.KZZD2 = vs.kzzd2
                    this.KZZD3 = vs.kzzd3
                }
                this.recordType = FSMPointEvent.EXPIRE
                this.created = now.plus(i.toLong(), ChronoUnit.MILLIS)
                this.modified = created
                this.effectiveDate = vs.effectiveDate
                this.overdueDate = vs.overdueDate
                this.point = vs.point
                this.changePoint = -vs.point
                this.totalPoint = totalPoint
                this.status = PCStatus.EXPIRE
                this.recordDetail = "积分过期-${vs.gainStatementId}"
            }

            val item = PointRecordItem().apply {
                this.id = Uuid.uuid
                this.planId = plan.id
                this.pointPlanId = hierarchy.id
                this.memberId = memberPoint.memberId
                this.traceId = record.traceId
                this.point = record.changePoint
                this.sort = 0
                this.recordId = record.id
                this.status = FSMPointEvent.EXPIRE.name
                this.effectiveDate = record.effectiveDate
                this.overdueDate = record.overdueDate
                this.parentBackId = vs.id
                this.created = record.created
            }
            items.add(item)
            records.add(record)
        }

        gainService.updateStatusByIds(accountTypeId, gssIds,FSMPointEvent.EXPIRE)
        validService.deleteBatchValidPoint(vssIds.toTypedArray(), accountTypeId)
        recordService.batchInsert(records, accountTypeId.toString())
        itemService.batchInsert(items, accountTypeId.toString())

        // 查询小于前两天的数据准备删除
        val segmentsIds = segmentService.findIdsDateBeforeByMemberId(
            accountTypeId,
            memberPointId,
            now.minusDays(2).shDate()
        ).sorted()
        segmentService.deleteByIds(accountTypeId, segmentsIds)
        notify(records)
        logger.info("过期积分处理完成 accountTypeId: {} memberId: {}", accountTypeId, memberId)
    }


    @DmTransaction
    fun migrateSegment(pointAccountTypeId: Long, memberId: String) {
        DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
            Segments.rebuildSegment(sdk, pointAccountTypeId, memberId, reference = 1)
        }
    }


    private fun cf(accountTypeId: Long, memberPointId: String, now: ZonedDateTime): Tuple4<Plan, Subject, PointAccountType, MemberPoint>? {
        val plan = LoyaltyPrograms.findPlanByAccountTypeId(accountTypeId)
        val subject = plan?.subjectList?.firstOrNull()
        val hierarchy = subject?.pointAccountTypeList?.firstOrNull()
        val memberPoint = memberPointService.getByMemberPointId(accountTypeId, memberPointId, now.shDate())
        if (plan == null || subject == null || hierarchy == null || memberPoint == null) {
            return null
        }
        return Tuple4(plan, subject, hierarchy, memberPoint)
    }


    data class Tuple4<out A, out B, out C, out D>(val first: A, val second: B, val third: C, val fourth: D)

    private fun checkSpecialList(
        subjectId: Long,
        subjectFqn: String,
        accountTypeId: Long,
        memberId: String,
        forbiddenOperation: ForbiddenOperation
    ): Boolean {
        return try {
            val configs = specialListService.find(subjectId, accountTypeId, TypeEnum.POINT)
            specialListService.check(
                subjectFqn,
                memberId,
                forbiddenOperation,
                ForbiddenPort.TIME_EVENT,
                configs
            )
        } catch (_: MemberException) {
            val s = if (forbiddenOperation == ForbiddenOperation.POINT_EFFECT) "生效" else "过期"
            logger.warn("会员积分{}时找不到主体会员Id accountTypeId: {} memberId: {}", s, accountTypeId, memberId)
            true
        }
    }


    @Suppress("unused")
    private fun findRecord(gs: MemberPointGainStatement): MemberPointRecord? {
        val record = recordService.findById(gs.id!!, gs.pointPlanId!!.toString()).getOrNull()
        if (record == null && gs.traceId != null) {
            val records = recordService.findByMemberIdAndTraceIdList(
                accountTypeId = gs.pointPlanId!!,
                memberId = gs.memberId,
                traceId = gs.traceId!!,
            ).filter { it.recordType == FSMPointEvent.DELAY_SEND }
            return records.find { it.ruleId == gs.ruleId }
                ?: records.find { it.effectiveDate == gs.effectiveDate }
                ?: records.find { it.effectiveDate!!.shDate() == gs.effectiveDate.shDate() }
                ?: records.firstOrNull()
        } else {
            return record
        }
    }


    private fun notify(records: List<MemberPointRecord>) {
        TransactionInfoHolder.afterCommit {
            records.sendNotify()
        }
    }

}