package com.shuyun.loyalty.monitoring


import com.shuyun.lite.client.DiscoveryClientHolder
import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.sdk.Property
import okhttp3.*
import okhttp3.Headers.Companion.headersOf
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.IOException
import java.security.MessageDigest
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.concurrent.CompletableFuture

object HttpC {

    private const val ENABLE_DISCOVERY_KEY = "system.discovery.enable"
    private const val SERVER_API_ADDRESS_KEY = "system.api.address"
    private const val DEFAULT_SERVER_PROTOCOL = "http"
    private val SIGN_HEX_CODE = "0123456789ABCDEF".toCharArray()
    private val client = OkHttpClient()



    private fun server(
        targetServerName: String,
        targetServerVersion: String,
        defaultTargetServerPort: Int = 8080,
        useDiscovery: Boolean = Property.getSysOrEnv(ENABLE_DISCOVERY_KEY, true)
    ): Triple<String, String, Int> {
        val it = if (useDiscovery) {
            val s = GlobalContext.tenantProvider().getWith(GlobalContext.defTenantId()) {
                DiscoveryClientHolder.find(
                    GlobalContext.environment(),
                    targetServerName,
                    targetServerVersion,
                    defaultTargetServerPort
                )
            }
            Triple(DEFAULT_SERVER_PROTOCOL, s.key!!, s.value!!)
        } else {
            val address = Property.getSysOrEnv(SERVER_API_ADDRESS_KEY)?.trimEnd('/')
                ?: throw IllegalStateException("$SERVER_API_ADDRESS_KEY is not set")
            val pattern = Regex("((?<protocol>http|https)://)?(?<host>[a-zA-Z0-9.-]+)(:(?<port>\\d+))?")
            val matchResult = pattern.find(address)
            if (matchResult != null) {
                val protocol = matchResult.groups["protocol"]?.value ?: DEFAULT_SERVER_PROTOCOL
                val host = matchResult.groups["host"]!!.value
                val port = matchResult.groups["port"]?.value?.toInt() ?: (if (protocol == "https") 443 else 80)
                Triple(protocol, host, port)
            } else {
                throw IllegalArgumentException("Invalid address: $address")
            }
        }
        return it
    }

    private fun sign(
        currentServerName: String,
        targetServiceName: String,
        targetServerVersion: String,
        requestPath: String,
        timestamp: String
    ): String {
        val parameters = linkedMapOf(
            "callerService" to currentServerName,
            "contextPath" to targetServiceName,
            "requestPath" to requestPath,
            "timestamp" to timestamp,
            "v" to targetServerVersion
        )
        val secret = Property.getSysOrEnv("system.api.secret", "")
        val sb = StringBuilder().append(secret)
        for ((key, value) in parameters) {
            sb.append(key).append(value)
        }
        val content = sb.append(secret).toString()
        val md5 = MessageDigest.getInstance("MD5")
        val bytes = md5.digest(content.toByteArray(Charsets.UTF_8))

        val sign = StringBuilder(bytes.size * 2)
        for (b in bytes) {
            sign.append(SIGN_HEX_CODE[b.toInt() shr 4 and 0xF])
            sign.append(SIGN_HEX_CODE[b.toInt() and 0xF])
        }
        return sign.toString()
    }

    private val FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")


    fun request(targetServiceName: String, targetServerVersion: String, requestPath: String, method: String, body: String?, headerBuilder: ((Headers.Builder) -> Unit)? = null): CompletableFuture<Boolean> {
        val (protocol, host, port) = server(targetServiceName, targetServerVersion, useDiscovery = true)
        val url = "${protocol}://${host}:${port}/${targetServiceName}/${targetServerVersion}${requestPath}"

        val timestamp = FORMATTER.format(LocalDateTime.now())
        val callerService = GlobalContext.serviceName()
        val callerSign = sign(
            callerService,
            targetServiceName,
            targetServerVersion,
            requestPath.split("?")[0],
            timestamp
        )
        val defaultHeaders = headersOf(
            "X-Caller-Service", callerService,
            "X-Caller-Timestamp", timestamp,
            "X-Caller-Key", callerService,
            "X-Caller-Sign", callerSign,
            "X-Tenant-Id", GlobalContext.defTenantId()
        )
        val headers = Headers.Builder()
        headers.addAll(defaultHeaders)
        headerBuilder?.let { headers.apply(headerBuilder) }

        val requestBody = body?.toRequestBody("application/json".toMediaType())
        val request = Request.Builder()
            .url(url)
            .headers(headers.build())
            .method(method, requestBody)
            .build()
        val future = CompletableFuture<Boolean>()
        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                future.completeExceptionally(e)
            }
            override fun onResponse(call: Call, response: Response) {
                response.use {
                    if (response.isSuccessful) {
                        future.complete(true)
                    } else {
                        future.complete(false)
                    }
                }
            }
        })
        return future
    }
}