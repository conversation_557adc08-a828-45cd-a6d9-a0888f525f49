package com.shuyun.loyalty.monitoring

import com.fasterxml.jackson.annotation.JsonInclude
import com.shuyun.loyalty.monitoring.Sqlite.connection
import com.shuyun.loyalty.sdk.Dataapi
import com.shuyun.loyalty.sdk.toEpochMilli
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Segments
import org.springframework.core.io.FileSystemResource
import org.springframework.core.io.Resource
import org.springframework.http.ContentDisposition
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.io.File
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime

@RestController("/")
class EndP {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class Alert(
        val id: Int,
        val bid: String?,
        val planId: String,
        val planName: String,
        val typeId: String,
        val typeName: String,
        val memberId: String,
        val name: String,
        val date: String,
        val detail: Any?
    )


    @GetMapping("/alerts")
    fun queryAlerts(
        @RequestParam startDateTime: ZonedDateTime? = null,
        @RequestParam endDateTime: ZonedDateTime? = null,
        @RequestParam bid: String? = null,
        @RequestParam typeId: String? = null,
        @RequestParam memberId: String? = null,
        @RequestParam lastId: Int? = null
    ): List<Alert> {
        val idStr = lastId?.let { "AND id < $it" } ?: ""
        val typeIdStr = typeId?.let { "AND type_id = '$it'" } ?: ""
        val memberIdStr = memberId?.let { "AND member_id = '$it'" } ?: ""
        val bidStr = bid?.let { "AND bid like '${it}%'" } ?: ""
        val start = startDateTime ?: ZonedDateTime.now().minusDays(5)
        val end = endDateTime ?: ZonedDateTime.now()
        val sql = """
            SELECT id, bid, plan_id, plan_name, type_id, type_name, member_id, name, date, detail 
            FROM t_alert 
            WHERE created_at >= :start AND created_at <= :end $idStr $typeIdStr $memberIdStr $bidStr
            ORDER BY id DESC 
            LIMIT 1000
        """.trimIndent()
        val alerts = mutableListOf<Alert>()
        connection().use { connection ->
            connection.prepareStatement(sql).use { statement ->
                statement.setLong(1, start.toEpochMilli())
                statement.setLong(2, end.toEpochMilli())
                statement.executeQuery().use { rs ->
                    while (rs.next()) {
                        alerts.add(
                            Alert(
                                rs.getInt("id"),
                                rs.getString("bid"),
                                rs.getString("plan_id"),
                                rs.getString("plan_name"),
                                rs.getString("type_id"),
                                rs.getString("type_name"),
                                rs.getString("member_id"),
                                rs.getString("name"),
                                rs.getString("date"),
                                rs.getString("detail")
                            )
                        )
                    }
                }
            }
        }
        return alerts
    }


    @GetMapping("/download-alerts")
    fun downloadAlerts(
        @RequestParam startDateTime: ZonedDateTime? = null,
        @RequestParam endDateTime: ZonedDateTime? = null,
    ): ResponseEntity<Resource> {
        val start = startDateTime ?: ZonedDateTime.now().minusDays(5)
        val end = endDateTime ?: ZonedDateTime.now()
        val sql = """
            SELECT id, bid, plan_id, plan_name, type_id, type_name, member_id, name, date, detail 
            FROM t_alert 
            WHERE created_at >= :start AND created_at <= :end
            ORDER BY id DESC
        """.trimIndent()
        // 转成csv文件
        val csvFile = File.createTempFile("alerts", ".csv")
        csvFile.bufferedWriter().use { writer ->
            writer.write("id,bid,plan_id,plan_name,type_id,type_name,member_id,name,date,detail\n")
            connection().use { connection ->
                connection.prepareStatement(sql).use { statement ->
                    statement.setLong(1, start.toEpochMilli())
                    statement.setLong(2, end.toEpochMilli())
                    statement.executeQuery().use { rs ->
                        while (rs.next()) {
                            writer.write(
                                "${rs.getInt("id")},${rs.getString("bid")},${rs.getString("plan_id")},${rs.getString("plan_name")},${rs.getString("type_id")},${rs.getString("type_name")},${rs.getString("member_id")},${rs.getString("name")},${rs.getString("date")},${rs.getString("detail") ?: "-"}\n"
                            )
                        }
                    }
                }
            }
        }
        val resource = FileSystemResource(csvFile)
        val headers = HttpHeaders()
        headers.contentDisposition = ContentDisposition.builder("attachment").filename("alerts.csv").build()
        headers.contentType = MediaType.parseMediaType("text/csv")
        csvFile.deleteOnExit()
        return ResponseEntity.ok()
            .headers(headers)
            .body(resource)
    }


    data class Order(
        val id: Long,
        val memberId: String,
        val traceId: String,
        val data: Map<String, Any?>,
        val fqn: String,
        val k: String,
        val detectedAt: ZonedDateTime,
        val occurredAt: ZonedDateTime,
        val createdAt: ZonedDateTime
    )


    @GetMapping("/order")
    fun queryOrder(
        @RequestParam fqn: String? = null,
        @RequestParam memberId: String? = null,
        @RequestParam traceId: String? = null,
        @RequestParam k: String? = null
    ): List<Order> {
        val fqnStr = fqn?.let { "AND fqn = '$it'" } ?: ""
        val memberIdStr = memberId?.let { "AND member_id = '$it'" } ?: ""
        val traceIdStr = traceId?.let { "AND trace_id = '$it'" } ?: ""
        val kStr = k?.let { "AND k = '$it'" } ?: ""
        val sql = """
            SELECT id, member_id, trace_id, payload, fqn, k, detected_at, occurred_at, created_at FROM t_orders 
            WHERE id > 0 $fqnStr $memberIdStr $traceIdStr $kStr
            ORDER BY created_at ASC
            LIMIT 200
        """.trimIndent()
        val orders = mutableListOf<Order>()
        connection().use { connection ->
            connection.prepareStatement(sql).use { statement ->
                statement.executeQuery().use { rs ->
                    while (rs.next()) {
                        val order = Order(
                            rs.getLong("id"),
                            rs.getString("member_id"),
                            rs.getString("trace_id"),
                            Json.parse(rs.getString("payload")),
                            rs.getString("fqn"),
                            rs.getString("k"),
                            ZonedDateTime.ofInstant(Instant.ofEpochMilli(rs.getLong("detected_at")), ZoneId.systemDefault()),
                            ZonedDateTime.ofInstant(Instant.ofEpochMilli(rs.getLong("occurred_at")), ZoneId.systemDefault()),
                            ZonedDateTime.ofInstant(Instant.ofEpochMilli(rs.getLong("created_at")), ZoneId.systemDefault())
                        )
                        orders.add(order)
                    }
                }
            }
        }
        return orders
    }


    data class Points(
        val id: Long,
        val typeId: Int,
        val memberId: String,
        val points: Double,
        val modifiedAt: ZonedDateTime,
        val createdAt: ZonedDateTime
    )

    @GetMapping("/points")
    fun queryPoints(
        @RequestParam typeId: String,
        @RequestParam memberId: String? = null,
        @RequestParam lastId: Int? = null
    ): List<Points> {
        val memberIdStr = memberId?.let { "AND member_id = '$it'" } ?: ""
        val idStr = lastId?.let { "AND id < $it" } ?: ""
        val sql = """
            SELECT id, type_id, member_id, points, modified_at, created_at FROM t_points 
            WHERE id > 0 AND type_id = ? $memberIdStr $idStr
            ORDER BY id DESC
            LIMIT 100
        """.trimIndent()
        val points = mutableListOf<Points>()
        connection().use { connection ->
            connection.prepareStatement(sql).use { statement ->
                statement.setInt(1, typeId.toInt())
                statement.executeQuery().use { rs ->
                    while (rs.next()) {
                        val point = Points(
                            rs.getLong("id"),
                            rs.getInt("type_id"),
                            rs.getString("member_id"),
                            rs.getDouble("points"),
                            ZonedDateTime.ofInstant(Instant.ofEpochMilli(rs.getLong("modified_at")), ZoneId.systemDefault()),
                            ZonedDateTime.ofInstant(Instant.ofEpochMilli(rs.getLong("created_at")), ZoneId.systemDefault())
                        )
                        points.add(point)
                    }
                }
            }
        }
        return points
    }


    data class Changelog(
        val id: Long,
        val typeId: Int,
        val memberId: String,
        val changePoints: Double,
        val afterPoints: Double,
        val createdAt: ZonedDateTime
    )

    @GetMapping("/changelog")
    fun queryChangelog(
        @RequestParam typeId: String,
        @RequestParam memberId: String? = null,
        @RequestParam lastId: Int? = null
    ): List<Changelog> {
        val memberIdStr = memberId?.let { "AND member_id = '$it'" } ?: ""
        val idStr = lastId?.let { "AND id < $it" } ?: ""
        val sql = """
            SELECT id, type_id, member_id, change_points, after_points, created_at FROM t_changelog 
            WHERE id > 0 AND type_id = ? $memberIdStr $idStr
            ORDER BY id DESC
            LIMIT 100
        """.trimIndent()
        val changelogs = mutableListOf<Changelog>()
        connection().use { connection ->
            connection.prepareStatement(sql).use { statement ->
                statement.setInt(1, typeId.toInt())
                statement.executeQuery().use { rs ->
                    while (rs.next()) {
                        val changelog = Changelog(
                            rs.getLong("id"),
                            rs.getInt("type_id"),
                            rs.getString("member_id"),
                            rs.getDouble("change_points"),
                            rs.getDouble("after_points"),
                            ZonedDateTime.ofInstant(Instant.ofEpochMilli(rs.getLong("created_at")), ZoneId.systemDefault())
                        )
                        changelogs.add(changelog)
                    }
                }
            }
        }
        return changelogs
    }


    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class Task(
        val id: Long,
        val type: String,
        val payload: String,
        val executeAt: ZonedDateTime,
        val retryCount: Int?
    )

    @GetMapping("/tasks")
    fun queryTasks(@RequestParam type: String? = null): List<Task> {
        val typeStr = type?.let { "AND type = '$it'" } ?: ""
        val sql = """
            SELECT id, type, payload, execute_at,retry_count FROM t_tasks 
            WHERE execute_at > ? $typeStr
            ORDER BY execute_at
            LIMIT 50
        """.trimIndent()
        val tasks = mutableListOf<Task>()
        connection().use { connection ->
            connection.prepareStatement(sql).use { statement ->
                statement.setLong(1, ZonedDateTime.now().toEpochMilli())
                statement.executeQuery().use { rs ->
                    while (rs.next()) {
                        val retryCount = rs.getInt("retry_count")
                        val task = Task(
                            rs.getLong("id"),
                            rs.getString("type"),
                            rs.getString("payload"),
                            ZonedDateTime.ofInstant(Instant.ofEpochMilli(rs.getLong("execute_at")), ZoneId.systemDefault()),
                            if (retryCount == 0) null else retryCount
                        )
                        tasks.add(task)
                    }
                }
            }
        }
        return tasks
    }



    data class PointMigrateReq(val pointAccountId: Long, val memberId: String, val reference: Int = 1)
    @PostMapping("/migrate-point-segment")
    fun migrationSegment(@RequestBody request: PointMigrateReq): Map<String, Any?> {
        try {
            Dataapi.withTrans { sdk ->
                Segments.rebuildSegment(sdk, request.pointAccountId, request.memberId, request.reference)
            }
            return mapOf("ok" to true)
        } catch (e: Exception) {
            return mapOf("ok" to false, "error" to e.message)
        }
    }
}
