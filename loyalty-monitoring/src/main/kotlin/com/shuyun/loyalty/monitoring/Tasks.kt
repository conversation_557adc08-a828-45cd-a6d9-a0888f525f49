package com.shuyun.loyalty.monitoring

import com.alibaba.csp.sentinel.concurrent.NamedThreadFactory
import com.shuyun.loyalty.monitoring.PlanC.findH
import com.shuyun.loyalty.monitoring.PlanC.findP
import com.shuyun.loyalty.monitoring.Sqlite.connection
import com.shuyun.loyalty.monitoring.Tasks.LIMIT.*
import com.shuyun.loyalty.sdk.Dataapi
import com.shuyun.loyalty.sdk.toEpochMilli
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Property
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.time.*
import java.time.format.DateTimeFormatter
import java.time.temporal.TemporalAdjusters
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.SynchronousQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import kotlin.concurrent.thread
import kotlin.math.pow

object Tasks {

    private val logger = LoggerFactory.getLogger(Tasks::class.java)

    private const val MAX_RETRY_COUNT = 10

    private val tBlockingQueue = ArrayBlockingQueue<T>(50000)
    private val alertBlockingQueue = ArrayBlockingQueue<Alert>(50000)

    private var executor = ThreadPoolExecutor(
        100, 100,
        10L, TimeUnit.SECONDS,
        SynchronousQueue(),
        NamedThreadFactory("tsk"),
        ThreadPoolExecutor.CallerRunsPolicy()
    )

    fun addTask(t: T) {
        tBlockingQueue.put(t)
    }

    @Synchronized
    private fun addTask0(t: T) {
        val connection = connection()
        try {
            connection.autoCommit = false
            val sql = "INSERT OR IGNORE INTO t_tasks (type, payload, execute_at, retry_count) VALUES (?, ?, ?, ?)"
            connection.prepareStatement(sql).use { statement ->
                statement.setString(1, t.type)
                statement.setString(2, t.payload)
                statement.setLong(3, t.executeAt)
                statement.setInt(4, t.retryCount)
                statement.execute()
            }
            connection.commit()
        } catch (e: Exception) {
            connection.rollback()
            throw e
        } finally {
            connection.autoCommit = true
            connection.close()
        }
    }

    data class T(val id: String, val type: String, val payload: String, val executeAt: Long, val retryCount: Int = 0)

    @Synchronized
    private fun getNextTask(): T? {
        val connection = connection()
        connection.autoCommit = false
        try {
            val sql = """
                UPDATE t_tasks SET execute_at = ? WHERE id = (
                    SELECT id FROM t_tasks WHERE execute_at <= ? ORDER BY execute_at LIMIT 1
                )
                RETURNING id, type, payload, execute_at, retry_count
            """.trimIndent()
            connection.prepareStatement(sql).use { statement ->
                val now = System.currentTimeMillis()
                val next = now + 1000 * 60 * 60 * 5 // 5 hours
                statement.setLong(1, next)
                statement.setLong(2, now)
                statement.executeQuery().use { rs ->
                    if (rs.next()) {
                        return T(
                            id = rs.getString("id"),
                            type = rs.getString("type"),
                            payload = rs.getString("payload"),
                            executeAt = rs.getLong("execute_at"),
                            retryCount = rs.getInt("retry_count")
                        )
                    }
                }
            }
            connection.commit()
        } catch (e: Exception) {
            connection.rollback()
            throw e
        } finally {
            connection.autoCommit = true
            connection.close()
        }
        return null
    }


    @Synchronized
    private fun removeTask(id: String) {
        val connection = connection()
        try {
            connection.autoCommit = false
            val sql = "DELETE FROM t_tasks WHERE id = ?"
            connection.prepareStatement(sql).use { statement ->
                statement.setString(1, id)
                statement.execute()
            }
            connection.commit()
        } catch (e: Exception) {
            connection.rollback()
            logger.warn("删除任务失败 {}", e.message)
        } finally {
            connection.autoCommit = true
            connection.close()
        }
    }


    @Synchronized
    private fun updateTaskRetry(id: String, delayMillis: Long) {
        val connection = connection()
        try {
            connection.autoCommit = false
            val sql = "UPDATE t_tasks SET retry_count = retry_count + 1, execute_at = ? WHERE id = ?"
            connection.prepareStatement(sql).use { statement ->
                statement.setLong(1, System.currentTimeMillis() + delayMillis)
                statement.setString(2, id)
                statement.execute()
            }
            connection.commit()
        } catch (e: Exception) {
            connection.rollback()
            logger.warn("更新任务重试次数失败 {}", e.message)
        } finally {
            connection.autoCommit = true
            connection.close()
        }
    }

    init {
        thread(isDaemon = true, name = "保存任务") {
             while (true) {
                 try {
                     val t = tBlockingQueue.take()
                     addTask0(t)
                 } catch (e: Exception) {
                     logger.error("保存任务失败", e)
                 }
             }
        }

        thread(isDaemon = true, name = "保存告警") {
            while (true) {
                try {
                    val alert = alertBlockingQueue.take()
                    saveAlert0(alert)
                } catch (e: Exception) {
                    logger.error("保存告警失败", e)
                }
            }
        }

        thread(isDaemon = true, name = "monitoring-task") {
            while (true) {
                try {
                    val nt = getNextTask()
                    if (nt == null) {
                        Thread.sleep(1000)
                        continue
                    }
                    doTask(nt)
                } catch (e: Exception) {
                    logger.error("查询任务失败", e)
                    Thread.sleep(5000)
                }
            }
        }
        Runtime.getRuntime().addShutdownHook(Thread {
            executor.shutdown()
            executor.awaitTermination(10, TimeUnit.SECONDS)
        })
    }


    private fun doTask(t: T) {
        try {
            logger.debug("处理任务: t = {}", t)
            when (t.type) {
                "TYPE_A" -> doTypeATask(t)
                "TYPE_B" -> doTypeBTask(t)
                "TYPE_C" -> doTypeCTask(t)
                "TYPE_D" -> doTypeDTask(t)
                "TYPE_E" -> doTypeETask(t)
                "TYPE_F" -> doTypeFTask(t)
                "TYPE_G" -> doTypeGTask(t)
                else -> {}
            }
            removeTask(t.id)
        } catch (e: Exception) {
            if (t.retryCount < MAX_RETRY_COUNT) {
                val delayMillis = 1000L * 2.0.pow((t.retryCount + 1).toDouble()).toLong()
                updateTaskRetry(t.id, delayMillis)
            } else {
                logger.error("处理任务失败", e)
                removeTask(t.id)
            }
        }
    }


    enum class LIMIT(val desc: String, private val defaultValue: BigDecimal? = null) {
        ACCOUNT_CUMULATIVE_POSITIVE_LIMIT("积分账户累计正值上限", BigDecimal("*********")),
        ACCOUNT_CUMULATIVE_NEGATIVE_LIMIT("积分账户累计负值上限", BigDecimal("-*********")),
        ACCOUNT_SINGLE_ISSUE_LIMIT("积分账户单次发放上限", BigDecimal("100000")), // 10万
        ACCOUNT_SINGLE_DEDUCTION_LIMIT("积分账户单次扣减上限", BigDecimal("100000")), // 10万
        ACCOUNT_DAILY_ISSUE_LIMIT("积分账户单日发放上限", BigDecimal("500000")), // 50万
        ACCOUNT_DAILY_DEDUCTION_LIMIT("积分账户单日扣减上限", BigDecimal("500000")), // 50万
        ACCOUNT_ORDER_DAILY_LIMIT("积分账户单日订单数上限", BigDecimal("100")), // 100
        ACCOUNT_POINTS_ISSUE_FREQUENCY_LIMIT("积分账户发放频率上限"),
        ACCOUNT_POINTS_DEDUCTION_FREQUENCY_LIMIT("积分账户扣减频率上限"),


        HIERARCHY_CUMULATIVE_POSITIVE_LIMIT("积分体系累计正值上限"),
        HIERARCHY_CUMULATIVE_NEGATIVE_LIMIT("积分体系累计负值上限"),

        HIERARCHY_DAILY_ISSUE_LIMIT("积分体系单日发放上限"),
        HIERARCHY_DAILY_DEDUCTION_LIMIT("积分体系单日扣减上限"),

        ACCOUNT_POINTS_RECORD_INCREASE_UNTRACEABLE("账户积分明细增加无法溯源"),
        ACCOUNT_POINTS_RECORD_DECREASE_UNTRACEABLE("账户积分明细增加减少溯源"),
        ;

        val limit get() =  Property.getSysOrEnv(desc)?.toBigDecimal() to defaultValue

        enum class FREQUENCY(val desc: String, val defaultValue: BigDecimal) {
            SECONDLY("每秒", 5.toBigDecimal()),
            MINUTELY("每分钟", 10.toBigDecimal()),
            HOURLY("每小时", 20.toBigDecimal()),
            DAILY("每日", 40.toBigDecimal()),
            WEEKLY("每周", 80.toBigDecimal()),
            MONTHLY("每月", 160.toBigDecimal()),
            YEARLY("每年", 320.toBigDecimal()),
            ;
            fun startDateTime(end: ZonedDateTime): ZonedDateTime {
                return when(this) {
                    SECONDLY -> end.minusSeconds(1)
                    MINUTELY -> end.minusMinutes(1)
                    HOURLY -> end.minusHours(1)
                    DAILY -> end.with(LocalTime.MIDNIGHT)
                    WEEKLY -> end.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).with(LocalTime.MIDNIGHT)
                    MONTHLY -> end.with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIDNIGHT)
                    YEARLY -> end.with(TemporalAdjusters.firstDayOfYear()).with(LocalTime.MIDNIGHT)
                }
            }
        }
        fun limit(frequency: FREQUENCY) = Property.getSysOrEnv(desc + "-" + frequency.desc)?.toBigDecimal() to frequency.defaultValue
    }


    private fun doTypeATask(t: T) {
        val (typeId, memberId, changePoints, afterPoints, createdAt) = t.payload.split("##")
        val cp = changePoints.toBigDecimal()
        val ap = afterPoints.toBigDecimal()
        val date = createdAt.toLong()
        ACCOUNT_CUMULATIVE_POSITIVE_LIMIT.limit.let { (a, b) ->
            val c = a ?: b
            if (c != null && cp > BigDecimal.ZERO && ap > c) {
                saveAlert(
                    typeId, memberId, ACCOUNT_CUMULATIVE_POSITIVE_LIMIT.desc, date,
                    detail = "当前值=${ap.toPlainString()}，上限值=${c.toPlainString()}", v = a
                )
            }
        }

        ACCOUNT_CUMULATIVE_NEGATIVE_LIMIT.limit.let { (a, b) ->
            val c = a ?: b
            if (c != null && cp < BigDecimal.ZERO && ap < c) {
                saveAlert(typeId, memberId, ACCOUNT_CUMULATIVE_NEGATIVE_LIMIT.desc, date,
                    detail = "当前值=${ap.toPlainString()}，上限值=${c.toPlainString()}",
                    v = a
                )
            }
        }

        ACCOUNT_SINGLE_ISSUE_LIMIT.limit.let { (a, b) ->
            val c = a ?: b
            if (c != null && cp > BigDecimal.ZERO && cp > c) {
                saveAlert(typeId, memberId, ACCOUNT_SINGLE_ISSUE_LIMIT.desc, date,
                    detail = "当前值=${cp.toPlainString()}，上限值=${c.toPlainString()}",
                    v = a
                )
            }
        }

        ACCOUNT_SINGLE_DEDUCTION_LIMIT.limit.let { (a, b) ->
            val c = a ?: b
            if (c != null && cp < BigDecimal.ZERO && cp.abs() > c) {
                saveAlert(typeId, memberId, ACCOUNT_SINGLE_DEDUCTION_LIMIT.desc, date,
                    detail = "当前值=${cp.abs().toPlainString()}，上限值=${c.toPlainString()}",
                    v = a
                )
            }
        }

        ACCOUNT_DAILY_ISSUE_LIMIT.limit.let { (a, b) ->
            val c = a ?: b
            if (c != null && cp > BigDecimal.ZERO) {
                var dailyPoints = BigDecimal.ZERO
                connection().use { conn ->
                    val start = ofEpochMilli(date).with(LocalTime.MIDNIGHT)
                    val end = ofEpochMilli(date)
                    val sql = """
                        SELECT sum(change_points) as dailyPoints FROM t_changelog 
                        WHERE type_id = $typeId AND member_id = '${memberId}' 
                        AND created_at >= ${start.toEpochMilli()} AND created_at < ${end.toEpochMilli()}
                        AND change_points > 0
                    """.trimIndent()
                    conn.prepareStatement(sql).use { statement ->
                        statement.executeQuery().use { rs ->
                            if (rs.next()) {
                                dailyPoints = rs.getBigDecimal("dailyPoints") ?: BigDecimal.ZERO
                            }
                        }
                    }
                }
                if (dailyPoints > c) {
                    saveAlert(typeId, memberId, ACCOUNT_DAILY_ISSUE_LIMIT.desc, date,
                        detail = "当前值=${dailyPoints.toPlainString()}，上限值=${c.toPlainString()}",
                        v = a
                    )
                }
            }
        }

        ACCOUNT_DAILY_DEDUCTION_LIMIT.limit.let { (a, b) ->
            val c = a ?: b
            if (c != null && cp < BigDecimal.ZERO) {
                var dailyPoints = BigDecimal.ZERO
                connection().use { conn ->
                    val start = ofEpochMilli(date).with(LocalTime.MIDNIGHT)
                    val end = ofEpochMilli(date)
                    val sql = """
                        SELECT sum(change_points) as dailyPoints FROM t_changelog 
                        WHERE type_id = $typeId AND member_id = '${memberId}' 
                        AND created_at >= ${start.toEpochMilli()} AND created_at < ${end.toEpochMilli()} 
                        AND change_points < 0
                    """.trimIndent()
                    conn.prepareStatement(sql).use { statement ->
                        statement.executeQuery().use { rs ->
                            if (rs.next()) {
                                dailyPoints = rs.getBigDecimal("dailyPoints") ?: BigDecimal.ZERO
                            }
                        }
                    }
                }
                if (dailyPoints.abs() > c) {
                    saveAlert(typeId, memberId, ACCOUNT_DAILY_DEDUCTION_LIMIT.desc, date,
                        detail = "当前值=${dailyPoints.abs().toPlainString()}，上限值=${c.toPlainString()}",
                        v = a
                    )
                }
            }
        }

        for (frequency in FREQUENCY.entries) {
            val end = ofEpochMilli(date)
            if (cp > BigDecimal.ZERO) {
                ACCOUNT_POINTS_ISSUE_FREQUENCY_LIMIT.limit(frequency).let { (a, b) ->
                    val c = a ?: b
                    val start = frequency.startDateTime(end)
                    var count = 0
                    connection().use { conn ->
                        val sql = """
                            SELECT count(*) as cnt FROM t_changelog 
                            WHERE type_id = $typeId AND member_id = '${memberId}' 
                            AND created_at >= ${start.toEpochMilli()} AND created_at < ${end.toEpochMilli()} 
                            AND change_points > 0
                        """.trimIndent()
                        conn.prepareStatement(sql).use { statement ->
                            statement.executeQuery().use { rs ->
                                if (rs.next()) {
                                    count = rs.getInt("cnt")
                                }
                            }
                        }
                    }
                    if (count > c.toInt()) {
                        saveAlert(typeId, memberId,ACCOUNT_POINTS_ISSUE_FREQUENCY_LIMIT.desc + "-" + frequency.desc, date,
                            detail = "当前值=$count，上限值=${c.toPlainString()}，时间范围=${start}~${end}",
                            v = a
                        )
                    }
                }
            }

            if (cp < BigDecimal.ZERO) {
                ACCOUNT_POINTS_DEDUCTION_FREQUENCY_LIMIT.limit(frequency).let { (a, b) ->
                    val c = a ?: b
                    val start = frequency.startDateTime(end)
                    var count = 0
                    connection().use { conn ->
                        val sql = """
                            SELECT count(*) as cnt FROM t_changelog 
                            WHERE type_id = $typeId AND member_id = '${memberId}' 
                            AND created_at >= ${start.toEpochMilli()} AND created_at < ${end.toEpochMilli()} 
                            AND change_points < 0
                        """.trimIndent()
                        conn.prepareStatement(sql).use { statement ->
                            statement.executeQuery().use { rs ->
                                if (rs.next()) {
                                    count = rs.getInt("cnt")
                                }
                            }
                        }
                    }
                    if (count > c.toInt()) {
                        saveAlert(typeId, memberId, ACCOUNT_POINTS_DEDUCTION_FREQUENCY_LIMIT.desc + "-" + frequency.desc, date,
                            detail = "当前值=$count，上限值=${c.toPlainString()}，时间范围=${start}~${end}",
                            v = a
                        )
                    }
                }
            }
        }

        if (memberId == "-") {
            if (cp > BigDecimal.ZERO) {
                HIERARCHY_CUMULATIVE_POSITIVE_LIMIT.limit.let { (a, _) ->
                    if (a != null && ap > a) {
                        saveAlert(typeId, memberId, HIERARCHY_CUMULATIVE_POSITIVE_LIMIT.desc, date,
                            detail = "当前值=${ap.toPlainString()}，上限值=${a.toPlainString()}"
                        )
                    }
                }
                HIERARCHY_DAILY_ISSUE_LIMIT.limit.let { (a, _) ->
                    if (a == null) return@let
                    var dailyPoints = BigDecimal.ZERO
                    connection().use { conn ->
                        val start = ofEpochMilli(date).with(LocalTime.MIDNIGHT)
                        val sql = """
                            SELECT sum(change_points) as dailyPoints FROM t_changelog 
                            WHERE type_id = $typeId 
                            AND created_at >= ${start.toEpochMilli()} AND created_at < $date 
                            AND change_points > 0
                        """.trimIndent()
                        conn.prepareStatement(sql).use { statement ->
                            statement.executeQuery().use { rs ->
                                if (rs.next()) {
                                    dailyPoints = rs.getBigDecimal("dailyPoints") ?: BigDecimal.ZERO
                                }
                            }
                        }
                    }
                    if (dailyPoints > a) {
                        saveAlert(typeId, memberId, HIERARCHY_DAILY_ISSUE_LIMIT.desc, date, detail = "当前值=${dailyPoints.toPlainString()}，上限值=${a.toPlainString()}")
                    }
                }
            }

            if (cp < BigDecimal.ZERO) {
                HIERARCHY_CUMULATIVE_NEGATIVE_LIMIT.limit.let { (a, _) ->
                    if (a != null && ap < a) {
                        saveAlert(typeId, memberId, HIERARCHY_CUMULATIVE_NEGATIVE_LIMIT.desc, date,
                            detail = "当前值=${ap.toPlainString()}，上限值=${a.toPlainString()}"
                        )
                    }
                }
                HIERARCHY_DAILY_DEDUCTION_LIMIT.limit.let { (a, _) ->
                    if (a == null) return@let
                    var dailyPoints = BigDecimal.ZERO
                    connection().use { conn ->
                        val start = ofEpochMilli(date).with(LocalTime.MIDNIGHT)
                        val sql = """
                            SELECT sum(change_points) as dailyPoints FROM t_changelog 
                            WHERE type_id = $typeId 
                            AND created_at >= ${start.toEpochMilli()} AND created_at < $date 
                            AND change_points < 0
                        """.trimIndent()
                        conn.prepareStatement(sql).use { statement ->
                            statement.executeQuery().use { rs ->
                                if (rs.next()) {
                                    dailyPoints = rs.getBigDecimal("dailyPoints") ?: BigDecimal.ZERO
                                }
                            }
                        }
                    }
                    if (dailyPoints.abs() > a) {
                        saveAlert(typeId, memberId, HIERARCHY_DAILY_DEDUCTION_LIMIT.desc, date,
                            detail = "当前值=${dailyPoints.abs().toPlainString()}，上限值=${a.toPlainString()}"
                        )
                    }
                }
            }
        }

    }


    private fun doTypeBTask(t: T) {
        val (memberId, createdAt) = t.payload.split("##")
        val date = ofEpochMilli(createdAt.toLong())
        val typeId = "-"
        ACCOUNT_ORDER_DAILY_LIMIT.limit.let { (a, b) ->
            val c = a ?: b
            if (c == null) return@let
            var count = 0
            connection().use { conn ->
                val start = date.with(LocalTime.MIDNIGHT)
                val sql = """
                    SELECT count(*) as cnt FROM t_orders 
                    WHERE member_id = '${memberId}' 
                    AND created_at >= ${start.toEpochMilli()} AND created_at < ${date.toEpochMilli()}
                """.trimIndent()
                conn.prepareStatement(sql).use { statement ->
                    statement.executeQuery().use { rs ->
                        if (rs.next()) {
                            count = rs.getInt("cnt")
                        }
                    }
                }
            }
            if (count > c.toInt()) {
                saveAlert(typeId, memberId, ACCOUNT_ORDER_DAILY_LIMIT.desc, createdAt.toLong(),
                    detail = "当前值=$count，上限值=${c.toPlainString()}",
                    v = a
                )
            }
        }
    }


    private fun doTypeCTask(t: T) {
        val (typeId, bid, expectExecuteAt, checkExpire) = t.payload.split("##")
        val date = ofEpochMilli(t.executeAt)
        val now = ZonedDateTime.now()
        val nextExecuteAt = now.withHour(6).withMinute(0).withSecond(0).plusDays(1)
        val newBid = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
        addTask(t.copy(executeAt = nextExecuteAt.toEpochMilli(), payload = "$typeId##$newBid##${date.plusDays(1).toEpochMilli()}##-"))
        if (now.hour in 6..18 && checkExpire.isNotEmpty()) {
            executor.execute {
                val desc = "应失效的积分未失效"
                val sql = "select id, memberId, point, overdueDate from data.loyalty.member.account.ValidStatement$typeId where overdueDate < :overdueDate and fromStatus not in ('OPEN_FROZE', 'SPECIAL_FROZE')"
                Dataapi.fetch(sql, mapOf("overdueDate" to date)) { maps ->
                    val conn = connection()
                    try {
                        conn.autoCommit = false
                        for (map in maps) {
                            saveAlert(typeId, map["memberId"].toString(),
                                desc,
                                date.toEpochMilli(),
                                detail = "id=${map["id"]}, point=${map["point"]}, overdueDate=${map["overdueDate"]}",
                                bid = bid
                            )
                        }
                        conn.commit()
                    } catch (_: Exception) {
                        conn.rollback()
                    } finally {
                        conn.autoCommit = true
                        conn.close()
                    }
                }
            }
        }

        executor.execute {
            val sql = "select memberId from data.loyalty.member.account.Point$typeId"
            var i = 0L
            Dataapi.fetch(sql) { maps ->
                for (map in maps) {
                    val memberId = map["memberId"].toString()
                    i += 1
                    // 正负积分不符合预期
                    val nextT = T(
                        id = "x",
                        type = "TYPE_E",
                        payload = listOf(typeId, memberId, "$bid-$i").joinToString("##"),
                        executeAt = expectExecuteAt.toLong()
                    )
                    addTask(nextT)
                }
            }
        }
    }


    // 积分明细增加/减少值无法溯源
    private fun doTypeDTask(t: T) {
        val f = Property.getSysOrEnv("do.type.e.task", false)
        if (!f) {
            return
        }
        val arr = t.payload.split("##")
        val recordId = arr[0]
        val typeId = arr[1]
        val memberId = arr[2]
        val traceId = arr[3]
        val key = arr[4]
        val points = arr[5]
        val signedPoint = arr[6].toBigDecimal()
        val desc = arr[7]
        val recordTime = arr[8]

        val detail = mapOf(
            "recordId" to recordId,
            "typeId" to typeId,
            "memberId" to memberId,
            "traceId" to traceId,
            "key" to key,
            "points" to points,
            "signedPoint" to signedPoint,
            "desc" to desc,
            "recordTime" to recordTime
        )

        val sql = "select uniqueId,data from data.loyalty.member.PointCalculateJournal${typeId} where traceId = :traceId and action != 'MIGRATE' order by id desc"
        val params = mapOf("traceId" to traceId)
        val list = Dataapi.sdk.use {
            val response = it.execute(sql, params)
            response.data ?: emptyList()
        }
        val e = if (signedPoint >= BigDecimal.ZERO) ACCOUNT_POINTS_RECORD_INCREASE_UNTRACEABLE else ACCOUNT_POINTS_RECORD_DECREASE_UNTRACEABLE
        if (list.isEmpty()) {
            saveAlert(typeId, memberId, e.desc, System.currentTimeMillis(), detail = detail.toString())
            return
        }
        var found = false
        for (m in list) {
            val row = m as Map<*, *>
            val uniqueId = row["uniqueId"].toString()
            val data = row["data"].toString()
            val tree = Json.objectMapper.readTree(data)
            val attr = tree.get("attr")
            val attrTraceId = attr.get("traceId").asText()
            val attrUniqueId = attr.get("uniqueId").asText()
            if (key == attrUniqueId || key == attrTraceId || key == uniqueId) {
                found = true
                break
            }
        }
        if (!found) {
            saveAlert(typeId, memberId, e.desc, System.currentTimeMillis(), detail = detail.toString())
        }
    }


    // 积分账户值大于0时相关数据状态不符合预期
    // 积分账户值小于0时相关数据状态不符合预期
    private fun doTypeETask(t: T) {
        executor.submit {
            try {
                val (typeId, memberId, bid) = t.payload.split("##")
                val date = LocalDate.now()
                val dateTime = ZonedDateTime.of(date, LocalTime.MIDNIGHT, ZoneId.systemDefault())
                val (p,b) = Points.getLatestPoints(typeId.toLong(), memberId, date)
                val s = if (b) "积分块值" else "账户积分值"
                when  {
                    p > BigDecimal.ZERO -> {
                        // 负积分不应该有值
                        val desc = "${s}大于0时相关数据状态不符合预期"
                        val validSum = sumValidStatement(typeId, memberId, dateTime)
                        if (validSum.compareTo(p) != 0) {
                            saveAlert(typeId, memberId, desc, System.currentTimeMillis(),
                                detail = "${s}大于0时有效积分值不等于${s}，${s}=${p.toPlainString()}，有效积分=${validSum.toPlainString()}",
                                bid = bid
                            )
                            return@submit
                        }
                        val negativePoints = sumNegativeStatement(typeId, memberId)
                        if (negativePoints > BigDecimal.ZERO) {
                            saveAlert(typeId, memberId, desc, System.currentTimeMillis(),
                                detail = "${s}大于0时不应该存在负积分，${s}=${p.toPlainString()}，负积分=${(-negativePoints).toPlainString()}",
                                bid = bid
                            )
                            return@submit
                        }
                    }
                    p < BigDecimal.ZERO -> {
                        // 负积分应该有值
                        val desc = "${s}小于0时相关数据状态不符合预期"
                        val negativePoints = sumNegativeStatement(typeId, memberId)
                        if (negativePoints <= BigDecimal.ZERO) {
                            saveAlert(typeId, memberId, desc, System.currentTimeMillis(),
                                detail = "${s}小于0时应该存在负积分，${s}=${p.toPlainString()}，负积分=${(-negativePoints).toPlainString()}",
                                bid = bid
                            )
                            return@submit
                        }
                        if (negativePoints.compareTo(p.abs()) != 0) {
                            saveAlert(typeId, memberId, desc, System.currentTimeMillis(),
                                detail = "${s}小于0时${s}不等于负积分值，${s}=${p.toPlainString()}，负积分=${(-negativePoints).toPlainString()}",
                                bid = bid
                            )
                            return@submit
                        }
                        val validSum = sumValidStatement(typeId, memberId, dateTime)
                        if (validSum > BigDecimal.ZERO) {
                            saveAlert(typeId, memberId, desc, System.currentTimeMillis(),
                                detail = "${s}小于0时不应该存在有效积分值，${s}=${p.toPlainString()}，有效积分=${validSum.toPlainString()}",
                                bid = bid
                            )
                            return@submit
                        }
                    }
                    else -> {
                        // 零积分不应该有值
                        val desc = "${s}等于0时相关数据状态不符合预期"
                        val negativePoints = sumNegativeStatement(typeId, memberId)
                        if (negativePoints > BigDecimal.ZERO) {
                            saveAlert(typeId, memberId, desc, System.currentTimeMillis(),
                                detail = "${s}等于0时不应该存在负积分，负积分=${(-negativePoints).toPlainString()}",
                                bid = bid
                            )
                            return@submit
                        }
                        val validSum = sumValidStatement(typeId, memberId, dateTime)
                        if (validSum > BigDecimal.ZERO) {
                            saveAlert(typeId, memberId, desc, System.currentTimeMillis(),
                                detail = "${s}等于0时有效积分不应该有值，有效积分=${validSum.toPlainString()}",
                                bid = bid
                            )
                            return@submit
                        }
                    }
                }
            } catch (e: Exception) {
                logger.error("积分账户值检查任务失败", e)
            }
        }
    }


    // 删除过期数据
    @Synchronized
    private fun doTypeFTask(t: T) {
        executor.execute {
            try {
                val date = ZonedDateTime.now().minusYears(1).toEpochMilli()
                runCatching {
                    connection().use {
                        it.prepareStatement("delete from t_orders where created_at < $date").use { statement -> statement.execute() }
                    }
                }
                runCatching {
                    connection().use {
                        it.prepareStatement("delete from t_changelog where created_at < $date").use { statement -> statement.execute() }
                    }
                }
                runCatching {
                    connection().use {
                        it.prepareStatement("delete from t_alert where created_at < $date").use { statement -> statement.execute() }
                    }
                }
                runCatching {
                    connection().use {
                        it.createStatement().use { statement ->
                            statement.execute("VACUUM")
                        }
                    }
                }
            } finally {
                addTask(t.copy(executeAt = ZonedDateTime.now().plusDays(7).toEpochMilli()))
            }
        }
    }


    private fun doTypeGTask(t: T) {
        logger.debug("开始执行等级规则检查任务 expectExecuteAt={}", ofEpochMilli(t.executeAt))
        if (!Grade.hasNextSequenceEvent()) {
            saveAlert("-", "-", "新的等级过期任务未生成", System.currentTimeMillis())
        }
        val rules = Grade.selectGradeRules()
        for (rule in rules) {
            rule.gradeHierarchyName = PlanC.findHierarchyName(rule.gradeHierarchyId)
            if (rule.groupName.isNullOrEmpty()) {
                rule.groupName = "'以上都不满足的'的降级规则"
            }
            var b = false
            if (rule.validTimeType == "UNCHANGE") {
                if (rule.gradeEffectForever == true || rule.changeTimeValue > 0 || rule.endDayTime > 0 || rule.endMonthTime > 0 || rule.endYearTime > 0) {
                    b = true
                }
            }
            if (rule.validTimeType == "CHANGE" || rule.validTimeType == "RESET") {
                if (rule.gradeEffectForever == false && rule.changeTimeValue <= 0 && rule.endDayTime <= 0 && rule.endMonthTime <= 0 && rule.endYearTime <= 0) {
                    b = true
                }
            }
            if (b) {
                saveAlert(rule.gradeHierarchyId.toString(), "-", "存在不符合预期的等级规则数据", System.currentTimeMillis(), detail = rule.toString())
            }
        }
    }


    private fun sumNegativeStatement(typeId: String, memberId: String): BigDecimal {
        val sql = """
            select sum(point) as sumPoints
            from data.loyalty.member.account.NegativeStatement${typeId}
            where memberId = :memberId
        """.trimIndent()
        Dataapi.sdk.use {
            val response = it.execute(sql, mapOf("memberId" to memberId))
            return (response.data?.firstOrNull() as? Map<*, *>)?.get("sumPoints")?.toString()?.toBigDecimal() ?: BigDecimal.ZERO
        }
    }


    private fun sumValidStatement(typeId: String, memberId: String, date: ZonedDateTime): BigDecimal {
        val sql = """
            select sum(point) as sumPoints
            from data.loyalty.member.account.ValidStatement${typeId}
            where memberId = :memberId and fromStatus not in ('OPEN_FROZE', 'SPECIAL_FROZE') and overdueDate >= :now
        """.trimIndent()
        Dataapi.sdk.use {
            val response = it.execute(sql, mapOf("memberId" to memberId, "now" to date))
            return (response.data?.firstOrNull() as? Map<*, *>)?.get("sumPoints")?.toString()?.toBigDecimal() ?: BigDecimal.ZERO
        }
    }

    data class Alert(
        val typeId: String,
        val memberId: String,
        val name: String,
        val timestamp: Long,
        val detail: String = "",
        val bid: String? = null,
        val v: BigDecimal? = null
    )

    private fun saveAlert(typeId: String, memberId: String, name: String, timestamp: Long, detail: String = "", bid: String? = null, v: BigDecimal? = null) {
        alertBlockingQueue.put(Alert(typeId, memberId, name, timestamp, detail, bid, v))
    }

    private fun saveAlert0(a: Alert) {
        val (planId, planName, _, typeName) =
            if (a.typeId == "-") listOf("-", "-", "-", "-")
            else ((findP(a.typeId.toLong()) ?: findH(a.typeId.toLong())) ?: listOf("-", "-", "-", "-"))
        val connection = connection()
        connection.autoCommit = false
        try {
            val sql = "INSERT INTO t_alert (bid, plan_id, plan_name, type_id, type_name, member_id, name, date, detail, created_at) VALUES (?, ?,?,?,?, ?, ?, ?, ?, ?)"
            connection.prepareStatement(sql).use { statement ->
                statement.setString(1, a.bid)
                statement.setString(2, planId)
                statement.setString(3, planName)
                statement.setString(4, a.typeId)
                statement.setString(5, typeName)
                statement.setString(6, a.memberId)
                statement.setString(7, a.name)
                statement.setString(8, ZonedDateTime.ofInstant(Instant.ofEpochMilli(a.timestamp), ZoneId.systemDefault()).toString())
                statement.setString(9, a.detail)
                statement.setLong(10, a.timestamp)
                statement.execute()
            }
        } catch (e: Exception) {
            connection.rollback()
            throw e
        } finally {
            connection.autoCommit = true
            connection.close()
        }
        var str = "name = ${a.name}, "
        if (planId != "-") str += "planId = $planId, "
        if (planName != "-") str += "planName = $planName, "
        if (a.typeId != "-") str += "typeId = ${a.typeId}, "
        if (typeName != "-") str += "typeName = $typeName, "
        if (a.memberId != "-") str += "memberId = ${a.memberId}, "
        str += "date = ${ZonedDateTime.ofInstant(Instant.ofEpochMilli(a.timestamp), ZoneId.systemDefault())}"
        if (a.detail.isNotBlank()) str += ", detail = ${a.detail}"
        if (a.bid != null) str += ", bid = ${a.bid}"
        logger.warn("告警: $str")
        val strategy = Property.getSysOrEnv("risk.control.strategy", "BLACKLIST")
        if (a.name.contains("积分账户") && a.name.contains("上限") && a.typeId != "-" && a.memberId != "-" && strategy == "BLACKLIST") {
            kotlin.runCatching { Risks.addSpecialList(a.typeId.toLong(), a.memberId, a.name) }
        }
    }

    private fun ofEpochMilli(epochMilli: Long): ZonedDateTime {
        return ZonedDateTime.ofInstant(Instant.ofEpochMilli(epochMilli), ZoneId.systemDefault())
    }


    private operator fun Int?.compareTo(other: Int): Int {
        val v = this ?: 0
        return v.compareTo(other)
    }

}