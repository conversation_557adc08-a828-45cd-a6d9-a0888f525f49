package com.shuyun.loyalty.monitoring

import com.fasterxml.jackson.annotation.JsonAnyGetter
import com.fasterxml.jackson.annotation.JsonAnySetter
import com.shuyun.loyalty.sdk.Dataapi.sdk
import com.shuyun.loyalty.sdk.Vers
import java.math.BigDecimal
import java.time.LocalDate

class Points {

    private val _holder = LinkedHashMap<String, Any?>()

    companion object {
        private const val POINT_ACCOUNT_TYPE_ID = "pointAccountTypeId"
        private const val MEMBER_ID = "memberId"

        fun getLatestPoints(typeId: Long, memberId: String, now: LocalDate = LocalDate.now()): Pair<BigDecimal, Boolean> {
            return Points().apply {
                put(POINT_ACCOUNT_TYPE_ID, typeId)
                put(MEMBER_ID, memberId)
            }.getLatestPoints(now)
        }
    }

    val pointAccountTypeId get(): Int = _holder[POINT_ACCOUNT_TYPE_ID]?.toString()?.toInt() ?: throw NoSuchElementException(POINT_ACCOUNT_TYPE_ID)
    val memberId get(): String = _holder[MEMBER_ID]?.toString() ?: throw NoSuchElementException(MEMBER_ID)

    fun get(key: String): Any? {
        return _holder[key]
    }

    @JsonAnyGetter
    fun all(): Map<String, Any?> {
        return _holder
    }

    @JsonAnySetter
    fun put(key: String, value: Any?) {
        _holder[key] = value
    }


    @Suppress("UNCHECKED_CAST")
    fun getLatestPoints(now: LocalDate = LocalDate.now()): Pair<BigDecimal, Boolean> {
        if (!Vers.isHigherLoyaltyVersion) {
            val sql = """
                SELECT point,openSegmentFlag 
                FROM data.loyalty.member.account.Point${pointAccountTypeId} 
                WHERE memberId = :memberId 
                LIMIT 1 
            """.trimIndent()
            val param = mapOf("memberId" to memberId)
            val (point, openSegmentFlag) = sdk.use { x ->
                val data = x.execute(sql, param).data.firstOrNull()
                val row = data as Map<String, Any?>
                val point = row["point"]?.toString()?.toBigDecimal() ?: BigDecimal.ZERO
                val openSegmentFlag = row["openSegmentFlag"]?.toString()?.toBoolean() == true
                point to openSegmentFlag
            }
            if (!openSegmentFlag) {
                return point to false
            }
        }
        val sql = if (Vers.isHigherLoyaltyVersion) {
            """
                SELECT point,expireDate 
                FROM data.loyalty.member.account.PointSegment${pointAccountTypeId} 
                WHERE memberId = :memberId AND ((expireDate >= :expireDate AND point > 0) OR (expireDate = :longTermOverdueDate AND point <= 0))
                ORDER BY expireDate ASC 
                LIMIT 1 
            """.trimIndent()
        } else {
            """
                SELECT point,expireDate 
                FROM data.loyalty.member.account.PointSegment${pointAccountTypeId}  
                WHERE memberId = :memberId AND ((expireDate >= :expireDate AND point > 0) OR expireDate IS NULL) 
                ORDER BY expireDate IS NULL,expireDate ASC 
                LIMIT 1
            """.trimIndent()
        }

        val param = mapOf(
            "memberId" to memberId,
            "expireDate" to now,
            "longTermOverdueDate" to "3000-12-12"
        )
        sdk.use { x ->
            return (x.execute(sql, param).data.firstOrNull()?.let {
                val row = it as Map<String, Any?>
                row["point"]?.toString()?.toBigDecimal()
            } ?: BigDecimal.ZERO) to true
        }
    }



    fun persist(p: BigDecimal) {
        val connection = Sqlite.connection()
        connection.autoCommit = false
        try {
            val sql0 = "UPDATE t_points SET points = ?, modified_at = ? WHERE type_id = ? AND member_id = ?"
            connection.prepareStatement(sql0).use { statement ->
                statement.setBigDecimal(1, p)
                statement.setLong(2, System.currentTimeMillis())
                statement.setInt(3, pointAccountTypeId)
                statement.setString(4, "-")
                statement.execute()
            }
            val sql1 = """
                INSERT INTO t_points (type_id,member_id,points,modified_at,created_at)
                VALUES (?,?,?,?,?)
                ON CONFLICT (type_id,member_id) DO UPDATE SET 
                points = EXCLUDED.points, modified_at = EXCLUDED.modified_at
            """.trimIndent()
            connection.prepareStatement(sql1).use { statement ->
                statement.setInt(1, pointAccountTypeId)
                statement.setString(2, memberId)
                statement.setBigDecimal(3, p)
                statement.setLong(4, System.currentTimeMillis())
                statement.setLong(5, System.currentTimeMillis())
                statement.execute()
            }
            connection.commit()
        } catch (e: Exception) {
            connection.rollback()
            throw e
        } finally {
            connection.autoCommit = true
            connection.close()
        }
    }
}