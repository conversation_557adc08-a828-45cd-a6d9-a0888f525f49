package com.shuyun.loyalty.monitoring

import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Property
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.time.Duration
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.*
import javax.annotation.PostConstruct
import kotlin.concurrent.thread


@Component
class Kafka {

    private val logger = LoggerFactory.getLogger(Kafka::class.java)

    @PostConstruct
    fun start() {
        thread {
            while (true) {
                if (!PlanC.initialized.get()) {
                    Thread.sleep(1000)
                    continue
                }
                break
            }
            logger.info("启动kafka消费者")
            consume()
        }
    }


    private fun consume() {
        val env = GlobalContext.environment()
        val props = Properties()
        props["bootstrap.servers"] = Property.getSysOrEnv("kafka.address", "localhost:9092")
        props.setProperty("group.id", "loyalty-monitoring_$env")
        props.setProperty("enable.auto.commit", "false")
        props.setProperty("max.poll.records", "10")
        props.setProperty("auto.offset.reset", "latest")
        props.setProperty("max.poll.interval.ms", "600000")
        props.setProperty("key.deserializer", "org.apache.kafka.common.serialization.StringDeserializer")
        props.setProperty("value.deserializer", "org.apache.kafka.common.serialization.StringDeserializer")
        thread(isDaemon = true, name = "order-consumer") {
            val consumer = KafkaConsumer<String, String>(props)
            consumer.subscribe(listOf("LOYALTY_NORMAL_${env}", "LOYALTY_GREEN_CHANNEL_${env}"))
            while (true) {
                try {
                    val records: ConsumerRecords<String, String> = consumer.poll(Duration.ofMillis(100))
                    for (record in records) {
                        try {
                            val value = record.value()
                            if (
                                value.contains("event.loyalty.medal.over.dispatch.event", ignoreCase = true) ||
                                value.contains("event.loyalty.grade.over.dispatch.event", ignoreCase = true) ||
                                value.contains("eventHandlerType", ignoreCase = true)) {
                                continue
                            }
                            logger.info("收到订单消息: topic = {}, partition = {}, offset = {}, key = {}, value = {}", record.topic(), record.partition(), record.offset(), record.key(), value)
                            val message = Json.parse<Order>(value)
                            if (message.validate()) {
                                if (message.memberId.isNullOrEmpty() || message.traceId.isNullOrEmpty()) {
                                    if (record.topic() == "LOYALTY_GREEN_CHANNEL_${env}") {
                                        logger.error("订单消息中缺少关键字段值: memberId={} traceId={} {}", message.memberId, message.traceId, value)
                                    } else {
                                        continue
                                    }
                                }
                                withRetry { message.persist() }
                            }
                        } catch (e: Exception) {
                            logger.error("处理消息失败 {}", record.value(), e)
                        }
                    }
                    consumer.commitSync()
                } catch (e: Exception) {
                    logger.error("kafka 出现错误", e)
                    Thread.sleep(5000)
                }
            }
        }

        thread(isDaemon = true, name = "points-change-consumer") {
            val consumer = KafkaConsumer<String, String>(props)
            consumer.subscribe(listOf("${env}_LOYALTY_HTTP_REQUEST_ASYNC_PROCESS"))
            while (true) {
                try {
                    val records: ConsumerRecords<String, String> = consumer.poll(Duration.ofMillis(100))
                    if (records.isEmpty) {
                        continue
                    }
                    val localDate = LocalDate.now()
                    for (record in records) {
                        try {
                            val value = record.value()
                            val points = Json.parse<Points>(value)
                            val (p, _) = points.getLatestPoints(localDate)
                            logger.info("收到账户积分变化消息: topic = {}, partition = {}, offset = {}, key = {}, value = {} p = {}", record.topic(), record.partition(), record.offset(), record.key(), value, p.toPlainString())
                            withRetry { points.persist(p) }
                        } catch (e: Exception) {
                            logger.error("处理消息失败 {}", record.value(), e)
                        }
                    }
                    consumer.commitSync()
                } catch (e: Exception) {
                    logger.error("kafka 出现错误", e)
                    Thread.sleep(5000)
                }
            }
        }


        thread(isDaemon = true, name = "points-record-consumer") {
            val consumer = KafkaConsumer<String, String>(props)
            consumer.subscribe(listOf("LOYALTY_POINT_NOTIFY_${env}"))
            while (true) {
                try {
                    val records: ConsumerRecords<String, String> = consumer.poll(Duration.ofMillis(100))
                    if (records.isEmpty) {
                        continue
                    }
                    for (record in records) {
                        try {
                            val value = record.value()
                            logger.info("积分变更记录消息: topic = {}, partition = {}, offset = {}, key = {}, value = {}", record.topic(), record.partition(), record.offset(), record.key(), value)
                            val map = Json.parse<Map<String, Any?>>(value)
                            val recordId = map["id"].toString()
                            val typeId = map["pointAccountId"].toString()
                            val memberId = map["memberId"].toString()
                            val traceId = map["traceId"].toString()
                            val key = map["key"].toString()
                            val points = map["changePoint"].toString().toBigDecimal()
                            val signedPoint = map["signedPoint"].toString().toBigDecimal()
                            val desc = map["desc"].toString()
                            val recordType = map["recordType"].toString()
                            if (recordType == "TIMER" || recordType == "EXPIRE") {
                                continue
                            }
                            val recordTime = try { Json.parse<ZonedDateTime>(map["recordTime"].toString()) } catch (_: Exception) { ZonedDateTime.parse(map["recordTime"].toString()) }
                            val data = listOf(recordId, typeId, memberId, traceId, key, points.toPlainString(), signedPoint.toPlainString(), desc, recordTime.toString())
                            val t = Tasks.T(
                                id = "x",
                                type = "TYPE_D",
                                payload = data.joinToString("##"),
                                executeAt = System.currentTimeMillis()
                            )
                            Tasks.addTask(t)
                        } catch (e: Exception) {
                            logger.error("处理消息失败 {}", record.value(), e)
                        }
                    }
                    consumer.commitSync()
                } catch (e: Exception) {
                    logger.error("kafka 出现错误", e)
                    Thread.sleep(5000)
                }
            }
        }

    }

    private fun withRetry(block: () -> Unit) {
        var retry = 0
        while (true) {
            try {
                block()
                break
            } catch (e: Exception) {
                if (retry++ > 3) {
                    throw e
                }
                logger.warn("出现异常 {} ，重试第 {} 次", e.message, retry)
                Thread.sleep(1000)
            }
        }
    }
}