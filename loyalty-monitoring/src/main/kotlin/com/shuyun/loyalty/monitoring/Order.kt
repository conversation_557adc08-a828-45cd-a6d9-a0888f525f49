package com.shuyun.loyalty.monitoring

import com.fasterxml.jackson.annotation.JsonAnyGetter
import com.fasterxml.jackson.annotation.JsonAnySetter
import com.shuyun.loyalty.sdk.Json

class Order {

    private val _holder = LinkedHashMap<String, Any?>()

    companion object {
        private const val FQN = "fqn"
        private const val KEY = "key"
        const val OCCURRENCE_TS = "occurrenceTs"
        const val DETECTION_TS = "detectionTs"
    }


    fun get(key: String): Any? {
        return _holder[key]
    }

    @JsonAnyGetter
    fun all(): Map<String, Any?> {
        return _holder
    }

    @JsonAnySetter
    fun put(key: String, value: Any?) {
        _holder[key] = value
    }

    private val fqn get(): String = _holder[FQN]?.toString() ?: throw NoSuchElementException(FQN)

    private val key get(): String = _holder[KEY]?.toString() ?: throw NoSuchElementException(KEY)

    private val occurrenceTs get(): Long {
        return _holder[OCCURRENCE_TS]?.toString()?.toLong() ?: throw NoSuchElementException(OCCURRENCE_TS)
    }

    private val detectionTs get(): Long {
        return _holder[DETECTION_TS]?.toString()?.toLong() ?: throw NoSuchElementException(DETECTION_TS)
    }

    //private val occurrenceZonedDateTime get(): ZonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(occurrenceTs), ZoneId.systemDefault())


    val memberId get(): String? {
        val eventType = PlanC.getEventType(fqn)
        return eventType?.referencePath?.let {
            val m = Json.parse<HashMap<String, String>>(it)
            val k = m[eventType.subjectFqn]
            k?.let { get(k)?.toString() }
        }
    }

    val traceId get(): String? {
        return PlanC.getEventType(fqn)?.originalOrderPath?.let { get(it)?.toString() }
    }

    private fun has(key: String): Boolean {
        return _holder.containsKey(key)
    }

    fun validate(): Boolean {
        if (!has(FQN) || !has(KEY) || !has(OCCURRENCE_TS) || !has(DETECTION_TS)) {
            return false
        }
        if (fqn.isBlank() || key.isBlank() || occurrenceTs <= 0 || detectionTs <= 0) {
            return false
        }
        return true
    }


    override fun toString(): String {
        return Json.toJson(this)
    }


    fun persist() {
        val connection = Sqlite.connection()
        try {
            connection.autoCommit = false
            val sql = "insert into t_orders (member_id, trace_id, payload, fqn, k, detected_at, occurred_at, created_at) values (?, ?, ?, ?, ?, ?, ?, ?)"
            connection.prepareStatement(sql).use { statement ->
                statement.setString(1, memberId)
                statement.setString(2, traceId)
                statement.setString(3, Json.toJson(_holder))
                statement.setString(4, fqn)
                statement.setString(5, key)
                statement.setLong(6, detectionTs)
                statement.setLong(7, occurrenceTs)
                statement.setLong(8, System.currentTimeMillis())
                statement.execute()
            }
            connection.commit()
        } catch (e: Exception) {
            connection.rollback()
            throw e
        } finally {
            connection.autoCommit = true
            connection.close()
        }
    }
}