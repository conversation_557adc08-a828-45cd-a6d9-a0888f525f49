package com.shuyun.loyalty.monitoring

import com.shuyun.dm.api.domain.ExtensionMeta
import com.shuyun.dm.metadata.sdk.MetadataSdkFactory
import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.lite.client.Passport
import com.shuyun.lite.client.PassportClientFactory
import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.sdk.Dataapi
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.sdk.utcStr
import org.slf4j.LoggerFactory
import java.time.ZonedDateTime
import java.util.concurrent.atomic.AtomicInteger

object Risks {

    private val logger = LoggerFactory.getLogger(Risks::class.java)

    private val specialListSubjectMap = HashMap<String, String>()

    private const val PREFIX = "loyalty-monitoring-"

    private const val GROUP = "bmyjv_zhongchengduxitongfengkong"

    private val ID = AtomicInteger(999999)

    // 创建特殊名单主题
    fun createSpecialListSubject(subjectFqn: String) {
        val fqn = "${PREFIX}${subjectFqn}"
        Mysql.sysDS.connection.use { connection ->
            // 先查询是否已经存在
            val sql = "SELECT `id` FROM `ccms_checklist_subject` WHERE `fqn` = ?"
            connection.prepareStatement(sql).use { statement ->
                statement.setString(1, fqn)
                statement.executeQuery().use { rs ->
                    if (rs.next()) {
                        logger.info("已经存在ID为${rs.getLong("id")}的主题${fqn}")
                        specialListSubjectMap[fqn] = rs.getLong("id").toString()
                        return
                    }
                }
            }

            logger.info("创建主题${fqn}")
            val id = ID.incrementAndGet().toString()
            val insert = """
                REPLACE INTO `ccms_checklist_subject` 
                (`id`, `subject_name`, `fqn`, `creator`, `updator`, `remark`, `create_time`, `update_time`, `is_redirect`) VALUES 
                (?, '忠诚度风控特殊名单${id}', '${fqn}', 'loyalty-monitoring', 'loyalty-monitoring', '忠诚度风控特殊名单', ?, ?, x'00')
            """.trimIndent()
            connection.prepareStatement(insert).use { statement ->
                statement.setString(1, id)
                statement.setTimestamp(2, java.sql.Timestamp(System.currentTimeMillis()))
                statement.setTimestamp(3, java.sql.Timestamp(System.currentTimeMillis()))
                val affectedRows = statement.executeUpdate()
                if (affectedRows == 0) {
                    return
                }
                specialListSubjectMap[fqn] = id
            }
        }
    }


    // 创建特殊名单分组
    fun createSpecialGroup() {
        val title = "忠诚度系统风控"
        val id = Property.getSysOrEnv("create.special.group.id", 999999)
        val sql = """
            INSERT IGNORE INTO `check_list_org_rule` 
            (`id`, `rule_type`, `checklist_type`, `checklist_name`, `org_role_id`, `create_at`, `tenant_id`) 
            VALUES (${id}, 'ALL', 'BLACK', '${GROUP}', 'ALL', ?, '${GlobalContext.defTenantId()}');
        """.trimIndent()
        Mysql.riskDS.connection.use { connection ->
            connection.prepareStatement(sql).use { statement ->
                statement.setTimestamp(1, java.sql.Timestamp(System.currentTimeMillis()))
                val r = statement.executeUpdate()
                if (r > 0) {
                    MetadataSdkFactory.createMetadataHttpSdk().addTag("data.prctvmkt.common.Checklist", ExtensionMeta().apply {
                        this.name = GROUP
                        this.title = title
                        this.dictionaryId = 1
                    })
                }
            }
        }
    }



    // 判断新旧特殊名单格式
    private fun isOldSpecialListConfig(): Boolean {
        val sql = """
            select installed_rank 
            from flyway_schema_history 
            where `version` = '2023122510100'
            limit 1
        """.trimIndent()
        var id: Int? = null
        Mysql.loyDS.connection.use { connection ->
            connection.prepareStatement(sql).use { statement ->
                statement.executeQuery().use { rs ->
                    while (rs.next()) {
                        id = rs.getInt("installed_rank")
                    }
                }
            }
        }
        return id == null
    }

    data class SpecialListConfig(
        val versionId: Long? = null,
        val id: Long? = null,
        val name: String? = null,
        val planName: String? = null,
        val subjectName: String? = null,
        val forbiddenConfigString: String? = null,
        val specialListThemeId: String? = null,
        val specialListThemeName: String? = null,
        val columnPath: String? = null,
        val specialListGroupIds: String? = null,
        val specialListGroupNames: String? = null,
        val status: String? = null,
        val publishedTime: String? = null,
        val display: Boolean? = null,
        val disabled: Boolean? = null,
        val planId: Long? = null,
        val subjectId: Long? = null
    )



    // 创建特殊名单配置
    fun createLoyaltySpecialListConf(pointTypeId: Long) {
        var planId = 0L
        var planName = ""
        var subjectId = 0L
        var subjectFqn = ""
        var subjectName = ""
        loop@ for (p in PlanC.plans.values) {
            for (s in p.subjects) {
                for (pointAccountType in s.pointAccountTypes) {
                    if (pointTypeId == pointAccountType.id) {
                        planId = p.id
                        planName = p.name
                        subjectId = s.id
                        subjectFqn = s.fqn
                        subjectName = s.name
                        break@loop
                    }
                }
            }
        }
        if (planId == 0L) {
            logger.warn("积分风控特殊名单配置失败，找不到积分类型${pointTypeId}对应的方案")
            return
        }

        val specialListThemeId = specialListSubjectMap[PREFIX+subjectFqn]
        if (specialListThemeId == null) {
            logger.warn("积分风控特殊名单配置失败，找不到积分类型${pointTypeId}对应的主题")
            return
        }
        val json = if (isOldSpecialListConfig()) {
            """
            [
                {
                    "id": ${pointTypeId},
                    "name": "积分风控特殊名单${pointTypeId}",
                    "type": "POINT",
                    "forbiddenOperations": [ "POINT_SEND", "POINT_RECALCULATE", "POINT_UNFREEZE", "POINT_FREEZE", "POINT_DEDUCT_BY_USE", "POINT_DEDUCT_BY_ABOLISH", "POINT_FREEZE_RECALCULATE" ],
                    "forbiddenPorts": [ "EVENT", "MANUAL", "INTERFACE" ]
                }
            ]
             """
        } else {
            """
            {
                "POINT": [
                    {
                        "id": ${pointTypeId},
                        "name": "积分风控特殊名单${pointTypeId}",
                        "sort": 0,
                        "forbiddens": {
                            "CALC_EVENT": [ "POINT_SEND", "POINT_FREEZE", "POINT_UNFREEZE", "POINT_DEDUCT", "POINT_DEDUCT_BY_ABOLISH" ],
                            "TIME_EVENT": [],
                            "INTERFACE": [ "POINT_SEND", "POINT_DEDUCT_BY_USE", "POINT_FREEZE", "POINT_UNFREEZE", "POINT_SEND_RECYCLE", "POINT_DEDUCT_RECYCLE", "POINT_DEDUCT_BY_ABOLISH" ],
                            "MANUAL": [ "POINT_SEND", "POINT_DEDUCT_BY_USE", "POINT_DEDUCT_BY_ABOLISH", "POINT_FREEZE", "POINT_UNFREEZE" ]
                        }
                    }
                ]
            }
            """
        }

        val c = SpecialListConfig(
            id = 100000 + pointTypeId,
            versionId = 100000 + pointTypeId,
            name = "积分风控特殊名单${pointTypeId}",
            planId = planId,
            planName = planName,
            subjectId = subjectId,
            subjectName = subjectName,
            forbiddenConfigString = Json.toJson(Json.parse<Any>(json)),
            specialListThemeId = specialListThemeId,
            specialListThemeName = "忠诚度风控特殊名单",
            columnPath = "${subjectFqn}.id",
            specialListGroupIds = GROUP,
            specialListGroupNames = "忠诚度系统风控",
            status = "ENABLED",
            disabled = false,
            display = false,
        )

        try {
            Dataapi.sdk.use {
                it.delete("data.loyalty.manager.specialListConfig", c.versionId.toString(), false)
                it.insert("data.loyalty.manager.specialListConfig", mapOf(
                    "versionId" to c.versionId,
                    "id" to c.id,
                    "status" to c.status,
                    "name" to c.name,
                    "specialListThemeId" to c.specialListThemeId,
                    "columnPath" to c.columnPath,
                    "specialListGroupIds" to c.specialListGroupIds,
                    "planId" to c.planId,
                    "planName" to c.planName,
                    "subjectId" to c.subjectId,
                    "subjectName" to c.subjectName,
                    "specialListThemeName" to c.specialListThemeName,
                    "specialListGroupNames" to c.specialListGroupNames,
                    "forbiddenConfigString" to c.forbiddenConfigString,
                    "display" to c.display,
                    "disabled" to c.disabled,
                    "publishedTime" to ZonedDateTime.now().utcStr(),
                    "createTime" to ZonedDateTime.now().utcStr(),
                    "updateTime" to ZonedDateTime.now().utcStr(),
                    "creatorId" to "loyalty-monitoring",
                    "creatorName" to "loyalty-monitoring",
                ), false, false)
            }
        } catch (e: SdkException) {
            if (e.error_code == "151110" || e.error_code == "151111") {
                return
            }
            throw e
        }
    }


    private val token by lazy {
        val appKey = Property.getSysOrEnv("passport.client.http.token.appKey", Property.getSysOrEnv("system.epassport.appKey", Property.getSysOrEnv(GlobalContext.SERVICE_NAME, "")))
        val appSecret = Property.getSysOrEnv("passport.client.http.token.appSecret", Property.getSysOrEnv("system.epassport.appSecret", Property.getSysOrEnv("system.api.secret", "")))
        val grantType = Property.getSysOrEnv("passport.client.http.token.grantType", "app")
        val tenantId = Property.getSysOrEnv("passport.client.http.token.tenantId", Property.getSysOrEnv("system.tenant", ""))
        PassportClientFactory.instance().token(Passport.TokenRequest.of(appKey, appSecret, grantType, tenantId))
    }


    // 添加特殊名单
    fun addSpecialList(pointTypeId: Long, memberId: String, remark: String) {
        var subjectFqn = ""
        loop@ for (p in PlanC.plans.values) {
            for (s in p.subjects) {
                for (pointAccountType in s.pointAccountTypes) {
                    if (pointTypeId == pointAccountType.id) {
                        subjectFqn = s.fqn
                        break@loop
                    }
                }
            }
        }
        if (subjectFqn.isEmpty()) {
            return
        }

        val body = """
            {
                "checklistType": "BLACK",
                "tenantId": "${GlobalContext.defTenantId()}",
                "groupId": "$GROUP",
                "customer": "$memberId",
                "fqn": "$PREFIX$subjectFqn",
                "remark": "$remark"
            }
        """.trimIndent()
        HttpC.request(
            targetServiceName = "risk-control",
            targetServerVersion = "v1",
            requestPath = "/api/checklist",
            method = "PUT",
            body = body,
            headerBuilder = {
                val bearerToken = if (token.startsWith("Bearer ")) token else "Bearer $token"
                it.add("authorization", bearerToken)
            }
        ).whenComplete { f, e ->
            if (e != null) {
                logger.error("积分风控自动添加会员到黑名单异常", e)
                return@whenComplete
            }
            if (f == false) {
                logger.warn("积分风控自动添加会员到黑名单失败")
                return@whenComplete
            }
            logger.info("积分风控自动添加会员到黑名单成功 pointTypeId=$pointTypeId memberId=$memberId remark=$remark")
        }
    }
}