package com.shuyun.loyalty.monitoring

import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.monitoring.Tasks.T
import com.shuyun.loyalty.sdk.Dataapi.sdk
import com.shuyun.loyalty.sdk.toEpochMilli
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Plans
import com.shuyun.loyalty.sdk.Plans.APlan
import com.shuyun.loyalty.sdk.Plans.ASubjectEventType
import com.shuyun.loyalty.sdk.Property
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.concurrent.thread


@Suppress("UNCHECKED_CAST")
object PlanC {
    val plans = ConcurrentHashMap<Long, APlan>()
    private val notExistIds = mutableSetOf<Long>()
    val initialized = AtomicBoolean(false)
    private val logger = org.slf4j.LoggerFactory.getLogger(PlanC::class.java)

    fun init() {
        thread(isDaemon = true, name = "plan-init") {
            while (true) {
                try {
                    load()
                } catch (e: Exception) {
                    logger.warn("加载计划失败 {}", e.message)
                }
                TimeUnit.SECONDS.sleep(30)
            }
        }
    }


    private fun load() {
        val list = Plans.findPlans()
        list.forEach { plan -> plans[plan.id] = plan }
        if (!initialized.get()) {
            val pointsMap = mutableMapOf<Long, BigDecimal>()
            val notConfigLimitRuleHierarchyIds = mutableSetOf<Long>()
            val bid = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"))
            plans.values.forEach { p ->
                p.subjects.forEach { s ->
                    s.pointAccountTypes.forEach { p ->
                        if (p.sendLimitRule.isNullOrEmpty() || Json.parse<List<Map<String, Any>>>(p.sendLimitRule!!).isEmpty()) {
                            notConfigLimitRuleHierarchyIds.add(p.id)
                        }
                        sdk.use {
                            try {
                                val sql = "select sum(point) as points from data.loyalty.member.account.Point${p.id}"
                                val result = it.execute(sql, mapOf()).data.firstOrNull() as? Map<String, Any?>
                                val points = result?.get("points")?.toString()?.toBigDecimal() ?: BigDecimal.ZERO
                                pointsMap[p.id] = points
                            } catch (e: SdkException) {
                                if (e.error_code == "152201") {
                                    // 模型不存在
                                    notExistIds.add(p.id)
                                }
                            }
                        }
                    }
                }
            }

            Risks.createSpecialGroup()
            plans.values.flatMap { it.subjects }.map { it.fqn }.toSet().forEach { Risks.createSpecialListSubject(it) }
            val connection = Sqlite.connection()
            connection.autoCommit = false
            try {
                val map = LinkedHashMap<Long, BigDecimal>()
                Property.getSysOrEnv("higher.point.priority.ids")?.split(",")?.map { it.toLong() }?.forEach { id ->
                    val points = pointsMap.remove(id)
                    if (points != null) {
                        map[id] = points
                    }
                }
                map.putAll(pointsMap)
                for ((id, points) in map) {
                    Risks.createLoyaltySpecialListConf(id)
                    val sql = """
                        INSERT INTO t_points (type_id,member_id,points,modified_at,created_at) 
                        VALUES (?,?,?,?,?) 
                        ON CONFLICT (type_id,member_id) DO UPDATE SET points = EXCLUDED.points
                    """.trimIndent()
                    connection.prepareStatement(sql).use { statement ->
                        statement.setLong(1, id)
                        statement.setString(2, "-")
                        statement.setBigDecimal(3, points)
                        statement.setLong(4, System.currentTimeMillis())
                        statement.setLong(5, System.currentTimeMillis())
                        statement.execute()
                    }
                    val executeAt = System.currentTimeMillis()
                    Tasks.addTask(T("x", "TYPE_C", "$id##$bid##$executeAt##", executeAt))
                }
                for (notConfigLimitRuleHierarchyId in notConfigLimitRuleHierarchyIds) {
                    val (planId, planName, typeId, typeName) = findP(notConfigLimitRuleHierarchyId) ?: continue
                    val sql = "INSERT INTO t_alert (bid, plan_id, plan_name, type_id, type_name, member_id, name, date, created_at) VALUES (?,?, ?, ?, ?, ?, ?, ?, ?)"
                    connection.prepareStatement(sql).use { statement ->
                        statement.setString(1, bid)
                        statement.setString(2, planId)
                        statement.setString(3, planName)
                        statement.setString(4, typeId)
                        statement.setString(5, typeName)
                        statement.setString(6, "-")
                        statement.setString(7, "积分限额规则未配置")
                        statement.setString(8, ZonedDateTime.now().toString())
                        statement.setLong(9, System.currentTimeMillis())
                        statement.execute()
                    }
                }
                connection.commit()
                System.getProperties().forEach {
                    if (it.key.toString().contains("积分")) {
                        logger.info("${it.key} = ${it.value}")
                    }
                }
                initialized.set(true)
            } catch (e: Exception) {
                connection.rollback()
                throw e
            } finally {
                connection.autoCommit = true
                connection.close()
            }
            Tasks.addTask(T("x", "TYPE_F", "VACUUM", ZonedDateTime.now().plusDays(7).toEpochMilli()))
            Tasks.addTask(T("x", "TYPE_G", "GRADE", System.currentTimeMillis()))
        }
    }


    fun findP(typeId: Long): List<String>? {
        for (p in plans.values) {
            for (s in p.subjects) {
                for (pointAccountType in s.pointAccountTypes) {
                    if (typeId == pointAccountType.id) {
                        return listOf(p.id.toString(), p.name, typeId.toString(), pointAccountType.name)
                    }
                }
            }
        }
        return null
    }

    fun findH(typeId: Long): List<String>? {
        for (p in plans.values) {
            for (s in p.subjects) {
                for (gradeHierarchy in s.gradeHierarchies) {
                    if (typeId == gradeHierarchy.id) {
                        return listOf(p.id.toString(), p.name, typeId.toString(), gradeHierarchy.name)
                    }
                }
            }
        }
        return null
    }


    fun findHierarchyName(hierarchyId: Long): String? {
        for (p in plans.values) {
            for (s in p.subjects) {
                for (hierarchy in s.gradeHierarchies) {
                    if (hierarchyId == hierarchy.id) {
                        return hierarchy.name
                    }
                }
            }
        }
        return null
    }

    fun getEventType(eventStream: String): ASubjectEventType? {
        for (p in plans.values) {
            for (s in p.subjects) {
                for (e in s.subjectEventTypes) {
                    if (e.eventStream == eventStream) {
                        e.subjectFqn = s.fqn
                        return e
                    }
                }
            }
        }
        return null
    }
}