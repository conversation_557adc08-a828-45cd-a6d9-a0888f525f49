package com.shuyun.loyalty.monitoring

import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.sdk.Property
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource

object Mysql {

    private fun dsConf(): HikariConfig {
        val config = HikariConfig()
        config.jdbcUrl = Property.getSysOrEnv("database.url")
        config.username = Property.getSysOrEnv("database.username")
        config.password = Property.getSysOrEnv("database.password")
        var classDriver = Property.getSysOrEnv("database.driverClass", "com.mysql.cj.jdbc.Driver")
        if (classDriver == "com.mysql.jdbc.Driver") {
            classDriver = "com.mysql.cj.jdbc.Driver"
        }
        config.driverClassName = classDriver
        config.addDataSourceProperty("cachePrepStmts", "true")
        config.addDataSourceProperty("prepStmtCacheSize", "250")
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048")
        return config
    }

    val loyDS: HikariDataSource by lazy {
        val ds = HikariDataSource(dsConf())
        Runtime.getRuntime().addShutdownHook(Thread { ds.close() })
        ds
    }


    val sysDS: HikariDataSource by lazy {
        val config = dsConf()
        val old = "${GlobalContext.defTenantId()}_loyalty_manager"
        val new = "${GlobalContext.defTenantId()}_system_configuration"
        config.jdbcUrl = config.jdbcUrl!!.replace(old, new)
        val ds = HikariDataSource(config)
        Runtime.getRuntime().addShutdownHook(Thread { ds.close() })
        ds
    }

    val riskDS: HikariDataSource by lazy {
        val config = dsConf()
        val old = "${GlobalContext.defTenantId()}_loyalty_manager"
        val new = "${GlobalContext.defTenantId()}_risk_control"
        config.jdbcUrl = config.jdbcUrl!!.replace(old, new)
        val ds = HikariDataSource(config)
        Runtime.getRuntime().addShutdownHook(Thread { ds.close() })
        ds
    }

}

