package com.shuyun.loyalty

import com.shuyun.lite.client.ConfigurationManagerHolder
import com.shuyun.lite.client.PassportClientFactory
import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.monitoring.PlanC
import com.shuyun.loyalty.monitoring.Sqlite
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication

@SpringBootApplication(proxyBeanMethods = false)
class MonitoringApplication

private val logger = LoggerFactory.getLogger(MonitoringApplication::class.java)

fun main(args: Array<String>) {
    logger.info("服务启动中...")
    runApplication<MonitoringApplication>(*args) {
        ConfigurationManagerHolder.init()
        val passportBuilder = PassportClientFactory.passportBuilder().appKey(GlobalContext.serviceName())
        val passportClient = passportBuilder.build()
        passportClient.registerClient(GlobalContext.serviceName(), GlobalContext.serviceName())
        Sqlite.init()
        PlanC.init()
    }
    logger.info("服务启动完成")
}
