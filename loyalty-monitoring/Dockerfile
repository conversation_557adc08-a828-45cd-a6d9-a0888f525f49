FROM hub.shuyun.com/base/java:zulu-jdk11

WORKDIR /loyalty-monitoring
COPY target/loyalty-monitoring.jar /loyalty-monitoring/loyalty-monitoring.jar

# 将脚本文件复制到项目中
COPY src/scripts/rename-lib.sh /usr/local/bin/rename-lib.sh

# 执行脚本文件，对原始文件中的依赖包进行重命名
RUN sh /usr/local/bin/rename-lib.sh /loyalty-monitoring/loyalty-monitoring.jar

EXPOSE 8080

RUN groupadd -g 1324 shuyunuser && useradd -m -u 1324 -g shuyunuser shuyunuser
RUN chown shuyunuser:shuyunuser -R /loyalty-monitoring

ENTRYPOINT ["java", "-jar", "loyalty-monitoring.jar"]