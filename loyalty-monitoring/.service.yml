kind: "Service"
specVersion: v4
metadata:
  name: loyalty-monitoring
  apiVersion: "v1"
  accessPoint:
    container: basecrm/loyalty-monitoring
  middleware:
    mysql:
      requireSchemas: loyalty_manager
containers:
  - name: basecrm/loyalty-monitoring
    ports:
      - name: web
        protocol: tcp
        targetPort: 0
        containerPort: 8080
profiles:
  - name: default
    cpu: 1
    mem: 2048
    replicas: 1
    containers:
      - name: basecrm/loyalty-monitoring
        cpu: 1
        mem: 2048