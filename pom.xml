<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.shuyun.loyalty4</groupId>
    <artifactId>loyalty-engine</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <description>忠诚度引擎计算服务</description>

    <modules>
        <module>loyalty-sdk</module>
        <module>loyalty-service</module>
        <module>loyalty-manager</module>
        <module>loyalty-stream</module>
        <module>loyalty-analysis</module>
        <module>loyalty-facade</module>
        <module>loyalty-monitoring</module>
    </modules>

    <properties>
        <revision>LP3.25.2</revision>
        <kotlin.version>2.1.20</kotlin.version>
        <kotlinx.version>1.10.2</kotlinx.version>
        <kotlin.code.style>official</kotlin.code.style>
        <kotlin.compiler.incremental>false</kotlin.compiler.incremental>
        <kotlin.compiler.languageVersion>2.1</kotlin.compiler.languageVersion>
        <kotlin.compiler.apiVersion>2.1</kotlin.compiler.apiVersion>
        <kotlin.compiler.jvmTarget>11</kotlin.compiler.jvmTarget>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <maven.deploy.skip>false</maven.deploy.skip>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-framework.version>5.3.39</spring-framework.version>
        <spring-cloud.version>2021.0.9</spring-cloud.version>
        <scala.version>2.12.8</scala.version>
        <jakarta.ws.rs-api.version>3.1.0</jakarta.ws.rs-api.version>


        <pip.version>*********</pip.version>
        <dm.version>1.40.0.RELEASE</dm.version>
        <es-sdk.version>1.0.1</es-sdk.version>
        <lite.version>1.9.5</lite.version>
        <epassport.version>4.13.0-RC1</epassport.version>
        <spectrum.version>3.7.1.RELEASE</spectrum.version>
        <eawf.version>1.8.0-RELEASE</eawf.version>
        <fx-box.version>********</fx-box.version>
        <data-selector.version>3.45.0</data-selector.version>
        <generator-api.version>2.3.0</generator-api.version>
        <druid.version>1.2.24</druid.version>
        <log4j2.version>2.24.3</log4j2.version>
        <logback.version>1.5.18</logback.version>
        <redisson.version>3.46.0</redisson.version>
        <lettuce-core.version>6.5.5.RELEASE</lettuce-core.version>
        <risk-control.version>5.0.31-RELEASE</risk-control.version>
        <system-configuration-api.version>5.0.31-RELEASE</system-configuration-api.version>
        <super-csv.version>2.4.0</super-csv.version>
        <snakeyaml.version>2.0</snakeyaml.version>
        <jetty.version>9.4.57.v20241219</jetty.version>
        <jackson.version>2.16.1</jackson.version>
        <netty.version>4.1.119.Final</netty.version>
        <poi.version>5.4.0</poi.version>
        <kafka.version>3.9.0</kafka.version>
        <mysql-connector-j.version>9.2.0</mysql-connector-j.version>
        <oceanbase-client.version>2.4.11</oceanbase-client.version>
        <oceanbase-support.version>1.0.0.RC1</oceanbase-support.version>
        <flyway.version>9.22.3</flyway.version>
        <guava.version>32.1.3-jre</guava.version>
        <HikariCP.version>5.1.0</HikariCP.version>
        <json.version>20240303</json.version>
        <slf4j.version>2.0.17</slf4j.version>
        <gson.version>2.10.1</gson.version>
        <commons-lang3.version>3.14.0</commons-lang3.version>
        <commons-collections4.version>4.4</commons-collections4.version>
        <sqlite-jdbc.version>3.46.0.0</sqlite-jdbc.version>
        <simpleclient.version>0.16.5-RELEASE</simpleclient.version>
        <aws-java-sdk.version>1.12.782</aws-java-sdk.version>
        <tencentcloud-sdk-java-kms.version>3.1.1218</tencentcloud-sdk-java-kms.version>
        <quartz.version>2.5.0</quartz.version>
        <retrofit.version>2.11.0</retrofit.version>
        <xstream.version>1.4.21</xstream.version>
        <shedlock-spring.version>4.48.0</shedlock-spring.version>
    </properties>

    <dependencyManagement>

        <dependencies>
            <dependency>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-bom</artifactId>
                <version>${jetty.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-bom</artifactId>
                <version>${netty.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.fasterxml.jackson</groupId>
                <artifactId>jackson-bom</artifactId>
                <version>${jackson.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-bom</artifactId>
                <version>${aws-java-sdk.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit-bom</artifactId>
                <version>${retrofit.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.shuyun.air</groupId>
                <artifactId>air-bom</artifactId>
                <version>1.0.2</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlinx</groupId>
                <artifactId>kotlinx-coroutines-bom</artifactId>
                <version>${kotlinx.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>io.opentelemetry</groupId>
                <artifactId>opentelemetry-bom</artifactId>
                <version>1.40.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.shuyun.pip</groupId>
                <artifactId>pip-exception</artifactId>
                <version>${pip.version}</version>
            </dependency>

            <dependency>
                <groupId>org.scala-lang</groupId>
                <artifactId>scala-library</artifactId>
                <version>${scala.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib</artifactId>
                <version>${kotlin.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-stdlib-jdk8</artifactId>
                <version>${kotlin.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-reflect</artifactId>
                <version>${kotlin.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-stdlib</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-stdlib-jdk8</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlinx</groupId>
                <artifactId>kotlinx-coroutines-core</artifactId>
                <version>${kotlinx.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlinx</groupId>
                <artifactId>kotlinx-coroutines-core-jvm</artifactId>
                <version>${kotlinx.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jetbrains.kotlinx</groupId>
                <artifactId>kotlinx-coroutines-slf4j</artifactId>
                <version>${kotlinx.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.kylin.es</groupId>
                <artifactId>es-sdk</artifactId>
                <version>${es-sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.loyalty4</groupId>
                <artifactId>loyalty-service</artifactId>
                <version>${project.version}</version>
            </dependency>


            <dependency>
                <groupId>com.shuyun.dm</groupId>
                <artifactId>dm-metadata-sdk</artifactId>
                <version>${dm.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.dm</groupId>
                <artifactId>dm-api</artifactId>
                <version>${dm.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.dm</groupId>
                <artifactId>dm-sdk-common</artifactId>
                <version>${dm.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.dm</groupId>
                <artifactId>dm-dataapi-sdk</artifactId>
                <version>${dm.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.kylin.crm.epassport</groupId>
                <artifactId>epassport-sdk</artifactId>
                <version>${epassport.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.lite.module</groupId>
                <artifactId>lite-base</artifactId>
                <version>${lite.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.lite.module</groupId>
                <artifactId>lite-passport-sdk</artifactId>
                <version>${lite.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.shuyun.pip</groupId>
                        <artifactId>pip-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.shuyun.lite.module</groupId>
                <artifactId>lite-sdk-base</artifactId>
                <version>${lite.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.lite.module</groupId>
                <artifactId>lite-sdk-core</artifactId>
                <version>${lite.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.lite.module</groupId>
                <artifactId>lite-server-base</artifactId>
                <version>${lite.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.spectrum</groupId>
                <artifactId>spectrum-client</artifactId>
                <version>${spectrum.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.spectrum</groupId>
                <artifactId>client-common</artifactId>
                <version>${spectrum.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.spectrum</groupId>
                <artifactId>client-support</artifactId>
                <version>${spectrum.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuyun.spectrum</groupId>
                <artifactId>configuration-api</artifactId>
                <version>${spectrum.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuyun.spectrum</groupId>
                <artifactId>configuration-client</artifactId>
                <version>${spectrum.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.spectrum</groupId>
                <artifactId>discovery-api</artifactId>
                <version>${spectrum.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.spectrum</groupId>
                <artifactId>discovery-client</artifactId>
                <version>${spectrum.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.spectrum</groupId>
                <artifactId>service-spec</artifactId>
                <version>${spectrum.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.fx</groupId>
                <artifactId>fx-box</artifactId>
                <version>${fx-box.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.csp</groupId>
                <artifactId>sentinel-core</artifactId>
                <version>1.8.6</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.pip</groupId>
                <artifactId>pip-core</artifactId>
                <version>${pip.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuyun.pip</groupId>
                <artifactId>pip-discovery</artifactId>
                <version>${pip.version}</version>
            </dependency>

            <dependency>
                <groupId>com.shuyun.pip</groupId>
                <artifactId>pip-feign-client-starter</artifactId>
                <version>${pip.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuyun.pip</groupId>
                <artifactId>pip-retrofit-starter</artifactId>
                <version>${pip.version}</version>
            </dependency>
            <dependency>
                <groupId>com.shuyun.api.mgmt</groupId>
                <artifactId>api-mgmt-sdk</artifactId>
                <version>1.5.5.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.shuyun</groupId>
                <artifactId>shuyun-support-common</artifactId>
                <version>1.10.0.RELEASE</version>
            </dependency>
            <dependency>
                <groupId>com.shuyun</groupId>
                <artifactId>motor-common</artifactId>
                <version>202001182216</version>
            </dependency>
            <dependency>
                <groupId>com.shuyun.easy</groupId>
                <artifactId>easy-file-storage-core</artifactId>
                <version>2.5.RELEASE</version>
                <exclusions>
                    <exclusion>
                        <artifactId>ant-launcher</artifactId>
                        <groupId>org.apache.ant</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>joda-time</artifactId>
                        <groupId>joda-time</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>okio</artifactId>
                        <groupId>com.squareup.okio</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>4.12.0</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio-jvm</artifactId>
                <version>3.8.0</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okio</groupId>
                <artifactId>okio</artifactId>
                <version>3.8.0</version>
            </dependency>

            <dependency>
                <groupId>com.typesafe</groupId>
                <artifactId>config</artifactId>
                <version>1.4.2</version>
            </dependency>

            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>1.16.0</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.14.0</version>
            </dependency>

            <dependency>
                <groupId>commons-logging</groupId>
                <artifactId>commons-logging</artifactId>
                <version>1.2</version>
            </dependency>

            <dependency>
                <groupId>org.apache.ant</groupId>
                <artifactId>ant</artifactId>
                <version>1.10.13</version>
            </dependency>

            <dependency>
                <groupId>org.objenesis</groupId>
                <artifactId>objenesis</artifactId>
                <version>2.6</version>
                <scope>compile</scope>
            </dependency>


            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.version}</version>
            </dependency>

            <dependency>
                <groupId>org.lz4</groupId>
                <artifactId>lz4-java</artifactId>
                <version>1.8.0</version>
                <scope>runtime</scope>
            </dependency>

            <dependency>
                <groupId>org.xerial.snappy</groupId>
                <artifactId>snappy-java</artifactId>
                <version>1.1.10.5</version>
                <scope>runtime</scope>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.26.1</version>
            </dependency>

            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector-j.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>com.google.protobuf</groupId>
                        <artifactId>protobuf-java</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>joda-time</groupId>
                <artifactId>joda-time</artifactId>
                <version>2.14.0</version>
            </dependency>
            <dependency>
                <groupId>com.typesafe.akka</groupId>
                <artifactId>akka-actor_2.12</artifactId>
                <version>2.5.23</version>
            </dependency>
            <dependency>
                <groupId>org.javassist</groupId>
                <artifactId>javassist</artifactId>
                <version>3.29.2-GA</version>
            </dependency>

            <dependency>
                <groupId>com.google.errorprone</groupId>
                <artifactId>error_prone_annotations</artifactId>
                <version>2.10.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <dependency>
                <groupId>org.checkerframework</groupId>
                <artifactId>checker-qual</artifactId>
                <version>3.37.0</version>
            </dependency>
            <dependency>
                <groupId>com.oceanbase</groupId>
                <artifactId>oceanbase-client</artifactId>
                <version>${oceanbase-client.version}</version>
            </dependency>

            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-core</artifactId>
                <version>${flyway.version}</version>
            </dependency>

            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-mysql</artifactId>
                <version>${flyway.version}</version>
            </dependency>

            <dependency>
                <groupId>jakarta.ws.rs</groupId>
                <artifactId>jakarta.ws.rs-api</artifactId>
                <version>${jakarta.ws.rs-api.version}</version>
            </dependency>


            <dependency>
                <groupId>org.jetbrains</groupId>
                <artifactId>annotations</artifactId>
                <version>23.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${HikariCP.version}</version>
            </dependency>
            <dependency>
                <groupId>org.json</groupId>
                <artifactId>json</artifactId>
                <version>${json.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-collections4</artifactId>
                <version>${commons-collections4.version}</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-api</artifactId>
                <version>${slf4j.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-classic</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>ch.qos.logback</groupId>
                <artifactId>logback-core</artifactId>
                <version>${logback.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>org.xerial</groupId>
                <artifactId>sqlite-jdbc</artifactId>
                <version>${sqlite-jdbc.version}</version>
            </dependency>
            <!-- prometheus The client -->
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient</artifactId>
                <version>${simpleclient.version}</version>
            </dependency>
            <!-- Exposition HTTPServer-->
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_httpserver</artifactId>
                <version>${simpleclient.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_servlet</artifactId>
                <version>${simpleclient.version}</version>
            </dependency>
            <!-- trace and common -->
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_common</artifactId>
                <version>${simpleclient.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_tracer_otel</artifactId>
                <version>${simpleclient.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_tracer_common</artifactId>
                <version>${simpleclient.version}</version>
            </dependency>
            <dependency>
                <groupId>io.prometheus</groupId>
                <artifactId>simpleclient_tracer_otel_agent</artifactId>
                <version>${simpleclient.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.f4b6a3</groupId>
                <artifactId>tsid-creator</artifactId>
                <version>5.2.6</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>1.7.0</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-kotlin</artifactId>
                <version>1.7.0</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-csv</artifactId>
                <version>1.10.0</version>
            </dependency>
            <dependency>
                <groupId>com.github.albfernandez</groupId>
                <artifactId>juniversalchardet</artifactId>
                <version>2.4.0</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-migrations</artifactId>
                <version>3.3.11</version>
            </dependency>
            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>0.9.12</version>
            </dependency>
            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java-kms</artifactId>
                <version>${tencentcloud-sdk-java-kms.version}</version>
            </dependency>
            <dependency>
                <groupId>org.quartz-scheduler</groupId>
                <artifactId>quartz</artifactId>
                <version>${quartz.version}</version>
            </dependency>
            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>${xstream.version}</version>
            </dependency>
            <dependency>
                <groupId>io.lettuce</groupId>
                <artifactId>lettuce-core</artifactId>
                <version>${lettuce-core.version}</version>
            </dependency>

            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-spring</artifactId>
                <version>${shedlock-spring.version}</version>
            </dependency>

            <dependency>
                <groupId>net.javacrumbs.shedlock</groupId>
                <artifactId>shedlock-provider-redis-spring</artifactId>
                <version>${shedlock-spring.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
            <version>${kotlin.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.jetbrains.kotlin</groupId>
                    <artifactId>kotlin-stdlib</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <distributionManagement>
        <repository>
            <id>nexus</id>
            <name>Nexus</name>
            <url>http://ci.yunat.com:8091/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>nexus</id>
            <name>Nexus</name>
            <url>http://ci.yunat.com:8091/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <executions>
                    <execution>
                        <id>compile</id>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <args>
                        <arg>-Xjsr305=strict</arg>
                    </args>
                    <compilerPlugins>
                        <plugin>spring</plugin>
                        <plugin>all-open</plugin>
                        <plugin>no-arg</plugin>
                        <plugin>jpa</plugin>
                        <plugin>sam-with-receiver</plugin>
                    </compilerPlugins>
                    <jvmTarget>${kotlin.compiler.jvmTarget}</jvmTarget>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-noarg</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-sam-with-receiver</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>11</source>
                    <target>${kotlin.compiler.jvmTarget}</target>
                </configuration>
                <executions>
                    <!-- Replacing default-compile as it is treated specially by maven -->
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <!-- Replacing default-testCompile as it is treated specially by maven -->
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>java-compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>java-test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
                <version>2.16.0</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>1.5.0</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <!-- enable flattening -->
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <!-- ensure proper cleanup -->
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>