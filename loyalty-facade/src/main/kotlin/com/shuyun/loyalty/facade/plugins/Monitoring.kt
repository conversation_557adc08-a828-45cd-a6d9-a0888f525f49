package com.shuyun.loyalty.facade.plugins

import com.github.f4b6a3.tsid.Tsid
import com.shuyun.loyalty.facade.model.Constants
import com.shuyun.loyalty.facade.model.Constants.LOYALTY_TRACE_ID
import io.ktor.server.application.*
import io.ktor.server.plugins.callid.*
import io.ktor.server.plugins.calllogging.*
import io.ktor.server.request.*
import io.ktor.util.*
import org.apache.commons.lang3.RandomStringUtils
import java.nio.charset.StandardCharsets
import java.util.zip.CRC32


fun Application.configureMonitoring() {
    install(CallId) {
        retrieve { call ->
            call.request.headers[LOYALTY_TRACE_ID] ?: RandomStringUtils.random(8, Constants.RANDOM_CHAR_DICTIONARY)
        }
    }
    install(CallLogging) {
        val excludeUrls = setOf(
            "/ops/ping",
            "/plan:info"
        )
        filter { call ->
            excludeUrls.none { call.request.path().endsWith(it) }
        }
        callIdMdc(LOYALTY_TRACE_ID)
        disableDefaultColors()
    }

    install(RequestIdPlugin)
}


private val onCallReqIdKey = AttributeKey<Long>("onCallReqId")
private val RequestIdPlugin = createApplicationPlugin(name = "RequestIdPlugin") {
    onCall { call ->
        call.attributes.put(onCallReqIdKey, Tsid.fast().toLong())
    }
    onCallRespond { call ->
        call.callId?.let {
            call.response.headers.append(LOYALTY_TRACE_ID, it)
        }
    }
}

private fun String.crc32ToLong(): Long {
    val crc = CRC32()
    crc.update(this.toByteArray(StandardCharsets.UTF_8))
    return crc.value
}

fun ApplicationCall.getReqId(): Long = attributes.getOrNull(onCallReqIdKey)
    ?: callId?.crc32ToLong() ?: 0
