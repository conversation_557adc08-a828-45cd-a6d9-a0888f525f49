package com.shuyun.loyalty.facade.repository

import com.shuyun.dm.api.metadata.request.GetModelRequest
import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.dm.metadata.sdk.MetadataSdkFactory
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.sdk.Dataapi.sdk
import com.shuyun.loyalty.sdk.Json
import java.math.RoundingMode
import java.time.ZonedDateTime
import java.util.concurrent.CopyOnWriteArrayList
import kotlin.concurrent.thread

object ProgramRepository {

    fun queryPlans(): List<Plan> {
        val ts = ArrayList<Thread>()
        val plans = CopyOnWriteArrayList<Plan>()
        findPlans().forEach { p ->
            val t = thread {
                sdk.use {
                    val subjects = findSubjects(p.planVersionId).map { s ->
                        val pointAccountTypes = findPointAccountTypes(it, p, s)
                        val gradeHierarchies = findGradeHierarchies(it, p, s)
                        val medalHierarchies = findMedalHierarchies(it, p, s)
                        val subjectEventTypes = findSubjectEventTypes(it, s)
                        Subject(s, subjectEventTypes, pointAccountTypes, gradeHierarchies, medalHierarchies)
                    }
                    plans.add(Plan(p, subjects))
                }
            }
            ts.add(t)
        }
        ts.forEach { it.join() }
        return plans.sortedBy { it.plan0.id }
    }


    fun findChangeModes(): List<MetaChangeMode> {
        val sdk = MetadataSdkFactory.createMetadataHttpSdk()
        val request = GetModelRequest().apply { fqn = "data.loyalty.member.enum.ChangeMode" }
        val enums = sdk.getModel(request).enums ?: emptyList()
        return enums.map { MetaChangeMode(it.text, it._i18nPayload["zh-CN"] ?: "") }
    }


    fun findChannelTypes(): List<MetaChannelType> {
        val request = GetModelRequest().apply { fqn = "data.loyalty.member.enum.ChannelType" }
        val enums = MetadataSdkFactory.createMetadataHttpSdk().getModel(request).enums ?: emptyList()
        return enums.map {
            MetaChannelType(
                code = it.text,
                name = it._i18nPayload["zh-CN"] ?: ""
            )
        }
    }


    @Suppress("UNCHECKED_CAST")
    private fun findPlans(): List<Plan0> {
        return sdk.use {
            val planSql = """
                SELECT id, versionId, name, sendLimitRuleSwitch
                FROM data.loyalty.manager.plan 
                WHERE disabled = 0 AND status='PUBLISHED'
            """.trimIndent()
            val response = it.execute(planSql, mapOf())
            val plans = response.data.map { d ->
                val row = d as Map<String, Any?>
                Plan0(
                    id = row["id"].toString().toLong(),
                    planVersionId = row["versionId"].toString().toLong(),
                    name = (row["name"] as? String) ?: "",
                    sendLimitRuleSwitch = row["sendLimitRuleSwitch"]?.let { x -> x as Boolean } ?: false,
                )
            }
            plans
        }

    }


    @Suppress("UNCHECKED_CAST")
    private fun findSubjects(planVersionId: Long): List<Subject0> {
        return sdk.use {
            val subjectSql = """
                SELECT id, versionId, name, dataType as fqn ,mergeFqn ,hasMerge,status
                FROM data.loyalty.manager.subject 
                WHERE planVersionId = :planVersionId 
                AND disabled = 0 
                AND status = 'ENABLED'
            """.trimIndent()
            val subjectResponse = it.execute(subjectSql, mapOf("planVersionId" to planVersionId))
            val subjects = subjectResponse.data.map { s ->
                val row = s as Map<String, Any?>
                Subject0(
                    id = row["id"].toString().toLong(),
                    subjectVersionId = row["versionId"].toString().toLong(),
                    name = row["name"] as String,
                    fqn = row["fqn"] as String,
                    mergeFqn = row["mergeFqn"] as String?,
                    hasMerge =  row["hasMerge"] as Boolean?,
                    status = row["status"] as String?,
                )
            }
            subjects
        }
    }

    @Suppress("UNCHECKED_CAST")
    private fun findSubjectEventTypes(sdk: DataapiWebSocketSdk, subject0: Subject0): List<SubjectEventType> {
        val sql = """
            SELECT `versionId`,`id`, `name`,`eventStream`,`operation`,`operationGrade`,`operationMedal`,`status`
            FROM data.loyalty.manager.eventType 
            WHERE subjectVersionId = :subjectVersionId
            AND disabled = 0 
            AND status = 'ENABLED'
        """.trimIndent()
        val params = mapOf("subjectVersionId" to subject0.subjectVersionId)
        val eventTypeResponse = sdk.execute(sql, params)
        val eventTypes = eventTypeResponse.data.map { e ->
            val row = e as Map<String, Any?>
            SubjectEventType(
                id = row["id"].toString().toLong(),
                versionId = row["versionId"].toString().toLong(),
                name = row["name"] as String?,
                eventStream = row["eventStream"] as String?,
                operation = row["operation"] as String?,
                operationGrade =row["operationGrade"] as String?,
                operationMedal = row["operationMedal"] as String?,
                status =  row["status"] as String?,
            )
        }
        return eventTypes
    }


    @Suppress("UNCHECKED_CAST")
    private fun findPointAccountTypes(sdk: DataapiWebSocketSdk, plan0: Plan0, subject0: Subject0): List<PointAccountType> {
        val pointAccountTypeSql = """
            SELECT 
                id, name, priorityDeduction, negativeStrategy, 
                topLimit, singleTopLimit, sendLimitRule,
                precision, unit, rounding,updateTime
            FROM data.loyalty.manager.pointAccountType 
            WHERE subjectVersionId = :subjectVersionId  
            AND disabled = 0 
            AND status = 'PUBLISHED'
        """.trimIndent()
        val pointAccountTypeResponse = sdk.execute(pointAccountTypeSql, mapOf("subjectVersionId" to subject0.subjectVersionId))
        val defaultDeductionPriorities = listOf(
            DeductionPriority("overdueDate", "EARLIEST", "effectiveDate"),
            DeductionPriority("effectiveDate", "EARLIEST")
        )
        val accountTypes = pointAccountTypeResponse.data.map { a ->
            val row = a as Map<String, Any?>
            var priorityDeduction = row["priorityDeduction"] as String?
            if (priorityDeduction.isNullOrBlank()) {
                priorityDeduction = Json.toJson(defaultDeductionPriorities)
            }
            PointAccountType(
                id = row["id"].toString().toLong(),
                name = row["name"] as String,
                priorityDeduction = priorityDeduction,
                negativeStrategy = row["negativeStrategy"] as String,
                topLimit = row["topLimit"]?.toString()?.toBigDecimal(),
                singleTopLimit = row["singleTopLimit"]?.toString()?.toBigDecimal(),
                sendLimitRule = (row["sendLimitRule"] as String?)?.let { it.ifBlank { null } },
                precision = row["precision"]?.toString()?.toInt(),
                unit = row["unit"] as String?,
                rounding = row["rounding"]?.toString()?.let { x -> RoundingMode.valueOf(x) },
                updateTime = row["updateTime"]?.toString()?.let { x -> ZonedDateTime.parse(x) },
                plan = plan0,
                subject = subject0
            )
        }
        return accountTypes
    }


    @Suppress("UNCHECKED_CAST")
    private fun findGradeHierarchies(sdk: DataapiWebSocketSdk, plan0: Plan0, subject0: Subject0): List<GradeHierarchy> {
        val gradeHierarchySql = """
            SELECT versionId, id, name 
            FROM data.loyalty.manager.gradeHierarchy 
            WHERE subjectVersionId = :subjectVersionId 
            AND disabled = false 
            AND status = 'PUBLISHED' 
        """.trimIndent()
        val gradeHierarchyResponse = sdk.execute(gradeHierarchySql, mapOf("subjectVersionId" to subject0.subjectVersionId))
        val gradeHierarchies = gradeHierarchyResponse.data.map { g ->
            val row = g as Map<String, Any?>
            val gradeDefinitionSql = """
                SELECT id, name, sort 
                FROM data.loyalty.manager.gradeDefinition 
                WHERE gradeHierarchyVersionId = :gradeHierarchyVersionId 
                AND disabled = false 
                ORDER BY sort
            """.trimIndent()
            val gradeDefinitionResponse = sdk.execute(gradeDefinitionSql, mapOf("gradeHierarchyVersionId" to row["versionId"]))
            val gradeDefinitions = gradeDefinitionResponse.data.map { gd ->
                val gdRow = gd as Map<String, Any?>
                GradeDefinition(
                    id = gdRow["id"].toString().toLong(),
                    name = gdRow["name"] as String,
                    sort = gdRow["sort"].toString().toLong()
                )
            }
            GradeHierarchy(
                id = row["id"].toString().toLong(),
                name = row["name"] as String,
                gradeDefinitions = gradeDefinitions,
                plan = plan0,
                subject = subject0,
            )
        }
        return gradeHierarchies
    }


    @Suppress("UNCHECKED_CAST")
    private fun findMedalHierarchies(sdk: DataapiWebSocketSdk, plan0: Plan0, subject0: Subject0): List<MedalHierarchy> {
        val medalHierarchySql = """
            SELECT versionId, id, name 
            FROM data.loyalty.manager.medalHierarchy 
            WHERE subjectVersionId = :subjectVersionId 
            AND disabled = false 
            AND status = 'PUBLISHED' 
        """.trimIndent()
        val medalHierarchyResponse = sdk.execute(medalHierarchySql, mapOf("subjectVersionId" to subject0.subjectVersionId))
        val medalHierarchies = medalHierarchyResponse.data.map { m ->
            val row = m as Map<String, Any?>
            val medalDefinitionSql = """
                SELECT id, name, sort 
                FROM data.loyalty.manager.medalDefinition 
                WHERE medalHierarchyVersionId = :medalHierarchyVersionId 
                AND disabled = false
                ORDER BY sort
            """.trimIndent()
            val medalDefinitionResponse = sdk.execute(medalDefinitionSql, mapOf("medalHierarchyVersionId" to row["versionId"]))
            val medalDefinitions = medalDefinitionResponse.data.map { md ->
                val mdRow = md as Map<String, Any?>
                MedalDefinition(
                    id = mdRow["id"].toString().toLong(),
                    name = mdRow["name"] as String,
                    sort = mdRow["sort"].toString().toLong()
                )
            }
            MedalHierarchy(
                id = row["id"].toString().toLong(),
                name = row["name"] as String,
                medalDefinitions = medalDefinitions,
                plan = plan0,
                subject = subject0,
            )
        }
        return medalHierarchies
    }
}