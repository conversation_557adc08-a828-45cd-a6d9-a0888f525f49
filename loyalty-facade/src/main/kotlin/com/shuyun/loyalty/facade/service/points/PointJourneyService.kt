package com.shuyun.loyalty.facade.service.points

import com.github.f4b6a3.tsid.Tsid
import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.repository.points.PointJourneyRepository
import com.shuyun.loyalty.sdk.Json
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime

object PointJourneyService {

    // 保存积分明细处理任务
    fun save(
        sdk: DataapiWebSocketSdk,
        pointAccountType: PointAccountType,
        memberPoint: MemberPoint,
        beforeTotalPoints: BigDecimal,
        afterTotalPoints: BigDecimal,
        pointValue: BigDecimal,
        action: PointAction,
        pointAttr: PointAttr,
        now: ZonedDateTime,
        afterPoints: BigDecimal? = null,
        sendLimitResults: List<PointLimitResult>? = null,
        leftSegmentDate: LocalDate? = null,
        requestType: LoyaltyRequestType = LoyaltyRequestType.API
    ) {
        val subject = pointAccountType.subject
        val plan = pointAccountType.plan
        val data = MemberPointJourneyData(
            plan = PointPlan(plan.id, plan.name, plan.sendLimitRuleSwitch),
            subject = PointSubject(subject.id, subject.name, subject.fqn),
            hierarchy = PointHierarchy(
                pointAccountType.id,
                pointAccountType.name,
                pointAccountType.precision!!,
                pointAccountType.rounding!!,
                pointAccountType.unit!!,
                negativeStrategy = NegativeStrategy.valueOf(pointAccountType.negativeStrategy),
                topLimit = pointAccountType.topLimit?.toLong(),
                singleTopLimit = pointAccountType.singleTopLimit?.toLong(),
                limitRules = if (!pointAccountType.sendLimitRule.isNullOrEmpty()) Json.parse(pointAccountType.sendLimitRule) else null,
                sendLimitResults = sendLimitResults
            ),
            member = PointMember(
                memberPoint.id,
                memberPoint.memberId,
                beforeTotalPoints,
                leftSegmentDate
            ),
            date = now,
            attr = pointAttr,
            type = requestType,
            pointValue = pointValue,
            afterPoints = afterPoints ?: pointValue,
            afterTotalPoints = afterTotalPoints
        )

        if (action == PointAction.FREEZE || action == PointAction.DEDUCT || action == PointAction.USE_FROZEN || action == PointAction.REVERSE_DEDUCT) {
            data.hierarchy.negativeStrategy = NegativeStrategy.valueOf(pointAccountType.negativeStrategy)
            data.hierarchy.deductionPriorities = Json.parse(pointAccountType.priorityDeduction)
        }

        try {
            PointJourneyRepository.insertJourneyTask(
                sdk = sdk,
                id = Tsid.fast().toLowerCase(),
                hierarchyId = memberPoint.pointPlanId,
                traceId = pointAttr.traceId,
                uniqueId = pointAttr.uniqueId,
                memberId = memberPoint.memberId,
                memberPointId = memberPoint.id,
                action = action.name,
                status = PointStatus.NEW.name,
                comment = "",
                data = Json.toJson(data),
                version = 0,
                created = ZonedDateTime.now(),
                modified = ZonedDateTime.now()
            )
        } catch (e: SdkException) {
            if (e.isSdkDuplicate()) {
                throw DuplicateRequestException("重复请求：${pointAttr.uniqueId}")
            }
            throw e
        }
    }


    fun findOpenFreezePointJourney(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, action: PointAction, traceId: String): PointJourney? {
        val list = PointJourneyRepository.findBy(
            sdk = sdk,
            hierarchyId = memberPoint.pointPlanId,
            memberPointId = memberPoint.id,
            action = action.name,
            traceId = traceId
        )
        return list.firstOrNull { it.isAPIType }
    }
}








