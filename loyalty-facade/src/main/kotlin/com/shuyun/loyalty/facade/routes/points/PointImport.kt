package com.shuyun.loyalty.facade.routes.points

import com.shuyun.loyalty.facade.common.withLock
import com.shuyun.loyalty.facade.model.BadParamRequestException
import com.shuyun.loyalty.facade.model.Constants
import com.shuyun.loyalty.facade.model.ImportPointRecord
import com.shuyun.loyalty.facade.model.RequireMemberLockException
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.points.PointImportService
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointImportRequest
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointImportResult
import com.shuyun.loyalty.sdk.Json
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import java.math.BigDecimal
import java.time.Duration

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.points.PointImport.kt")


fun Route.pointImport() {

    // 保存历史积分记录
    post("/point:import:save-record") {
        val importId = call.parameters["importId"] ?: throw BadParamRequestException("导入ID不能为空")
        val pointAccountTypeId = call.parameters["pointAccountTypeId"]?.toLong() ?: throw BadParamRequestException("积分账户类型ID不能为空")
        val pointAccountType = ProgramService.getPointAccountTypeById(pointAccountTypeId) ?: throw BadParamRequestException("积分账户类型不存在")
        val overrideHistoryPoint = call.parameters["overrideHistoryPoint"]?.toBoolean() ?: false
        val planId = pointAccountType.plan.id

        val reqs = call.receive<List<MemberPointImportRequest>>().also {
            if (it.isEmpty()) throw BadParamRequestException("导入积分变更数据不能为空")
            it.forEach { x -> x.validate() }
        }

        if (LOGGER.isDebugEnabled) {
            LOGGER.debug("保存历史积分变更数据: {}", Json.toJson(reqs))
        } else {
            LOGGER.info("保存历史积分变更数据: importId={} pointAccountTypeId={} size={}", importId, pointAccountTypeId, reqs.size)
        }

        reqs.groupBy { it.memberId }.forEach { (_, v) ->
            val sortedSeq = v.sortedBy { it.createdDate }
            sortedSeq.first().overrideHistory = overrideHistoryPoint
            sortedSeq.chunked(500).forEach { chunk ->
                val records = ArrayList<ImportPointRecord>()
                for (req in chunk) {
                    val record = ImportPointRecord(
                        id = "",
                        migrationId = importId,
                        planId = planId,
                        pointId = pointAccountTypeId,
                        memberId = req.memberId!!,
                        action = req.action!!,
                        point = req.point!!,
                        effectiveDate = req.effectiveDate!!,
                        overdueDate = req.overdueDate!!,
                        description = req.description,
                        channelType = req.channelType!!,
                        shopId = req.shopId,
                        kzzd1 = req.kzzd1,
                        kzzd2 = req.kzzd2,
                        kzzd3 = req.kzzd3,
                        key = req.key!!,
                        overrideHistory = (req.overrideHistory ?: false),
                        createdDate = req.createdDate!!
                    )
                    records.add(record)
                }
                PointImportService.saveImportRecords(records)
            }
        }


        call.respond(HttpStatusCode.OK)
    }


    // 执行历史积分记录导入
    post("/point:import:apply-record") {
        val importId = call.parameters["importId"] ?: throw BadParamRequestException("导入ID不能为空")
        val pointAccountTypeId = call.parameters["pointAccountTypeId"]?.toLong() ?: throw BadParamRequestException("积分账户类型ID不能为空")
        LOGGER.info("执行历史积分变更数据: importId={} pointAccountTypeId={}", importId, pointAccountTypeId)
        val lockKey = "LOYALTY_MIGRATION:${pointAccountTypeId}_${importId}"
        try {
            withLock(key = lockKey, waitTime = Duration.ofSeconds(1), leaseTime = Duration.ofHours(5)) {
                PointImportService.applyImportRecords(importId, pointAccountTypeId)
            }
        } catch (_: RequireMemberLockException) {
            LOGGER.warn("导入ID: $importId 积分账户类型ID: $pointAccountTypeId 正在处理中")
        }
        call.respond(HttpStatusCode.Accepted)
    }


    // 查询导入执行结果
    get("/point:import:result") {
        val importId = call.parameters["importId"] ?: throw BadParamRequestException("导入ID不能为空")
        val pointAccountTypeId = call.parameters["pointAccountTypeId"]?.toLong() ?: throw BadParamRequestException("积分账户类型ID不能为空")
        val cnt = PointImportService.countImportMember(importId, pointAccountTypeId)
        if (cnt == 0L) {
            call.respond(MemberPointImportResult().apply { this.progress = "已完成" })
        } else {
            call.respond(MemberPointImportResult().apply { this.progress = "剩余：${cnt}个会员未完成" })
        }
    }

}


private fun MemberPointImportRequest.validate(): MemberPointImportRequest {
    if (memberId == null) throw BadParamRequestException("会员ID不能为空")
    if (point == null || point!! <= BigDecimal.ZERO) throw BadParamRequestException("积分值必须大于0")
    if (action == null) throw BadParamRequestException("积分变更类型不能为空")
    if (createdDate == null) throw BadParamRequestException("积分生成时间不能为空")
    if (overdueDate == null) overdueDate = Constants.LONG_TERM_OVERDUE_DATE
    if (effectiveDate == null) effectiveDate = createdDate!!
    if (createdDate!!.isAfter(overdueDate)) throw BadParamRequestException("创建时间(${createdDate})不能大于积分过期时间(${overdueDate}) 会员ID: $memberId key: $key")
    if (effectiveDate!!.isAfter(overdueDate)) throw BadParamRequestException("积分生效时间(${effectiveDate})不能大于积分过期时间(${overdueDate}) 会员ID: $memberId key: $key")
    if (channelType != null) {
        ProgramService.findChannelTypeByCode(channelType!!) ?: throw BadParamRequestException("无效的渠道类型")
    } else {
        channelType = Constants.DEFAULT_CHANNEL_TYPE
    }
    overrideHistory = false
    myId = null
    return this
}
