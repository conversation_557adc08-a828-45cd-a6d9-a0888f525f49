package com.shuyun.loyalty.facade.remote.api

import com.shuyun.lite.client.DiscoveryClientHolder
import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Property
import io.ktor.client.*
import io.ktor.client.engine.okhttp.*
import io.ktor.client.plugins.contentnegotiation.*
import io.ktor.serialization.jackson.*
import java.security.MessageDigest

internal object ApiGateway {

    private const val ENABLE_DISCOVERY_KEY = "system.discovery.enable"
    private const val SERVER_API_ADDRESS_KEY = "system.api.address"
    private const val DEFAULT_SERVER_PROTOCOL = "http"
    private val SIGN_HEX_CODE = "0123456789ABCDEF".toCharArray()


    val client by lazy {
        HttpClient(OkHttp) {
//            install(Logging) {
//                level = LogLevel.ALL
//            }
            install(ContentNegotiation) {
                jackson { Json.setDefault(this) }
            }
        }
    }


    fun server(
        targetServerName: String,
        targetServerVersion: String,
        defaultTargetServerPort: Int = 8080,
        useDiscovery: Boolean = Property.getSysOrEnv(ENABLE_DISCOVERY_KEY, true)
    ): Triple<String, String, Int> {
        val it = if (useDiscovery) {
            val s = GlobalContext.tenantProvider().getWith(GlobalContext.defTenantId()) {
                DiscoveryClientHolder.find(
                    GlobalContext.environment(),
                    targetServerName,
                    targetServerVersion,
                    defaultTargetServerPort
                )
            }
            Triple(DEFAULT_SERVER_PROTOCOL, s.key!!, s.value!!)
        } else {
            val address = Property.getSysOrEnv(SERVER_API_ADDRESS_KEY)?.trimEnd('/')
                ?: throw IllegalStateException("$SERVER_API_ADDRESS_KEY is not set")
            val pattern = Regex("((?<protocol>http|https)://)?(?<host>[a-zA-Z0-9.-]+)(:(?<port>\\d+))?")
            val matchResult = pattern.find(address)
            if (matchResult != null) {
                val protocol = matchResult.groups["protocol"]?.value ?: DEFAULT_SERVER_PROTOCOL
                val host = matchResult.groups["host"]!!.value
                val port = matchResult.groups["port"]?.value?.toInt() ?: (if (protocol == "https") 443 else 80)
                Triple(protocol, host, port)
            } else {
                throw IllegalArgumentException("Invalid address: $address")
            }
        }
        return it
    }

    fun sign(
        currentServerName: String,
        targetServiceName: String,
        targetServerVersion: String,
        requestPath: String,
        timestamp: String
    ): String {
        val parameters = linkedMapOf(
            "callerService" to currentServerName,
            "contextPath" to targetServiceName,
            "requestPath" to requestPath,
            "timestamp" to timestamp,
            "v" to targetServerVersion
        )
        val secret = Property.getSysOrEnv("system.api.secret", "")
        val sb = StringBuilder().append(secret)
        for ((key, value) in parameters) {
            sb.append(key).append(value)
        }
        val content = sb.append(secret).toString()
        val md5 = MessageDigest.getInstance("MD5")
        val bytes = md5.digest(content.toByteArray(Charsets.UTF_8))

        val sign = StringBuilder(bytes.size * 2)
        for (b in bytes) {
            sign.append(SIGN_HEX_CODE[b.toInt() shr 4 and 0xF])
            sign.append(SIGN_HEX_CODE[b.toInt() and 0xF])
        }
        return sign.toString()
    }
}