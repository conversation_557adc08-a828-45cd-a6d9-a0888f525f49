package com.shuyun.loyalty.facade.remote

import com.shuyun.api.hub.client.ApiMgmtHttpClientFactory
import com.shuyun.api.hub.mgmt.request.HttpApiRegisterRequest
import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.sdk.Json
import org.slf4j.LoggerFactory
import org.yaml.snakeyaml.Yaml
import kotlin.concurrent.thread


object OpenApiRegister {

    private val LOGGER = LoggerFactory.getLogger(OpenApiRegister::class.java)

    fun register() {
        thread(isDaemon = true, name = "API文档注册器") {
            LOGGER.info("开始注册API文档")
            val st = System.currentTimeMillis()
            val swaggerFile = "openapi/documentation.yaml"
            val resource  = Thread.currentThread().contextClassLoader
                .getResourceAsStream(swaggerFile)?.bufferedReader()
                ?: return@thread
            val doc = Json.toJson(Yaml().loadAs(resource, Map::class.java))
            while (true) {
                try {
                    val req = HttpApiRegisterRequest().apply {
                        this.title = GlobalContext.serviceTitle()
                        this.serviceName = GlobalContext.serviceName()
                        this.appVersion = GlobalContext.appVersion()
                        this.apiVersion = GlobalContext.apiVersion()
                        this.rootPath = "/" + this.serviceName + "/" + this.apiVersion
                        this.description = title + "接口文档"
                        this.openApiDoc = doc
                    }
                    ApiMgmtHttpClientFactory.apiMgmt().register(req)
                    break
                } catch (_: InterruptedException) {
                    LOGGER.warn("API文档注册器休眠被中断")
                    break
                } catch (_: Throwable) {
                    LOGGER.warn("注册API文档异常，休眠10秒后重试")
                    Thread.sleep(1000 * 10)
                }
            }
            LOGGER.info("注册API文档完成，耗时{}毫秒", (System.currentTimeMillis() - st))
        }
    }
}
