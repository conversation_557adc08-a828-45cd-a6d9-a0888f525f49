package com.shuyun.loyalty.facade.service.medal

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.MedalBatchModifyParam
import com.shuyun.loyalty.facade.model.MemberMedal
import com.shuyun.loyalty.facade.model.MemberMedalRecord
import com.shuyun.loyalty.facade.repository.medal.MedalRecordRepository
import com.shuyun.loyalty.facade.repository.medal.MedalRepository
import com.shuyun.loyalty.sdk.Dataapi.withTransaction

object MedalService {

    //查询会员有效勋章
    fun queryMedal(sdk: DataapiWebSocketSdk, memberId:String, medalHierarchyId: Long,medalDefinitionId:Long?):List<MemberMedal> {
        return MedalRepository.queryMemberMedals( sdk, medalHierarchyId, medalDefinitionId, memberId, disabled = false)
    }

    fun queryMedalByMemberId(sdk: DataapiWebSocketSdk, memberId:String, medalHierarchyId: Long, disabled:Boolean):List<MemberMedal>? {
       return MedalRepository.queryMedalByMemberId(sdk,medalHierarchyId,memberId,disabled)
    }


    suspend fun modify(targetMedal: MemberMedal, oldMedal: MemberMedal?, memberMedalRecord: MemberMedalRecord) {
        withTransaction { sdk ->
            modify(sdk, targetMedal, oldMedal, memberMedalRecord)
        }
    }


    suspend fun batchModify(batchModifyParam: List<MedalBatchModifyParam>){
        withTransaction { sdk ->
            batchModifyParam.forEach { param ->
                modify(sdk, param.targetMemberMedal, param.currentMedal, param.medalRecord)
            }
        }
    }

    fun modify(
        sdk: DataapiWebSocketSdk,
        targetMedal: MemberMedal,
        oldMedal: MemberMedal?,
        memberMedalRecord: MemberMedalRecord,
    ) {
        if (oldMedal == null) {
            MedalRepository.insertMemberMedal(sdk, targetMedal)
        } else {
            MedalRepository.updateMemberMedal(sdk, targetMedal)
        }
        MedalRecordRepository.insertMemberMedal(sdk,memberMedalRecord)
    }

}