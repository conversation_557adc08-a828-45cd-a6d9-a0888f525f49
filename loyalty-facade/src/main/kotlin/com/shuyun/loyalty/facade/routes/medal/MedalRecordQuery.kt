package com.shuyun.loyalty.facade.routes.medal

import com.shuyun.loyalty.facade.model.BadParamRequestException
import com.shuyun.loyalty.facade.model.MedalHierarchyNotFoundExistException
import com.shuyun.loyalty.facade.model.QuerySortType
import com.shuyun.loyalty.facade.model.checkISODate
import com.shuyun.loyalty.facade.repository.medal.MedalRecordRepository
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.sdk.api.model.MedalRecordType
import com.shuyun.loyalty.sdk.api.model.http.medal.MemberMedalRecordResponse
import com.shuyun.loyalty.sdk.Dataapi.noneTransaction
import com.shuyun.loyalty.sdk.Json
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import java.time.ZonedDateTime

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.medal.medalRecordQuery.kt")

fun Route.medalRecordQuery() {
    // 分页获取会员勋章记录
    get("/medal/record") {
        val parameters = call.request.queryParameters
        val req = MemberMedalRecordRequest(
            planId = parameters["planId"]?.toLong(),
            medalHierarchyId = parameters["medalHierarchyId"]?.toLong(),
            medalDefinitionId = parameters["medalDefinitionId"]?.toLong(),
            memberId = parameters["memberId"],
            channelType = parameters["channelType"],
            sortType = QuerySortType.from(parameters["sortType"]),
            recordType = parameters["recordType"]?.let { MedalRecordType.valueOf(it) },
            startTime = parameters["startTime"],
            endTime = parameters["endTime"],
            number = parameters["number"]?.toInt() ?: 0,
            pageSize = parameters["pageSize"]?.toInt() ?: 20
        ).also { LOGGER.info("获取会员勋章详情记录: {}", Json.toJson(it)) }.validate()
        val hierarchy = ProgramService.getMedalHierarchyById(req.medalHierarchyId!!)!!
        val res = noneTransaction {
            MedalRecordRepository.queryMemberMedalRecord(
                it,
                req.medalHierarchyId!!,
                req.medalDefinitionId,
                req.memberId,
                req.startTime?.let { x -> ZonedDateTime.parse(x) },
                req.endTime?.let { x -> ZonedDateTime.parse(x) },
                req.channelType,
                req.recordType,
                req.sortType?.v,
                req.number?: 0,
                req.pageSize?: 0,
            )
        }
       val responses =  res.map {
            MemberMedalRecordResponse().apply {
                this.planId = hierarchy.plan.id
                this.planName = hierarchy.plan.name
                this.medalHierarchyId = hierarchy.id
                this.medalHierarchyName = hierarchy.name
                this.memberId = it.memberId
                this.medalDefinitionId = it.medalDefinitionId
                this.medalDefinitionName = it.medalDefinitionName
                this.currentOverdueDate = it.currentOverdueDate
                this.recordType = it.recordType
                this.channelType = it.channel
                this.ruleName = it.ruleName
                this.description = it.description
                this.changeWay = it.changeWay
                this.kzzd1 = it.KZZD1
                this.kzzd2 = it.KZZD2
                this.kzzd3 = it.KZZD3
                this.created = it.created
            }
        }
        call.respond(responses)
    }
}


private data class MemberMedalRecordRequest(
    var number: Int?,
    var pageSize: Int?,
    var planId: Long?,
    var medalHierarchyId: Long?,
    var medalDefinitionId: Long?,
    var memberId: String?,
    var startTime: String?,
    var endTime: String?,
    var channelType: String?,
    var recordType: MedalRecordType?,
    var sortType: QuerySortType? = null
)


private fun MemberMedalRecordRequest.validate(): MemberMedalRecordRequest {
    if (medalHierarchyId == null) throw BadParamRequestException("medalHierarchyId参数为必填")
    startTime?.checkISODate("startTime参数格式错误")
    endTime?.checkISODate("endTime参数格式错误")
    ProgramService.getMedalHierarchyById(medalHierarchyId!!) ?: throw MedalHierarchyNotFoundExistException()
    return this
}
