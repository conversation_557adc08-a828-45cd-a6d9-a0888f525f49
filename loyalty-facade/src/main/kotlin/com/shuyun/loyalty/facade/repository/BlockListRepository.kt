package com.shuyun.loyalty.facade.repository

import com.shuyun.loyalty.facade.model.SpecialConfig
import com.shuyun.loyalty.facade.model.SpecialListConfig
import com.shuyun.loyalty.sdk.Dataapi.sdk
import com.shuyun.loyalty.sdk.Json
import org.slf4j.LoggerFactory

@Suppress("UNCHECKED_CAST")
object BlockListRepository {

    private val logger = LoggerFactory.getLogger(BlockListRepository::class.java)

    fun findSpecialConfigs(): List<SpecialListConfig> {
        val sql = """
            SELECT id, subjectId, columnPath, specialListGroupIds, forbiddenConfigString 
            FROM data.loyalty.manager.specialListConfig 
            WHERE disabled = 0 and status = 'ENABLED'
        """.trimIndent()
        sdk.use {
            val response = it.execute(sql, emptyMap())
            val specialList = ArrayList<SpecialListConfig>()
            for (d in response.data) {
                try {
                    val m = d as Map<String, Any?>
                    val forbiddenConfigString = m["forbiddenConfigString"] as String
                    val secialConfigs = Json.parse<Map<String,List<SpecialConfig>>>(forbiddenConfigString)
                    val config = SpecialListConfig(
                        id = m["id"].toString().toLong(),
                        subjectId = m["subjectId"].toString().toLong(),
                        columnPath = m["columnPath"] as String,
                        specialListGroupIds = m["specialListGroupIds"] as String,
                        specialConfigs = secialConfigs
                    )
                    specialList.add(config)
                } catch (e: Exception) {
                    logger.warn("特殊名单格式错误 {} {}", Json.toJson(d), e.message)
                    continue
                }
            }
            return specialList
        }
    }
}