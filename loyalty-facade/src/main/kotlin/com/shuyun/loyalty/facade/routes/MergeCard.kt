package com.shuyun.loyalty.facade.routes

import com.shuyun.loyalty.facade.model.BadParamRequestException
import com.shuyun.loyalty.facade.model.Constants
import com.shuyun.loyalty.facade.service.MergeCardService
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.sdk.api.model.*
import com.shuyun.loyalty.sdk.Json
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import kotlinx.coroutines.*
import kotlinx.coroutines.slf4j.MDCContext

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.MergeCard.kt")

fun Route.mergeCard() {
    // 合并会员卡
    post("/merge") {
        val req = call.receive<MemberMergeRequest>().also { LOGGER.info("会员合卡: {}", Json.toJson(it)) }.validate()
        req.mergePointRequest?.let {
            if (it.pointPlanIds.isNullOrEmpty()) throw BadParamRequestException("积分账户ID不能为空")
            it.pointPlanIds = it.pointPlanIds!!.distinct()
        }
        req.mergeGradeRequest?.let {
            if (it.gradeHierarchyIds.isNullOrEmpty()) throw BadParamRequestException("等级体系ID不能为空")
            if (it.gradeMergeType == null) throw BadParamRequestException("等级合并规则类型不能为空")
            it.gradeHierarchyIds = it.gradeHierarchyIds!!.distinct()
        }
        req.mergeMedalRequest?.let {
            if (it.medalHierarchyIds.isNullOrEmpty()) throw BadParamRequestException("勋章体系ID不能为空")
            it.medalHierarchyIds = it.medalHierarchyIds!!.distinct()
        }
        if (req.mergePointRequest == null && req.mergeGradeRequest == null && req.mergeMedalRequest == null) {
            throw BadParamRequestException("合卡数据不能为空")
        }
        req.subMemberIds = req.subMemberIds?.distinct()
        suspend fun mergeCard() {
            when (req.txType) {
                MemberMergeTXType.WHOLE ->  MergeCardService.merge(req)
                MemberMergeTXType.INDIVIDUAL -> {
                    val f1 = call.async(Dispatchers.IO) {
                        req.mergePointRequest?.let {
                            try {
                                MergeCardService.mergePoint(req)
                            } catch (e: Exception) {
                                LOGGER.error("合并积分失败", e)
                            }
                        }
                    }
                    val f2 = call.async(Dispatchers.IO) {
                        req.mergeGradeRequest?.let {
                            try {
                                MergeCardService.mergeGrade(req)
                            } catch (e: Exception) {
                                LOGGER.error("合并等级失败", e)
                            }
                        }
                    }
                    val f3 = call.async(Dispatchers.IO) {
                        req.mergeMedalRequest?.let {
                            try {
                                MergeCardService.mergeMedal(req)
                            } catch (e: Exception) {
                                LOGGER.error("合并勋章失败", e)
                            }
                        }
                    }
                    f1.await()
                    f2.await()
                    f3.await()
                }
            }
        }
        if (req.synchronized) {
            mergeCard()
        } else {
            call.application.launch(Dispatchers.IO + MDCContext()) {
                withContext(SupervisorJob()){
                    mergeCard()
                }
            }
        }
        call.respond(HttpStatusCode.OK)
    }

    // 只合并积分
    post("/merge:point") {
        val req = call.receive<OnlyMergePointRequest>().also { LOGGER.info("会员积分合卡: {}", Json.toJson(it)) }.validate()
        req.mergePointRequest?.let {
            if (it.pointPlanIds.isNullOrEmpty()) throw BadParamRequestException("积分账户ID不能为空")
            it.pointPlanIds = it.pointPlanIds!!.distinct()
        }
        MergeCardService.mergePoint(req)
        call.respond(HttpStatusCode.OK)
    }

    // 只合并等级
    post("/merge:grade") {
        val req = call.receive<OnlyMergeGradeRequest>().also { LOGGER.info("会员等级合卡: {}", Json.toJson(it)) }.validate()
        req.mergeGradeRequest?.let {
            if (it.gradeHierarchyIds.isNullOrEmpty()) throw BadParamRequestException("等级体系ID不能为空")
            if (it.gradeMergeType == null) throw BadParamRequestException("等级合并规则类型不能为空")
            it.gradeHierarchyIds = it.gradeHierarchyIds!!.distinct()
        }
        MergeCardService.mergeGrade(req)
        call.respond(HttpStatusCode.OK)
    }
    // 只合并勋章
    post("/merge:medal") {
        val req = call.receive<OnlyMergeMedalRequest>().also { LOGGER.info("会员勋章合卡: {}", Json.toJson(it)) }.validate()
        req.mergeMedalRequest?.let {
            if (it.medalHierarchyIds.isNullOrEmpty()) throw BadParamRequestException("勋章体系ID不能为空")
            it.medalHierarchyIds = it.medalHierarchyIds!!.distinct()
        }
        MergeCardService.mergeMedal(req)
        call.respond(HttpStatusCode.OK)
    }
}


private fun MemberMergeRequest.validate(): MemberMergeRequest {
    if (memberId == null) throw BadParamRequestException("会员Id不能为空")
    if (subMemberIds.isNullOrEmpty()) throw BadParamRequestException("副卡会员Id不能为空")
    for (subMemberId in subMemberIds!!) {
        if (subMemberId == memberId) throw BadParamRequestException("主卡会员Id不能与副卡会员Id相同")
    }
    if (channelType != null) {
        ProgramService.findChannelTypeByCode(channelType!!) ?: throw BadParamRequestException("无效的渠道类型")
    } else {
        channelType = Constants.DEFAULT_CHANNEL_TYPE
    }
    return this
}

private fun OnlyMergePointRequest.validate() = Json.convert<MemberMergeRequest>(this).validate()
private fun OnlyMergeGradeRequest.validate() = Json.convert<MemberMergeRequest>(this).validate()
private fun OnlyMergeMedalRequest.validate() = Json.convert<MemberMergeRequest>(this).validate()




