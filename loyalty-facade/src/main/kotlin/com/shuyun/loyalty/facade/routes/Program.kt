package com.shuyun.loyalty.facade.routes

import com.shuyun.loyalty.facade.model.BadParamRequestException
import com.shuyun.loyalty.facade.model.PlanNotFoundException
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.ProgramService.getApiDefinition
import com.shuyun.loyalty.sdk.api.model.http.PlanResponse
import com.shuyun.loyalty.sdk.Json
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*


fun Route.program() {

    // 获取计划(积分、等级、勋章)信息
    get("/plan:info") {
        val planId = call.parameters["planId"]?.toLong() ?: throw BadParamRequestException("planId不能为空")
        val plan = ProgramService.getPlanById(planId) ?: throw PlanNotFoundException(planId)
        val response = Json.parse<PlanResponse>(Json.toJson(plan))
        call.respond(response)
    }


    // 获取所有计划计划(积分、等级、勋章)信息
    get("/plan:all") {
        val planResponses = ProgramService.getAllPlans().map { Json.parse<PlanResponse>(Json.toJson(it)) }
        call.respond(planResponses)
    }

    get("/record-api-definition") {
        val apiName = call.parameters["apiName"] ?: throw BadParamRequestException("apiName不能为空")
        var lang = "zh_CN"
        call.request.acceptLanguageItems().forEach { item ->
            if (item.value.contains("zh")) {
                if (item.value.contains("HK") || item.value.contains("TW") ||
                    item.value.contains("MO") || item.value.contains("Hant")) {
                    lang = "zh_HK"
                    return@forEach
                } else {
                    lang = "zh_CN"
                    return@forEach
                }
            }
            if (item.value.contains("en")) {
                lang = "en_US"
                return@forEach
            }
        }
        val path = when (apiName) {
            "OPEN_POINT_MEMBER_RECORD" -> "openapi/member_point_record_${lang}.yaml"
            "OPEN_GRADE_MEMBER_RECORD" -> "openapi/member_grade_record_${lang}.yaml"
            else -> null
        }
        if (path == null) {
            call.respond(HttpStatusCode.NotFound)
            return@get
        }
        val res = getApiDefinition(path)
        if (res == null || res.isEmpty()) {
            call.respond(HttpStatusCode.NotFound)
        } else {
            call.respond(res)
        }
    }

}