package com.shuyun.loyalty.facade.service

import com.shuyun.dm.api.response.BaseResponse
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.remote.api.BlackListClient
import com.shuyun.loyalty.facade.remote.api.CheckRequest
import com.shuyun.loyalty.sdk.Dataapi
import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.sdk.RiskCtrlC
import org.slf4j.LoggerFactory

object BlockListService {

    private val logger = LoggerFactory.getLogger(BlockListService::class.java)

    // 检查特殊名单
    suspend fun check(
        type: TypeEnum,
        subjectFqn: String,
        hierarchyId: Long,
        memberId: String,
        forbiddenOperation: ForbiddenOperation?
    )  {
        MemberService.checkMemberId(subjectFqn, memberId)
        if (forbiddenOperation == null) {
            return
        }
        val list = ProgramService.findSpecialListConfigs(hierarchyId, type)
        for (specialListConfig in list) {
            val scs = specialListConfig.specialConfigs[type.name] ?: emptyList()
            for (sc in scs) {
                if (sc.forbiddens[ForbiddenPort.INTERFACE]?.contains(forbiddenOperation) != true || hierarchyId != sc.id) {
                    continue
                }
                // 存在该动作的特殊名单配置
                val fqn = specialListConfig.columnPath.substringBeforeLast(".")
                val columnPath = specialListConfig.columnPath.substringAfterLast(".")
                val columnValue = if (columnPath == "id") memberId else MemberService.findMemberField(fqn, memberId, columnPath)

                val inSpecialList = columnValue?.let {
                    val useLegacyCheckListAPI = Property.getSysOrEnv("use.legacy.checklist.api", false)
                    if (useLegacyCheckListAPI) {
                        val inCheckList = RiskCtrlC.existBlack(columnValue, fqn, specialListConfig.specialListGroupIds.split(",").toList())
                        { sql, params ->
                            Dataapi.sdk.use { x ->
                                @Suppress("UNCHECKED_CAST")
                                (x.execute(sql, params) as BaseResponse<Map<String, Any?>>).data
                            }
                        }
                        inCheckList
                    } else {
                        val req = CheckRequest(specialListConfig.specialListGroupIds, listOf(it))
                        try {
                            val inCheckList = BlackListClient.check(req)
                            inCheckList
                        } catch (e: Exception) {
                            logger.error("调用黑名单服务失败: req: {}", req, e)
                            throw FacadeException("调用黑名单服务失败")
                        }
                    }
                } ?: false
                if (inSpecialList) {
                    throw MemberInSpecialListException("会员ID: $memberId 在黑名单中")
                }
            }
        }
    }

}