package com.shuyun.loyalty.facade.routes.points

import com.shuyun.loyalty.facade.common.Kafka
import com.shuyun.loyalty.facade.common.withMemberPointLock
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.service.BlockListService
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.points.PointModifyService
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointDeductRequest
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Property
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import java.math.BigDecimal
import java.time.Duration
import java.time.ZonedDateTime

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.points.PointDeduct.kt")


fun Route.pointDeduct() {

    post(Regex("point:(?<type>deduct|allowedNegativeDetect)")) {
        val deductType = call.parameters["type"]!!
        val req = call.receive<MemberPointDeductRequest>().also { LOGGER.info("扣减会员积分: {}", Json.toJson(it)) }.validate(deductType)
        val point = req.point!!.scale(req.pointAccountId!!)
        val pointAccountType = ProgramService.getPointAccountTypeById(req.pointAccountId!!)!!
        if (pointAccountType.negativeStrategy == NegativeStrategy.NOT_ALLOWED.name) {
            throw PointDeductNotAllowedException()
        }

        // 判断会员是否在会员表中存在 和 检查特殊名单
        BlockListService.check(
            TypeEnum.POINT,
            pointAccountType.subject.fqn,
            pointAccountType.id,
            req.memberId!!,
            ForbiddenOperation.POINT_DEDUCT_BY_USE
        )

        val now = ZonedDateTime.now()
        val timeout = req.lockWaitTime ?: Constants.DEFAULT_WAIT_TIME
        withMemberPointLock(req.pointAccountId!!, req.memberId!!, waitTime = Duration.ofMillis(timeout)) {
            val b = allowNegative(pointAccountType.negativeStrategy, deductType)
            val memberPoint = PointModifyService.deductMemberPoint(req, point, now, b, req.tx)
            Kafka.sendPointCalc(req.pointAccountId!!, memberPoint.id, req.memberId!!)
            call.respond(HttpStatusCode.Accepted)
        }
    }
}


private fun MemberPointDeductRequest.validate(deductType: String): MemberPointDeductRequest {
    ProgramService.basicValidate(pointAccountId, memberId, channelType, changeMode, uniqueId)
    if (point == null || point!! <= BigDecimal.ZERO) throw BadParamRequestException("积分值必须大于0")
    if (tx == true) {
        if (deductType == "deduct") {
            if (triggerId == null) throw BadParamRequestException("triggerId参数为必填")
        } else {
            tx = false
        }
    }
    return this
}


private fun allowNegative(negativeStrategy: String, deductType: String): Boolean {
    val envNotAllowedNegative = Property.getSysOrEnv("point.use.less.zero.enabled", true)
    val enableOverUse = Property.getSysOrEnv("point.over.use.enabled", false)
    val reqAllowedNegative = (deductType == "allowedNegativeDetect" || deductType == "allowedNegativeDeduct")
    return negativeStrategy == NegativeStrategy.TO_NEGATIVE.name && reqAllowedNegative && (!envNotAllowedNegative || enableOverUse)
}

