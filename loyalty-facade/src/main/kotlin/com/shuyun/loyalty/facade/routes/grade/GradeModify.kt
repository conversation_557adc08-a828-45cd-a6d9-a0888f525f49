package com.shuyun.loyalty.facade.routes.grade

import com.shuyun.loyalty.facade.common.Kafka
import com.shuyun.loyalty.facade.common.withLock
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.repository.grade.GradeRepository
import com.shuyun.loyalty.facade.service.BlockListService
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.grade.GradeService
import com.shuyun.loyalty.sdk.Dataapi.noneTransaction
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Uuid
import com.shuyun.loyalty.sdk.api.model.GradeRecordType
import com.shuyun.loyalty.sdk.api.model.http.grade.GradeBatchModifyType
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeBatchModifyRequest
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeInitRequest
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeModifyRequest
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import java.time.ZonedDateTime

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.grade.GradeModify.kt")


fun Route.gradeModify() {



    post("/grade/init") {
        val req = call.receive<MemberGradeInitRequest>().also { LOGGER.info("等级初始化请求: {}", Json.toJson(it)) }.validate()
        val hierarchy = ProgramService.getGradeHierarchyById(req.gradeHierarchyId!!) ?: throw BadParamRequestException("gradeHierarchyId参数错误")
        val lockKey = "grade_calculate_${req.gradeHierarchyId}-${req.memberId}"
        withLock(lockKey) {
            //查询当前等级
            val currentGrade = noneTransaction {
                GradeRepository.queryMemberGrade(it, req.gradeHierarchyId!!, req.memberId!!)
            }
            if (currentGrade != null) {
                LOGGER.debug("等级已经存在，无需初始化 {}", currentGrade.currentGradeName)
                return@withLock
            }
            // 目标等级定义
            val targetGradeDefinition = hierarchy.gradeDefinitions.minBy { it.sort }
            val recordType = GradeRecordType.UPGRADE

            // 目标等级
            val targetGrade = MemberGrade(
                id = Uuid.uuid,
                memberId = req.memberId,
                planId = hierarchy.plan.id,
                planName = hierarchy.plan.name,
                subjectFqn = hierarchy.subject.fqn,
                gradeHierarchyId = hierarchy.id,
                gradeHierarchyName = hierarchy.name,
                currentGradeDefinitionId = targetGradeDefinition.id,
                currentGradeName = targetGradeDefinition.name,
                version = 1,
                effectDate = ZonedDateTime.now(),
                overdueDate = req.overdueDate,
                created = ZonedDateTime.now()
            )
            val gradeRecord = buildGradeRecord(
                hierarchy = hierarchy,
                originalMemberGrade = currentGrade,
                targetMemberGrade = targetGrade,
                recordType = recordType,
                changeWayType = req.changeWayType,
                triggerId = req.triggerId,
                description = req.description,
                channelType = req.channelType
            )
            GradeService.init(targetMemberGrade = targetGrade, memberGradeRecord = gradeRecord)
        }
        call.respond(HttpStatusCode.OK)
    }


    // 等级变更
    post("/grade/modify") {
        val req = call.receive<MemberGradeModifyRequest>().also { LOGGER.info("等级变更请求: {}", Json.toJson(it)) }.validate()
        val hierarchy = ProgramService.getGradeHierarchyById(req.gradeHierarchyId!!) ?: throw BadParamRequestException("gradeHierarchyId参数错误")
        val lockKey = "grade_calculate_${req.gradeHierarchyId}-${req.memberId}"
        withLock(lockKey) {
            // 目标等级定义
            val targetGradeDefinition: GradeDefinition
            if (req.gradeDefinitionId == -1L) {
                targetGradeDefinition = GradeDefinition(id = -1, name = "无等级", sort = 0)
            } else {
                val definition = hierarchy.gradeDefinitions.find { it.id == req.gradeDefinitionId }
                if (definition == null) throw BadParamRequestException("目标等级不存在")
                targetGradeDefinition = definition
            }

            //查询当前等级
            val currentGrade = noneTransaction { GradeRepository.queryMemberGrade(it, req.gradeHierarchyId!!, req.memberId!!) }
            val currentGradeDefinition = hierarchy.gradeDefinitions.find { it.id == currentGrade?.currentGradeDefinitionId }
            val recordType = GradeService.getRecordType(currentGradeDefinition, targetGradeDefinition)

            // 检查特殊名单
            BlockListService.check(
                TypeEnum.GRADE,
                hierarchy.subject.fqn,
                hierarchy.id,
                req.memberId!!,
                recordType.getForbiddenOperation()
            )

            // 目标等级
            val targetGrade = currentGrade?.copy(
                currentGradeDefinitionId = targetGradeDefinition.id,
                currentGradeName = targetGradeDefinition.name,
                version = (currentGrade.version ?: 0) + 1,
                effectDate = if (req.useOriginalEffectTime == true && currentGrade.effectDate != null) currentGrade.effectDate else ZonedDateTime.now(),
                overdueDate = req.overdueDate
            ) ?: MemberGrade(
                id = Uuid.uuid,
                memberId = req.memberId,
                planId = hierarchy.plan.id,
                planName = hierarchy.plan.name,
                subjectFqn = hierarchy.subject.fqn,
                gradeHierarchyId = hierarchy.id,
                gradeHierarchyName = hierarchy.name,
                currentGradeDefinitionId = targetGradeDefinition.id,
                currentGradeName = targetGradeDefinition.name,
                version = 1,
                effectDate = ZonedDateTime.now(),
                overdueDate = req.overdueDate,
                created = ZonedDateTime.now()
            )

            val gradeRecord = buildGradeRecord(
                hierarchy = hierarchy,
                originalMemberGrade = currentGrade,
                targetMemberGrade = targetGrade,
                recordType = recordType,
                changeWayType = req.changeWayType,
                triggerId = req.triggerId,
                description = req.description,
                channelType = req.channelType
            )

            val uniqueRecord = InterfaceRecord(
                id = req.triggerId,
                sequence = req.triggerId,
                content = Json.toJson(req),
                created = ZonedDateTime.now()
            )
            GradeService.modify(currentGrade, targetGrade, gradeRecord, uniqueRecord)
            Kafka.sendGradeNotify(req.memberId!!, gradeRecord)
        }
        call.respond(HttpStatusCode.OK)
    }

    // 批量等级变更到最高或者最低
    post("/grade/modify:batch") {
        val req = call.receive<MemberGradeBatchModifyRequest>().also { LOGGER.info("批量等级变更请求: {}", Json.toJson(it)) }.validate()
        val gradeHierarchyIds = req.gradeHierarchyIds!!.toSet()
        val params = ArrayList<GradeBatchModifyData>()

        for (gradeHierarchyId in gradeHierarchyIds) {
            val hierarchy = ProgramService.getGradeHierarchyById(gradeHierarchyId)
            if (hierarchy == null) {
                LOGGER.error("等级体系Id不存在: {}", gradeHierarchyId)
                throw BadParamRequestException("gradeHierarchyIds参数错误")
            }
            // 目标等级定义
            val targetGradeDefinition = when (req.gradeModifyType!!) {
                GradeBatchModifyType.LOWEST_GRADE -> hierarchy.gradeDefinitions.minBy { it.sort }
                GradeBatchModifyType.HIGHEST_GRADE -> hierarchy.gradeDefinitions.maxBy { it.sort }
                GradeBatchModifyType.NO_GRADE -> GradeDefinition(id = -1, name = "无等级", sort = 0)
            }

            //查询当前等级
            val currentGrade = noneTransaction { GradeRepository.queryMemberGrade(it, gradeHierarchyId, req.memberId!!) }
            val currentGradeDefinition = hierarchy.gradeDefinitions.find { it.id == currentGrade?.currentGradeDefinitionId }

            val recordType = GradeService.getRecordType(currentGradeDefinition, targetGradeDefinition)

            // 检查特殊名单
            BlockListService.check(
                TypeEnum.GRADE,
                hierarchy.subject.fqn,
                hierarchy.id,
                req.memberId!!,
                recordType.getForbiddenOperation()
            )

            // 目标等级
            val targetGrade = currentGrade?.copy(
                currentGradeDefinitionId = targetGradeDefinition.id,
                currentGradeName = targetGradeDefinition.name,
                version = (currentGrade.version ?: 0) + 1,
                effectDate = ZonedDateTime.now(),
                overdueDate = req.overdueDate
            ) ?: MemberGrade(
                id = Uuid.uuid,
                memberId = req.memberId,
                planId = hierarchy.plan.id,
                planName = hierarchy.plan.name,
                subjectFqn = hierarchy.subject.fqn,
                gradeHierarchyId = hierarchy.id,
                gradeHierarchyName = hierarchy.name,
                currentGradeDefinitionId = targetGradeDefinition.id,
                currentGradeName = targetGradeDefinition.name,
                version = 1,
                effectDate = ZonedDateTime.now(),
                overdueDate = req.overdueDate,
                created = ZonedDateTime.now()
            )

            val gradeRecord = buildGradeRecord(
                hierarchy = hierarchy,
                originalMemberGrade = currentGrade,
                targetMemberGrade = targetGrade,
                recordType = recordType,
                changeWayType = req.changeWayType,
                triggerId = req.triggerId,
                description = req.description,
                channelType = req.channelType
            )

            params.add(GradeBatchModifyData(currentGrade, targetGrade, gradeRecord, recordType))
        }

        val keys = params.map { "grade_calculate_${it.gradeRecord}-${it.gradeRecord.memberId}" }

        withLock(keys) { GradeService.modify(params) }

        params.forEach { Kafka.sendGradeNotify(req.memberId!!, it.gradeRecord) }

        call.respond(HttpStatusCode.OK)
    }

}


private fun MemberGradeBatchModifyRequest.validate(): MemberGradeBatchModifyRequest {
    if (gradeHierarchyIds.isNullOrEmpty()) throw BadParamRequestException("等级体系不能为空")
    if (memberId == null) throw BadParamRequestException("会员ID不能为空")
    if (description == null) throw BadParamRequestException("备注不能为空")
    if (channelType == null) throw BadParamRequestException("渠道类型不能为空")
    if (changeWayType == null) throw BadParamRequestException("变更方式不能为空")
    if (gradeModifyType == null) throw BadParamRequestException("变更类型不能为空")
    if (triggerId == null) throw BadParamRequestException("追溯ID不能为空")
    if(overdueDate != null && overdueDate!!.isBefore(ZonedDateTime.now())) throw BadParamRequestException("过期时间不能早于当前时间")
    ProgramService.findChannelTypeByCode(channelType!!) ?: throw ChannelTypeNotExistException()
    return this
}


private fun MemberGradeModifyRequest.validate(): MemberGradeModifyRequest {
    if (gradeHierarchyId == null) throw BadParamRequestException("等级体系ID不能为空")
    if (memberId == null) throw BadParamRequestException("会员ID不能为空")
    if (description == null) throw BadParamRequestException("备注不能为空")
    if (channelType == null) throw BadParamRequestException("渠道类型不能为空")

    if (triggerId == null) throw BadParamRequestException("追溯ID不能为空")
    if(overdueDate != null && overdueDate!!.isBefore(ZonedDateTime.now())) throw BadParamRequestException("过期时间不能早于当前时间")
    ProgramService.findChannelTypeByCode(channelType!!) ?: throw ChannelTypeNotExistException()
    if (changeWayType == null) {
        changeWayType = "INTERFACE"
    } else {
        ProgramService.findChangeModeByCode(changeWayType!!) ?: throw ChangeModeNotExistException()
    }
    return this
}

private fun MemberGradeInitRequest.validate(): MemberGradeInitRequest {
    if (gradeHierarchyId == null) throw BadParamRequestException("等级体系ID不能为空")
    if (memberId == null) throw BadParamRequestException("会员ID不能为空")
    if (description == null) throw BadParamRequestException("备注不能为空")
    if (channelType == null) throw BadParamRequestException("渠道类型不能为空")
    if (triggerId == null) throw BadParamRequestException("追溯ID不能为空")
    if(overdueDate != null && overdueDate!!.isBefore(ZonedDateTime.now())) throw BadParamRequestException("过期时间不能早于当前时间")
    ProgramService.findChannelTypeByCode(channelType!!) ?: throw ChannelTypeNotExistException()
    if (changeWayType == null) {
        changeWayType = "INTERFACE"
    } else {
        ProgramService.findChangeModeByCode(changeWayType!!) ?: throw ChangeModeNotExistException()
    }
    return this
}


// 黑名单配置类型
private fun GradeRecordType.getForbiddenOperation(): ForbiddenOperation? {
    return when (this) {
        GradeRecordType.HOLD_BACK_GRADE -> ForbiddenOperation.GRADE_HOLD
        GradeRecordType.DEGRADE -> ForbiddenOperation.GRADE_DEGRADE
        GradeRecordType.UPGRADE -> ForbiddenOperation.GRADE_UPGRADE
        else -> null
    }
}

// 构建等级变更记录
private fun buildGradeRecord(
    hierarchy: GradeHierarchy,
    originalMemberGrade: MemberGrade?,
    targetMemberGrade: MemberGrade,
    recordType: GradeRecordType,
    changeWayType: String? = null,
    triggerId: String? = null,
    description: String? = null,
    channelType: String? = null
): MemberGradeRecord {
    return MemberGradeRecord(
        id = Uuid.uuid,
        planId = hierarchy.plan.id,
        planName = hierarchy.plan.name,
        gradeHierarchyId = hierarchy.id,
        gradeHierarchyName = hierarchy.name,
        memberId = targetMemberGrade.memberId!!,
        originalGradeId = originalMemberGrade?.currentGradeDefinitionId,
        originalGradeName = originalMemberGrade?.currentGradeName,
        originalEffectDate = originalMemberGrade?.effectDate,
        originalOverdueDate = originalMemberGrade?.overdueDate,
        currentGradeId = targetMemberGrade.currentGradeDefinitionId,
        currentGradeName = targetMemberGrade.currentGradeName,
        currentEffectDate = targetMemberGrade.effectDate,
        currentOverdueDate = targetMemberGrade.overdueDate,
        recordType = recordType.name,
        changeWay = changeWayType ?: "INTERFACE",
        triggerId = triggerId,
        traceId = triggerId,
        description = description,
        subjectFqn = hierarchy.subject.fqn,
        created = ZonedDateTime.now(),
        channel = channelType!!,
        memberGradeId = originalMemberGrade?.id ?: targetMemberGrade.id
    )
}

