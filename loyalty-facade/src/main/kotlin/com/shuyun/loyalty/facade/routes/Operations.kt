package com.shuyun.loyalty.facade.routes

import com.shuyun.loyalty.facade.common.withMemberPointLock
import com.shuyun.loyalty.sdk.Dataapi.withTransaction
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Segments
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import org.apache.logging.log4j.core.config.Configurator
import java.time.Duration

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.Operations.kt")

fun Route.ops() {

    get("/ops/ping") {
        call.respondText("pong")
    }

    post("/ops/loggers") {
        val request = call.receive<LevelRequest>()
        Configurator.setLevel(request.packageName, request.configuredLevel)
        LOGGER.info("日志级别修改为: {}", request.configuredLevel)
        call.respond(LevelEffectResponse(request.configuredLevel))
    }

    post("/ops/point-migrate") {
        val req = call.receive<PointMigrateReq>().also { LOGGER.info("迁移积分: {}", Json.toJson(it)) }
        withMemberPointLock(req.pointAccountId, req.memberId, Duration.ofMillis(10000)) {
            withTransaction {
                Segments.rebuildSegment(it, req.pointAccountId, req.memberId, req.reference)
            }
        }
        call.respond(HttpStatusCode.OK)
    }
}

private data class PointMigrateReq(val pointAccountId: Long, val memberId: String, val reference: Int = 0)

private data class LevelRequest(val packageName: String, val configuredLevel: String)

private data class LevelEffectResponse(val effectiveLevel: String)