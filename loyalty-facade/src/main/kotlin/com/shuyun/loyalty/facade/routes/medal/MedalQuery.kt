package com.shuyun.loyalty.facade.routes.medal

import com.shuyun.loyalty.facade.model.BadParamRequestException
import com.shuyun.loyalty.facade.model.MedalHierarchyNotFoundExistException
import com.shuyun.loyalty.facade.model.PlanNotFoundException
import com.shuyun.loyalty.facade.repository.medal.MedalRepository
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.sdk.api.model.http.medal.MemberMedalQueryResponse
import com.shuyun.loyalty.sdk.Dataapi.noneTransaction
import com.shuyun.loyalty.sdk.Json
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*


private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.medal.medalQuery.kt")

fun Route.medalQuery() {

    // 分页勋章查询
    get("/medal/query") {
        val medalHierarchyId = call.request.queryParameters["medalHierarchyId"]
        val planId = call.request.queryParameters["planId"]
        val memberId = call.request.queryParameters["memberId"]
        val number = call.request.queryParameters["number"]
        val pageSize = call.request.queryParameters["pageSize"]
        val req = MemberMedalQueryReq(
            medalHierarchyId = medalHierarchyId?.toLong(),
            planId = planId?.toLong(),
            memberId = memberId,
            number = number?.toInt() ?: 0,
            pageSize = pageSize?.toInt() ?: 20
        ).also { LOGGER.info("获取会员勋章记录: {}", Json.toJson(it)) }.validate()

        val hierarchy = ProgramService.getMedalHierarchyById(req.medalHierarchyId!!)
            ?: throw MedalHierarchyNotFoundExistException()
        if (hierarchy.plan.id != req.planId) throw PlanNotFoundException(req.planId!!)
        //查询当前勋章
        val memberMedalQueryResponses = noneTransaction {
            MedalRepository.queryMemberMedals(
                it,
                req.medalHierarchyId,
                null,
                req.memberId!!,
                disabled = false
            )
        }.map {
            MemberMedalQueryResponse().apply {
                this.planId = it.planId
                this.planName = it.planName
                this.subjectId = hierarchy.subject.id
                this.subjectName = hierarchy.subject.name
                this.subjectFqn = it.subjectFqn
                this.medalHierarchyId = it.medalHierarchyId
                this.medalHierarchyName = it.medalHierarchyName
                this.memberId = it.memberId
                this.medalDefinitionId = it.medalDefinitionId
                this.medalDefinitionName = it.medalDefinitionName
                this.effectDate = it.effectDate
                this.overdueDate = it.overdueDate
            }
        }
        call.respond(memberMedalQueryResponses)
    }
}

private data class MemberMedalQueryReq(
    val medalHierarchyId: Long?,
    val planId: Long?,
    val memberId: String?,
    val number: Int,
    val pageSize: Int,
)


private fun MemberMedalQueryReq.validate(): MemberMedalQueryReq {
    if (planId == null) throw BadParamRequestException("计划ID不能为空")
    if (memberId == null) throw BadParamRequestException("会员ID不能为空")
    if (medalHierarchyId == null) throw BadParamRequestException("勋章账号id不能为空")
    return this
}

