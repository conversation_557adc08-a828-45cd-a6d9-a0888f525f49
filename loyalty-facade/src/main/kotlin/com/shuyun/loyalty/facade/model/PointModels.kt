package com.shuyun.loyalty.facade.model

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty
import com.shuyun.loyalty.sdk.Json
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.ZonedDateTime

data class MemberPoint(
    var id: String,
    var planId: Long,
    var pointPlanId: Long,
    var memberId: String,
    var subjectFqn: String,
    var point: BigDecimal,
    var version: Long,
    var openSegmentFlag: Boolean,
)

data class MemberPointSegment(
    var point: BigDecimal,
    var expireDate: LocalDate
)

data class BasicSegment(
    val id: String,
    val memberPointId: String,
    var point: BigDecimal,
    var expireDate: LocalDate
)

data class TimeBasedLimitRule(val cycle: TimeBasedLimitUnit, val value: BigDecimal, val type: String)

data class PointLimitResult(
    val beforeTotalPoints: BigDecimal,
    val beforePoints: BigDecimal,
    val afterPoints: BigDecimal,
    val limitRuleType: PointLimitRuleType,
    val topLimit: BigDecimal? = null,
    val singleTopLimit: BigDecimal? = null,
    var timeBasedRule: Any? = null,
    var sendLimitCalc: Any? = null,
)

data class SendLimitCalc(
    val id: String,
    val value: Map<String, Number>,
)

data class DeductionPriority(val name: String, val order: String, var next: String? = null, var equalValue: Any? = null)
data class PointMember(
    val id: String,
    val memberId: String,
    val point: BigDecimal,
    var leftSegmentDate: LocalDate? = null, // 扣减有值
)
data class PointAttr(
    var traceId: String,
    var uniqueId: String,
    var changeMode: String,
    var channel: String,
    var operatorId: String,
    var operator: String,
    var effectiveDate: String? = null,
    var overdueDate: String? = null,
    var shopId: String? = null,
    var desc: String? = null,
    var kzzd1: String? = null,
    var kzzd2: String? = null,
    var kzzd3: String? = null,
    var actionId: String? = null,
    var actionName: String? = null,
    var actionNodeId: String? = null,
    var actionNodeName: String? = null,
    var recordDetail: String? = null,
    var deductByAbolish: Boolean? = null,
    var autoFillShopId: Boolean? = null,
    var backId: String? = null,
    var businessId: String? = null, // 用于可撤销的发放
    var sourceId: String? = null, // 用于撤销的操作
    var reverseType: String? = null, // 用于撤销的操作
    var pendingPoints: Boolean? = null,
)

data class MemberPointJourneyData(
    val plan: PointPlan,
    val subject: PointSubject,
    val hierarchy: PointHierarchy,
    val member: PointMember,
    val date: ZonedDateTime,
    val type: LoyaltyRequestType,
    val attr: PointAttr,
    var pointValue: BigDecimal = BigDecimal.ZERO,
    var afterPoints: BigDecimal = BigDecimal.ZERO,
    var afterTotalPoints: BigDecimal = member.point
)

data class PointPlan(val id: Long, val name: String, val enableSendLimit: Boolean?)
data class PointSubject(val id: Long, val name: String, val dataType: String)
data class PointHierarchy(
    val id: Long,
    val name: String,
    var precision: Int,
    val rounding: RoundingMode,
    var unit: String,
    var topLimit: Long? = null,
    val singleTopLimit: Long? = null, // 999999999
    var limitRules: List<TimeBasedLimitRule>? = null,
    var sendLimitResults: List<PointLimitResult>? = null,
    var deductionPriorities: List<DeductionPriority>? = null,
    var negativeStrategy: NegativeStrategy? = null,
)

data class InitMemberPointResult(val memberPoint: MemberPoint, val actualPoint: BigDecimal, val sendLimitResult: PointLimitResult? = null)
data class SendMemberPointResult(val memberPoint: MemberPoint, val actualPoint: BigDecimal, val sendLimitResult: PointLimitResult? = null)


data class PointJourney(
    val id: String,
    val hierarchyId: Long,
    val traceId: String,
    val uniqueId: String,
    val memberId: String,
    val memberPointId: String,
    val action: String,
    val status: String,
    val data: String,
) {

    val afterPoints: BigDecimal by lazy {
        Json.objectMapper.readTree(this.data).get("afterPoints").decimalValue()
    }

    val isAPIType: Boolean by lazy {
        Json.objectMapper.readTree(this.data).get("type").textValue() == LoyaltyRequestType.API.name
    }
}

data class PointRecord(
    var id: String? = null,
    var planId: Long? = null,
    var planName: String? = null,
    var pointPlanId: Long? = null,
    var pointPlanName: String? = null,
    var memberPointId: String? = null,
    var memberId: String? = null,
    var subjectFqn: String? = null,
    var traceId: String? = null,
    var key: String? = null,
    var changeMode: String? = null,
    var channel: String? = null,
    var desc: String? = null,
    var shopId: String? = null,
    @JsonProperty("KZZD1") var kzzd1: String? = null,
    @JsonProperty("KZZD2") var kzzd2: String? = null,
    @JsonProperty("KZZD3") var kzzd3: String? = null,
    var actionId: String? = null,
    var actionName: String? = null,
    var actionNodeId: String? = null,
    var actionNodeName: String? = null,
    var ruleId: Long? = null,
    var ruleName: String? = null,
    var ruleGroupId: Long? = null,
    var ruleGroup: String? = null,
    var eventTypeName: String? = null,
    var operatorId: String? = null,
    var operator: String? = null,
    var recordType: String? = null,
    var modified: ZonedDateTime? = null,
    var created: ZonedDateTime? = null,
    var effectiveDate: ZonedDateTime? = null,
    var overdueDate: ZonedDateTime? = null,
    var point: BigDecimal? = null,
    var changePoint: BigDecimal? = null,
    var totalPoint: BigDecimal? = null,
    var status: String? = null,
    var recordDetail: String? = null,
    var extralInfo: String? = null,
    var recordSourceDetail: String? = null,
)

data class ValidPointRecord(
    var id: String,
    var planId: Long,
    var pointPlanId: Long,
    var memberPointId: String,
    var memberId: String,
    var subjectFqn: String,
    var effectiveDate: ZonedDateTime,
    var overdueDate: ZonedDateTime?,
    var point: BigDecimal,
    var gainStatementId: String,
    var fromStatus: String,
    var openTraceId: String?,
    var modified: ZonedDateTime?,
    var created: ZonedDateTime?
)


data class PointRecordItem(
    var id: String,
    var planId: Long,
    var status: String?,
    var sort: Long,
    var pointPlanId: Long,
    var traceId: String?,
    var memberId: String,
    var point: BigDecimal,
    var effectiveDate: ZonedDateTime?,
    var overdueDate: ZonedDateTime?,
    var backId: String?,
    var parentBackId: String?,
    var recordId: String?,
    var created: ZonedDateTime?,
)


data class NegativePointRecord(
    var id: String,
    var point: BigDecimal,
)

data class GainPointRecord(
    val id: String,
    val point: BigDecimal,
    val status: String?,
)


data class PointTransaction(
    var id: String? = null,
    var businessId: String? = null,
    var point: BigDecimal = BigDecimal.ZERO,
    var recordType: String? = null,
    var memberId: String? = null,
    var created: ZonedDateTime? = null,
    var modified: ZonedDateTime? = null,
)


data class PointTransactionCalc(
    var id: String? = null,
    var businessId: String? = null,
    var gainStatementId: String? = null,
    var point: BigDecimal = BigDecimal.ZERO,
    var effectiveDate: ZonedDateTime? = null,
    var overdueDate: ZonedDateTime? = null,
    var sourceId: String? = null,
    var sort: Int? = null,
    var recordId: String? = null,
    var shopId: String? = null,
    var backId: String? = null,
    var ext1: String? = null,
    var ext2: String? = null,
    var ext3: String? = null,
    var created: ZonedDateTime? = null,
    var modified: ZonedDateTime? = null,
)


data class PointTransactionStatement(
    var id: String? = null,
    var pointTransactionId: String? = null,
    var gainStatementId: String? = null,
    var point: BigDecimal = BigDecimal.ZERO,
    var effectiveDate: ZonedDateTime? = null,
    var overdueDate: ZonedDateTime? = null,
    var sort: Int? = null,
    var shopId: String? = null,
    var backId: String? = null,
    var ext1: String? = null,
    var ext2: String? = null,
    var ext3: String? = null,
    var created: ZonedDateTime? = null,
    var modified: ZonedDateTime? = null,
)

data class ImportMember(
    val id: String,
    val migrationId: String,
    val planId: Long,
    val pointId: Long,
    val memberId: String,
    val partitionNum: Int,
    val status: String,
    val createdDate: ZonedDateTime,
    val updateDate: ZonedDateTime
)

data class ImportPointRecord(
    val id: String,
    val migrationId: String,
    val planId: Long,
    val pointId: Long,
    val memberId: String,
    val action: String,
    val point: BigDecimal,
    val effectiveDate: ZonedDateTime?,
    val overdueDate: ZonedDateTime?,
    val description: String?,
    val channelType: String?,
    val shopId: String?,
    @JsonAlias("kzzd1","KZZD1") val kzzd1: String?,
    @JsonAlias("kzzd2","KZZD2") val kzzd2: String?,
    @JsonAlias("kzzd3","KZZD3") val kzzd3: String?,
    val key: String,
    val overrideHistory: Boolean,
    val createdDate: ZonedDateTime,
    var myId: String? = null
)