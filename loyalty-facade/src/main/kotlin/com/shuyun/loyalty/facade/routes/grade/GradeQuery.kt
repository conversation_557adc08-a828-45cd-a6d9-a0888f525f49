package com.shuyun.loyalty.facade.routes.grade

import com.shuyun.loyalty.facade.model.BadParamRequestException
import com.shuyun.loyalty.facade.repository.grade.GradeRepository
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeResponse
import com.shuyun.loyalty.sdk.Dataapi.noneTransaction
import com.shuyun.loyalty.sdk.Json
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.grade.GradeQuery.kt")


fun Route.gradeQuery() {
    // 查询等级主模型数据
    get("/grade") {
        val gradeHierarchyId = call.request.queryParameters["gradeHierarchyId"]
        val gradeDefinitionId = call.request.queryParameters["gradeDefinitionId"]
        val memberId = call.request.queryParameters["memberId"]
        val number = call.request.queryParameters["number"]
        val pageSize = call.request.queryParameters["pageSize"]
        val req = MemberGradeQueryReq(
            gradeHierarchyId = gradeHierarchyId?.toLong(),
            gradeDefinitionId = gradeDefinitionId?.toLong(),
            memberId = memberId,
            number = number?.toInt() ?: 0,
            pageSize = pageSize?.toInt() ?: 20
        ).also { LOGGER.info("查询等级列表: {}", Json.toJson(it)) }.validate()

        val hierarchy = ProgramService.getGradeHierarchyById(req.gradeHierarchyId!!)
        val res = noneTransaction { sdk ->
            val grades = GradeRepository.queryMemberGrades(
                sdk,
                req.gradeHierarchyId,
                req.gradeDefinitionId,
                req.memberId,
                req.number,
                req.pageSize
            )
            grades.map {
                Json.convert<MemberGradeResponse>(it).apply {
                    this.subjectId = hierarchy!!.subject.id
                    this.subjectName = hierarchy.subject.name
                    this.gradeDefinitionName = it.currentGradeName
                }
            }
        }
        call.respond(res)
    }

    // 查询当前会员等级
    get("/currentMemberGrade") {
        val gradeHierarchyId = call.request.queryParameters["gradeHierarchyId"] ?: throw BadParamRequestException("gradeHierarchyId参数为必填")
        val memberId = call.request.queryParameters["memberId"] ?: throw BadParamRequestException("memberId参数为必填")
        LOGGER.info("查询会员等级: {} {}", gradeHierarchyId, memberId)
        val hierarchy = ProgramService.getGradeHierarchyById(gradeHierarchyId.toLong()) ?: throw BadParamRequestException("gradeHierarchyId参数错误")
        val res = noneTransaction { sdk ->
            val grades = GradeRepository.queryMemberGrades(
                sdk,
                gradeHierarchyId.toLong(),
                memberId = memberId,
                pageSize = 1,
                number = 0
            )
            if (grades.isEmpty()) {
                MemberGradeResponse().apply {
                    this.planId = hierarchy.plan.id
                    this.planName = hierarchy.plan.name
                    this.subjectId = hierarchy.subject.id
                    this.subjectName = hierarchy.subject.name
                    this.subjectFqn = hierarchy.subject.fqn
                    this.gradeHierarchyId = hierarchy.id
                    this.gradeHierarchyName = hierarchy.name
                    this.memberId = memberId
                    this.currentGradeDefinitionId = -1
                    this.gradeDefinitionName = "无等级"
                }
            } else {
                grades.map {
                    Json.convert<MemberGradeResponse>(it).apply {
                        this.subjectId = hierarchy.subject.id
                        this.subjectName = hierarchy.subject.name
                        this.gradeDefinitionName = it.currentGradeName
                    }
                }.first()
            }
        }
        call.respond(res)
    }
}


private data class MemberGradeQueryReq(
    val gradeHierarchyId: Long?,
    val gradeDefinitionId: Long?,
    val memberId: String?,
    val number: Int,
    val pageSize: Int,
)


private fun MemberGradeQueryReq.validate(): MemberGradeQueryReq {
    if (gradeHierarchyId == null) throw BadParamRequestException("gradeHierarchyId参数为必填")
    ProgramService.getGradeHierarchyById(gradeHierarchyId) ?: throw BadParamRequestException("gradeHierarchyId参数错误")
    return this
}

