package com.shuyun.loyalty.facade.routes.points

import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.points.PointRecordService
import com.shuyun.loyalty.sdk.api.model.Page
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointRecordCountResponse
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointRecordDetailResponse
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointRecordItemResponse
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointRecordResponse
import com.shuyun.loyalty.sdk.Json
import io.ktor.http.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import java.math.BigDecimal
import java.time.ZonedDateTime

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.points.PointRecordQuery.kt")


fun Route.pointRecordQuery() {

    // 分页查询会员积分记录
    get("/point:record:page") {
        val req = call.parameters.getMemberRecordQueryReq().also { LOGGER.debug("分页查询会员积分记录: {}", Json.toJson(it)) }.validate()
        val hierarchy = ProgramService.getPointAccountTypeById(req.pointAccountId!!) ?: throw BadParamRequestException("积分账户不存在")
        val recordTypes = mutableSetOf<String>()
        req.recordType?.let { recordTypes.add(it) }
        req.recordTypes?.let { recordTypes.addAll(it) }
        val response = PointRecordService.pagePointRecord(
            pointAccountId = req.pointAccountId,
            recordTypes = recordTypes.toList(),
            status = req.status,
            memberId = req.memberId,
            shopId = req.shopId,
            channelType = req.channelType,
            startTime = req.startTime.toZonedDateTime(),
            endTime = req.endTime.toZonedDateTime(),
            traceId = req.traceId,
            sortType = req.sortType?.v,
            changeMode = req.changeMode,
            number = req.number,
            pageSize = req.pageSize,
            withTotal = true
        )
        val page = Page(
            content = response.content.map { it.toMemberPointRecordResponse(hierarchy) },
            totalElements = response.totalElements,
            totalPages = response.totalPages,
            number = response.number,
            size = response.size
        )
        call.respond(page)
    }


    // 查询会员积分记录列表
    get("/point:record:list") {
        val req = call.parameters.getMemberRecordQueryReq().also { LOGGER.debug("查询会员积分记录列表: {}", Json.toJson(it)) }.validate()
        val hierarchy = ProgramService.getPointAccountTypeById(req.pointAccountId!!) ?: throw BadParamRequestException("积分账户不存在")
        val recordTypes = mutableSetOf<String>()
        req.recordType?.let { recordTypes.add(it) }
        req.recordTypes?.let { recordTypes.addAll(it) }
        val response = PointRecordService.queryPointRecord(
            pointAccountId = req.pointAccountId,
            recordTypes = recordTypes.toList(),
            status = req.status,
            memberId = req.memberId,
            shopId = req.shopId,
            channelType = req.channelType,
            startTime = req.startTime.toZonedDateTime(),
            endTime = req.endTime.toZonedDateTime(),
            traceId = req.traceId,
            sortType = req.sortType?.v,
            changeMode = req.changeMode,
            number = req.number,
            pageSize = req.pageSize,
        )
        val list = response.map { it.toMemberPointRecordResponse(hierarchy) }
        call.respond(list)
    }


    get("/point:record:count") {
        val req = call.parameters.getMemberRecordQueryReq().also { LOGGER.debug("查询会员积分记录条数: {}", Json.toJson(it)) }.validate()
        ProgramService.getPointAccountTypeById(req.pointAccountId!!) ?: throw BadParamRequestException("积分账户不存在")
        val response = PointRecordService.pagePointRecord(
            pointAccountId = req.pointAccountId,
            recordTypes = req.recordTypes,
            status = req.status,
            memberId = req.memberId,
            shopId = req.shopId,
            channelType = req.channelType,
            startTime = req.startTime.toZonedDateTime(),
            endTime = req.endTime.toZonedDateTime(),
            traceId = req.traceId,
            sortType = req.sortType?.v,
            changeMode = req.changeMode,
            number = req.number,
            pageSize = req.pageSize,
            withTotal = true,
            withoutPageContent = false
        )
        call.respond(MemberPointRecordCountResponse().apply {
            this.totalElements = response.totalElements
        })
    }


    // 查询单笔会员积分记录详情
    get("/point:record:detail") {
        val pointAccountId = call.parameters["pointAccountId"] ?: throw BadParamRequestException("积分账户ID不能为空")
        val recordId = call.parameters["recordId"] ?: throw BadParamRequestException("积分记录ID不能为空")
        LOGGER.debug("查询单笔会员积分记录详情: {} {}", pointAccountId, recordId)
        val hierarchy = ProgramService.getPointAccountTypeById(pointAccountId.toLong()) ?: throw BadParamRequestException("积分账户不存在")
        val response = PointRecordService.queryPointRecord(
            pointAccountId = hierarchy.id,
            recordId = recordId,
            number = 0,
            pageSize = 1,
        )
        val list = response.map {
            Json.convert<MemberPointRecordDetailResponse>(it).apply {
                this.planName = hierarchy.plan.name
                this.pointPlanName = hierarchy.name
                this.subjectFqn = hierarchy.subject.fqn
                this.created = it.created ?: ZonedDateTime.now()
            }
        }
        val res = list.firstOrNull() ?: throw PointRecordNotFoundException()
        call.respond(res)
    }


    // 查询积分明细记录
    get("/point:record:item") {
        val pointAccountId = call.parameters["pointAccountId"]?.toLong() ?: throw BadParamRequestException("pointAccountId不能为空")
        val triggerId = call.parameters["triggerId"] ?: throw BadParamRequestException("triggerId不能为空")
        LOGGER.debug("查询积分明细记录: {} {}", pointAccountId, triggerId)
        val response = PointRecordService.queryPointRecordItemByTraceId(pointAccountId, triggerId)
        val list = response.map { Json.convert<MemberPointRecordItemResponse>(it) }
        call.respond(list)
    }

}


private data class MemberRecordQueryReq(
    val pointAccountId: Long?,
    val recordType: String?,
    val recordTypes: List<String>?,
    val status: List<String>?,
    val memberId: String?,
    val shopId: String?,
    val channelType: String?,
    val startTime: String?,
    val endTime: String?,
    val traceId: String?,
    val sortType: QuerySortType?,
    val changeMode: String?,
    val number: Int,
    val pageSize: Int,
)


private fun String?.toZonedDateTime(): ZonedDateTime? {
    if (this == null) return null
    return ZonedDateTime.parse(this)
}

private fun MemberRecordQueryReq.validate(): MemberRecordQueryReq {
    if (pointAccountId == null) {
        throw BadParamRequestException("积分账户ID不能为空")
    }
    startTime?.checkISODate("startTime参数格式错误")
    endTime?.checkISODate("endTime参数格式错误")
    return this
}


private fun Parameters.getMemberRecordQueryReq(): MemberRecordQueryReq {
    val req = MemberRecordQueryReq(
        pointAccountId = this["pointAccountId"]?.toLong(),
        recordType = this["recordType"],
        recordTypes = this.getAll("recordTypes"),
        status = this.getAll("status"),
        memberId = this["memberId"],
        shopId = this["shopId"],
        channelType = this["channelType"],
        startTime = this["startTime"],
        endTime = this["endTime"],
        traceId = this["traceId"],
        sortType = QuerySortType.from(this["sortType"]),
        changeMode = this["changeMode"],
        number = this["number"]?.toInt() ?: 0,
        pageSize = this["pageSize"]?.toInt() ?: 20,
    )
    return req
}


private fun getSignedPoint(status: String?, point: BigDecimal, recordType: String?): BigDecimal {
    // 负积分状态
    val negativePCStatus = listOf("EXPIRE","FROZEN","ABOLISH","USED")
    // 正积分状态
    val positivePCStatus = listOf("DELAY", "DELAY_FROZEN","DELAY_ABOLISH","FROZEN_ABOLISH")
    return when {
        recordType == "RECALCULATE" -> point
        negativePCStatus.contains(status) -> -point
        status == "VALID" && recordType != "TIMER" -> point
        positivePCStatus.contains(status) -> BigDecimal.ZERO
        else -> point
    }
}

private fun PointRecord.toMemberPointRecordResponse(accountType: PointAccountType): MemberPointRecordResponse {
    val self = this
    return Json.convert<MemberPointRecordResponse>(this).apply {
        this.channelType = self.channel
        this.planName = accountType.plan.name
        this.subjectId = accountType.subject.id
        this.subjectName = accountType.subject.name
        this.subjectFqn = accountType.subject.fqn
        this.pointAccountTypeId = accountType.id
        this.pointAccountTypeName = accountType.name
        this.signedPoint = getSignedPoint(this.status, this.point!!, this.recordType)
    }
}




