package com.shuyun.loyalty.facade.common

import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.facade.model.Constants
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Property
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.withContext
import org.apache.kafka.clients.admin.AdminClient
import org.apache.kafka.clients.admin.ListTopicsOptions
import org.apache.kafka.clients.admin.NewTopic
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.Producer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.header.internals.RecordHeader
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import java.time.ZonedDateTime
import java.util.*
import kotlin.reflect.full.memberProperties

object Kafka {

    private val logger = LoggerFactory.getLogger(Kafka::class.java)

    private lateinit var producer: Producer<String, String>

    private val POINT_TOPIC by lazy { GlobalContext.environment() + "_" + "LOYALTY_HTTP_REQUEST_ASYNC_PROCESS" }
    private val GRADE_NOTIFY_TOPIC by lazy {  "LOYALTY_GRADE_NOTIFY_${GlobalContext.environment()}" }
    private val POINT_MODIFY_TOPIC by lazy {  "POINT_MODIFY_CHANNEL_${GlobalContext.environment()}" }
    private val POINT_GREEN_TOPIC by lazy {  "LOYALTY_GREEN_CHANNEL_${GlobalContext.environment()}" }
    private val MEDAL_NOTIFY_TOPIC by lazy {  "LOYALTY_MEDAL_NOTIFY_${GlobalContext.environment()}" }

    fun init() {
        //初始化topic
        val props = Properties()
        props["bootstrap.servers"] = Property.getSysOrEnv("kafka.address", "localhost:9092")

        val admin = AdminClient.create(props)
        val topics = admin.listTopics(ListTopicsOptions().listInternal(true)).names().get()
        if (!topics.contains(POINT_TOPIC)) {
            admin.createTopics(
                listOf(
                    NewTopic(
                        POINT_TOPIC,
                        Property.getSysOrEnv("kafka.min.partition.num", 60),
                        Property.getSysOrEnv("kafka.replication.factor", 1).toShort()
                    )
                )
            ).all().get()
        }
        admin.close()

        props.apply {
            this["acks"] = Property.getSysOrEnv("kafka.acks", "all")
            this["retries"] = Property.getSysOrEnv("kafka.retries", 3)
            this["batch.size"] = Property.getSysOrEnv("kafka.batch.size", 16384)
            this["linger.ms"] = Property.getSysOrEnv("kafka.linger.ms", 1)
            this["buffer.memory"] = Property.getSysOrEnv("kafka.buffer.memory", ********)
            this["key.serializer"] = "org.apache.kafka.common.serialization.StringSerializer"
            this["value.serializer"] = "org.apache.kafka.common.serialization.StringSerializer"
        }
        producer = KafkaProducer(props)

        Runtime.getRuntime().addShutdownHook(Thread { producer.close() })
    }

    fun sendPointCalc(pointAccountTypeId: Long, memberPointId: String, memberId: String, reqTraceId: String? = null) {
        val payload = mapOf(
            "pointAccountTypeId" to pointAccountTypeId,
            "memberPointId" to memberPointId,
            "memberId" to memberId
        )
        val logTraceId = reqTraceId ?: (MDC.get(Constants.LOYALTY_TRACE_ID) ?: "-")
        val record = ProducerRecord(POINT_TOPIC, memberId, Json.toJson(payload)).also {
            it.headers().add(RecordHeader(Constants.LOYALTY_TRACE_ID, logTraceId.toByteArray()))
        }
        producer.send(record) { _, exception ->
            exception?.let { logger.error("发送kafka遇到错误 logTraceId: {} payload: {}", logTraceId, payload) }
        }
    }


    fun sendGradeNotify(memberId: String, payload: Any) {
        if (Property.getSysOrEnv("loyalty.notify.enabled", false)) {
            val logTraceId = MDC.get(Constants.LOYALTY_TRACE_ID) ?: "-"
            val record = ProducerRecord(GRADE_NOTIFY_TOPIC, memberId, toNotifyJson(payload)).also {
                it.headers().add(RecordHeader(Constants.LOYALTY_TRACE_ID, logTraceId.toByteArray()))
            }
            producer.send(record) { _, exception ->
                exception?.let { logger.error("发送kafka遇到错误 logTraceId: {} payload: {}", logTraceId, payload) }
            }
        }
    }

    suspend fun sendPointModify(memberId: String, payload: String, onError: ((Throwable) -> Unit)? = null) {
        val logTraceId = MDC.get(Constants.LOYALTY_TRACE_ID) ?: "-"
        val record = ProducerRecord(POINT_MODIFY_TOPIC, memberId, payload).also {
            it.headers()
                .add(RecordHeader(Constants.LOYALTY_TRACE_ID, logTraceId.toByteArray()))
                .add(RecordHeader("tenantId", Constants.localTenantId.toByteArray()))
                .add(RecordHeader("memberId", memberId.toByteArray()))
        }
        withContext(Dispatchers.IO + MDCContext()) {
            producer.send(record) { _, exception ->
                exception?.let { onError?.invoke(exception) }
            }
        }
    }


    fun sendPointGreen(memberId: String, payload: String) {
        val logTraceId = MDC.get(Constants.LOYALTY_TRACE_ID) ?: "-"
        val record = ProducerRecord(POINT_GREEN_TOPIC, memberId, payload).also {
            it.headers().add(RecordHeader(Constants.LOYALTY_TRACE_ID, logTraceId.toByteArray()))
        }
        producer.send(record) { _, exception ->
            exception?.let { logger.error("发送kafka遇到错误 logTraceId: {} topic: {} payload: {}", logTraceId, POINT_GREEN_TOPIC, payload) }
        }
    }

    fun sendMedalNotify(memberId: String, payload: Any) {
        if (Property.getSysOrEnv("loyalty.notify.enabled", false)) {
            val logTraceId = MDC.get(Constants.LOYALTY_TRACE_ID) ?: "-"
            val record = ProducerRecord(MEDAL_NOTIFY_TOPIC, memberId, toNotifyJson(payload)).also {
                it.headers().add(RecordHeader(Constants.LOYALTY_TRACE_ID, logTraceId.toByteArray()))
            }
            producer.send(record) { _, exception ->
                exception?.let { logger.error("发送kafka遇到错误 logTraceId: {} payload: {}", logTraceId, payload) }
            }
        }
    }

    private fun <T : Any> T.toMap(): Map<String, Any?> {
        return this::class.memberProperties.associate { prop ->
            prop.name to prop.call(this)
        }
    }


    private fun toNotifyJson(data: Any): String {
        if (Property.getSysOrEnv("use.escape.date.c", true)) {
            val map = data.toMap()
            val map2 = LinkedHashMap<String, Any?>()
            for ((k, v) in map) {
                if (k == "created" || k == "modified") {
                    continue
                }
                if (v is ZonedDateTime) {
                    map2[k] = Json.toJson(v)
                } else {
                    map2[k] = v
                }
            }
            return Json.toJson(map2)
        } else {
            return Json.toJson(data)
        }
    }
}