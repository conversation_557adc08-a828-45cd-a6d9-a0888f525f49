package com.shuyun.loyalty.facade.routes.points

import com.shuyun.loyalty.facade.common.Kafka
import com.shuyun.loyalty.facade.common.withMemberPointLock
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.model.Constants.DEFAULT_TX_CHANGE_MODE
import com.shuyun.loyalty.facade.model.Constants.DEFAULT_WAIT_TIME
import com.shuyun.loyalty.facade.service.BlockListService
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.points.MemberPointService
import com.shuyun.loyalty.facade.service.points.PointModifyService
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointSendRequest
import com.shuyun.loyalty.sdk.Json
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import java.math.BigDecimal
import java.time.Duration
import java.time.ZonedDateTime

// 发放积分
private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.points.PointSend.kt")


fun Route.pointSend() {
    post("/point:send") {
        val req = call.receive<MemberPointSendRequest>().also { LOGGER.info("发放会员积分: {}", Json.toJson(it)) }.validate()
        val accountType = ProgramService.getPointAccountTypeById(req.pointAccountId!!)!!
        // 判断会员是否在会员表中存在 和 检查特殊名单
        BlockListService.check(
            TypeEnum.POINT,
            accountType.subject.fqn,
            accountType.id,
            req.memberId!!,
            ForbiddenOperation.POINT_SEND
        )
        val timeout = req.lockWaitTime ?: DEFAULT_WAIT_TIME
        val now = ZonedDateTime.now()
        val point = req.point!!.scale(req.pointAccountId!!)
        val res = withMemberPointLock(req.pointAccountId!!, req.memberId!!, Duration.ofMillis(timeout)) {
            val memberPoint = MemberPointService.findMemberPoint(req.pointAccountId!!, req.memberId!!, now.shDate())
            val isTX = req.tx == true
            PointModifyService.sendMemberPoint(accountType, memberPoint, req, point, now, tx = isTX)
        }
        Kafka.sendPointCalc(req.pointAccountId!!, res.memberPointId!!, req.memberId!!)
        call.respond(res)
    }
}


private fun MemberPointSendRequest.validate(): MemberPointSendRequest {
    if (pointAccountId == null) throw BadParamRequestException("pointAccountId参数为必填")
    if (memberId == null) throw BadParamRequestException("memberId参数为必填")
    if (uniqueId == null) throw BadParamRequestException("uniqueId参数为必填")
    if (channelType == null) throw BadParamRequestException("channelType参数为必填")
    if (effectiveDate != null && overdueDate != null && effectiveDate!!.isAfter(overdueDate!!)) throw BadParamRequestException("积分生效时间必须小于积分失效时间")
    if (overdueDate != null && overdueDate!!.shDate().isBefore(ZonedDateTime.now().shDate())) throw BadParamRequestException("积分失效时间必须大于当前时间")
    if (point == null || point!! <= BigDecimal.ZERO) throw BadParamRequestException("积分值必须大于0")
    ProgramService.getPointAccountTypeById(pointAccountId!!) ?: throw BadParamRequestException("pointAccountId参数错误")
    ProgramService.findChannelTypeByCode(channelType!!) ?: throw ChannelTypeNotExistException()
    changeMode?.let { if (ProgramService.findChangeModeByCode(it) == null) throw ChangeModeNotExistException() }
    effectiveDate = effectiveDate?.withNano(0) ?: ZonedDateTime.now().withNano(0)
    if (changeMode == null) {
        changeMode = if (tx == true) DEFAULT_TX_CHANGE_MODE else Constants.DEFAULT_CHANGE_MODE
    }
    if (tx == true) {
        if (triggerId.isNullOrEmpty()) throw BadParamRequestException("triggerId参数为必填")
    }
    overdueDate = overdueDate.adjustToSHTimeEnd()
    return this
}


