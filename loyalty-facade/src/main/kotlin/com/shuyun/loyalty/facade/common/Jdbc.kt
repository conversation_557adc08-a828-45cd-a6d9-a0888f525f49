package com.shuyun.loyalty.facade.common

import com.shuyun.loyalty.sdk.Property
import com.zaxxer.hikari.HikariConfig
import com.zaxxer.hikari.HikariDataSource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.withContext
import java.sql.Connection


object Jdbc {

    private val ds: HikariDataSource by lazy {
        val config = HikariConfig()
        config.jdbcUrl = Property.getSysOrEnv("database.url")
        config.username = Property.getSysOrEnv("database.username")
        config.password = Property.getSysOrEnv("database.password")
        config.driverClassName = Property.getSysOrEnv("database.driverClass","com.mysql.cj.jdbc.Driver")
        config.addDataSourceProperty("cachePrepStmts", "true")
        config.addDataSourceProperty("prepStmtCacheSize", "250")
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048")
        val ds = HikariDataSource(config)
        Runtime.getRuntime().addShutdownHook(Thread { ds.close() })
        ds
    }

    suspend fun <T> withTransaction(readOnly: Boolean = false, block: (Connection) -> T): T = withContext(Dispatchers.IO + MDCContext()) {
        withTx(readOnly) { block(it) }
    }

    fun <T> withTx(readOnly: Boolean = false, block: (Connection) -> T): T {
        return ds.connection.use {
            it.autoCommit = false
            it.isReadOnly = readOnly
            try {
                val result = block(it)
                it.commit()
                result
            } catch (e: Exception) {
                it.rollback()
                throw e
            } finally {
                it.autoCommit = true
            }
        }
    }
}