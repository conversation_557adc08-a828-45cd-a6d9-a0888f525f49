package com.shuyun.loyalty.facade.repository.medal

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.MemberMedalRecord
import com.shuyun.loyalty.facade.model.utcStr
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.api.model.MedalRecordType
import java.time.ZonedDateTime

object MedalRecordRepository {

    private const val COLUMNS = "`id`,`planId`,`planName`,`medalHierarchyId`,`medalHierarchyName`,`medalDefinitionId`,`medalDefinitionName`,`currentMedalDefinitionIds`,`memberMedalId`,`memberId`,`originalEffectDate`,`originalOverdueDate`,`currentEffectDate`,`currentOverdueDate`,`recordType`,`changeWay`,`triggerId`,`traceId`,`recordSourceDetail`,`operator`,`subjectFqn`,`description`,`recordDetail`,`created`,`channel`,`eventTypeName`,`ruleId`,`ruleName`,`KZZD1`,`KZZD2`,`KZZD3`,`eventTypeId`,`key`,`eventFqn`"
    private const val COLUMNS_WITH_REF = "${COLUMNS}, `memberMedal` "


    fun queryMemberMedalRecord(
        sdk: DataapiWebSocketSdk,
        medalHierarchyId: Long,
        medalDefinitionId: Long?,
        memberId: String? = null,
        startTime: ZonedDateTime? = null,
        endTime: ZonedDateTime? = null,
        channelType: String? = null,
        recordType: MedalRecordType? = null,
        sortType: String? = null,
        number: Int = 0,
        pageSize: Int = 20,
    ): List<MemberMedalRecord> {
        val fromWhere = """
            FROM data.loyalty.member.hierarchy.MedalRecord${medalHierarchyId} 
            WHERE medalHierarchyId = :medalHierarchyId
            ${if (memberId != null) " AND memberId = :memberId" else ""}
            ${if (medalDefinitionId != null) " AND medalDefinitionId = :medalDefinitionId" else ""}
            ${if (channelType != null) " AND channel = :channelType" else ""}
            ${if (recordType != null) " AND recordType = :recordType" else ""}
            ${if (startTime != null) " AND created >= :startTime" else ""}
            ${if (endTime != null) " AND created <= :endTime" else ""}
            ORDER BY created ${sortType ?: "DESC"}
            LIMIT $pageSize OFFSET ${number * pageSize}
        """.trimIndent()
        val params = mapOf(
            "medalHierarchyId" to medalHierarchyId,
            "medalDefinitionId" to medalDefinitionId,
            "memberId" to memberId,
            "channelType" to channelType,
            "recordType" to recordType?.name,
            "startTime" to startTime?.utcStr(),
            "endTime" to endTime?.utcStr()
        )
        val columnSql = "SELECT $COLUMNS $fromWhere"
        val response = sdk.execute(columnSql, params)
        val records: List<MemberMedalRecord> = Json.convert(response.data)
        return  records
    }






    fun insertMemberMedal(sdk: DataapiWebSocketSdk, record: MemberMedalRecord) {
        val sql = """
            INSERT INTO data.loyalty.member.hierarchy.MedalRecord${record.medalHierarchyId} (
                $COLUMNS_WITH_REF
            ) VALUES (
               :id, :planId, :planName, :medalHierarchyId, :medalHierarchyName, :medalDefinitionId, :medalDefinitionName, :currentMedalDefinitionIds, :memberMedalId, :memberId, :originalEffectDate, :originalOverdueDate, :currentEffectDate, :currentOverdueDate, :recordType, :changeWay, :triggerId, :traceId, :recordSourceDetail, :operator, :subjectFqn, :description, :recordDetail, :created, :channel, :eventTypeName, :ruleId, :ruleName, :KZZD1, :KZZD2, :KZZD3 :,eventTypeId, :key, :eventFqn, :memberMedal
            )
        """.trimIndent()
        val params = mapOf(
            "id" to record.id,
            "planId" to record.planId,
            "planName" to record.planName,
            "medalHierarchyId" to record.medalHierarchyId,
            "medalHierarchyName" to record.medalHierarchyName,
            "medalDefinitionId" to record.medalDefinitionId,
            "medalDefinitionName" to record.medalDefinitionName,
            "currentMedalDefinitionIds" to record.currentMedalDefinitionIds,
            "memberMedalId" to record.memberMedalId,
            "memberId" to record.memberId,
            "originalEffectDate" to record.originalEffectDate?.utcStr(),
            "originalOverdueDate" to record.originalOverdueDate?.utcStr(),
            "currentEffectDate" to record.currentEffectDate?.utcStr(),
            "currentOverdueDate" to record.currentOverdueDate?.utcStr(),
            "recordType" to record.recordType,
            "changeWay" to record.changeWay,
            "triggerId" to record.triggerId,
            "traceId" to record.traceId,
            "recordSourceDetail" to record.recordSourceDetail,
            "operator" to record.operator,
            "subjectFqn" to record.subjectFqn,
            "description" to record.description,
            "recordDetail" to record.recordDetail,
            "created" to record.created,
            "channel" to record.channel,
            "eventTypeName" to record.eventTypeName,
            "ruleId" to record.ruleId,
            "ruleName" to record.ruleName,
            "KZZD1" to record.KZZD1,
            "KZZD2" to record.KZZD2,
            "KZZD3" to record.KZZD3,
            "eventTypeId" to record.eventTypeId,
            "key" to record.key,
            "eventFqn" to record.eventFqn,
            "memberMedal" to record.memberMedalId
        )
        sdk.execute(sql, params)
    }

}