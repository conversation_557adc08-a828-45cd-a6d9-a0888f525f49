package com.shuyun.loyalty.facade.repository.points

import com.github.f4b6a3.tsid.Tsid
import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.Constants.LONG_TERM_OVERDUE_DATE
import com.shuyun.loyalty.facade.model.ImportMember
import com.shuyun.loyalty.facade.model.ImportPointRecord
import com.shuyun.loyalty.sdk.Dataapi
import com.shuyun.loyalty.sdk.toEpochMilli
import com.shuyun.loyalty.sdk.utcStr
import org.apache.commons.codec.digest.DigestUtils
import java.time.ZonedDateTime

object PointImportRepository {

    private const val RECORD_ID_MIN_WIDTH = 10_000_000_000_000

    fun insertImportMember(sdk: DataapiWebSocketSdk, member: ImportMember) {
        val sql = """
            INSERT INTO data.loyalty.migration.transfer.member 
            (`id`,`migrationId`,`planId`,`pointId`,`memberId`,`partitionNum`,`status`,`createdDate`,`updateDate`)
            VALUES 
            (:id, :migrationId, :planId, :pointId, :memberId, :partitionNum, :status, :createdDate, :updateDate)
        """.trimIndent()
        val params = mapOf(
            "id" to member.id,
            "migrationId" to member.migrationId,
            "planId" to member.planId,
            "pointId" to member.pointId,
            "memberId" to member.memberId,
            "partitionNum" to member.partitionNum,
            "status" to member.status,
            "createdDate" to member.createdDate.utcStr(),
            "updateDate" to member.updateDate.utcStr()
        )
        sdk.execute(sql, params)
    }


    fun countImportMember(sdk: DataapiWebSocketSdk, migrationId: String, pointId: Long): Long {
        val sql = """
            SELECT count(1) as count 
            FROM data.loyalty.migration.transfer.member 
            WHERE migrationId = :migrationId AND pointId = :pointId
        """.trimIndent()
        val params = mapOf(
            "migrationId" to migrationId,
            "pointId" to pointId
        )
        val c = sdk.execute(sql, params).data.firstOrNull() as? Map<*, *>
        return c?.get("count")?.toString()?.toLong() ?: 0
    }


    fun batchInsertImportRecord(sdk: DataapiWebSocketSdk, records: List<ImportPointRecord>) {
        var sql = """
            INSERT INTO data.loyalty.migration.transfer.log 
            (`id`,`migrationId`,`planId`,`pointId`,`memberId`,`action`,`point`,`effectiveDate`,`overdueDate`,`description`,`channelType`,`shopId`,`kzzd1`,`kzzd2`,`kzzd3`,`key`,`overrideHistory`,`createdDate`)
            VALUES 
        """.trimIndent()
        for (r in records) {
            val effectiveDate = if (r.action == "SEND") (r.effectiveDate ?: r.createdDate) else null
            val overdueDate = if (r.action == "SEND" ) (r.overdueDate ?: LONG_TERM_OVERDUE_DATE) else null
            val id = r.myId ?: ((RECORD_ID_MIN_WIDTH + r.createdDate.toEpochMilli()).toString() + Tsid.fast().toLowerCase())
            sql += """
                (
                ${id.c()},
                ${r.migrationId.c()},
                ${r.planId},
                ${r.pointId},
                ${r.memberId.c()},
                ${r.action.c()},
                ${r.point},
                ${effectiveDate.utcStr().c()},
                ${overdueDate.utcStr().c()},
                ${r.description.c()},
                ${r.channelType.c()},
                ${r.shopId.c()},
                ${r.kzzd1.c()},
                ${r.kzzd2.c()},
                ${r.kzzd3.c()},
                ${r.key.c()},
                ${r.overrideHistory},
                ${r.createdDate.utcStr().c()}
                ),
            """.trimIndent()
        }
        sql = sql.dropLast(1)
        sdk.execute(sql, emptyMap())
    }


    fun fetchImportMember(migrationId: String, pointAccountTypeId: Long, block: (ImportMember) -> Unit) {
        val sql ="""
            SELECT migrationId, planId, `pointId`,`memberId` 
            FROM data.loyalty.migration.transfer.log 
            WHERE migrationId = :migrationId AND pointId = :accountTypeId 
            ORDER by pointId, memberId, createdDate asc"""
        .trimIndent()
        var cur = ""
        Dataapi.fetch(sql, mapOf("migrationId" to migrationId, "accountTypeId" to pointAccountTypeId)) {
            for (map in it) {
                val planId = map["planId"].toString().toLong()
                val hierarchyId = map["pointId"].toString().toLong()
                val memberId = map["memberId"] as String
                val md5 = DigestUtils.md5Hex("$migrationId:$hierarchyId:$memberId")
                if (cur != md5) {
                    cur = md5
                    val member = ImportMember(
                        id = md5,
                        migrationId = migrationId,
                        planId = planId,
                        pointId = hierarchyId,
                        memberId = memberId,
                        partitionNum = 0,
                        status = "IN_PROGRESS",
                        createdDate = ZonedDateTime.now(),
                        updateDate = ZonedDateTime.now()
                    )
                    block(member)
                }
            }
        }
    }


    private fun String?.c(): String {
        return when (this) {
            null -> "NULL"
            else -> "'${this.replace("""(?<!\\)\\(?!\\)""".toRegex(), """\\\\""").replace("'", "\\'").replace("\"", "\\\"")}'"
        }
    }
}