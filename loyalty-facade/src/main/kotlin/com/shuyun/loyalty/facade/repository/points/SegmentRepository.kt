package com.shuyun.loyalty.facade.repository.points

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.sdk.Json
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit

object SegmentRepository {

    private const val COLUMNS = "id, planId, pointPlanId, memberPointId,memberId, subjectFqn, point, expireDate, modified, created, memberPoint"
    private const val VALUES_COLUMNS = ":id, :planId, :pointPlanId, :memberPointId, :memberId, :subjectFqn, :point, :expireDate, :modified, :created, :memberPoint"

    fun insertPointSegment(
        sdk: DataapiWebSocketSdk,
        id: String,
        planId: Long,
        pointPlanId: Long,
        memberPointId: String,
        memberId: String,
        subjectFqn: String,
        point: BigDecimal,
        expireDate: LocalDate
    ) {
        val sql = "INSERT INTO data.loyalty.member.account.PointSegment${pointPlanId} ( $COLUMNS ) VALUES ( $VALUES_COLUMNS )"
        val params = mapOf(
            "id" to id,
            "planId" to planId,
            "pointPlanId" to pointPlanId,
            "memberPointId" to memberPointId,
            "memberId" to memberId,
            "subjectFqn" to subjectFqn,
            "point" to point,
            "expireDate" to expireDate,
            "modified" to ZonedDateTime.now(),
            "created" to ZonedDateTime.now(),
            "memberPoint" to memberPointId
        )
        sdk.execute(sql, params)
    }


    fun batchInsert(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, segments: List<BasicSegment>) {
        if (segments.isEmpty()) return
        var sql = "INSERT INTO data.loyalty.member.account.PointSegment${memberPoint.pointPlanId} ($COLUMNS) VALUES "
        var i = 0L
        val now = ZonedDateTime.now()
        for (item in segments) {
            val created = now.plus(i++, ChronoUnit.MILLIS).utcStr()
            sql += """
                (
                '${item.id}', ${memberPoint.planId}, ${memberPoint.pointPlanId}, '${memberPoint.id}', 
                '${memberPoint.memberId}', '${memberPoint.subjectFqn}', ${item.point}, '${item.expireDate}', 
                '${created}', '${created}', '${memberPoint.id}'
                ),
            """.trimIndent()
        }
        sql = sql.trim().dropLast(1)
        sdk.execute(sql, emptyMap())
    }


    fun deleteByIds(sdk: DataapiWebSocketSdk, pointAccountId: Long, ids: List<String>) {
        if (ids.isEmpty()) {
            return
        }
        val sql = """
            DELETE FROM data.loyalty.member.account.PointSegment${pointAccountId} 
            WHERE id IN (:ids)
        """.trimIndent()
        val params = mapOf("ids" to ids)
        sdk.execute(sql, params)
    }


    fun updateByIds(sdk: DataapiWebSocketSdk, pointAccountId: Long, ids: List<String>, point: BigDecimal) {
        if (ids.isEmpty()) {
            return
        }
        val sql = """
            UPDATE data.loyalty.member.account.PointSegment${pointAccountId} 
            SET point = point + :point, modified = :modified
            WHERE id IN (:ids)
        """.trimIndent()
        val params = mapOf(
            "ids" to ids,
            "point" to point,
            "modified" to ZonedDateTime.now()
        )
        sdk.execute(sql, params)
    }


    fun selectPointSegments(sdk: DataapiWebSocketSdk, pointAccountId: Long, memberPointId: String): List<BasicSegment> {
        val sql = """
            SELECT id, memberPointId, point, expireDate FROM data.loyalty.member.account.PointSegment${pointAccountId} 
            WHERE memberPointId = :memberPointId
            LIMIT 10000
        """.trimIndent()
        val params = mapOf("memberPointId" to memberPointId)
        val response = sdk.execute(sql, params)
        return Json.convert(response.data)
    }


    fun selectPointSegment(sdk: DataapiWebSocketSdk, pointAccountId: Long, memberPointId: String, date: LocalDate): BasicSegment? {
        val sql = """
            SELECT id, memberPointId, point, expireDate FROM data.loyalty.member.account.PointSegment${pointAccountId} 
            WHERE memberPointId = :memberPointId
            AND ((expireDate >= :date AND point >= 0) OR (expireDate = :foreverDate AND point <= 0))
            ORDER BY expireDate ASC
            LIMIT 1
        """.trimIndent()
        val params = mapOf(
            "memberPointId" to memberPointId,
            "date" to date,
            "foreverDate" to Constants.LONG_TERM_OVERDUE_DATE.shDate()
        )
        val response = sdk.execute(sql, params)
        return response.data.firstOrNull()?.let { Json.convert(it) }
    }



    fun updatePointSegment(sdk: DataapiWebSocketSdk, pointPlanId: Long, segmentId: String, point: BigDecimal) {
        val sql = """
            UPDATE data.loyalty.member.account.PointSegment${pointPlanId} 
            SET point = :point, modified = :modified
            WHERE id = :id
        """.trimIndent()
        val params = mapOf(
            "point" to point,
            "modified" to ZonedDateTime.now(),
            "id" to segmentId,
        )
        sdk.execute(sql, params)
    }



    fun deleteByMemberPointId(sdk: DataapiWebSocketSdk, pointPlanId: Long, memberPointId: String) {
        val sql = """
            DELETE FROM data.loyalty.member.account.PointSegment${pointPlanId} 
            WHERE memberPointId = :memberPointId
        """.trimIndent()
        val params = mapOf("memberPointId" to memberPointId)
        sdk.execute(sql, params)
    }

    fun deleteByMemberId(sdk: DataapiWebSocketSdk, pointPlanId: Long, memberId: String) {
        deleteByMemberIds(sdk, pointPlanId, listOf(memberId))
    }

    fun deleteByMemberIds(sdk: DataapiWebSocketSdk, pointPlanId: Long, memberIds: List<String>) {
        if (memberIds.isEmpty()) {
            return
        }
        val sql = """
            DELETE FROM data.loyalty.member.account.PointSegment${pointPlanId} 
            WHERE memberId in (:memberIds)
        """.trimIndent()
        val params = mapOf("memberIds" to memberIds)
        sdk.execute(sql, params)
    }

}