package com.shuyun.loyalty.facade.repository.grade

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.GradeRuleGroup
import com.shuyun.loyalty.facade.model.MemberGrade
import com.shuyun.loyalty.sdk.utcStr
import com.shuyun.loyalty.sdk.Json
import java.sql.Connection
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

object GradeRepository {

    private const val COLUMNS = " `id`,`planId`,`gradeHierarchyId`,`version`,`memberId`,`currentGradeDefinitionId`, `effectDate`,`overdueDate`,`subjectFqn`,`created`,`planName`,`gradeHierarchyName`,`currentGradeName` "
    private const val COLUMNS_WITH_REF = "${COLUMNS}, `member` "

    fun queryMemberGrades(
        sdk: DataapiWebSocketSdk,
        gradeHierarchyId: Long,
        gradeDefinitionId: Long? = null,
        memberId: String? = null,
        number: Int = 0,
        pageSize: Int = 20
    ): List<MemberGrade> {
        return queryMemberGrades(sdk, gradeHierarchyId, listOfNotNull(memberId), gradeDefinitionId, number, pageSize)
    }


    fun queryMemberGrades(
        sdk: DataapiWebSocketSdk,
        gradeHierarchyId: Long,
        memberIds: List<String>? = null,
        gradeDefinitionId: Long? = null,
        number: Int = 0,
        pageSize: Int = 20
    ): List<MemberGrade> {
        val sql = """
            SELECT $COLUMNS 
            FROM data.loyalty.member.hierarchy.Grade${gradeHierarchyId} 
            WHERE gradeHierarchyId = :gradeHierarchyId
            ${if (gradeDefinitionId != null) " AND currentGradeDefinitionId = :gradeDefinitionId" else ""}
            ${if (!memberIds.isNullOrEmpty()) " AND memberId in (:memberIds)" else ""}
            ORDER BY memberId ASC
            LIMIT $pageSize OFFSET ${number * pageSize}
        """.trimIndent()
        val params = mapOf(
            "gradeHierarchyId" to gradeHierarchyId,
            "gradeDefinitionId" to gradeDefinitionId,
            "memberIds" to memberIds,
        )
        val response = sdk.execute(sql, params)
        return Json.convert(response.data)
    }


    fun queryMemberGrade(
        sdk: DataapiWebSocketSdk,
        gradeHierarchyId: Long,
        memberId: String
    ): MemberGrade? {
        val sql = """
            SELECT $COLUMNS 
            FROM data.loyalty.member.hierarchy.Grade${gradeHierarchyId} 
            WHERE gradeHierarchyId = :gradeHierarchyId
            AND memberId = :memberId"
            LIMIT 1
        """.trimIndent()
        val params = mapOf(
            "gradeHierarchyId" to gradeHierarchyId,
            "memberId" to memberId,
        )
        val response = sdk.execute(sql, params)
        return response.data.firstOrNull()?.let { Json.convert(it) }
    }


    fun insertMemberGrade(sdk: DataapiWebSocketSdk, memberGrade: MemberGrade) {
        val sql = """
            INSERT INTO data.loyalty.member.hierarchy.Grade${memberGrade.gradeHierarchyId} (
                $COLUMNS_WITH_REF
            ) VALUES (
                :id, :planId, :gradeHierarchyId, :version, :memberId, :currentGradeDefinitionId, :effectDate, :overdueDate, :subjectFqn, :created, :planName, :gradeHierarchyName, :currentGradeName, :member
            )
        """.trimIndent()
        val params = mapOf(
            "id" to memberGrade.id,
            "planId" to memberGrade.planId,
            "gradeHierarchyId" to memberGrade.gradeHierarchyId,
            "version" to memberGrade.version,
            "memberId" to memberGrade.memberId,
            "currentGradeDefinitionId" to memberGrade.currentGradeDefinitionId,
            "effectDate" to memberGrade.effectDate,
            "overdueDate" to memberGrade.overdueDate,
            "subjectFqn" to memberGrade.subjectFqn,
            "created" to memberGrade.created,
            "planName" to memberGrade.planName,
            "gradeHierarchyName" to memberGrade.gradeHierarchyName,
            "currentGradeName" to memberGrade.currentGradeName,
            "member" to memberGrade.memberId
        )
        sdk.execute(sql, params)
    }


    fun updateMemberGrade(sdk: DataapiWebSocketSdk, memberGrade: MemberGrade) {
        val sql = """
            UPDATE data.loyalty.member.hierarchy.Grade${memberGrade.gradeHierarchyId} 
            SET 
                currentGradeDefinitionId = :currentGradeDefinitionId, 
                currentGradeName = :currentGradeName,
                effectDate = :effectDate, 
                overdueDate = :overdueDate, 
                version = :version
            WHERE id = :id
        """.trimIndent()
        val params = mapOf(
            "id" to memberGrade.id,
            "currentGradeDefinitionId" to memberGrade.currentGradeDefinitionId,
            "currentGradeName" to memberGrade.currentGradeName,
            "effectDate" to memberGrade.effectDate.utcStr(),
            "overdueDate" to memberGrade.overdueDate?.utcStr(),
        )
        sdk.execute(sql, params)
    }


    fun deleteMemberGrade(sdk: DataapiWebSocketSdk, gradeHierarchyId: Long, memberIds: List<String>) {
        if (memberIds.isEmpty()) return
        val sql = """
            DELETE FROM data.loyalty.member.hierarchy.Grade${gradeHierarchyId} 
            WHERE memberId in (:memberIds)
        """.trimIndent()
        val params = mapOf(
            "memberIds" to memberIds,
        )
        sdk.execute(sql, params)
    }



    // 查询等级规则组信息
    fun findGradeRuleGroup(connection: Connection, gradeHierarchyId: Long, gradeDefinitionId: Long, eventTypeId: Long): List<GradeRuleGroup> {
        val sql = """
            select id, name, group_type, event_type_id, event_type_name,start_time,end_time,status
            from grade_rule_group 
            where grade_hierarchy_id = ?  and disabled = ? and event_type_id = ?
        """.trimIndent()
        val list = mutableListOf<GradeRuleGroup>()
        connection.prepareStatement(sql).use {
            it.setLong(1, gradeHierarchyId)
            it.setBoolean(2, false)
            it.setLong(3, eventTypeId)
            it.executeQuery().use { rs ->
                while(rs.next()) {
                    val group = GradeRuleGroup(
                        gradeHierarchyId = gradeHierarchyId,
                        gradeDefinitionId = gradeDefinitionId,
                        eventTypeId = eventTypeId,
                    ).apply {
                        id = rs.getString("id")
                        name = rs.getString("name")
                        groupType = rs.getString("group_type")
                        val format =DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                        eventTypeName = rs.getString("event_type_name")
                        startTime =  LocalDateTime.parse(rs.getString("start_time"),format)
                        if (!rs.getString("end_time").isNullOrEmpty()){
                            endTime =  LocalDateTime.parse(rs.getString("end_time"),format)
                        }
                        status = rs.getString("status")
                    }
                    list.add(group)
                }
            }
        }
        return list
    }
}