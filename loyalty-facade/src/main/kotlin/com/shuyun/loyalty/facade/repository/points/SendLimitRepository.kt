package com.shuyun.loyalty.facade.repository.points

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.MemberPoint
import com.shuyun.loyalty.facade.model.SendLimitCalc
import com.shuyun.loyalty.sdk.Json
import java.time.ZonedDateTime

@Suppress("UNCHECKED_CAST")
object SendLimitRepository {

    private const val SEND_LIMIT_TYPE = "MEMBER"

    fun insert(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, value: String) {
        sdk.insert(
            "data.loyalty.member.point.SendLimitCalc${memberPoint.pointPlanId}", mapOf(
                "planId" to memberPoint.planId,
                "pointAccountId" to memberPoint.pointPlanId,
                "memberId" to memberPoint.memberId,
                "type" to SEND_LIMIT_TYPE,
                "value" to value,
                "refId" to memberPoint.id,
                "refIdType" to memberPoint.id + "|" + SEND_LIMIT_TYPE,
                "modified" to ZonedDateTime.now(),
                "created" to ZonedDateTime.now()
            ), false, false
        )
    }

    fun updateById(sdk: DataapiWebSocketSdk, pointAccountId: Long, id: String, value: String) {
        val sql = """
            UPDATE data.loyalty.member.point.SendLimitCalc${pointAccountId}
            SET value = :value, modified = :modified
            WHERE id = :id
        """.trimIndent()
        sdk.execute(sql, mapOf("id" to id, "value" to value, "modified" to ZonedDateTime.now()))
    }


    fun findLimitCalc(sdk: DataapiWebSocketSdk, pointAccountTypeId: Long, memberPointId: String): SendLimitCalc? {
        val sql = """
            SELECT id, value
            from data.loyalty.member.point.SendLimitCalc$pointAccountTypeId 
            WHERE refId = :refId
            LIMIT 1
        """.trimIndent()
        val response = sdk.execute(sql, mapOf("refId" to memberPointId))
        return response.data.firstOrNull()?.let { data ->
            val row = data as Map<String, Any?>
            SendLimitCalc(
                id = row["id"] as String,
                value = row["value"].toString().let { Json.parse(it) },
            )
        }
    }

}






