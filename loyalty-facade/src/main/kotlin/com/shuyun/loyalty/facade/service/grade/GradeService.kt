package com.shuyun.loyalty.facade.service.grade

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.repository.UniqueRecordRepository
import com.shuyun.loyalty.facade.repository.grade.GradeRecordRepository
import com.shuyun.loyalty.facade.repository.grade.GradeRepository
import com.shuyun.loyalty.sdk.Dataapi.withTransaction
import com.shuyun.loyalty.sdk.Uuid
import com.shuyun.loyalty.sdk.api.model.GradeRecordType
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeImportRequest
import java.time.ZonedDateTime


object GradeService {

    suspend fun init(targetMemberGrade: MemberGrade, memberGradeRecord: MemberGradeRecord) {
        withTransaction { sdk ->
            modify(sdk, null, targetMemberGrade, memberGradeRecord)
        }
    }

    suspend fun modify(currentGrade: MemberGrade?, targetMemberGrade: MemberGrade, memberGradeRecord: MemberGradeRecord, uniqueRecord: InterfaceRecord) {
        withTransaction { sdk ->
            UniqueRecordRepository.insert(sdk, uniqueRecord)
            modify(sdk, currentGrade, targetMemberGrade, memberGradeRecord)
        }
    }


    fun modify(sdk: DataapiWebSocketSdk, currentGrade: MemberGrade?, targetMemberGrade: MemberGrade, memberGradeRecord: MemberGradeRecord) {
        if (currentGrade == null) {
            GradeRepository.insertMemberGrade(sdk, targetMemberGrade)
        } else {
            GradeRepository.updateMemberGrade(sdk, targetMemberGrade)
        }
        GradeRecordRepository.insertGradeRecord(sdk, memberGradeRecord)
    }


    suspend fun modify(modifyParams: List<GradeBatchModifyData>) {
        withTransaction { sdk ->
            for (modifyParam in modifyParams) {
                if (modifyParam.currentGrade == null) {
                    GradeRepository.insertMemberGrade(sdk, modifyParam.targetMemberGrade)
                } else {
                    GradeRepository.updateMemberGrade(sdk, modifyParam.targetMemberGrade)
                }
                GradeRecordRepository.insertGradeRecord(sdk, modifyParam.gradeRecord)
            }
        }
    }


    suspend fun import(importId: String, hierarchy: GradeHierarchy, memberId: String, requests: List<MemberGradeImportRequest>) {
        withTransaction { sdk ->
            // 查询当前等级
            val currentGrade = GradeRepository.queryMemberGrade(sdk, hierarchy.id, memberId)
            // 查询等级记录
            val records0 = GradeRecordRepository.queryMemberGradeRecord(
                sdk, hierarchy.id,
                memberId = memberId,
                number = 0,
                pageSize = 3000
            ).content.filter { it.traceId == importId }
            val memberGradeId = currentGrade?.id ?: Uuid.uuid
            val records1 = requests.map {
                MemberGradeRecord(
                    id = Uuid.uuid,
                    planId = hierarchy.plan.id,
                    planName = hierarchy.plan.name,
                    gradeHierarchyId = hierarchy.id,
                    gradeHierarchyName = hierarchy.name,
                    memberGradeId = memberGradeId,
                    memberId = it.memberId,
                    originalGradeId = null,
                    originalGradeName = null,
                    originalEffectDate = it.createdDate,
                    originalOverdueDate = it.overdueDate,
                    currentGradeId = it.targetGradeDefinitionId,
                    currentGradeName = null,
                    currentEffectDate = it.createdDate,
                    currentOverdueDate = it.overdueDate,
                    recordType = null,
                    changeWay = "MANUAL",
                    triggerId = it.key,
                    traceId = importId,
                    description = it.description,
                    subjectFqn = hierarchy.subject.fqn,
                    created = it.createdDate,
                    channel = it.channelType,
                    operator = Constants.DEFAULT_OPERATOR
                )
            }
            val newGradeRecords = listOf(
                *records0.toTypedArray(),
                *records1.toTypedArray()
            ).distinctBy { it.triggerId!! }.sortedBy { it.created!! }

            var sort: Long? = null
            var gradeId: Long? = null
            var gradeName: String? = null
            var effectDate: ZonedDateTime? = null
            var overdueDate: ZonedDateTime? = null
            var created: ZonedDateTime? = null

            // 重新推算等级数据
            for (gradeRecord in newGradeRecords) {
                val currentGradeDefinition = hierarchy.gradeDefinitions.find { it.id == gradeRecord.currentGradeId }!!
                gradeRecord.originalGradeId = gradeId
                gradeRecord.originalGradeName = gradeName
                gradeRecord.currentGradeName = currentGradeDefinition.name
                gradeRecord.recordType = getRecordType(sort, currentGradeDefinition.sort).name

                sort = currentGradeDefinition.sort
                gradeId = currentGradeDefinition.id
                gradeName = currentGradeDefinition.name
                effectDate = gradeRecord.currentEffectDate
                overdueDate = gradeRecord.currentOverdueDate
                created = gradeRecord.created
            }

            GradeRecordRepository.deleteGradeRecordByMemberId(sdk, hierarchy.id, memberId)

            if (currentGrade == null) {
                val mg = MemberGrade(
                    id = memberGradeId,
                    memberId = memberId,
                    planId = hierarchy.plan.id,
                    planName = hierarchy.plan.name,
                    subjectFqn = hierarchy.subject.fqn,
                    gradeHierarchyId = hierarchy.id,
                    gradeHierarchyName = hierarchy.name,
                    currentGradeDefinitionId = gradeId,
                    currentGradeName = gradeName,
                    version = 1,
                    effectDate = effectDate,
                    overdueDate = overdueDate,
                    created = created
                )
                GradeRepository.insertMemberGrade(sdk, mg)
            } else {
                val mg = currentGrade.copy(
                    currentGradeDefinitionId = gradeId,
                    currentGradeName = gradeName,
                    effectDate = effectDate,
                    overdueDate = overdueDate,
                    created = created
                )
                GradeRepository.updateMemberGrade(sdk, mg)
            }
            GradeRecordRepository.batchInsertGradeRecord(sdk, hierarchy.id, newGradeRecords)
        }
    }


    fun getRecordType(currentGradeDefinition: GradeDefinition?, targetGradeDefinition: GradeDefinition): GradeRecordType {
        return getRecordType(currentGradeDefinition?.sort, targetGradeDefinition.sort)
    }

    private fun getRecordType(currentGradeDefinitionSort: Long?, targetGradeDefinitionSort: Long): GradeRecordType {
        val recordType = when {
            currentGradeDefinitionSort == null -> GradeRecordType.UPGRADE
            currentGradeDefinitionSort == targetGradeDefinitionSort -> GradeRecordType.HOLD_BACK_GRADE
            currentGradeDefinitionSort > targetGradeDefinitionSort -> GradeRecordType.DEGRADE
            else -> GradeRecordType.UPGRADE
        }
        return recordType
    }
}