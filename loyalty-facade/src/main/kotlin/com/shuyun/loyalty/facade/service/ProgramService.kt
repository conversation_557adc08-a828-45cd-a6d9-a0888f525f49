package com.shuyun.loyalty.facade.service

import com.alibaba.csp.sentinel.concurrent.NamedThreadFactory
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.repository.BlockListRepository
import com.shuyun.loyalty.facade.repository.ProgramRepository
import io.ktor.server.routing.*
import org.slf4j.LoggerFactory
import org.yaml.snakeyaml.Yaml
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ScheduledThreadPoolExecutor
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean


object ProgramService {

    private val started = AtomicBoolean(false)

    private val schedule = ScheduledThreadPoolExecutor(1, NamedThreadFactory("schedule"))

    private val logger = LoggerFactory.getLogger(ProgramService::class.java)

    private var plans = ConcurrentHashMap<Long, Plan>()

    private var channels = ConcurrentHashMap<String, MetaChannelType>()

    private val metaChangeModes = ConcurrentHashMap<String, MetaChangeMode>()

    private val specialList = ConcurrentHashMap<Long, SpecialListConfig>()

    private val apiDefinition = ConcurrentHashMap<String, Map<*, *>>()

    private const val INITIAL_DELAY_SECONDS = 15L
    private const val FIXED_DELAY_SECONDS = 150L

    private fun stop() {
        if (!started.compareAndSet(true, false)) {
            return
        }
        logger.info("停止定时任务...")
        schedule.shutdown()
    }

    fun loadCache() {
        if (!started.compareAndSet(false, true)) {
            return
        }
        Runtime.getRuntime().addShutdownHook(Thread { stop() })
        logger.info("开始加载计划方案信息")
        val planList = ProgramRepository.queryPlans().onEach { x -> plans[x.plan0.id] = x }
        printPlans(planList)

        logger.info("开始加载渠道信息")
        ProgramRepository.findChannelTypes().forEach { channels[it.code] = it }

        logger.info("开始加载调用方信息")
        ProgramRepository.findChangeModes().forEach { metaChangeModes[it.code] = it }

        logger.info("开始加载特殊名单策略信息")
        BlockListRepository.findSpecialConfigs().forEach { specialList[it.id] = it }

        startRefresh("刷新积分账户信息异常") {
            ProgramRepository.queryPlans().forEach { plans[it.plan0.id] = it }
        }

        startRefresh("刷新渠道信息异常") {
            val codes = HashSet<String>()
            ProgramRepository.findChannelTypes().forEach {
                channels[it.code] = it
                codes.add(it.code)
            }
            channels.forEach {
                if (it.key !in codes) {
                    channels.remove(it.key)
                }
            }
        }

        startRefresh("刷新调用方信息异常") {
            val codes = HashSet<String>()
            ProgramRepository.findChangeModes().forEach {
                metaChangeModes[it.code] = it
                codes.add(it.code)
            }
            metaChangeModes.forEach {
                if (it.key !in codes) {
                    metaChangeModes.remove(it.key)
                }
            }
        }

        startRefresh("刷新特殊名单策略异常") {
            val ids = HashSet<Long>()
            BlockListRepository.findSpecialConfigs().forEach {
                ids.add(it.id)
                specialList[it.id] = it
            }
            specialList.forEach { (k, _) ->
                if (k !in ids) {
                    specialList.remove(k)
                }
            }
        }
    }


    fun getPlanById(planId: Long): Plan? {
        return plans[planId]
    }

    fun getAllPlans(): List<Plan> {
        return plans.values.toList()
    }


    // 通过积分账户ID获取积分账户信息
    fun getPointAccountTypeById(pointAccountTypeId: Long): PointAccountType? {
        for (plan in plans.values) {
            for (subject in plan.subjects) {
                for (pointAccountType in subject.pointAccountTypes) {
                    if (pointAccountType.id == pointAccountTypeId) {
                        return pointAccountType
                    }
                }
            }
        }
        return null
    }

    // 通过等级体系ID获取等级体系信息
    fun getGradeHierarchyById(gradeHierarchyId: Long): GradeHierarchy? {
        for (plan in plans.values) {
            for (subject in plan.subjects) {
                for (gradeHierarchy in subject.gradeHierarchies) {
                    if (gradeHierarchy.id == gradeHierarchyId) {
                        return gradeHierarchy
                    }
                }
            }
        }
        return null
    }

    fun getMedalHierarchyById(medalHierarchyId: Long): MedalHierarchy? {
        for (plan in plans.values) {
            for (subject in plan.subjects) {
                for (medalHierarchy in subject.medalHierarchies) {
                    if (medalHierarchy.id == medalHierarchyId) {
                        return medalHierarchy
                    }
                }
            }
        }
        return null
    }

    // 通过等级体系ID获取等级体系信息
    fun getGradeSubjectEventType(gradeHierarchyId: Long, eventFqn: String): SubjectEventType? {
        for (plan in plans.values) {
            for (subject in plan.subjects) {
                for (gradeHierarchy in subject.gradeHierarchies) {
                    if (gradeHierarchy.id == gradeHierarchyId) {
                        return subject.subjectEventTypes.find { it.eventStream == eventFqn }
                    }
                }
            }
        }
        return null
    }



    // 通过积分账户ID获取特殊名单配置
    fun findSpecialListConfigs(hierarchyId: Long, type: TypeEnum): List<SpecialListConfig> {
        return specialList.filter { (_, conf) ->
            val specialConfigs = conf.specialConfigs[type.name]
            specialConfigs?.any { sc ->
                sc.id == hierarchyId
            } ?: false
        }.values.toList()
    }


    // 通过code获取渠道类型
    fun findChannelTypeByCode(code: String): MetaChannelType? {
        return channels[code]
    }


    // 通过code获取调用方式信息
    fun findChangeModeByCode(code: String): MetaChangeMode? {
        return metaChangeModes[code]
    }


    fun RoutingContext.getApiDefinition(definitionPath: String): Map<*, *>? {
        val definition = apiDefinition[definitionPath]
        if (definition != null) {
            return definition
        }
        val resource = call.application.environment.classLoader.getResourceAsStream(definitionPath)?.bufferedReader()
        if (resource != null) {
            apiDefinition[definitionPath] = Yaml().loadAs(resource.readText(), Map::class.java)
        }
        return apiDefinition[definitionPath]
    }


    fun basicValidate(pointAccountId: Long?, memberId: String?) {
        if (pointAccountId == null) throw BadParamRequestException("pointAccountId参数为必填")
        getPointAccountTypeById(pointAccountId) ?: throw BadParamRequestException("pointAccountId参数错误")
        if (memberId == null) throw BadParamRequestException("memberId参数为必填")
    }


    private fun basicValidate(pointAccountId: Long?, memberId: String?, channelType: String?, changeMode: String?) {
        basicValidate(pointAccountId, memberId)
        if (channelType == null) throw BadParamRequestException("channelType参数为必填")
        findChannelTypeByCode(channelType) ?: throw ChannelTypeNotExistException()
        changeMode?.let { x ->
            if (findChangeModeByCode(x) == null) throw ChangeModeNotExistException()
        }
    }


    fun basicValidate(pointAccountId: Long?, memberId: String?, channelType: String?, changeMode: String?, businessId: String?) {
        basicValidate(pointAccountId, memberId, channelType, changeMode)
        if (businessId == null) throw BadParamRequestException("businessId参数为必填")
    }


    private fun exec(message: String, action: () -> Unit) {
        try {
            if (started.get().not()) {
                return
            }
            action()
        } catch (e: Throwable) {
            logger.error("$message: {}", e.message, e)
        }
    }

    private fun startRefresh(error: String, action: () -> Unit) {
        schedule.scheduleWithFixedDelay({ exec(error, action) }, INITIAL_DELAY_SECONDS, FIXED_DELAY_SECONDS, TimeUnit.SECONDS)
    }


    private fun printPlans(plans: List<Plan>) {
        val sb = StringBuilder()
        sb.append("\n加载到的方案信息如下:\n")
        for ((i,plan) in plans.withIndex()) {
            sb.append("\t${i}. 计划(${plan.plan0.id}: ${plan.plan0.name})\n")
            for (s in plan.subjects) {
                sb.append("\t\t\t\\__ 主体(${s.subject0.name}: ${s.subject0.fqn})\n")
                for (pat in s.pointAccountTypes) {
                    sb.append("\t\t\t\t\t\\__ 积分体系(${pat.id}: ${pat.name})\n")
                }
                for (gh in s.gradeHierarchies) {
                    sb.append("\t\t\t\t\t\\__ 等级体系(${gh.id}: ${gh.name}) -- ${
                        gh.gradeDefinitions.joinToString(
                            separator = " - "
                        ) { ("${it.id}:${it.name}") }
                    }\n")
                }
                for (mh in s.medalHierarchies) {
                    sb.append("\t\t\t\t\t\\__ 勋章体系(${mh.id}: ${mh.name}) -- ${
                        mh.medalDefinitions.joinToString(
                            separator = "-"
                        ) { it.name }
                    }\n")
                }
            }
            sb.append("\n")
        }
        logger.info(sb.toString())
    }
}


