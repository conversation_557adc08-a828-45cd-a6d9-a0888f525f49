package com.shuyun.loyalty.facade.service.points

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.common.Jdbc
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.repository.ModifyLogRepository
import com.shuyun.loyalty.facade.repository.points.MemberPointRepository
import com.shuyun.loyalty.facade.repository.points.PointStatementRepository
import com.shuyun.loyalty.sdk.Dataapi.noneTransaction
import com.shuyun.loyalty.sdk.Dataapi.withTransaction
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime

object MemberPointService {

    // 查询会员积分账户
    suspend fun findMemberPoint(
        pointAccountTypeId: Long,
        memberId: String,
        now: LocalDate = ZonedDateTime.now().shDate()
    ): MemberPoint? = withTransaction { findMemberPoint(it, pointAccountTypeId, memberId, now) }


    // 查询会员积分账户
    fun findMemberPoint(
        sdk: DataapiWebSocketSdk,
        pointAccountTypeId: Long,
        memberId: String,
        now: LocalDate = ZonedDateTime.now().shDate()
    ): MemberPoint? {
        var memberPoint = MemberPointRepository.findMemberPoint(sdk, pointAccountTypeId, memberId, now)
        if (memberPoint != null && !memberPoint.openSegmentFlag) {
            PointMigrateService.rebuildSegment(sdk, pointAccountTypeId, memberId, reference = 0)
            memberPoint = MemberPointRepository.findMemberPoint(sdk, pointAccountTypeId, memberId, now)
        }
        return memberPoint
    }


    // 查询会员积分账户列表
    suspend fun queryMemberPoints(
        pointAccountTypeId: Long,
        memberId: String?,
        number: Int,
        pageSize: Int,
    ): List<MemberPoint> {
        return noneTransaction {
            MemberPointRepository.selectMemberPoints(
                it,
                pointAccountTypeId,
                memberId,
                number,
                pageSize
            )
        }
    }


    fun findFrozenPoints(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, traceId: String): BigDecimal {
        val journey = PointJourneyService.findOpenFreezePointJourney(sdk, memberPoint, PointAction.FREEZE, traceId)
        val freezePoint = journey?.afterPoints
            ?: PointStatementRepository.sumPoints(
                sdk,
                memberPoint.pointPlanId,
                memberPoint.id,
                fromStatus = "OPEN_FROZE",
                openTraceId = traceId
            )
        if (freezePoint <= BigDecimal.ZERO) {
            throw FrozenPointNotFoundException()
        }
        return freezePoint
    }



    suspend fun findParentBatchModifyLog(triggerId: String): BatchModifyLog? {
        return Jdbc.withTransaction(readOnly = true) { conn -> ModifyLogRepository.selectByTriggerId(conn, triggerId) }
    }

    suspend fun findChildBatchModifyLog(parentId: Long): List<BatchModifyLog> {
        return Jdbc.withTransaction(readOnly = true) { conn -> ModifyLogRepository.selectByParentId(conn, parentId) }
    }
}
