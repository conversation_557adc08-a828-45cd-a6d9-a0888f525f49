package com.shuyun.loyalty.facade.service

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.common.Jdbc
import com.shuyun.loyalty.facade.common.Kafka
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.repository.MergeRecordRepository
import com.shuyun.loyalty.facade.repository.UniqueRecordRepository
import com.shuyun.loyalty.facade.repository.grade.GradeRepository
import com.shuyun.loyalty.facade.repository.medal.MedalRepository
import com.shuyun.loyalty.facade.repository.points.MemberPointRepository
import com.shuyun.loyalty.facade.repository.points.PointMergeRepository
import com.shuyun.loyalty.facade.repository.points.SegmentRepository
import com.shuyun.loyalty.facade.service.grade.GradeService
import com.shuyun.loyalty.facade.service.medal.MedalService
import com.shuyun.loyalty.sdk.api.model.MemberMergeRequest
import com.shuyun.loyalty.sdk.api.model.RuleGroupStatusEnum
import com.shuyun.loyalty.sdk.Dataapi.withTransaction
import com.shuyun.loyalty.sdk.toEpochMilli
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Segments
import com.shuyun.loyalty.sdk.Uuid
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.ZonedDateTime

object MergeCardService {

    private val logger = LoggerFactory.getLogger(MergeCardService::class.java)

    suspend fun merge(req: MemberMergeRequest) {
        val record = MergeRecord(
            memberId = req.memberId,
            subMemberIds = req.subMemberIds!!.joinToString(","),
            created = ZonedDateTime.now(),
            modified = ZonedDateTime.now(),
        )
        val pointMergeRecords = req.mergePointRequest?.let {
            it.pointPlanIds!!.map { pointAccountTypeId ->
                record.copy(id = Uuid.uuid, pointAccountTypeId = pointAccountTypeId, pointStatus = "FAIL")
            }
        } ?: emptyList()
        val gradeMergeRecords = req.mergeGradeRequest?.let {
            it.gradeHierarchyIds!!.map { gradeHierarchyId ->
                record.copy(id = Uuid.uuid, gradeHierarchyId = gradeHierarchyId, gradeStatus = "FAIL")
            }
        } ?: emptyList()
        val medalMergeRecords = req.mergeMedalRequest?.let {
            it.medalHierarchyIds!!.map { medalHierarchyId ->
                record.copy(id = Uuid.uuid, medalHierarchyId = medalHierarchyId, medalStatus = "FAIL")
            }
        } ?: emptyList()
        try {
            withTransaction { sdk ->
                req.mergePointRequest?.let { mergePoint(sdk, req) }
                req.mergeGradeRequest?.let { mergeGrade(sdk, req) }
                req.mergeMedalRequest?.let { mergeMedal(sdk, req) }
                if (req.generateMergeRecord) {
                    for (mergeRecord in pointMergeRecords) {
                        mergeRecord.pointStatus = "SUCCESS"
                        mergeRecord.status = "SUCCESS"
                        MergeRecordRepository.save(sdk, mergeRecord)
                    }
                    req.mergeGradeRequest?.let {
                        it.gradeMergeType?.let { x ->
                            if (x == "HIGHEST") {
                                for (mergeRecord in gradeMergeRecords) {
                                    mergeRecord.gradeStatus = "SUCCESS"
                                    mergeRecord.status = "SUCCESS"
                                    MergeRecordRepository.save(sdk, mergeRecord)
                                }
                            }
                        }
                    }
                    for (mergeRecord in medalMergeRecords) {
                        mergeRecord.medalStatus = "SUCCESS"
                        mergeRecord.status = "SUCCESS"
                        MergeRecordRepository.save(sdk, mergeRecord)
                    }
                }
            }
        } catch (e: Exception) {
            logger.warn("合卡失败", e)
            if (req.generateMergeRecord) {
                withTransaction {
                    for (mergeRecord in pointMergeRecords) {
                        mergeRecord.pointStatus = "FAIL"
                        mergeRecord.status = "FAIL"
                        MergeRecordRepository.save(it, mergeRecord)
                    }
                    for (mergeRecord in gradeMergeRecords) {
                        mergeRecord.gradeStatus = "FAIL"
                        mergeRecord.status = "FAIL"
                        MergeRecordRepository.save(it, mergeRecord)
                    }
                    for (mergeRecord in medalMergeRecords) {
                        mergeRecord.medalStatus = "FAIL"
                        mergeRecord.status = "FAIL"
                        MergeRecordRepository.save(it, mergeRecord)
                    }
                }
            }
        }
    }


    private fun mergePoint(sdk: DataapiWebSocketSdk, req: MemberMergeRequest) {
        val now = ZonedDateTime.now()
        val pointAccountTypeIds = req.mergePointRequest!!.pointPlanIds!!
        for (pointAccountTypeId in pointAccountTypeIds) {
            val pointAccountType = ProgramService.getPointAccountTypeById(pointAccountTypeId) ?: continue
            if (pointAccountType.subject.mergeFqn == null) {
                throw MergeModelNotExistException()
            }
            var memberPoint = MemberPointRepository.findMemberPoint(
                sdk,
                pointAccountTypeId,
                req.memberId!!,
                includeSegmentPoint = false
            )
            for (subMemberId in req.subMemberIds!!) {
                val subMemberPoint = MemberPointRepository.findMemberPoint(
                    sdk,
                    pointAccountTypeId,
                    subMemberId,
                    includeSegmentPoint = false
                )
                if (memberPoint == null && subMemberPoint == null) {
                    continue
                }
                if (memberPoint != null && subMemberPoint == null) {
                    logger.info("副卡会员积分账户不存在 subMemberId={}", subMemberId)
                    continue
                }
                if (memberPoint == null && subMemberPoint != null) {
                    MemberService.checkMemberId(sdk, pointAccountType.subject.fqn, req.memberId!!)
                    logger.info("主卡会员积分账户不存在, 创建新的会员积分账户 {}", req.memberId)
                    memberPoint = MemberPoint(
                        id = Uuid.uuid,
                        planId = pointAccountType.plan.id,
                        pointPlanId = subMemberPoint.pointPlanId,
                        memberId = req.memberId!!,
                        subjectFqn = pointAccountType.subject.fqn,
                        point = BigDecimal.ZERO,
                        version = 0,
                        openSegmentFlag = false,
                    )
                    MemberPointRepository.insertMemberPoint(sdk, memberPoint, now)
                }

                PointMergeRepository.updateValidStatement(
                    sdk,
                    pointAccountTypeId,
                    subMemberPoint!!.memberId,
                    memberPoint!!.memberId,
                    memberPoint.id,
                    now
                )
                PointMergeRepository.updateGainStatement(
                    sdk,
                    pointAccountTypeId,
                    subMemberPoint.memberId,
                    memberPoint.memberId,
                    memberPoint.id,
                    now
                )
                PointMergeRepository.updateNegativeStatement(
                    sdk,
                    pointAccountTypeId,
                    subMemberPoint.memberId,
                    memberPoint.memberId,
                    memberPoint.id,
                    now
                )
                PointMergeRepository.updateFrozenStatement(
                    sdk,
                    pointAccountTypeId,
                    subMemberPoint.memberId,
                    memberPoint.memberId,
                    memberPoint.id,
                    now
                )
                PointMergeRepository.updateFrozenPoints(
                    sdk,
                    pointAccountTypeId,
                    subMemberPoint.memberId,
                    memberPoint.memberId,
                    memberPoint.id
                )
                PointMergeRepository.updatePointRecord(
                    sdk,
                    pointAccountTypeId,
                    subMemberPoint.memberId,
                    memberPoint.memberId,
                    memberPoint.id,
                    now
                )
                PointMergeRepository.updateCalculateJournal(
                    sdk,
                    pointAccountTypeId,
                    subMemberPoint.memberId,
                    memberPoint.memberId,
                    memberPoint.id,
                    now
                )
                PointMergeRepository.updatePointRecordItem(
                    sdk,
                    pointAccountTypeId,
                    subMemberPoint.memberId,
                    memberPoint.memberId
                )
                PointMergeRepository.updateMemberPointEvent(
                    sdk,
                    pointAccountTypeId,
                    subMemberPoint.memberId,
                    memberPoint.memberId
                )

                SegmentRepository.deleteByMemberIds(sdk, pointAccountTypeId, req.subMemberIds!!)

                Segments.rebuildSegment(sdk, pointAccountTypeId, memberPoint.memberId, reference = 1, now = now).let {
                    memberPoint.point = it
                }

                PointMergeRepository.deletePointLog(
                    sdk,
                    pointAccountTypeId,
                    listOf(subMemberPoint.memberId, memberPoint.memberId)
                )
                PointMergeRepository.batchInsertPointLog(sdk, memberPoint)
            }
            if (req.mergePointRequest!!.deleteSubMemberPointAccount) {
                PointMergeRepository.deleteMemberPoint(sdk, pointAccountTypeId, req.subMemberIds!!)
            } else {
                PointMergeRepository.updateMemberPointToZero(sdk, pointAccountTypeId, req.subMemberIds!!)
            }
        }
    }

    private fun mergeGrade(sdk: DataapiWebSocketSdk, req: MemberMergeRequest) {
        val gradeReq = req.mergeGradeRequest!!
        val now = ZonedDateTime.now()
        val gradeRecords = mutableListOf<MemberGradeRecord>()
        val uniqueRecord = InterfaceRecord(
            id = req.triggerId,
            sequence = req.triggerId,
            content = Json.toJson(req),
            created = now
        )
        UniqueRecordRepository.insert(sdk, uniqueRecord)
        for (gradeHierarchyId in gradeReq.gradeHierarchyIds!!) {
            val gradeHierarchy = ProgramService.getGradeHierarchyById(gradeHierarchyId) ?: throw BadParamRequestException("无效的等级体系ID")
            if (gradeHierarchy.subject.mergeFqn == null) {
                throw MergeModelNotExistException()
            }
            when (gradeReq.gradeMergeType!!) {
                "RECALCULATE" -> {
                    val currentMemberGrade = GradeRepository.queryMemberGrade(sdk, gradeHierarchyId, req.memberId!!)
                        ?: throw MemberGradeNotFoundException()
                    val mergeFqn = "event.loyalty.grade.merge.event"
                    val subjectEventType =
                        ProgramService.getGradeSubjectEventType(gradeHierarchyId, eventFqn = mergeFqn)
                    if (subjectEventType == null) {
                        logger.error("合卡时机事件({})不存在", mergeFqn)
                        throw EventTypeNotExistException()
                    }
                    val gradeRuleGroups = Jdbc.withTx(readOnly = true) {
                        GradeRepository.findGradeRuleGroup(
                            connection = it,
                            gradeHierarchyId = gradeHierarchyId,
                            gradeDefinitionId = currentMemberGrade.currentGradeDefinitionId!!,
                            eventTypeId = subjectEventType.id!!
                        )
                    }
                    val mergeGradeRuleGroups = gradeRuleGroups.filter {
                        calculateStatusByDate(it, LocalDateTime.now()) == RuleGroupStatusEnum.RUNNING
                    }
                    if (mergeGradeRuleGroups.isEmpty()) {
                        throw GradeRuleNotExistException("等级合卡重算的规则不存在")
                    }
                    val record = MergeRecord(
                        id = Uuid.uuid,
                        memberId = req.memberId,
                        subMemberIds = req.subMemberIds!!.joinToString(","),
                        gradeHierarchyId = gradeHierarchyId,
                        created = now,
                        modified = now,
                    )
                    val message = mapOf(
                        "fqn" to mergeFqn,
                        "key" to "merge_grade_${req.memberId}_${Uuid.uuid}",
                        "occurrenceTs" to now.toEpochMilli(),
                        "detectionTs" to now.toEpochMilli(),
                        "plan" to mapOf(
                            "id" to currentMemberGrade.planId,
                            "name" to currentMemberGrade.planName
                        ),
                        "memberId" to req.memberId,
                        "gradeDefinitionId" to currentMemberGrade.currentGradeDefinitionId!!,
                        "overdueDate" to currentMemberGrade.overdueDate,
                        "mergeRecord" to if (req.generateMergeRecord) record else null,
                        "gradeHierarchy" to gradeHierarchy,
                    )
                    logger.debug("等级合卡重算消息推送{}",Json.toJson(message))
                    Kafka.sendPointGreen(req.memberId!!, Json.toJson(message))
                }

                "HIGHEST" -> {
                    // 取最高等级
                    val memberIds = req.subMemberIds!! + req.memberId!!
                    val grades = GradeRepository.queryMemberGrades(sdk, gradeHierarchyId, memberIds).filterNot {
                        it.overdueDate != null && it.overdueDate!!.isBefore(now)
                    }
                    val maxGrade = grades.maxByOrNull {
                        gradeHierarchy.gradeDefinitions.find { g -> g.id == it.currentGradeDefinitionId }?.sort ?: -1
                    }
                    // 多个相同的等级取过期时间最晚的
                    val maxOverdueDate = if (maxGrade != null && maxGrade.currentGradeDefinitionId!! != -1L) {
                        grades.filter { it.currentGradeDefinitionId == maxGrade.currentGradeDefinitionId }
                            .maxByOrNull { it.overdueDate ?: Constants.LONG_TERM_OVERDUE_DATE }?.overdueDate
                    } else null

                    // 目标等级定义
                    val targetGradeDefinition = if (maxGrade == null || maxGrade.currentGradeDefinitionId == -1L) {
                        GradeDefinition(id = -1, name = "无等级", sort = 0)
                    } else {
                        val definition = gradeHierarchy.gradeDefinitions.find { it.id == maxGrade.currentGradeDefinitionId }
                            ?: throw BadParamRequestException("目标等级不存在")
                        definition
                    }

                    // 主会员等级
                    val mainGrade = GradeRepository.queryMemberGrade(sdk, gradeHierarchyId, req.memberId!!)
                    val mainGradeDefinition = gradeHierarchy.gradeDefinitions.find { it.id == mainGrade?.currentGradeDefinitionId }
                    val recordType = GradeService.getRecordType(mainGradeDefinition, targetGradeDefinition)

                    // 目标等级
                    val targetGrade = mainGrade?.copy(
                        currentGradeDefinitionId = targetGradeDefinition.id,
                        currentGradeName = targetGradeDefinition.name,
                        version = (mainGrade.version ?: 0) + 1,
                        effectDate = if (gradeReq.useHighestEffectTimeType == "UNCHANGE") mainGrade.effectDate else ZonedDateTime.now(),
                        overdueDate = maxOverdueDate
                    ) ?: MemberGrade(
                        id = Uuid.uuid,
                        memberId = req.memberId,
                        planId = gradeHierarchy.plan.id,
                        planName = gradeHierarchy.plan.name,
                        subjectFqn = gradeHierarchy.subject.fqn,
                        gradeHierarchyId = gradeHierarchy.id,
                        gradeHierarchyName = gradeHierarchy.name,
                        currentGradeDefinitionId = targetGradeDefinition.id,
                        currentGradeName = targetGradeDefinition.name,
                        version = 1,
                        effectDate = now,
                        overdueDate = maxOverdueDate,
                        created = now
                    )

                    val gradeRecord =MemberGradeRecord(
                        id = Uuid.uuid,
                        planId = gradeHierarchy.plan.id,
                        planName = gradeHierarchy.plan.name,
                        gradeHierarchyId = gradeHierarchy.id,
                        gradeHierarchyName = gradeHierarchy.name,
                        memberId = req.memberId!!,
                        originalGradeId = mainGrade?.currentGradeDefinitionId ?: -1,
                        originalGradeName = mainGrade?.currentGradeName ?: "无等级",
                        originalEffectDate = mainGrade?.effectDate,
                        originalOverdueDate = mainGrade?.overdueDate,
                        currentGradeId = targetGrade.currentGradeDefinitionId,
                        currentGradeName = targetGrade.currentGradeName,
                        currentEffectDate = targetGrade.effectDate,
                        currentOverdueDate = targetGrade.overdueDate,
                        recordType = recordType.name,
                        changeWay = "INTERFACE",
                        triggerId = req.triggerId,
                        traceId = req.triggerId,
                        description = req.description,
                        subjectFqn = gradeHierarchy.subject.fqn,
                        created = now,
                        channel = req.channelType!!,
                        memberGradeId = mainGrade?.id ?: targetGrade.id
                    )
                    if (gradeReq.deleteSubMemberGradeAccount) {
                        GradeRepository.deleteMemberGrade(sdk, gradeHierarchyId, req.subMemberIds!!)
                    }

                    GradeService.modify(sdk, mainGrade, targetGrade, gradeRecord)
                    gradeRecords.add(gradeRecord)
                }
                else -> {
                    throw BadParamRequestException("Unknown grade merge type: ${gradeReq.gradeMergeType}")
                }
            }
        }
        gradeRecords.forEach { gradeRecord ->
            Kafka.sendGradeNotify(req.memberId!!, gradeRecord)
        }

    }


    private fun calculateStatusByDate(rule: GradeRuleGroup, date: LocalDateTime): RuleGroupStatusEnum {
        return if (RuleGroupStatusEnum.FILED != RuleGroupStatusEnum.valueOf(rule.status!!)) {
            when {
                "DEGRADE" == rule.groupType -> RuleGroupStatusEnum.RUNNING
                date.isBefore(rule.startTime!!) -> RuleGroupStatusEnum.WAIT_EFFECT
                null != rule.endTime && date.isAfter(rule.endTime) -> RuleGroupStatusEnum.END
                else -> RuleGroupStatusEnum.RUNNING
            }
        } else {
            RuleGroupStatusEnum.valueOf(rule.status!!)
        }
    }

    private fun  mergeMedal(sdk: DataapiWebSocketSdk, req: MemberMergeRequest) {
        req.subMemberIds?.forEach subMember@{subMemberId ->
            try {
                req.mergeMedalRequest?.medalHierarchyIds?.forEach medalHierarchyId@{ medalHierarchyId ->
                    val subMemberMedalList = MedalService.queryMedalByMemberId(
                        sdk, subMemberId, medalHierarchyId,
                        false
                    )
                    val medalHierarchy = ProgramService.getMedalHierarchyById(medalHierarchyId) ?: throw BadParamRequestException("无效的勋章体系ID")

                    subMemberMedalList?.forEach subMemberMedal@{ subMemberMedal ->
                        val memberMedal = MedalService.queryMedal(sdk, req.memberId!!, medalHierarchyId, subMemberMedal.medalDefinitionId).firstOrNull()
                        if (memberMedal != null) {
                            if (memberMedal.overdueDate == null) return@subMemberMedal
                            if (subMemberMedal.overdueDate != null &&
                                subMemberMedal.overdueDate!!.isBefore(memberMedal.overdueDate)
                            ) return@subMemberMedal
                        }
                        val targetMedal = memberMedal?.copy(
                            version = (subMemberMedal.version ?: 0) + 1,
                            effectDate = if (subMemberMedal.effectDate !=null) subMemberMedal.effectDate else ZonedDateTime.now(),
                            disabled = false
                        )?:MemberMedal(
                            id = Uuid.uuid,
                            memberId = req.memberId,
                            planId = subMemberMedal.planId,
                            planName = subMemberMedal.planName,
                            subjectFqn =subMemberMedal.subjectFqn,
                            medalHierarchyId = subMemberMedal.medalHierarchyId,
                            medalHierarchyName = subMemberMedal.medalHierarchyName,
                            medalDefinitionId =subMemberMedal.medalDefinitionId,
                            medalDefinitionName =subMemberMedal.medalDefinitionName,
                            version = 0,
                            effectDate = ZonedDateTime.now(),
                            overdueDate = subMemberMedal.overdueDate,
                            created = ZonedDateTime.now(),
                            disabled = false
                        )
                      val medalRecord = MemberMedalRecord(
                            id = Uuid.uuid,
                            planId = medalHierarchy.plan.id,
                            planName = medalHierarchy.plan.name,
                            medalHierarchyId = medalHierarchy.id,
                            medalHierarchyName = medalHierarchy.name,
                            medalDefinitionId = targetMedal.medalDefinitionId,
                            medalDefinitionName = targetMedal.medalDefinitionName,
                            memberMedalId = targetMedal.id,
                            memberId = targetMedal.memberId,
                            originalEffectDate = memberMedal?.effectDate,
                            originalOverdueDate = memberMedal?.overdueDate,
                            currentMedalDefinitionIds = MedalRepository.queryMedalDefinitionIdByMemberId(
                                  sdk,
                                  targetMedal.medalHierarchyId!!,
                                  targetMedal.memberId!!,
                                  false
                              )?.joinToString(","),
                            currentEffectDate = targetMedal.effectDate,
                            currentOverdueDate = targetMedal.overdueDate,
                            recordType = if (memberMedal == null) "OBTAIN" else "KEEP",
                            changeWay = "INTERFACE",
                            triggerId = null,
                            traceId = null,
                            recordSourceDetail = null,
                            subjectFqn = medalHierarchy.subject.fqn,
                            description = null,
                            created = ZonedDateTime.now(),
                            recordDetail = null,
                            channel = req.channelType,
                        )
                        if (req.mergeMedalRequest!!.deleteSubMemberMedalAccount) {
                            MedalRepository.deleteMemberMedal(sdk, medalHierarchyId, req.subMemberIds!!)
                        }
                        MedalService.modify(sdk,targetMedal,memberMedal,medalRecord)
                    }
                }
            } catch (e: Exception) {
                throw e
            }
        }
    }

    suspend fun mergePoint(req: MemberMergeRequest) {
        val pointMergeRecords = req.mergePointRequest!!.pointPlanIds!!.map {
            MergeRecord(
                id = Uuid.uuid,
                pointAccountTypeId = it,
                memberId = req.memberId,
                subMemberIds = req.subMemberIds!!.joinToString(","),
                status = "FAIL",
                pointStatus = "FAIL",
                created = ZonedDateTime.now(),
                modified = ZonedDateTime.now(),
            )
        }
        try {
            withTransaction { sdk ->
                mergePoint(sdk, req)
                if (req.generateMergeRecord) {
                    for (mergeRecord in pointMergeRecords) {
                        mergeRecord.pointStatus = "SUCCESS"
                        mergeRecord.status = "SUCCESS"
                        MergeRecordRepository.save(sdk, mergeRecord)
                    }
                }
            }
        } catch (e: Exception) {
            logger.warn("积分合卡失败", e)
            if (req.generateMergeRecord) {
                withTransaction {
                    for (mergeRecord in pointMergeRecords) {
                        mergeRecord.pointReason = if (e is FacadeException) e.message else "未知异常"
                        MergeRecordRepository.save(it, mergeRecord)
                    }
                }
            }
        }
    }


    suspend fun mergeGrade(req: MemberMergeRequest) {
        val gradeMergeRecords = req.mergeGradeRequest!!.gradeHierarchyIds!!.map {
            MergeRecord(
                gradeHierarchyId = it,
                memberId = req.memberId,
                subMemberIds = req.subMemberIds!!.joinToString(","),
                status = "FAIL",
                gradeStatus = "FAIL",
                created = ZonedDateTime.now(),
                modified = ZonedDateTime.now(),
            )
        }
        try {
            withTransaction { sdk ->
                mergeGrade(sdk, req)
                if (req.generateMergeRecord) {
                    if (req.mergeGradeRequest!!.gradeMergeType!! == "HIGHEST") {
                        for (mergeRecord in gradeMergeRecords) {
                            mergeRecord.gradeStatus = "SUCCESS"
                            mergeRecord.status = "SUCCESS"
                            MergeRecordRepository.save(sdk, mergeRecord)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            logger.warn("等级合卡失败", e)
            if (req.generateMergeRecord) {
                withTransaction {
                    for (mergeRecord in gradeMergeRecords) {
                        mergeRecord.gradeReason = if (e is FacadeException) e.message else "未知异常"
                        MergeRecordRepository.save(it, mergeRecord)
                    }
                }
            }
        }
    }


    suspend fun mergeMedal(req: MemberMergeRequest){
        val medalMergeRecords = req.mergeMedalRequest!!.medalHierarchyIds!!.map {
            MergeRecord(
                medalHierarchyId = it,
                memberId = req.memberId,
                subMemberIds = req.subMemberIds!!.joinToString(","),
                status = "FAIL",
                medalStatus = "FAIL",
                created = ZonedDateTime.now(),
                modified = ZonedDateTime.now(),
            )
        }
        try {
            withTransaction { sdk ->
                mergeMedal(sdk, req)
                if (req.generateMergeRecord) {
                        for (mergeRecord in medalMergeRecords) {
                            mergeRecord.medalStatus = "SUCCESS"
                            mergeRecord.status = "SUCCESS"
                            MergeRecordRepository.save(sdk, mergeRecord)
                        }
                }
            }
        } catch (e: Exception) {
            logger.warn("勋章合卡失败", e)
            if (req.generateMergeRecord) {
                withTransaction {
                    for (mergeRecord in medalMergeRecords) {
                        mergeRecord.medalReason = if (e is FacadeException) e.message else "未知异常"
                        MergeRecordRepository.save(it, mergeRecord)
                    }
                }
            }
        }
    }
}