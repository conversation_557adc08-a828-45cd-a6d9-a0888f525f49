package com.shuyun.loyalty.facade.routes.points

import com.shuyun.loyalty.facade.common.Kafka
import com.shuyun.loyalty.facade.common.withMemberPointLock
import com.shuyun.loyalty.facade.model.BadParamRequestException
import com.shuyun.loyalty.facade.model.Constants
import com.shuyun.loyalty.facade.model.ForbiddenOperation
import com.shuyun.loyalty.facade.model.TypeEnum
import com.shuyun.loyalty.facade.service.BlockListService
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.points.PointModifyService
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointRevertRequest
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointRevertResponse
import com.shuyun.loyalty.sdk.api.model.http.points.ReversePointEvent
import com.shuyun.loyalty.sdk.Json
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import java.time.Duration
import java.time.ZonedDateTime


private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.points.PointRevert.kt")

fun Route.pointRevert() {

    post("/point:revert") {
        val req = call.receive<MemberPointRevertRequest>().also { LOGGER.info("撤销会员积分: {}", Json.toJson(it)) }.validate()
        val now = ZonedDateTime.now()
        val accountType = ProgramService.getPointAccountTypeById(req.pointAccountId!!) ?: throw BadParamRequestException("无效的积分账户ID")

        val forbiddenOperation = when (req.recordType!!) {
            ReversePointEvent.SEND -> ForbiddenOperation.POINT_SEND_RECYCLE
            ReversePointEvent.DEDUCT -> ForbiddenOperation.POINT_DEDUCT_RECYCLE
            ReversePointEvent.USE_FREEZE -> ForbiddenOperation.POINT_DEDUCT_RECYCLE
        }

        BlockListService.check(
            TypeEnum.POINT,
            accountType.subject.fqn,
            accountType.id,
            req.memberId!!,
            forbiddenOperation,
        )
        val memberPoint = withMemberPointLock(req.pointAccountId!!, req.memberId!!) {
            PointModifyService.reverseMemberPoint(req, now)
        }
        Kafka.sendPointCalc(req.pointAccountId!!, memberPoint.id, memberPoint.memberId)
        call.respond(MemberPointRevertResponse().apply { requestId = req.uniqueId })
    }
}

private fun MemberPointRevertRequest.validate(): MemberPointRevertRequest {
    if (tradeId == null) throw BadParamRequestException("交易ID不能为空")
    if (pointAccountId == null) throw BadParamRequestException("积分账户ID不能为空")
    if (memberId == null) throw BadParamRequestException("会员ID不能为空")
    if (recordType == null) throw BadParamRequestException("正向触发动作不能为空")
    if (uniqueId == null) throw BadParamRequestException("唯一请求ID不能为空")
    channelType?.let { ProgramService.findChannelTypeByCode(it) ?: throw BadParamRequestException("渠道类型参数错误") }
    changeMode?.let { ProgramService.findChangeModeByCode(it) ?: throw BadParamRequestException("变更方式参数错误") }
    if (channelType == null) channelType = Constants.DEFAULT_CHANNEL_TYPE
    if (changeMode == null) changeMode = Constants.DEFAULT_TX_CHANGE_MODE
    return this
}
