package com.shuyun.loyalty.facade.service.points

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.BasicSegment
import com.shuyun.loyalty.facade.model.Constants.LONG_TERM_OVERDUE_DATE
import com.shuyun.loyalty.facade.model.MemberPoint
import com.shuyun.loyalty.facade.model.shDate
import com.shuyun.loyalty.facade.repository.points.SegmentRepository
import com.shuyun.loyalty.sdk.Uuid
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime

object PointSegmentService {

    // 保存积分段
    private fun initPointSegment(
        sdk: DataapiWebSocketSdk,
        memberPoint: MemberPoint,
        expireDate: LocalDate,
        id: String = Uuid.uuid
    ) {
        SegmentRepository.insertPointSegment(
            sdk,
            id,
            memberPoint.planId,
            memberPoint.pointPlanId,
            memberPoint.id,
            memberPoint.memberId,
            memberPoint.subjectFqn,
            memberPoint.point,
            expireDate
        )
    }


    // 添加积分段
    fun addPointSegment(
        sdk: DataapiWebSocketSdk,
        memberPoint: MemberPoint,
        point: BigDecimal,
        expireDate: LocalDate,
        now: ZonedDateTime
    ) {
        val basicSegments = SegmentRepository.selectPointSegments(sdk, memberPoint.pointPlanId, memberPoint.id).sortedBy { it.expireDate }
        if (basicSegments.isEmpty()) {
            initPointSegment(sdk, memberPoint.copy(point = point), expireDate)
            return
        }
        val insertSegments = mutableListOf<BasicSegment>()
        val deleteIds = mutableListOf<String>()
        val updateIds = mutableListOf<String>()
        var added = false
        for (basicSegment in basicSegments) {
            if (
                basicSegment.expireDate.isBefore(now.shDate()) ||
                (!basicSegment.expireDate.isEqual(LONG_TERM_OVERDUE_DATE.shDate()) && basicSegment.point <= BigDecimal.ZERO) ||
                (basicSegment.expireDate.isEqual(LONG_TERM_OVERDUE_DATE.shDate()) && basicSegment.point.compareTo(BigDecimal.ZERO) == 0)) {
                deleteIds.add(basicSegment.id)
                continue
            }
            if (basicSegment.expireDate.isEqual(LONG_TERM_OVERDUE_DATE.shDate())) {
                if (basicSegment.point < BigDecimal.ZERO) {
                    deleteIds.addAll(basicSegments.map { it.id })
                    val newPoints = basicSegment.point + point
                    if (newPoints > BigDecimal.ZERO) {
                        insertSegments.add(basicSegment.copy(id = Uuid.uuid, point = newPoints, expireDate = expireDate))
                    }
                    if (newPoints < BigDecimal.ZERO) {
                        insertSegments.add(basicSegment.copy(id = Uuid.uuid, point = newPoints))
                    }
                    added = true
                    break
                }
            }
            if (basicSegment.expireDate.isBefore(expireDate)) {
                updateIds.add(basicSegment.id)
                continue
            }
            if (basicSegment.expireDate.isEqual(expireDate)) {
                added = true
                updateIds.add(basicSegment.id)
                continue
            }
            if (basicSegment.expireDate.isAfter(expireDate)) {
                if (!added) {
                    added = true
                    insertSegments.add(basicSegment.copy(id = Uuid.uuid, point = basicSegment.point + point, expireDate = expireDate))
                }
                break
            }
        }
        if (!added) {
            insertSegments.add(BasicSegment(id = Uuid.uuid, memberPointId = memberPoint.id, point = point, expireDate = expireDate))
        }
        SegmentRepository.deleteByIds(sdk, memberPoint.pointPlanId, deleteIds.sorted())
        SegmentRepository.updateByIds(sdk, memberPoint.pointPlanId, updateIds.sorted(), point)
        val sortedSegments = insertSegments.sortedBy { it.id }
        for (insertSegment in sortedSegments) {
            initPointSegment(
                sdk,
                memberPoint.copy(point = insertSegment.point),
                insertSegment.expireDate,
                id = insertSegment.id
            )
        }
    }


    // 扣减积分段
    fun subtractPointSegment(
        sdk: DataapiWebSocketSdk,
        memberPoint: MemberPoint,
        point: BigDecimal,
        date: ZonedDateTime
    ): LocalDate {
        var leftSegmentDate = LONG_TERM_OVERDUE_DATE.shDate()
        val basicSegment = findByDateAfter(sdk, memberPoint, date)
        if (basicSegment != null) {
            val newPoint = basicSegment.point - point
            if (newPoint > BigDecimal.ZERO) {
                leftSegmentDate = basicSegment.expireDate
                SegmentRepository.updatePointSegment(sdk, memberPoint.pointPlanId, basicSegment.id, newPoint)
            } else if (newPoint < BigDecimal.ZERO) {
                SegmentRepository.deleteByMemberPointId(sdk, memberPoint.pointPlanId, basicSegment.memberPointId)
                initPointSegment(
                    sdk,
                    memberPoint.copy(point = newPoint),
                    LONG_TERM_OVERDUE_DATE.shDate(),
                    id = Uuid.uuid
                )
            } else {
                SegmentRepository.deleteByMemberPointId(sdk, memberPoint.pointPlanId, basicSegment.memberPointId)
            }
        } else {
            // 顺便清理一下过期的积分段
            SegmentRepository.deleteByMemberPointId(sdk, memberPoint.pointPlanId, memberPoint.id)
            initPointSegment(
                sdk,
                memberPoint.copy(point = -point),
                LONG_TERM_OVERDUE_DATE.shDate(),
                id = Uuid.uuid
            )
        }
        return leftSegmentDate
    }


    fun findByDateAfter(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, date: ZonedDateTime): BasicSegment? {
        return SegmentRepository.selectPointSegment(sdk, memberPoint.pointPlanId, memberPoint.id, date.shDate())
    }

}