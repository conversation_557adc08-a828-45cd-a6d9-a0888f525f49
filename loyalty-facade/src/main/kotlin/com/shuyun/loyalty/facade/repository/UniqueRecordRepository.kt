package com.shuyun.loyalty.facade.repository

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.facade.model.DuplicateRequestException
import com.shuyun.loyalty.facade.model.InterfaceRecord
import com.shuyun.loyalty.facade.model.isSdkDuplicate
import com.shuyun.loyalty.sdk.Property
import java.time.ZonedDateTime

object UniqueRecordRepository {

    fun insert(sdk: DataapiWebSocketSdk, uniqueId: String) {
        val disabled = Property.getSysOrEnv("data.loyalty.interface.record.disabled", false)
        if (disabled) {
            return
        }
        try {
            sdk.insert(
                "data.loyalty.interface.Record",
                mapOf("id" to uniqueId, "sequence" to uniqueId, "created" to ZonedDateTime.now()),
                false,
                false
            )
        } catch (e: SdkException) {
            if (e.isSdkDuplicate()) {
                throw DuplicateRequestException("重复请求：${uniqueId}")
            } else {
                throw e
            }
        }
    }

    fun insert(sdk: DataapiWebSocketSdk, interfaceRecord: InterfaceRecord) {
        val disabled = Property.getSysOrEnv("data.loyalty.interface.record.disabled", false)
        if (disabled) {
            return
        }
        try {
            sdk.insert(
                "data.loyalty.interface.Record",
                mapOf(
                    "id" to interfaceRecord.id,
                    "sequence" to interfaceRecord.sequence,
                    "content" to interfaceRecord.content,
                    "created" to (interfaceRecord.created ?: ZonedDateTime.now())
                ), false, false
            )
        } catch (e: SdkException) {
            if (e.isSdkDuplicate()) {
                throw DuplicateRequestException("重复请求：${interfaceRecord.id}")
            } else {
                throw e
            }
        }

    }
}