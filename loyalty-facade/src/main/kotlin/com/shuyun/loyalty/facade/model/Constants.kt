package com.shuyun.loyalty.facade.model

import com.shuyun.loyalty.sdk.Property
import java.math.BigDecimal
import java.time.ZonedDateTime

object Constants {

    const val SERVICE_NAME = "loyalty-facade"
    private const val API_VERSION = "v1"
    const val CONTEXT_PATH = "/$SERVICE_NAME/$API_VERSION"

    const val DEFAULT_CHANNEL_TYPE = "loyalty"
    const val DEFAULT_OPERATOR = "System"
    const val DEFAULT_CHANGE_MODE = "INTERFACE"
    const val DEFAULT_TX_CHANGE_MODE = "TX_INTERFACE"
    const val LOYALTY_TRACE_ID = "loyalty_trace_id"
    const val RANDOM_CHAR_DICTIONARY = "abcdefghijklmnopqrstuvwxyz0123456789"
    val DEFAULT_ZERO_LINE = BigDecimal("0.000001")
    val DEFAULT_WAIT_TIME by lazy { Property.getSysOrEnv("safe.operator.wait", 5000L) }
    val LONG_TERM_OVERDUE_DATE: ZonedDateTime = ZonedDateTime.parse("3000-12-12T00:00:00.000Z")

    val localTenantId by lazy { Property.getSysOrEnv("system.tenant", "default") }
}
