package com.shuyun.loyalty.facade.routes.medal

import com.shuyun.loyalty.facade.common.Kafka
import com.shuyun.loyalty.facade.common.withLock
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.repository.medal.MedalRepository
import com.shuyun.loyalty.facade.service.BlockListService
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.medal.MedalService
import com.shuyun.loyalty.sdk.api.model.MedalRecordType
import com.shuyun.loyalty.sdk.api.model.http.medal.MemberMedalBatchRecycleRequest
import com.shuyun.loyalty.sdk.api.model.http.medal.MemberMedalModifyRequest
import com.shuyun.loyalty.sdk.api.model.http.medal.MemberMedalRecycleRequest
import com.shuyun.loyalty.sdk.api.model.http.medal.MemberMedalResetRequest
import com.shuyun.loyalty.sdk.Dataapi.noneTransaction
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Uuid
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.withContext
import java.time.ZonedDateTime

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.medal.medalModify.kt")


fun Route.medalModify() {

    // 勋章发放
    post("/medal/obtain") {
        val req = call.receive<MemberMedalModifyRequest>().also { LOGGER.info("勋章发放请求: {}", Json.toJson(it)) }.validate()
        val hierarchy = ProgramService.getMedalHierarchyById(req.medalHierarchyId!!) ?: throw MedalHierarchyNotFoundExistException()
        val definition = hierarchy.medalDefinitions.find { it.id == req.medalDefinitionId }?:throw MedalDefinitionNotFoundExistException()
        //查询当前勋章
        val currentMedal = noneTransaction { MedalRepository.queryMemberMedals(it, req.medalHierarchyId!!, req.medalDefinitionId!!, req.memberId!!, disabled = null) }.firstOrNull()
        val recordType =  if (currentMedal != null &&!currentMedal.disabled!!) { MedalRecordType.KEEP } else MedalRecordType.OBTAIN
        // 检查特殊名单
        BlockListService.check(
            TypeEnum.MEDAL,
            hierarchy.subject.fqn,
            hierarchy.id,
            req.memberId!!,
            recordType.getForbiddenOperation()
        )
        val targetMedal = currentMedal?.asTargetMedal()?.apply { overdueDate = req.overdueDate } ?: MemberMedal(
            id = Uuid.uuid,
            memberId = req.memberId,
            planId = hierarchy.plan.id,
            planName = hierarchy.plan.name,
            subjectFqn = hierarchy.subject.fqn,
            medalHierarchyId = hierarchy.id,
            medalHierarchyName = hierarchy.name,
            medalDefinitionId = definition.id,
            medalDefinitionName = definition.name,
            version = 1,
            effectDate = ZonedDateTime.now(),
            overdueDate = req.overdueDate,
            created = ZonedDateTime.now(),
            disabled = false
        )
        val currentMedalDefinitionIds = queryMedalDefinitionIds(targetMedal)
        val medalRecord = req.buildMedalRecord(hierarchy, targetMedal, currentMedalDefinitionIds, currentMedal, recordType.name)
        val lockKey = "medal_calculate_${req.medalHierarchyId}-${req.memberId}"
        withLock(lockKey) { MedalService.modify(targetMedal, currentMedal,medalRecord) }
        Kafka.sendMedalNotify(req.memberId!!, medalRecord)
        call.respond(HttpStatusCode.OK)
    }


    // 勋章回收
    post("/medal/recycle") {
        val req = call.receive<MemberMedalRecycleRequest>().also { LOGGER.info("勋章回收请求: {}", Json.toJson(it)) }.validate()
        val hierarchy = ProgramService.getMedalHierarchyById(req.medalHierarchyId!!) ?: throw MedalHierarchyNotFoundExistException()
        //查询当前勋章
        val currentMedal = noneTransaction {MedalRepository.queryMemberMedals(it, req.medalHierarchyId!!,req.medalDefinitionId!!, req.memberId!!, disabled = null)}.firstOrNull()
        if (currentMedal ==null ||currentMedal.disabled!!) throw MemberMedalNotFoundExistException()
         // 检查特殊名单
        BlockListService.check(
            TypeEnum.MEDAL,
            hierarchy.subject.fqn,
            hierarchy.id,
            req.memberId!!,
            ForbiddenOperation.MEDAL_RECYCLE
        )
        val targetMedal = currentMedal.asTargetMedal().apply { this.disabled = true }
        val currentMedalDefinitionIds = queryMedalDefinitionIds(targetMedal)
        val medalRecord = req.buildMedalRecord(hierarchy, targetMedal, currentMedalDefinitionIds, currentMedal, MedalRecordType.RECYCLE.name)
        val lockKey = "medal_calculate_${req.medalHierarchyId}-${req.memberId}"
        withLock(lockKey) { MedalService.modify(targetMedal,currentMedal, medalRecord) }
        Kafka.sendMedalNotify(req.memberId!!, medalRecord)

        call.respond(HttpStatusCode.OK)
    }

    //勋章重置
    post("/medal/reset") {
        val request = call.receive<MemberMedalResetRequest>().also { LOGGER.info("勋章重置请求: {}", Json.toJson(it)) }.validate()
        val hierarchy = ProgramService.getMedalHierarchyById(request.medalHierarchyId!!)
            ?: throw MedalHierarchyNotFoundExistException()
        if (request.requestType == "SYNC") {
            reset(request, hierarchy)
            call.respond(HttpStatusCode.OK)
        } else {
            call.application.launch(Dispatchers.IO + MDCContext()) {
                withContext(SupervisorJob()) {
                    reset(request, hierarchy)
                }
            }
            call.respond(HttpStatusCode.Accepted)
        }
    }


    // 勋章批量回收
    post("/medal/batchRecycle") {
        val req = call.receive<MemberMedalBatchRecycleRequest>().also { LOGGER.info("勋章批量回收请求: {}", Json.toJson(it)) }.validate()
        val params = ArrayList<MedalBatchModifyParam>()
        req.medalHierarchyIds.forEach { hierarchyId ->
            val hierarchy = ProgramService.getMedalHierarchyById(hierarchyId) ?: throw MedalHierarchyNotFoundExistException()
            BlockListService.check(
                TypeEnum.MEDAL,
                hierarchy.subject.fqn,
                hierarchy.id,
                req.memberId!!,
                ForbiddenOperation.MEDAL_RECYCLE
            )
            val currentMedals = noneTransaction {
                MedalRepository.queryMemberMedals(
                    it,
                    hierarchy.id,
                    null,
                    req.memberId!!,
                    disabled = false
                )
            }
            currentMedals.forEach { currentMedal ->
                val targetMedal = currentMedal.asTargetMedal().apply { this.disabled = true }
                val currentMedalDefinitionIds = queryMedalDefinitionIds(targetMedal)
                val medalRecord = req.buildMedalRecord(
                    hierarchy,
                    targetMedal,
                    currentMedalDefinitionIds,
                    currentMedal,
                    MedalRecordType.RECYCLE.name
                )
                params.add(MedalBatchModifyParam(currentMedal, targetMedal, medalRecord))
            }
        }
        val lockKey = req.medalHierarchyIds.map { "medal_calculate_${it}-${req.memberId}" }
        withLock(lockKey){ MedalService.batchModify(params) }
        params.forEach { Kafka.sendMedalNotify(req.memberId!!, it.medalRecord) }
        call.respond(HttpStatusCode.OK)
    }

}


private fun MemberMedal.asTargetMedal(): MemberMedal {
    return this.copy(
        version = (this.version ?: 0) + 1,
        effectDate = if (this.effectDate != null) this.effectDate else ZonedDateTime.now(),
    )
}


private suspend fun queryMedalDefinitionIds(memberMedal: MemberMedal): List<Long> {
    return noneTransaction {
        MedalRepository.queryMedalDefinitionIdByMemberId(
            it,
            memberMedal.medalHierarchyId!!,
            memberMedal.memberId!!,
            false
        )
    } ?: emptyList()
}


private suspend fun reset(request: MemberMedalResetRequest,hierarchy: MedalHierarchy) {
    val params = ArrayList<MedalBatchModifyParam>()
    val medalList = noneTransaction {
        MedalRepository.queryAllMedals(
            it,
            request.memberId,
            request.medalHierarchyId!!,
            request.medalDefinitionId,
            false
        )
    }

    if (medalList.isEmpty()) throw MemberMedalNotFoundExistException()

    medalList.forEach { currentMedal ->
        BlockListService.check(
            TypeEnum.MEDAL,
            hierarchy.subject.fqn,
            hierarchy.id,
            currentMedal.memberId!!,
            ForbiddenOperation.MEDAL_KEEP
        )
        val targetMedal = currentMedal.asTargetMedal().apply { overdueDate = request.overdueDate }
        val currentMedalDefinitionIds = queryMedalDefinitionIds(targetMedal)
        val medalRecord = request.buildMedalRecord(hierarchy, targetMedal, currentMedalDefinitionIds, currentMedal, MedalRecordType.KEEP.name)
        params.add(MedalBatchModifyParam(currentMedal, targetMedal, medalRecord))
    }
    MedalService.batchModify(params)
    params.forEach { Kafka.sendMedalNotify(it.medalRecord.memberId!!, it.medalRecord) }
}


private fun MemberMedalResetRequest.validate(): MemberMedalResetRequest {
    if (medalHierarchyId == null) throw BadParamRequestException("勋章体系ID不能为空")
    if (channelType == null) throw BadParamRequestException("渠道类型不能为空")
    if (medalDefinitionId ==null) throw BadParamRequestException("勋章ID不能为空")
    if(overdueDate != null && overdueDate!!.isBefore(ZonedDateTime.now())) throw BadParamRequestException("过期时间不能早于当前时间")
    ProgramService.findChannelTypeByCode(channelType!!) ?: throw ChannelTypeNotExistException()
    return this
}


private fun MemberMedalBatchRecycleRequest.validate(): MemberMedalBatchRecycleRequest {
    if (medalHierarchyIds.isEmpty()) throw BadParamRequestException("勋章体系ID不能为空")
    if (channelType == null) throw BadParamRequestException("渠道类型不能为空")
    ProgramService.findChannelTypeByCode(channelType!!) ?: throw ChannelTypeNotExistException()
    return this
}


private fun MemberMedalModifyRequest.validate(): MemberMedalModifyRequest {
    if (medalHierarchyId == null) throw BadParamRequestException("勋章体系ID不能为空")
    if (memberId == null) throw BadParamRequestException("会员ID不能为空")
    if (channelType == null) throw BadParamRequestException("渠道不能为空")
    if (medalDefinitionId ==null) throw BadParamRequestException("勋章ID不能为空")
    if(overdueDate != null && overdueDate!!.isBefore(ZonedDateTime.now())) throw BadParamRequestException("过期时间不能早于当前时间")
    ProgramService.findChannelTypeByCode(channelType!!) ?: throw ChannelTypeNotExistException()
    return this
}

private fun MemberMedalRecycleRequest.validate(): MemberMedalRecycleRequest {
    if (medalHierarchyId == null) throw BadParamRequestException("勋章体系ID不能为空")
    if (memberId == null) throw BadParamRequestException("会员ID不能为空")
    if (channelType == null) throw BadParamRequestException("渠道不能为空")
    if (medalDefinitionId ==null) throw BadParamRequestException("勋章ID不能为空")
    ProgramService.findChannelTypeByCode(channelType!!) ?: throw ChannelTypeNotExistException()
    return this
}


// 黑名单配置类型
private fun MedalRecordType.getForbiddenOperation(): ForbiddenOperation {
    return when (this) {
        MedalRecordType.OBTAIN -> ForbiddenOperation.MEDAL_OBTAIN
        MedalRecordType.KEEP -> ForbiddenOperation.MEDAL_KEEP
        MedalRecordType.RECYCLE -> ForbiddenOperation.MEDAL_RECYCLE
    }
}


private fun MemberMedalModifyRequest.buildMedalRecord(
    hierarchy: MedalHierarchy,
    targetMedal: MemberMedal,
    currentMedalDefinitionIds: List<Long>?,
    oldMedal: MemberMedal?,
    recordType: String,
): MemberMedalRecord {
    return buildMedalRecord(
        hierarchy, targetMedal, currentMedalDefinitionIds, oldMedal, recordType,
        this.channelType,
        this.kzzd1,
        this.kzzd2,
        this.kzzd3,
        this.description
    )
}


private fun MemberMedalRecycleRequest.buildMedalRecord(
    hierarchy: MedalHierarchy,
    targetMedal: MemberMedal,
    currentMedalDefinitionIds: List<Long>?,
    oldMedal: MemberMedal?,
    recordType: String
): MemberMedalRecord {
    return buildMedalRecord(
        hierarchy, targetMedal, currentMedalDefinitionIds, oldMedal, recordType,
        this.channelType,
        this.kzzd1,
        this.kzzd2,
        this.kzzd3,
        this.description
    )
}


private fun MemberMedalResetRequest.buildMedalRecord(
    hierarchy: MedalHierarchy,
    targetMedal: MemberMedal,
    currentMedalDefinitionIds: List<Long>?,
    oldMedal: MemberMedal?,
    recordType: String,
): MemberMedalRecord {
    return buildMedalRecord(
        hierarchy, targetMedal, currentMedalDefinitionIds, oldMedal, recordType,
        this.channelType, this.kzzd1, this.kzzd2, this.kzzd3, this.description
    )
}


private fun MemberMedalBatchRecycleRequest.buildMedalRecord(
    hierarchy: MedalHierarchy,
    targetMedal: MemberMedal,
    currentMedalDefinitionIds: List<Long>?,
    oldMedal: MemberMedal?,
    recordType: String,
): MemberMedalRecord {
    return buildMedalRecord(
        hierarchy, targetMedal, currentMedalDefinitionIds, oldMedal, recordType,
        this.channelType, this.kzzd1, this.kzzd2, this.kzzd3, this.description
    )
}


private fun buildMedalRecord(
    hierarchy: MedalHierarchy,
    targetMedal: MemberMedal,
    currentMedalDefinitionIds: List<Long>?,
    oldMedal: MemberMedal?,
    recordType: String,
    channel:String?,
    kzzd1: String?,
    kzzd2:String?,
    kzzd3:String?,
    description:String?
    ): MemberMedalRecord {
    return MemberMedalRecord(
        id = Uuid.uuid,
        planId = hierarchy.plan.id,
        planName = hierarchy.plan.name,
        medalHierarchyId = hierarchy.id,
        medalHierarchyName = hierarchy.name,
        medalDefinitionId = targetMedal.medalDefinitionId,
        medalDefinitionName = targetMedal.medalDefinitionName,
        memberMedalId = targetMedal.id,
        memberId = targetMedal.memberId,
        originalEffectDate = oldMedal?.effectDate,
        originalOverdueDate = oldMedal?.overdueDate,
        currentMedalDefinitionIds = currentMedalDefinitionIds?.joinToString(","),
        currentEffectDate = targetMedal.effectDate,
        currentOverdueDate = targetMedal.overdueDate,
        recordType = recordType,
        changeWay = "INTERFACE",
        triggerId = null,
        traceId = null,
        recordSourceDetail = null,
        subjectFqn = hierarchy.subject.fqn,
        description = description,
        created = ZonedDateTime.now(),
        recordDetail = null,
        channel = channel,
        KZZD1 = kzzd1,
        KZZD2 =  kzzd2,
        KZZD3 = kzzd3,
        key = null,
    )
}