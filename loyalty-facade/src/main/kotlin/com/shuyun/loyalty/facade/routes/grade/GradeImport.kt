package com.shuyun.loyalty.facade.routes.grade

import com.shuyun.loyalty.facade.model.BadParamRequestException
import com.shuyun.loyalty.facade.model.Constants
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.grade.GradeService
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeImportRequest
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import java.time.ZonedDateTime

fun Route.gradeImport() {

    // 迁移历史等级数据，如果当前存在等级数据会被覆盖，适用于新环境初始化
    post("/grade/import") {
        val importId = call.parameters["importId"] ?: throw BadParamRequestException("导入ID不能为空")
        val gradeHierarchyId = call.parameters["gradeHierarchyId"]?.toLong() ?: throw BadParamRequestException("等级体系ID不能为空")

        val hierarchy = ProgramService.getGradeHierarchyById(gradeHierarchyId) ?: throw BadParamRequestException("无效的等级体系ID")
        val reqs = call.receive<List<MemberGradeImportRequest>>().also {
            if (it.isEmpty()) throw BadParamRequestException("导入等级变更数据不能为空")
            if (it.size > 2000) throw BadParamRequestException("单次请求最多接收2000笔数据")
            it.forEach { req ->
                req.validate()
                val gradeDefinition = hierarchy.gradeDefinitions.find { x -> x.id == req.targetGradeDefinitionId }
                if (gradeDefinition == null) {
                    throw BadParamRequestException("无效的等级ID: ${req.targetGradeDefinitionId} key: ${req.key} memberId: ${req.memberId}")
                }
            }
        }

        reqs.groupBy { it.memberId!! }.map {(memberId, list) ->
            GradeService.import(importId, hierarchy, memberId, list)
        }
        call.respond(HttpStatusCode.OK)
    }
}


private fun MemberGradeImportRequest.validate(): MemberGradeImportRequest {
    if (memberId == null) throw BadParamRequestException("会员ID不能为空")
    if (targetGradeDefinitionId == null) throw BadParamRequestException("目标等级ID不能为空")
    if (createdDate == null) throw BadParamRequestException("等级变更时间不能为空")

    if (createdDate!!.isAfter(ZonedDateTime.now())) {
        throw BadParamRequestException("创建时间(${createdDate})不能大于当前时间(${ZonedDateTime.now()}) 会员ID: $memberId key: $key")
    }
    if (overdueDate != null && createdDate!!.isAfter(overdueDate)) {
        throw BadParamRequestException("创建时间(${createdDate})不能大于等级过期时间(${overdueDate}) 会员ID: $memberId key: $key")
    }
    if (channelType != null) {
        ProgramService.findChannelTypeByCode(channelType!!) ?: throw BadParamRequestException("无效的渠道类型")
    } else {
        channelType = Constants.DEFAULT_CHANNEL_TYPE
    }
    return this
}