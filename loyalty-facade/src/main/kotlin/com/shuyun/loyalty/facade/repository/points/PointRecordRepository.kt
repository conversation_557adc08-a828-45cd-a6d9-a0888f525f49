package com.shuyun.loyalty.facade.repository.points

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.PointRecord
import com.shuyun.loyalty.facade.model.PointRecordItem
import com.shuyun.loyalty.sdk.api.model.Page
import com.shuyun.loyalty.sdk.utcStr
import com.shuyun.loyalty.sdk.Json
import java.time.ZonedDateTime

object PointRecordRepository {

    private const val COLUMNS = "`id`,`planId`,`pointPlanId`,`memberPointId`,`memberId`,`subjectFqn`,`point`,`changePoint`,`totalPoint`, `recordType`,`recordSourceDetail`,`effectiveDate`,`overdueDate`,`extralInfo`,`recordDetail`,`desc`, `fromStatus`,`status`,`operator`,`operatorId`,`modified`,`created`,`channel`,`key`,`ruleGroup`, `ruleName`,`ruleId`,`changeMode`,`planName`,`pointPlanName`,`traceId`,`eventTypeName`,`actionId`, `actionName`,`actionNodeId`,`actionNodeName`,`shopId`,`KZZD1`,`KZZD2`,`KZZD3`"

    @Suppress("UNCHECKED_CAST")
    fun queryPointRecord(
        sdk: DataapiWebSocketSdk,
        pointAccountId: Long,
        recordTypes: List<String>?,
        status: List<String>?,
        memberId: String?,
        shopId: String?,
        channelType: String?,
        startTime: ZonedDateTime?,
        endTime: ZonedDateTime?,
        traceId: String?,
        sortType: String?,
        changeMode: String?,
        number: Int,
        pageSize: Int,
        recordId: String? = null,
        withTotal: Boolean = false,
        withoutPageContent: Boolean = false,
    ): Page<PointRecord> {
        val recordTypeList = if (recordTypes.isNullOrEmpty()) null else recordTypes
        val statusList = if (status.isNullOrEmpty()) null else status
        val fromWhere = """
            from data.loyalty.member.account.PointRecord${pointAccountId} 
            where `pointPlanId` = :pointPlanId
            ${recordTypeList?.let { " and `recordType` in (:recordTypes)" } ?: ""}
            ${statusList?.let { " and `status` in (:status)" } ?: ""}
            ${memberId?.let { " and `memberId` = :memberId" } ?: ""}
            ${shopId?.let { " and `shopId` = :shopId" } ?: ""}
            ${channelType?.let { " and `channel` = :channelType" } ?: ""}
            ${startTime?.let { " and `created` >= :startTime" } ?: ""}
            ${endTime?.let { " and `created` <= :endTime" } ?: ""}
            ${traceId?.let { " and `traceId` = :traceId" } ?: ""}
            ${changeMode?.let { " and `changeMode` = :changeMode" } ?: ""}
            ${recordId?.let { " and `id` = :recordId" } ?: ""}
        """.trimIndent().lines().filter { it.isNotBlank() }.joinToString(" ")
        val pageWhere = "$fromWhere order by `created` ${sortType ?: "desc"} LIMIT $pageSize OFFSET ${number * pageSize}"
        val params = mapOf(
            "pointPlanId" to pointAccountId,
            "recordTypes" to recordTypeList,
            "status" to statusList,
            "memberId" to memberId,
            "shopId" to shopId,
            "channelType" to channelType,
            "startTime" to startTime?.utcStr(),
            "endTime" to endTime?.utcStr(),
            "traceId" to traceId,
            "changeMode" to changeMode,
            "recordId" to recordId
        )
        var totalElements: Long? = null

        if (withTotal) {
            val countSql = "select count(*) as count $fromWhere"
            val countResponse = sdk.execute(countSql, params).data
            totalElements = (countResponse.firstOrNull() as Map<String, Any?>?)?.get("count")?.toString()?.toLong() ?: 0L
            if (withoutPageContent) {
                return Page(
                    content = emptyList(),
                    number = number,
                    size = pageSize,
                    totalPages = (totalElements + pageSize - 1) / pageSize,
                    totalElements = totalElements
                )
            }
        }

        val sql = "select $COLUMNS $pageWhere"
        val response = sdk.execute(sql, params).data
        val content: List<PointRecord> = Json.convert(response)

        return Page(
            content = content,
            number = number,
            size = pageSize,
            totalPages = totalElements?.let { (it + pageSize - 1) / pageSize },
            totalElements = totalElements
        )
    }


    fun queryPointRecordItems(
        sdk: DataapiWebSocketSdk,
        pointAccountId: Long,
        traceId: String,
    ): List<PointRecordItem> {
        val sql = """
            select `id`,`planId`,`status`,`sort`,`pointPlanId`,`traceId`,`memberId`,`point`,`created`,`effectiveDate`,`overdueDate`,`backId`,`parentBackId`,`recordId` 
            from data.loyalty.member.account.PointRecordItem${pointAccountId} 
            where `traceId` = :traceId
        """.trimIndent()
        val params = mapOf("traceId" to traceId)
        val response = sdk.execute(sql, params).data
        return Json.convert(response)
    }
}