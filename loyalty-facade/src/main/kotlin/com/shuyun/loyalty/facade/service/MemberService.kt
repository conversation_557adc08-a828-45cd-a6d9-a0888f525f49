package com.shuyun.loyalty.facade.service

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.MemberNotFoundException
import com.shuyun.loyalty.facade.repository.MemberRepository
import com.shuyun.loyalty.sdk.Dataapi
import com.shuyun.loyalty.sdk.Property
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.withContext

object MemberService {

    suspend fun findMemberField(fqn: String, memberId: String, column: String = "id"): String? =
        withContext(Dispatchers.IO + MDCContext()) {
            Dataapi.sdk.use {
                val map = MemberRepository.findMemberId(it, fqn, memberId, column)
                map[column] as String?
            }
        }

    suspend fun checkMemberId(subjectFqn: String, memberId: String) {
        val id = findMemberField(subjectFqn, memberId)
        val checkMemberExist = Property.getSysOrEnv("member.pointAccount.checkExist", true)
        if (checkMemberExist && id == null) {
            throw MemberNotFoundException("无效的会员ID: $memberId")
        }
    }

    fun checkMemberId(sdk: DataapiWebSocketSdk, subjectFqn: String, memberId: String, column: String = "id") {
        val map = MemberRepository.findMemberId(sdk, subjectFqn, memberId, column)
        val v = map[column] as String?
        val checkMemberExist = Property.getSysOrEnv("member.pointAccount.checkExist", true)
        if (checkMemberExist && v == null) {
            throw MemberNotFoundException("无效的会员ID: $memberId")
        }
    }
}