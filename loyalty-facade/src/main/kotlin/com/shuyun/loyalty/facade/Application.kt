package com.shuyun.loyalty.facade

import com.shuyun.lite.client.ConfigurationManagerHolder
import com.shuyun.lite.client.PassportClientFactory
import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.facade.common.Kafka
import com.shuyun.loyalty.facade.common.Redis
import com.shuyun.loyalty.facade.model.Constants.CONTEXT_PATH
import com.shuyun.loyalty.facade.model.Constants.SERVICE_NAME
import com.shuyun.loyalty.facade.plugins.*
import com.shuyun.loyalty.facade.remote.OpenApiRegister
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.sdk.Property
import io.ktor.server.application.*
import io.ktor.server.engine.*
import io.ktor.server.netty.*
import kotlinx.coroutines.IO_PARALLELISM_PROPERTY_NAME
import org.apache.logging.log4j.core.config.Configurator
import org.slf4j.LoggerFactory
import kotlin.system.exitProcess


fun main() {
    configureEnv()
    embeddedServer(
        factory = Netty,
        rootConfig = serverConfig {
            module { module() }
            rootPath = CONTEXT_PATH
        },
        configure = {
            connectors.add(EngineConnectorBuilder().apply {
                host = "0.0.0.0"
                port = Property.getSysOrEnv("server.port", 8080)
            })
        }
    ).start(wait = true)
}

private fun registerService() {
    val clientId = Property.getSysOrEnv("service.clientId", SERVICE_NAME)
    val passportBuilder = PassportClientFactory.passportBuilder().appKey(clientId)
    val passportClient = passportBuilder.build()
    passportClient.registerClient(clientId, GlobalContext.serviceName())
}


private fun resetLogback() {
    Thread.currentThread().contextClassLoader
        .getResource("log4j2.xml")
        ?.toURI()
        ?.let { Configurator.reconfigure(it) }
}


private fun configureEnv() {
    try {
        val s = System.currentTimeMillis()
        System.setProperty(IO_PARALLELISM_PROPERTY_NAME, 256.toString())
        ConfigurationManagerHolder.init()
        resetLogback()
        registerService()
        Redis.init()
        Kafka.init()
        OpenApiRegister.register()
        ProgramService.loadCache()
        LoggerFactory.getLogger("configureEnv").info("环境初始化完成，耗时{}ms", System.currentTimeMillis() - s)
    } catch (e: Throwable) {
        LoggerFactory.getLogger("configureEnv").error("服务启动失败", e)
        exitProcess(-1)
    }
}


fun Application.module() {
    configureHTTP()
    configureMonitoring()
    configureSerialization()
    configureExceptionHandling()
    configureRouting()
    monitor.subscribe(ApplicationStarted) { application ->
        application.log.info("服务启动完成 环境: {}", Property.getSysOrEnv("system.tenant"))
    }
}
