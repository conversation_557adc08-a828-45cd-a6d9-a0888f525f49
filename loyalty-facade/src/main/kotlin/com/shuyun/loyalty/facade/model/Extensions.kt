package com.shuyun.loyalty.facade.model

import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.facade.service.ProgramService
import java.math.BigDecimal
import java.time.*
import java.time.format.DateTimeFormatter


// BigDecimal
fun BigDecimal.scale(pointAccountId: Long): BigDecimal {
    val pointAccountType = ProgramService.getPointAccountTypeById(pointAccountId) ?: return BigDecimal.ZERO
    val value = this.setScale(pointAccountType.precision!!, pointAccountType.rounding)
    return if (value < Constants.DEFAULT_ZERO_LINE) {
        BigDecimal.ZERO
    } else {
        value
    }
}

fun ZonedDateTime.utcStr(): String {
    return DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").format(this.withZoneSameInstant(ZoneOffset.UTC))
}

fun ZonedDateTime.shDate(): LocalDate {
    return this.withZoneSameInstant(ZoneId.of("Asia/Shanghai")).toLocalDate()
}

fun ZonedDateTime?.adjustToSHTimeEnd(): ZonedDateTime {
    val zdt = this ?: Constants.LONG_TERM_OVERDUE_DATE
    return if (zdt.shDate() != Constants.LONG_TERM_OVERDUE_DATE.shDate()) {
        ZonedDateTime.of(zdt.shDate(), LocalTime.MAX, ZoneId.of("Asia/Shanghai"))
    } else zdt
}


fun String.isISODate(): Boolean {
    try {
        ZonedDateTime.parse(this)
        return true
    } catch (e: Exception) {
        return false
    }
}


fun String.checkISODate(message: String) {
    if (!this.isISODate()) {
        throw BadParamRequestException(message)
    }
}


fun Throwable.isSdkDuplicate(): Boolean {
    if (this is SdkException) {
        return this.error_code == "151110" || this.error_code == "151111"
    }
    return false
}
