package com.shuyun.loyalty.facade.repository.points

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Uuid
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime


@Suppress("UNCHECKED_CAST")
object MemberPointRepository {

    private const val POINT_COLUMNS = " id,planId,pointPlanId,memberId,subjectFqn,point,version,openSegmentFlag,created, modified "

    fun insertMemberPoint(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, now: ZonedDateTime = ZonedDateTime.now()) {
        try {
            sdk.insert(
                "data.loyalty.member.account.Point${memberPoint.pointPlanId}", mapOf(
                    "id" to memberPoint.id,
                    "planId" to memberPoint.planId,
                    "pointPlanId" to memberPoint.pointPlanId,
                    "memberId" to memberPoint.memberId,
                    "member" to mapOf(Pair("id", memberPoint.memberId)),
                    "subjectFqn" to memberPoint.subjectFqn,
                    "version" to 0,
                    "point" to memberPoint.point,
                    "modified" to now,
                    "created" to now,
                    "openSegmentFlag" to true
                ), false, false
            )
        } catch (e: SdkException) {
            if (e.isSdkDuplicate() || e.message?.contains("duplicate entry", ignoreCase = true) == true) {
                throw DBUpdateConcurrencyException("会员积分账户已存在, ${memberPoint.id}")
            } else {
                throw e
            }
        }
    }


    fun insertPointLog(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint) {
        val sql = """
            INSERT INTO data.loyalty.member.account.point.Log${memberPoint.pointPlanId} (
                `id`,`planId`,`pointPlanId`,`memberId`,`subjectFqn`,`point`,`created`, `member`
            ) VALUES (
                :id, :planId, :pointPlanId, :memberId, :subjectFqn, :point, :created, :member
            )
        """.trimIndent()
        val params = mapOf(
            "id" to Uuid.uuid,
            "planId" to memberPoint.planId,
            "pointPlanId" to memberPoint.pointPlanId,
            "memberId" to memberPoint.memberId,
            "subjectFqn" to memberPoint.subjectFqn,
            "point" to memberPoint.point,
            "created" to ZonedDateTime.now(),
            "member" to memberPoint.memberId
        )
        sdk.execute(sql, params)
    }



    fun findMemberPoint(
        sdk: DataapiWebSocketSdk,
        pointAccountId: Long,
        memberId: String,
        now: LocalDate = ZonedDateTime.now().shDate(),
        includeSegmentPoint: Boolean = true
    ): MemberPoint? {
        val sql = """
            SELECT $POINT_COLUMNS
            FROM data.loyalty.member.account.point$pointAccountId
            WHERE memberId = :memberId 
            LIMIT 1
        """.trimIndent()
        val memberPoint = sdk.execute(sql, mapOf("memberId" to memberId)).data.firstOrNull()?.let {
            val row = it as Map<String, Any?>
            MemberPoint(
                id = row["id"] as String,
                planId = row["planId"].toString().toLong(),
                pointPlanId = row["pointPlanId"].toString().toLong(),
                memberId = row["memberId"] as String,
                subjectFqn = row["subjectFqn"] as String,
                point = row["point"].toString().toBigDecimal(),
                version = row["version"]?.toString()?.toLong() ?: 0,
                openSegmentFlag = row["openSegmentFlag"]?.toString()?.toBoolean() ?: false,
            )
        }
        return if (!includeSegmentPoint) {
            memberPoint
        } else {
            memberPoint?.let {
                val segment = findMemberPointSegment(sdk, pointAccountId, memberId, now)
                val segmentPoint = segment?.point ?: BigDecimal.ZERO
                memberPoint.copy(point = segmentPoint)
            }
        }
    }


    fun selectMemberPoints(
        sdk: DataapiWebSocketSdk,
        pointAccountId: Long,
        memberId: String?,
        number: Int = 0,
        pageSize: Int = 20,
    ): List<MemberPoint> {
        val sql = """
            SELECT $POINT_COLUMNS
            FROM data.loyalty.member.account.point$pointAccountId
            WHERE 1 = 1 
            ${if (memberId != null) " AND memberId = :memberId" else ""}
            ORDER BY memberId asc
            LIMIT $pageSize OFFSET ${number * pageSize}
        """.trimIndent()
        val contents =  sdk.execute(sql, mapOf("memberId" to memberId)).data
        return Json.convert(contents)
    }


    private fun findMemberPointSegment(
        sdk: DataapiWebSocketSdk,
        pointAccountId: Long,
        memberId: String,
        now: LocalDate = ZonedDateTime.now().shDate()
    ): MemberPointSegment? {
        val sql = """
            SELECT point,expireDate 
            FROM data.loyalty.member.account.PointSegment${pointAccountId} 
            WHERE memberId = :memberId 
                AND ((expireDate >= :expireDate AND point > 0) OR (expireDate = :longTermOverdueDate AND point <= 0))
            ORDER BY expireDate ASC LIMIT 1 
        """.trimIndent()
        val param = mapOf(
            "memberId" to memberId,
            "expireDate" to now,
            "longTermOverdueDate" to Constants.LONG_TERM_OVERDUE_DATE.shDate()
        )
        return sdk.execute(sql, param).data.firstOrNull()?.let {
            val row = it as Map<String, Any?>
            MemberPointSegment(
                point = row["point"].toString().toBigDecimal(),
                expireDate = row["expireDate"].toString().let { x -> LocalDate.parse(x) }
            )
        }
    }


    fun updateMemberPoint(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint) {
        val sql = """
            UPDATE data.loyalty.member.account.point${memberPoint.pointPlanId} 
            SET 
                point = :point, 
                version = :newVersion, 
                openSegmentFlag = :openSegmentFlag, 
                modified = :modified 
            WHERE id = :id
        """.trimIndent()
        val param = mapOf(
            "point" to memberPoint.point,
            "newVersion" to memberPoint.version + 1,
            "version" to memberPoint.version,
            "openSegmentFlag" to memberPoint.openSegmentFlag,
            "modified" to ZonedDateTime.now(),
            "id" to memberPoint.id
        )
        sdk.execute(sql, param)
        memberPoint.version += 1
    }


    fun updateMemberPointAndLog(
        sdk: DataapiWebSocketSdk,
        memberPoint: MemberPoint,
        isDelay: Boolean = false,
    ) {
        updateMemberPoint(sdk, memberPoint)
        if (!isDelay) {
            // 保存总积分变更日志
            insertPointLog(sdk, memberPoint)
        }
    }
}
