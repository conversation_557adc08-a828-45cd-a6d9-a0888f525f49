package com.shuyun.loyalty.facade.routes.points

import com.shuyun.loyalty.facade.common.withMemberPointLock
import com.shuyun.loyalty.facade.model.BadParamRequestException
import com.shuyun.loyalty.facade.model.MemberNotFoundException
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.points.MemberPointService
import com.shuyun.loyalty.facade.service.points.PointModifyService
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointInitRequest
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointInitResponse
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointInitResult
import com.shuyun.loyalty.sdk.Dataapi.withTransaction
import com.shuyun.loyalty.sdk.Json
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import java.math.BigDecimal
import java.time.ZonedDateTime

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.points.PointInit.kt")


fun Route.pointInit() {
    post("/point:init") {
        val req = call.receive<MemberPointInitRequest>().also { LOGGER.info("积分账户初始化: {}", Json.toJson(it)) }.validate()
        val pointAccountIds = req.pointAccountIds!!.toSet()
        val now = ZonedDateTime.now()
        val results = ArrayList<MemberPointInitResult>()
        for (pointAccountId in pointAccountIds) {
            val pointAccountType = ProgramService.getPointAccountTypeById(pointAccountId)
            if (pointAccountType == null) {
                results.add(MemberPointInitResult().apply {
                    this.pointAccountId = pointAccountId
                    this.desc = "无效的积分账户类型ID"
                })
                continue
            }
            try {
                withMemberPointLock(pointAccountId, req.memberId!!) {
                    val memberPoint = MemberPointService.findMemberPoint(pointAccountId, req.memberId!!)
                    if (memberPoint != null) {
                        results.add(MemberPointInitResult().apply {
                            this.pointAccountId = pointAccountId
                            this.desc = "积分账户已存在"
                        })
                        return@withMemberPointLock
                    }
                    val result = withTransaction {
                        PointModifyService.initMemberPoint(
                            it,
                            pointAccountId,
                            req.memberId!!,
                            BigDecimal.ZERO,
                            now,
                        )
                        MemberPointInitResult().apply {
                            this.initResult = true
                            this.pointAccountId = pointAccountId
                        }
                    }
                    results.add(result)
                }
            } catch (e: Throwable) {
                LOGGER.warn(
                    "积分账户初始化失败 businessId={}, memberId={}, pointAccountId={}, channelType={} {}",
                    req.businessId,
                    req.memberId,
                    pointAccountId,
                    req.channelType,
                    e.message
                )
                val initResult = if (e is MemberNotFoundException) {
                    MemberPointInitResult().apply {
                        this.pointAccountId = pointAccountId
                        this.desc = "积分账户初始化失败，会员不存在"
                    }
                } else {
                    MemberPointInitResult().apply {
                        this.pointAccountId = pointAccountId
                        this.desc = e.message
                    }
                }
                results.add(initResult)
            }
        }
        call.respond(MemberPointInitResponse().apply {
            this.memberPointInitResults = results
        })
    }
}


private fun MemberPointInitRequest.validate(): MemberPointInitRequest {
    if (memberId == null) throw BadParamRequestException("memberId参数为必填")
    if (channelType == null) throw BadParamRequestException("channelType参数为必填")
    if (pointAccountIds.isNullOrEmpty()) throw BadParamRequestException("积分账户ID集合不能为空")
    return this
}



