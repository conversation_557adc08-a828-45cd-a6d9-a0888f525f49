package com.shuyun.loyalty.facade.service.points

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.common.Jdbc
import com.shuyun.loyalty.facade.common.Kafka
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.model.PointAction.REVERSE_SEND
import com.shuyun.loyalty.facade.repository.ModifyLogRepository
import com.shuyun.loyalty.facade.repository.UniqueRecordRepository
import com.shuyun.loyalty.facade.repository.points.MemberPointRepository
import com.shuyun.loyalty.facade.repository.points.PointTransactionRepository
import com.shuyun.loyalty.facade.repository.points.SegmentRepository
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.sdk.Dataapi.withTransaction
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Uuid
import com.shuyun.loyalty.sdk.api.model.http.points.*
import com.shuyun.loyalty.sdk.shDate
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

object PointModifyService {

    private val LOGGER = LoggerFactory.getLogger(PointModifyService::class.java)

    private fun MemberPointSendRequest.toPointAttr(): PointAttr {
        return PointAttr(
            traceId = uniqueId!!,
            uniqueId = uniqueId!!,
            changeMode = changeMode ?: Constants.DEFAULT_CHANGE_MODE,
            channel = channelType ?: Constants.DEFAULT_CHANNEL_TYPE,
            operatorId = Constants.DEFAULT_OPERATOR,
            operator = Constants.DEFAULT_OPERATOR,
            effectiveDate = effectiveDate?.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX")),
            overdueDate = overdueDate?.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSXXX")),
            shopId = shopId,
            kzzd1 = kzzd1,
            kzzd2 = kzzd2,
            kzzd3 = kzzd3,
            desc = desc,
            actionId = actionId,
            actionNodeId = actionNodeId,
            actionName = actionName,
            actionNodeName = actionNodeName
        )
    }


    private fun MemberPointDeductRequest.toPointAttr(): PointAttr {
        return PointAttr(
            traceId = uniqueId!!,
            uniqueId = uniqueId!!,
            changeMode = changeMode ?: Constants.DEFAULT_CHANGE_MODE,
            channel = channelType ?: Constants.DEFAULT_CHANNEL_TYPE,
            operatorId = Constants.DEFAULT_OPERATOR,
            operator = Constants.DEFAULT_OPERATOR,
            shopId = shopId,
            kzzd1 = kzzd1,
            kzzd2 = kzzd2,
            kzzd3 = kzzd3,
            desc = desc,
            actionId = actionId,
            actionNodeId = actionNodeId,
            actionName = actionName,
            actionNodeName = actionNodeName,
        )
    }


    private fun MemberPointFreezeRequest.toPointAttr(): PointAttr {
        return PointAttr(
            traceId = businessId!!,
            uniqueId = businessId!!,
            changeMode = changeMode ?: Constants.DEFAULT_CHANGE_MODE,
            channel = channelType ?: Constants.DEFAULT_CHANNEL_TYPE,
            operatorId = Constants.DEFAULT_OPERATOR,
            operator = Constants.DEFAULT_OPERATOR,
            shopId = shopId,
            kzzd1 = kzzd1,
            kzzd2 = kzzd2,
            kzzd3 = kzzd3,
            desc = desc,
            actionId = actionId,
            actionNodeId = actionNodeId,
            actionName = actionName,
            actionNodeName = actionNodeName,
        )
    }


    private fun MemberPointUnfreezeRequest.toPointAttr(uniqueId: String): PointAttr {
        return PointAttr(
            traceId = businessId!!,
            uniqueId = uniqueId,
            changeMode = changeMode ?: Constants.DEFAULT_CHANGE_MODE,
            channel = channelType ?: Constants.DEFAULT_CHANNEL_TYPE,
            operatorId = Constants.DEFAULT_OPERATOR,
            operator = Constants.DEFAULT_OPERATOR,
            shopId = shopId,
            kzzd1 = kzzd1,
            kzzd2 = kzzd2,
            kzzd3 = kzzd3,
            desc = desc,
            actionId = actionId,
            actionNodeId = actionNodeId,
            actionName = actionName,
            actionNodeName = actionNodeName,
        )
    }


    private fun MemberPointUseFrozenRequest.toPointAttr(uniqueId: String): PointAttr {
        return PointAttr(
            traceId = businessId!!,
            uniqueId = uniqueId,
            changeMode = changeMode ?: Constants.DEFAULT_CHANGE_MODE,
            channel = channelType ?: Constants.DEFAULT_CHANNEL_TYPE,
            operatorId = Constants.DEFAULT_OPERATOR,
            operator = Constants.DEFAULT_OPERATOR,
            shopId = shopId,
            kzzd1 = kzzd1,
            kzzd2 = kzzd2,
            kzzd3 = kzzd3,
            desc = desc,
            actionId = actionId,
            actionNodeId = actionNodeId,
            actionName = actionName,
            actionNodeName = actionNodeName,
        )
    }


    private fun MemberPointRevertRequest.toPointAttr(sourceId: String, transactionRecordType: String): PointAttr {
        val attr = PointAttr(
            traceId = tradeId!!,
            uniqueId = uniqueId!!,
            changeMode = changeMode ?: Constants.DEFAULT_CHANGE_MODE,
            channel = channelType ?: Constants.DEFAULT_CHANNEL_TYPE,
            operatorId = Constants.DEFAULT_OPERATOR,
            operator = Constants.DEFAULT_OPERATOR,
            shopId = shopId,
            kzzd1 = kzzd1,
            kzzd2 = kzzd2,
            kzzd3 = kzzd3,
            desc = desc,
            actionId = actionId,
            actionNodeId = actionNodeId,
            actionName = actionName,
            actionNodeName = actionNodeName,
            sourceId = sourceId
        )
        attr.recordDetail = when (recordType!!) {
            ReversePointEvent.SEND -> "撤销发放的积分"
            ReversePointEvent.DEDUCT -> "撤销扣减的积分"
            ReversePointEvent.USE_FREEZE -> "撤销使用已冻结的积分"
        }
        attr.reverseType = this.recordType!!.name
        if (transactionRecordType == "DELAY_SEND") {
            attr.pendingPoints = true
        }
        return attr
    }


    // 积分发放
    suspend fun sendMemberPoint(
        accountType: PointAccountType,
        memberPoint: MemberPoint?,
        req: MemberPointSendRequest,
        point: BigDecimal,
        now: ZonedDateTime,
        tx: Boolean = false
    ): MemberPointSendResponse = withTransaction { sdk ->
        UniqueRecordRepository.insert(sdk, req.uniqueId!!)
        val isDelay = req.effectiveDate!!.isAfter(now)
        val requestType = if (tx) LoyaltyRequestType.TX_API else LoyaltyRequestType.API
        val attr = req.toPointAttr().apply {
            if (tx) {
                businessId = Uuid.uuid
                traceId = req.triggerId!!
            }
        }
        val res = if (memberPoint == null) {
            // 如果积分账户不存在，则自动创建积分账户
            val initResult = initMemberPoint(
                sdk,
                req.pointAccountId!!,
                req.memberId!!,
                point,
                now,
                req.overdueDate,
                isDelay,
            )
            // 保存积分明细处理任务
            PointJourneyService.save(
                sdk,
                accountType,
                initResult.memberPoint,
                beforeTotalPoints = BigDecimal.ZERO,
                afterTotalPoints = if (isDelay) BigDecimal.ZERO else initResult.actualPoint,
                pointValue = req.point!!,
                afterPoints = initResult.actualPoint,
                action = PointAction.SEND,
                pointAttr = attr,
                now = now,
                sendLimitResults = initResult.sendLimitResult?.let { listOf(it) },
                requestType = requestType
            )
            MemberPointSendResponse().apply {
                this.memberPointId = initResult.memberPoint.id
                this.point = req.point!!
                this.realPoint = initResult.actualPoint
                this.type = if (isDelay) PointSendType.DELAY_SEND else PointSendType.SEND
            }
        } else {
            // 如果积分账户已存在，则更新积分账户
            val beforeTotalPoints = memberPoint.point

            val checkLimitResult = if (!isDelay) checkSendLimit(sdk, memberPoint, point, now) else null
            val afterPoints = checkLimitResult?.afterPoints ?: point

            if (!isDelay) {
                PointSegmentService.addPointSegment(sdk, memberPoint, afterPoints, req.overdueDate!!.shDate(), now)
            }

            memberPoint.point += if (isDelay) BigDecimal.ZERO else afterPoints
            MemberPointRepository.updateMemberPointAndLog(sdk, memberPoint, isDelay)

            val result = SendMemberPointResult(memberPoint.copy(), afterPoints, checkLimitResult)

            // 保存积分明细处理任务
            PointJourneyService.save(
                sdk,
                accountType,
                result.memberPoint,
                beforeTotalPoints = beforeTotalPoints,
                afterTotalPoints = result.memberPoint.point,
                pointValue = req.point!!,
                afterPoints = result.actualPoint,
                action = PointAction.SEND,
                pointAttr = attr,
                now = now,
                sendLimitResults = result.sendLimitResult?.let { listOf(it) },
                requestType = requestType
            )
            MemberPointSendResponse().apply {
                this.memberPointId = result.memberPoint.id
                this.point = req.point!!
                this.realPoint = result.actualPoint
                this.type = if (isDelay) PointSendType.DELAY_SEND else PointSendType.SEND
            }
        }

        if (tx) {
            val pt = PointTransaction(
                id = Uuid.uuid,
                businessId = req.triggerId!!,
                memberId = req.memberId!!,
                point = req.point!!,
                created = now,
                modified = now,
                recordType = PointSendType.SEND.name
            )

            val ptc = PointTransactionCalc(
                id = Uuid.uuid,
                businessId = req.triggerId,
                gainStatementId = attr.businessId,
                effectiveDate = req.effectiveDate,
                overdueDate = req.overdueDate,
                point = req.point!!,
                sourceId = pt.id,
                sort = 0,
                created = now,
                modified = now,
                recordId = attr.businessId,
                shopId = req.shopId,
                backId = attr.businessId,
                ext1 = req.kzzd1,
                ext2 = req.kzzd2,
                ext3 = req.kzzd3
            )
            val pts = PointTransactionStatement(
                id = attr.businessId,
                pointTransactionId = pt.id,
                gainStatementId = attr.businessId,
                point = req.point!!,
                effectiveDate = req.effectiveDate,
                overdueDate = req.overdueDate,
                sort = 0,
                shopId = req.shopId,
                backId = attr.businessId,
                ext1 = req.kzzd1,
                ext2 = req.kzzd2,
                ext3 = req.kzzd3,
                created = now,
                modified = now
            )
            PointTransactionRepository.insertPointTransaction(sdk, req.pointAccountId!!, pt)
            PointTransactionRepository.insertPointTransactionCalc(sdk, req.pointAccountId!!, ptc)
            PointTransactionRepository.insertPointTransactionStatement(sdk, req.pointAccountId!!, pts)
        }

        res
    }


    // 积分扣减
    suspend fun deductMemberPoint(req: MemberPointDeductRequest, pointValue: BigDecimal, now: ZonedDateTime, allowNegative: Boolean, tx: Boolean?): MemberPoint = withTransaction { sdk ->
        UniqueRecordRepository.insert(sdk, req.uniqueId!!)
        val memberPoint = MemberPointService.findMemberPoint(sdk, req.pointAccountId!!, req.memberId!!) ?: throw PointAccountNotFoundException()
        val requestType = if (tx == true) LoyaltyRequestType.TX_API else LoyaltyRequestType.API
        val beforeTotalPoints = memberPoint.point
        val afterTotalPoints = memberPoint.point - pointValue
        if (afterTotalPoints < BigDecimal.ZERO && !allowNegative) {
            LOGGER.warn("积分余额不足，剩余积分值：{} 扣减值：{}", memberPoint.point.toPlainString(), pointValue.toPlainString())
            throw PointInsufficientException()
        }

        val leftSegmentDate = PointSegmentService.subtractPointSegment(sdk, memberPoint, pointValue, now)

        memberPoint.point = afterTotalPoints
        MemberPointRepository.updateMemberPointAndLog(sdk, memberPoint)

        PointJourneyService.save(
            sdk,
            ProgramService.getPointAccountTypeById(req.pointAccountId!!)!!,
            memberPoint,
            beforeTotalPoints = beforeTotalPoints,
            afterTotalPoints = afterTotalPoints,
            pointValue = (beforeTotalPoints - afterTotalPoints).abs(),
            action = PointAction.DEDUCT,
            pointAttr = req.toPointAttr().apply {
                if (requestType == LoyaltyRequestType.TX_API) {
                    traceId = req.triggerId!!
                }
            },
            now = now,
            leftSegmentDate = leftSegmentDate,
            requestType = requestType
        )

        if (tx == true) {
            val pt = PointTransaction(
                id = Uuid.uuid,
                businessId = req.triggerId!!,
                memberId = req.memberId!!,
                point = pointValue,
                created = now,
                modified = now,
                recordType = "DEDUCT"
            )
            PointTransactionRepository.insertPointTransaction(sdk, req.pointAccountId!!, pt)
        }

        memberPoint
    }


    // 积分冻结
    suspend fun freezeMemberPoint(req: MemberPointFreezeRequest, pointValue: BigDecimal, now: ZonedDateTime): MemberPoint = withTransaction { sdk ->
        UniqueRecordRepository.insert(sdk, req.businessId!!)
        val memberPoint = MemberPointService.findMemberPoint(sdk, req.pointAccountId!!, req.memberId!!) ?: throw PointAccountNotFoundException()

        val beforeTotalPoints = memberPoint.point

        val afterTotalPoints = memberPoint.point - pointValue
        if (afterTotalPoints < BigDecimal.ZERO) {
            LOGGER.warn("积分余额不足，剩余积分值：{} 冻结值：{}", memberPoint.point, pointValue)
            throw PointInsufficientException("积分冻结失败，可用余额不足")
        }

        val leftSegmentDate = PointSegmentService.subtractPointSegment(sdk, memberPoint, pointValue, now)

        memberPoint.point = afterTotalPoints
        MemberPointRepository.updateMemberPointAndLog(sdk, memberPoint)

        PointJourneyService.save(
            sdk,
            ProgramService.getPointAccountTypeById(req.pointAccountId!!)!!,
            memberPoint,
            beforeTotalPoints = beforeTotalPoints,
            afterTotalPoints = afterTotalPoints,
            pointValue = (beforeTotalPoints - afterTotalPoints).abs(),
            action = PointAction.FREEZE,
            pointAttr = req.toPointAttr(),
            now = now,
            leftSegmentDate = leftSegmentDate
        )
        memberPoint
    }


    // 积分解冻
    suspend fun unfreezeMemberPoint(req: MemberPointUnfreezeRequest, now: ZonedDateTime): MemberPointUnfreezeResponse = withTransaction  { sdk ->
        val uniqueId = "OPEN_UNFREEZE-${req.businessId!!}"
        UniqueRecordRepository.insert(sdk, uniqueId)
        val memberPoint = MemberPointService.findMemberPoint(sdk, req.pointAccountId!!, req.memberId!!) ?: throw PointAccountNotFoundException()
        PointJourneyService.save(
            sdk,
            ProgramService.getPointAccountTypeById(req.pointAccountId!!)!!,
            memberPoint,
            beforeTotalPoints = memberPoint.point,
            afterTotalPoints = memberPoint.point,
            pointValue = BigDecimal.ZERO,
            action = PointAction.UNFREEZE,
            pointAttr = req.toPointAttr(uniqueId = uniqueId),
            now = now,
        )

        MemberPointUnfreezeResponse().apply {
            this.memberPointId = memberPoint.id
            this.requestId = uniqueId
        }
    }


    // 积分解冻并使用
    suspend fun useFrozenMemberPoint(req: MemberPointUseFrozenRequest, now: ZonedDateTime, tx: Boolean?): MemberPointUseFrozenResponse = withTransaction { sdk ->
        val uniqueId = "OPEN_UNFREEZE-${req.businessId!!}"
        UniqueRecordRepository.insert(sdk, uniqueId)
        val memberPoint = MemberPointService.findMemberPoint(sdk, req.pointAccountId!!, req.memberId!!) ?: throw PointAccountNotFoundException()
        val frozenPoints = MemberPointService.findFrozenPoints(sdk, memberPoint, req.businessId!!)
        val requestType = if (tx == true) LoyaltyRequestType.TX_API else LoyaltyRequestType.API
        val attr = req.toPointAttr(uniqueId = uniqueId)
        if (tx == true) {
            val pt = PointTransaction(
                id = Uuid.uuid,
                businessId = req.businessId!!,
                memberId = req.memberId!!,
                point = frozenPoints,
                created = now,
                modified = now,
                recordType = "DEDUCT"
            )
            PointTransactionRepository.insertPointTransaction(sdk, req.pointAccountId!!, pt)
            attr.backId = pt.id
        }
        PointJourneyService.save(
            sdk,
            ProgramService.getPointAccountTypeById(req.pointAccountId!!)!!,
            memberPoint,
            beforeTotalPoints = memberPoint.point,
            afterTotalPoints = memberPoint.point,
            pointValue = frozenPoints,
            action = PointAction.USE_FROZEN,
            pointAttr = attr,
            now = now,
            afterPoints = BigDecimal.ZERO,
            leftSegmentDate = now.shDate(),
            requestType = requestType
        )
        MemberPointUseFrozenResponse().apply {
            this.memberPointId = memberPoint.id
            this.point = frozenPoints
        }
    }


    // 积分撤销操作
    suspend fun reverseMemberPoint(req: MemberPointRevertRequest, now: ZonedDateTime): MemberPoint = withTransaction { sdk ->
        UniqueRecordRepository.insert(sdk, req.uniqueId!!)
        val pointAccountType = ProgramService.getPointAccountTypeById(req.pointAccountId!!)!!
        val memberPoint = MemberPointService.findMemberPoint(sdk, req.pointAccountId!!, req.memberId!!, now.shDate())!!
        val recordTypes = if (req.recordType == ReversePointEvent.SEND) listOf(ReversePointEvent.SEND.name, "DELAY_SEND") else listOf(ReversePointEvent.DEDUCT.name)
        val pt = PointTransactionRepository.selectPointTransaction(
            sdk,
            req.pointAccountId!!,
            req.memberId!!,
            req.tradeId!!,
            recordTypes
        )
        if (pt == null) {
            throw PointTransactionNotFoundException()
        }
        if ((req.point != null && req.point!! > pt.point) || pt.point.compareTo(BigDecimal.ZERO) == 0) {
            throw PointReverseNotEnoughException()
        }
        val p = if (req.point == null || req.point!!.compareTo(BigDecimal.ZERO) == 0) pt.point else req.point!!

        val attr = req.toPointAttr(pt.id!!, pt.recordType!!)
        val action = when (req.recordType!!.name) {
            "SEND" -> {
                // 撤销已经发放的积分
                if (pointAccountType.negativeStrategy == NegativeStrategy.NOT_ALLOWED.name)  {
                    LOGGER.warn("积分设置不允许扣减，撤销失败")
                    throw PointDeductNotAllowedException()
                }
                PointAction.REVERSE_DEDUCT
            }
            "DEDUCT","USE_FREEZE" -> REVERSE_SEND
            else -> throw BadParamRequestException("无效的撤销类型")
        }
        PointJourneyService.save(
            sdk,
            pointAccountType,
            memberPoint,
            beforeTotalPoints = memberPoint.point,
            afterTotalPoints = if (action == REVERSE_SEND) memberPoint.point + p else memberPoint.point - p,
            pointValue = p,
            action = action,
            pointAttr = attr,
            now = now,
            requestType = LoyaltyRequestType.TX_API
        )
        memberPoint
    }


    // 作废扣减所有剩余积分
    suspend fun deductAll(pointAccountType: PointAccountType, memberPoint: MemberPoint, pointAttr: PointAttr, now: ZonedDateTime) {
        if (memberPoint.point <= BigDecimal.ZERO) {
            return
        }
        withTransaction {
            UniqueRecordRepository.insert(it, pointAttr.uniqueId)
            SegmentRepository.deleteByMemberPointId(it, memberPoint.pointPlanId, memberPoint.id)
            val beforePoint = memberPoint.point
            memberPoint.point = BigDecimal.ZERO
            MemberPointRepository.updateMemberPointAndLog(it, memberPoint)
            PointJourneyService.save(
                it,
                pointAccountType,
                memberPoint,
                beforeTotalPoints = beforePoint,
                afterTotalPoints = BigDecimal.ZERO,
                pointValue = beforePoint,
                action = PointAction.INVALID_ALL,
                pointAttr = pointAttr,
                now = now,
            )
        }
        Kafka.sendPointCalc(memberPoint.pointPlanId, memberPoint.id, memberPoint.memberId)
    }


    // 初始化会员积分账户
    fun initMemberPoint(
        sdk: DataapiWebSocketSdk,
        pointAccountTypeId: Long,
        memberId: String,
        initPointValue: BigDecimal = BigDecimal.ZERO,
        now: ZonedDateTime = ZonedDateTime.now(),
        overdueDate: ZonedDateTime? = null,
        isDelay: Boolean = false,
    ): InitMemberPointResult {

        val accountType = ProgramService.getPointAccountTypeById(pointAccountTypeId)!!

        val memberPoint = MemberPoint(
            id = Uuid.uuid,
            planId = accountType.plan.id,
            pointPlanId = pointAccountTypeId,
            memberId = memberId,
            subjectFqn = accountType.subject.fqn,
            point = BigDecimal.ZERO,
            version = 0,
            openSegmentFlag = true
        )
        var afterPoints = initPointValue
        var sendLimitResult: PointLimitResult? = null
        if (initPointValue > BigDecimal.ZERO && !isDelay) {
            sendLimitResult = checkSendLimit(sdk, memberPoint, initPointValue, now)
            afterPoints = sendLimitResult?.afterPoints ?: initPointValue
        }
        memberPoint.point = if (isDelay) BigDecimal.ZERO else afterPoints
        if (memberPoint.point > BigDecimal.ZERO) {
            // 保存积分段
            SegmentRepository.insertPointSegment(
                sdk,
                Uuid.uuid,
                memberPoint.planId,
                memberPoint.pointPlanId,
                memberPoint.id,
                memberPoint.memberId,
                memberPoint.subjectFqn,
                memberPoint.point,
                overdueDate!!.shDate()
            )
            // 保存总积分变更日志
            MemberPointRepository.insertPointLog(sdk, memberPoint)
        }
        MemberPointRepository.insertMemberPoint(sdk, memberPoint)

        return InitMemberPointResult(memberPoint.copy(point = BigDecimal.ZERO), afterPoints, sendLimitResult)
    }


    // 批量积分变更
    suspend fun batchModifyMemberPoint(pointRecords: List<PointRecord>, batchLog: BatchModifyLog, interfaceRecord: InterfaceRecord) {
        withTransaction {
            UniqueRecordRepository.insert(it, interfaceRecord)
            Jdbc.withTransaction { conn -> ModifyLogRepository.insert(conn, batchLog) }
        }
        for (pointRecord in pointRecords) {
            val message = mapOf(
                "memberPointRecord" to pointRecord,
                "traceId" to pointRecord.traceId,
                "occurrenceTs" to System.currentTimeMillis().toString(),
                "batchId" to batchLog.id
            )
            Kafka.sendPointModify(pointRecord.memberId!!, Json.toJson(message)) {
                LOGGER.error("批量积分变更消息失败: {}", Json.toJson(message), it)
            }
        }
    }


    private fun checkSendLimit(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, pointValue: BigDecimal, now: ZonedDateTime): PointLimitResult? {
        val result = PointLimitService.filter(sdk, memberPoint, pointValue, now)
        if (result != null && result.limitRuleType != PointLimitRuleType.NONE) {
            if (result.afterPoints <= BigDecimal.ZERO) {
                LOGGER.warn("全部积分被发放上限拦截 memberId: {} limit: {}", memberPoint.memberId, Json.toJson(result))
                throw PointUpperLimitException("会员积分超过发放上限: ${result.limitRuleType.desc}")
            } else {
                LOGGER.info("部分积分被发放上限拦截 memberId: {} limit: {}", memberPoint.memberId, Json.toJson(result))
            }
        }
        return result
    }

}