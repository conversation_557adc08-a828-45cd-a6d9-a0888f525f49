package com.shuyun.loyalty.facade.remote.api

import com.shuyun.lite.client.DiscoveryClientHolder
import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.sdk.Property
import io.ktor.client.call.*
import io.ktor.client.request.*
import io.ktor.http.*
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter


object BlackListClient {

    private const val SERVICE_NAME = "risk-control"
    private const val SERVICE_VERSION = "v1"
    private const val ENABLE_DISCOVERY_KEY = "system.discovery.enable"
    private val FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
    suspend fun check(request: CheckRequest): Boolean {
        val useDiscovery = Property.getSysOrEnv(ENABLE_DISCOVERY_KEY, true)
        val (protocol, host, port) = ApiGateway.server(SERVICE_NAME, SERVICE_VERSION, useDiscovery = useDiscovery)
        val address = "$host:$port"
        val st = System.currentTimeMillis()
        val response = try {
            val res = ApiGateway.client.post {
                val requestPath = "/api/checklist/checkExist4Cache"
                url {
                    this. protocol = URLProtocol.createOrDefault(protocol)
                    this. host = host
                    this. port = port
                    path("$SERVICE_NAME/$SERVICE_VERSION$requestPath")
                }
                headers {
                    val timestamp = FORMATTER.format(LocalDateTime.now())
                    val callerService = GlobalContext.serviceName()
                    val callerSign = ApiGateway.sign(
                        callerService,
                        SERVICE_NAME,
                        SERVICE_VERSION,
                        requestPath,
                        timestamp
                    )
                    append("X-Caller-Service", callerService)
                    append("X-Caller-Timestamp", timestamp)
                    append("X-Caller-Key", callerService)
                    append("X-Caller-Sign", callerSign)
                    append("X-Tenant-Id", GlobalContext.defTenantId())
                }
                contentType(ContentType.Application.Json)
                setBody(request)
            }
            if (useDiscovery) DiscoveryClientHolder.trace(address, null, System.currentTimeMillis() - st)
            res
        } catch (e: Throwable) {
            if (useDiscovery) DiscoveryClientHolder.trace(address, e, System.currentTimeMillis() - st)
            throw e
        }
        val list = response.body<List<String>?>()
        return list?.isNotEmpty() == true
    }
}

enum class ChecklistType {
    BLACK,  // 客户黑名单
    WHITE,  // 客户白名单
    MOBILE, // 手机号
    EMAIL;  // 邮件地址
}

data class CheckRequest(val groupIds: String, val customers: List<String>, val checklistType : ChecklistType = ChecklistType.BLACK)
