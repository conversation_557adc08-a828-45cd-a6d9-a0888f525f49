package com.shuyun.loyalty.facade.common

import com.shuyun.loyalty.facade.common.Redis.reactiveClient
import com.shuyun.loyalty.facade.model.RequireMemberLockException
import com.shuyun.loyalty.facade.plugins.getReqId
import com.shuyun.loyalty.sdk.Property
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.future.await
import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.withContext
import org.redisson.Redisson
import org.redisson.api.RedissonClient
import org.redisson.api.RedissonReactiveClient
import org.redisson.config.Config
import org.redisson.config.SslVerificationMode
import java.time.Duration
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.common.Redis.kt")

object Redis {

    private lateinit var client: RedissonClient
    lateinit var reactiveClient: RedissonReactiveClient

    private val started = AtomicBoolean(false)

    fun init() {
        if (!started.compareAndSet(false, true)) {
            return
        }
        LOGGER.info("初始化Redis...")
        val useSSL = Property.getSysOrEnv("redis.ssl", Property.getSysOrEnv("spring.redis.ssl", false))
        val address = if (useSSL) "rediss://${Property.getSysOrEnv("redis.address")}" else "redis://${Property.getSysOrEnv("redis.address")}"
        val passwd = Property.getSysOrEnv("redis.password")
        val config = Config().apply { nettyThreads = Property.getSysOrEnv("redis.nettyThreads", 64) }
        config.useSingleServer().apply {
            this.address = address
            this.connectTimeout = Property.getSysOrEnv("redis.connectTimeout", 1000)
            this.timeout = Property.getSysOrEnv("redis.timeout", 1000)
            this.connectionMinimumIdleSize = Property.getSysOrEnv("redis.connectionMinimumIdleSize", 8)
            this.subscriptionConnectionPoolSize = Property.getSysOrEnv("redis.subscriptionConnectionPoolSize", 16)
            this.database = Property.getSysOrEnv("redis.lock.database", 0)
            if (!passwd.isNullOrEmpty()) {
                this.password = passwd
            }
            if (useSSL) {
                this.sslVerificationMode = SslVerificationMode.NONE
            }
        }
        client = Redisson.create(config)
        reactiveClient = client.reactive()
        LOGGER.info("初始化Redis完成")
        Runtime.getRuntime().addShutdownHook(Thread {
            LOGGER.info("停止Redis...")
            client.shutdown()
        })
    }
}


suspend fun <T> RoutingContext.withMemberPointLock(
    pointAccountTypeId: Long, memberId: String,
    waitTime: Duration = Duration.ofSeconds(5),
    leaseTime: Duration = Duration.ofMinutes(1),
    func: (suspend () -> T)
) = withContext(Dispatchers.IO + MDCContext()) {
    val key = "lck_mp_${pointAccountTypeId}_${memberId}"
    withLock(key, waitTime, leaseTime, func)
}


suspend fun <T> RoutingContext.withLock(
    key: String,
    waitTime: Duration = Duration.ofSeconds(5),
    leaseTime: Duration = Duration.ofMinutes(1),
    func: (suspend () -> T)
) = withContext(Dispatchers.IO + MDCContext()) {
    val reqId = call.getReqId()
    val lock = reactiveClient.getLock(key)
    val start = System.currentTimeMillis()
    val x = lock.tryLock(waitTime.toMillis(), leaseTime.toMillis(), TimeUnit.MILLISECONDS, reqId)
        .toFuture()
        .await()
    val end = System.currentTimeMillis()
    if (x != true) {
        throw RequireMemberLockException("获取会员全局操作锁失败 耗时: ${end - start}ms")
    }
    if (end - start > 1000) {
        LOGGER.warn("获取会员全局操作锁耗时过长 耗时: ${end - start}ms")
    }
    LOGGER.info("加锁成功：reqId: {} key: {} 耗时: {}ms", reqId, key, System.currentTimeMillis() - start)
    try {
        func.invoke()
    } finally {
        try {
            val start1 = System.currentTimeMillis()
            if (lock.isHeldByThread(reqId).toFuture().await()) {
                lock.unlock(reqId).toFuture().await()
                LOGGER.info("解锁成功：reqId: {} key: {} 耗时: {}ms", reqId, key, System.currentTimeMillis() - start1)
            }
        } catch (e: Exception) {
            LOGGER.error("解锁失败: key=$key, reqId=$reqId", e)
        }
    }
}

suspend fun <T> RoutingContext.withLock(
    keys: List<String>,
    waitTime: Duration = Duration.ofSeconds(5),
    leaseTime: Duration = Duration.ofMinutes(5),
    func: (suspend () -> T)
) = withContext(Dispatchers.IO + MDCContext()) {
    val reqId = call.getReqId()
    val locks = keys.distinct().sorted().map { reactiveClient.getLock(it) }
    val multiLock = reactiveClient.getMultiLock(*locks.toTypedArray())
    val acquired = multiLock.tryLock(waitTime.toSeconds(), leaseTime.toSeconds(), TimeUnit.SECONDS, reqId)
        .toFuture()
        .await()
    if (!acquired) {
        throw RequireMemberLockException()
    }
    try {
        func.invoke()
    } finally {
        try {
            multiLock.unlock(reqId).toFuture().await()
        } catch (e: Exception) {
            LOGGER.error("多锁解锁失败: keys={}, reqId={}", keys.joinToString(","), reqId, e)
        }
    }
}
