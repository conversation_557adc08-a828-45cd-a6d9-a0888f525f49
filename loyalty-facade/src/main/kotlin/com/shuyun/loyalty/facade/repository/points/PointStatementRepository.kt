package com.shuyun.loyalty.facade.repository.points

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Uuid
import java.math.BigDecimal
import java.time.ZonedDateTime

object PointStatementRepository {


    private const val VALID_COLUMNS = "`id`,`planId`,`pointPlanId`,`memberPointId`,`memberId`,`subjectFqn`,`point`,`channel`,`shopId`,`kzzd1`,`kzzd2`,`kzzd3`,`gainStatementId`,`effectiveDate`,`overdueDate`,`fromStatus`,`modified`,`created`,`openTraceId`"

    @Suppress("UNCHECKED_CAST")
    fun sumPoints(sdk: DataapiWebSocketSdk, hierarchyId: Long, memberPointId: String, fromStatus: String, openTraceId: String): BigDecimal {
        val sql = """
            select sum(point) as point 
            from data.loyalty.member.account.ValidStatement$hierarchyId
            where memberPointId = :memberPointId and fromStatus = :fromStatus and openTraceId = :openTraceId
        """.trimIndent()
        val params = mapOf(
            "memberPointId" to memberPointId,
            "fromStatus" to fromStatus,
            "openTraceId" to openTraceId
        )
        val row = sdk.execute(sql, params).data.firstOrNull() as? Map<String, Any?>
        return row?.get("point")?.let { BigDecimal(it.toString()) } ?: BigDecimal.ZERO
    }


    fun queryValidPoints(sdk: DataapiWebSocketSdk, hierarchyId: Long, memberId: String, overdueDate: ZonedDateTime = ZonedDateTime.now()): List<ValidPointRecord> {
        val pageSize = 3000
        var number = 0
        val map = HashMap<String, ValidPointRecord>()
        while (true) {
            val sql = """
                select $VALID_COLUMNS
                from data.loyalty.member.account.ValidStatement$hierarchyId
                where memberId = :memberId and fromStatus not in (:fromStatus) and (overdueDate > :overdueDate or overdueDate is null)
                order by id
                LIMIT $pageSize OFFSET ${number * pageSize}
            """.trimIndent()

            val params = mapOf(
                "memberId" to memberId,
                "fromStatus" to listOf("OPEN_FROZE", "SPECIAL_FROZE"),
                "overdueDate" to overdueDate.utcStr()
            )

            val data = sdk.execute(sql, params).data
            val records: List<ValidPointRecord> = Json.convert(data)
            for (record in records) {
                map[record.id] = record
            }
            if (records.isEmpty() || records.size < pageSize) {
                break
            }
            number++
        }
        return map.values.toList().sortedBy { it.overdueDate ?: Constants.LONG_TERM_OVERDUE_DATE }

    }


    fun updateValidPointsById(
        sdk: DataapiWebSocketSdk,
        hierarchyId: Long,
        id: String,
        newPoints: BigDecimal
    ) {
        val sql = """
            update data.loyalty.member.account.ValidStatement$hierarchyId
            set point = :newPoints
            where id = :id
        """.trimIndent()
        val params = mapOf(
            "newPoints" to newPoints,
            "id" to id
        )
        sdk.execute(sql, params)
    }


    fun deleteValidPointsById(
        sdk: DataapiWebSocketSdk,
        hierarchyId: Long,
        id: String
    ) {
        val sql = """
            delete from data.loyalty.member.account.ValidStatement$hierarchyId
            where id = :id
        """.trimIndent()
        val params = mapOf(
            "id" to id
        )
        sdk.execute(sql, params)
    }


    fun queryValidPointsExpiredDateBefore(
        sdk: DataapiWebSocketSdk,
        hierarchyId: Long,
        memberId: String,
        overdueDateBefore: ZonedDateTime,
        number: Int,
        pageSize: Int
    ): List<ValidPointRecord> {
        val sql = """
            select $VALID_COLUMNS
            from data.loyalty.member.account.ValidStatement$hierarchyId
            where memberId = :memberId and fromStatus not in (:fromStatus)
            and overdueDate < :overdueDateBefore
            order by created desc
            LIMIT $pageSize OFFSET ${number * pageSize}
        """.trimIndent()

        val params = mapOf(
            "memberId" to memberId,
            "fromStatus" to listOf("OPEN_FROZE", "SPECIAL_FROZE"),
            "overdueDateBefore" to overdueDateBefore.utcStr()
        )

        val data = sdk.execute(sql, params).data
        return Json.convert(data)
    }


    fun deleteValidPoints(sdk: DataapiWebSocketSdk, hierarchyId: Long, memberId: String) {
        val sql = """
            delete from data.loyalty.member.account.ValidStatement$hierarchyId
            where memberId = :memberId
        """.trimIndent()
        val params = mapOf("memberId" to memberId)
        sdk.execute(sql, params)
    }


    @Suppress("UNCHECKED_CAST")
    fun queryNotYetValidPointsPoints(sdk: DataapiWebSocketSdk, pointAccountId: Long, memberId: String, date: ZonedDateTime): BigDecimal {
        val sql = """
            select sum(g.point) as point from data.loyalty.member.account.GainStatement$pointAccountId as g
            where g.status = 'DELAY_SEND' and g.memberId = :memberId and g.effectiveDate > :date
        """.trimIndent()
        val params = mapOf(
            "memberId" to memberId,
            "date" to date.utcStr()
        )
        val row = sdk.execute(sql, params).data.firstOrNull() as? Map<String, Any?>
        return row?.get("point")?.let { BigDecimal(it.toString()) } ?: BigDecimal.ZERO
    }


    fun queryNegativePoints(sdk: DataapiWebSocketSdk, pointAccountId: Long, memberId: String): List<NegativePointRecord> {
        val sql = """
            select id, point from data.loyalty.member.account.NegativeStatement$pointAccountId
            where memberId = :memberId
            order by created asc
            limit 10000
        """.trimIndent()
        val params = mapOf("memberId" to memberId)
        val rows = sdk.execute(sql, params).data
        return Json.convert(rows)
    }


    fun insertNegativePoints(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, point: BigDecimal, recordId: String) {
        val sql = """
            INSERT INTO data.loyalty.member.account.NegativeStatement${memberPoint.pointPlanId}
            (id,planId,pointPlanId,memberPointId,memberId,subjectFqn,point,recordId,modified,created,memberPoint)
            VALUES (
              :id,:planId,:pointPlanId,:memberPointId,:memberId,:subjectFqn,:point,:recordId,:modified,:created,:memberPoint  
            )
        """.trimIndent()
        val params = mapOf(
            "id" to Uuid.uuid,
            "planId" to memberPoint.planId,
            "pointPlanId" to memberPoint.pointPlanId,
            "memberPointId" to memberPoint.id,
            "memberId" to memberPoint.memberId,
            "subjectFqn" to memberPoint.subjectFqn,
            "point" to point,
            "recordId" to recordId,
            "modified" to ZonedDateTime.now(),
            "created" to ZonedDateTime.now(),
            "memberPoint" to memberPoint.id
        )
        sdk.execute(sql, params)
    }


    fun deleteNegativePoints(sdk: DataapiWebSocketSdk, pointAccountId: Long, memberId: String) {
        val sql = """
            delete from data.loyalty.member.account.NegativeStatement$pointAccountId
            where memberId = :memberId
        """.trimIndent()
        val params = mapOf("memberId" to memberId)
        sdk.execute(sql, params)
    }


    fun updateNegativePointsById(
        sdk: DataapiWebSocketSdk,
        pointAccountId: Long,
        id: String,
        newPoints: BigDecimal
    ) {
        val sql = """
            update data.loyalty.member.account.NegativeStatement$pointAccountId
            set point = :newPoints
            where id = :id
        """.trimIndent()
        val params = mapOf(
            "newPoints" to newPoints,
            "id" to id
        )
        sdk.execute(sql, params)
    }


    fun deleteNegativePointsById(
        sdk: DataapiWebSocketSdk,
        pointAccountId: Long,
        id: String
    ) {
        val sql = """
            delete from data.loyalty.member.account.NegativeStatement$pointAccountId
            where id = :id
        """.trimIndent()
        val params = mapOf(
            "id" to id
        )
        sdk.execute(sql, params)
    }

}
