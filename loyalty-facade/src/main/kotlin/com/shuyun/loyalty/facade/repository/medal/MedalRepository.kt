package com.shuyun.loyalty.facade.repository.medal

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.MemberMedal
import com.shuyun.loyalty.sdk.utcStr
import com.shuyun.loyalty.sdk.Json

object MedalRepository {

    private const val COLUMNS = " `id`,`planId`,`planName`,`medalHierarchyId`,`medalHierarchyName`,`memberId`,`medalDefinitionId`,`medalDefinitionName`, `effectDate`,`overdueDate`,`subjectFqn`,`version`,`created`,`disabled`"
    private const val COLUMNS_WITH_REF = "${COLUMNS}, `member` "
    //分页查询会员勋章
    fun queryMemberMedals(
        sdk: DataapiWebSocketSdk,
        medalHierarchyId: Long,
        medalDefinitionId: Long? = null,
        memberId: String,
        number: Int = 0,
        pageSize: Int = 20,
        disabled :Boolean?
    ): List<MemberMedal> {
        return queryMemberMedals(sdk,medalHierarchyId, medalDefinitionId, listOfNotNull(memberId), number, pageSize,disabled)
    }

    //分页查询会员勋章
    fun queryMemberMedals(
        sdk: DataapiWebSocketSdk,
        medalHierarchyId: Long,
        medalDefinitionId: Long? = null,
        memberIds: List<String>? = null,
        number: Int = 0,
        pageSize: Int = 20,
        disabled :Boolean?
    ): List<MemberMedal> {
        val sql = """
            SELECT $COLUMNS 
            FROM data.loyalty.member.hierarchy.Medal${medalHierarchyId} 
            WHERE medalHierarchyId = :medalHierarchyId
            ${if (medalDefinitionId != null) " AND medalDefinitionId = :medalDefinitionId" else ""}
            ${if (!memberIds.isNullOrEmpty()) " AND memberId in (:memberIds)" else ""}
            ${if (disabled !=null) " AND disabled = :disabled" else ""}
            ORDER BY memberId ASC
            LIMIT $pageSize OFFSET ${number * pageSize}
        """.trimIndent()
        val params = mapOf(
            "medalHierarchyId" to medalHierarchyId,
            "medalDefinitionId" to medalDefinitionId,
            "memberIds" to memberIds,
            "disabled" to disabled
        )
        val response = sdk.execute(sql, params)
        return Json.convert(response.data)
    }

    /**
     * 根据会员Id查询勋章
     */
    fun queryMedalByMemberId(
        sdk: DataapiWebSocketSdk,
        medalHierarchyId: Long,
        memberId: String,
        disabled:Boolean? = false
    ): List<MemberMedal>? {
        val sql = """
            SELECT $COLUMNS 
            FROM data.loyalty.member.hierarchy.Medal${medalHierarchyId} 
            WHERE medalHierarchyId = :medalHierarchyId
            AND memberId = :memberId
            AND disabled = :disabled
        """.trimIndent()
        val params = mapOf(
            "medalHierarchyId" to medalHierarchyId,
            "memberId" to memberId,
            "disabled" to disabled
        )
        val response = sdk.execute(sql, params)
        return if (response.data.isNullOrEmpty()) null else response.data.map {Json.convert(it)}
    }


    fun queryMedalDefinitionIdByMemberId(
        sdk: DataapiWebSocketSdk,
        medalHierarchyId: Long,
        memberId: String,
        disabled:Boolean? = false
    ): List<Long>? {
       return queryMedalByMemberId(sdk,medalHierarchyId,memberId,disabled)?.map { it.medalDefinitionId!! }
    }


    fun insertMemberMedal(sdk: DataapiWebSocketSdk, memberMedal: MemberMedal) {
        val sql = """
            INSERT INTO data.loyalty.member.hierarchy.Medal${memberMedal.medalHierarchyId} (
                $COLUMNS_WITH_REF
            ) VALUES (
                :id, :planId, :planName, :medalHierarchyId, :medalHierarchyName, :memberId, :medalDefinitionId, :medalDefinitionName, :effectDate, :overdueDate, :subjectFqn, :version, :created,:disabled, :member
            )
        """.trimIndent()
        val params = mapOf(
            "id" to memberMedal.id,
            "planId" to memberMedal.planId,
            "planName" to memberMedal.planName,
            "medalHierarchyId" to memberMedal.medalHierarchyId,
            "medalHierarchyName" to memberMedal.medalHierarchyName,
            "memberId" to memberMedal.memberId,
            "medalDefinitionId" to memberMedal.medalDefinitionId,
            "medalDefinitionName" to memberMedal.medalDefinitionName,
            "effectDate" to memberMedal.effectDate,
            "overdueDate" to memberMedal.overdueDate,
            "subjectFqn" to memberMedal.subjectFqn,
            "version" to memberMedal.version,
            "created" to memberMedal.created,
            "disabled" to memberMedal.disabled,
            "member" to memberMedal.memberId
        )
        sdk.execute(sql, params)
    }

    fun updateMemberMedal(sdk: DataapiWebSocketSdk, memberMedal: MemberMedal) {
        val sql = """
            UPDATE data.loyalty.member.hierarchy.Medal${memberMedal.medalHierarchyId} 
            SET 
                effectDate = :effectDate, 
                overdueDate = :overdueDate, 
                version = :version,
                disabled = :disabled
            WHERE id = :id
        """.trimIndent()
        val params = mapOf(
            "id" to memberMedal.id,
            "effectDate" to memberMedal.effectDate.utcStr(),
            "overdueDate" to memberMedal.overdueDate?.utcStr(),
            "disabled" to memberMedal.disabled
        )
        sdk.execute(sql, params)
    }

   //查询此勋章账号下所有的数据
    fun queryAllMedals(
        sdk: DataapiWebSocketSdk,
        memberId: String?,
        medalHierarchyId: Long,
        medalDefinitionId: Long? = null,
        disabled :Boolean?
    ): List<MemberMedal> {
       val list = mutableListOf<MemberMedal>()
       while (true) {
           val sql = """
            SELECT $COLUMNS 
            FROM data.loyalty.member.hierarchy.Medal${medalHierarchyId} 
            WHERE medalHierarchyId = :medalHierarchyId
            ${if (memberId != null) " AND memberId = :memberId" else ""}
            ${if (medalDefinitionId != null) " AND medalDefinitionId = :medalDefinitionId" else ""}
            ${if (disabled != null) " AND disabled = :disabled" else ""}
            ORDER BY memberId ASC
            LIMIT 3000 OFFSET ${list.size}
        """.trimIndent()
           val params = mapOf(
               "memberId" to memberId,
               "medalHierarchyId" to medalHierarchyId,
               "medalDefinitionId" to medalDefinitionId,
               "disabled" to disabled
           )
           val response = sdk.execute(sql, params)
           if (response.data == null || response.data.size == 0) break
           list.addAll(Json.convert(response.data))
       }

        return list
    }

    fun deleteMemberMedal(sdk: DataapiWebSocketSdk, medalHierarchyId: Long, memberIds: List<String>) {
        if (memberIds.isEmpty()) return
        val sql = """
            DELETE FROM data.loyalty.member.hierarchy.Medal${medalHierarchyId} 
            WHERE memberId in (:memberIds)
        """.trimIndent()
        val params = mapOf(
            "memberIds" to memberIds,
        )
        sdk.execute(sql, params)
    }
}