package com.shuyun.loyalty.facade.routes.grade

import com.shuyun.loyalty.facade.model.BadParamRequestException
import com.shuyun.loyalty.facade.model.GradeRecordNotExistException
import com.shuyun.loyalty.facade.model.QuerySortType
import com.shuyun.loyalty.facade.model.checkISODate
import com.shuyun.loyalty.facade.repository.grade.GradeRecordRepository
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.sdk.api.model.GradeRecordType
import com.shuyun.loyalty.sdk.api.model.Page
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeRecordDetailResponse
import com.shuyun.loyalty.sdk.api.model.http.grade.MemberGradeRecordResponse
import com.shuyun.loyalty.sdk.Dataapi.noneTransaction
import com.shuyun.loyalty.sdk.Json
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import java.time.ZonedDateTime

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.grade.GradeRecordQuery.kt")



fun Route.gradeRecordQuery() {
    // 分页获取会员等级记录
    get(Regex(pattern = "/grade/(?<name>records|recordPage)")) {
        val parameters = call.request.queryParameters
        val name = call.parameters["name"]!!
        val req = MemberGradeRecordRequest(
            gradeHierarchyId = parameters["gradeHierarchyId"]?.toLong(),
            memberId = parameters["memberId"],
            channelType = parameters["channelType"],
            sortType = QuerySortType.from(parameters["sortType"]),
            recordType = parameters["recordType"]?.let { GradeRecordType.valueOf(it) },
            currentGradeId = parameters["currentGradeId"]?.toLong(),
            originalGradeId = parameters["originalGradeId"]?.toLong(),
            startTime = parameters["startTime"],
            endTime = parameters["endTime"],
            number = parameters["number"]?.toInt() ?: 0,
            pageSize = parameters["pageSize"]?.toInt() ?: 20
        ).also { LOGGER.info("分页获取会员等级记录: {}", Json.toJson(it)) }.validate()

        val hierarchy = ProgramService.getGradeHierarchyById(req.gradeHierarchyId!!)!!
        val isPage = name == "recordPage"
        val res = noneTransaction {
            val p = GradeRecordRepository.queryMemberGradeRecord(
                it,
                req.gradeHierarchyId!!,
                req.memberId,
                req.channelType,
                req.sortType?.v,
                req.recordType,
                req.currentGradeId,
                req.originalGradeId,
                req.startTime?.let { x -> ZonedDateTime.parse(x) },
                req.endTime?.let { x -> ZonedDateTime.parse(x) },
                req.number,
                req.pageSize,
                withTotalPages = isPage
            )
            val res = p.content.map { d ->
                val response = Json.convert<MemberGradeRecordResponse>(d)
                response.apply {
                    this.subjectId = hierarchy.subject.id
                    this.subjectName = hierarchy.subject.name
                    this.subjectFqn = hierarchy.subject.fqn
                }
                response
            }
            Page(
                content = res,
                number = p.number,
                size = p.size,
                totalPages = p.totalPages,
                totalElements = p.totalElements
            )
        }
        if (isPage) {
            call.respond(res)
        } else {
            call.respond(res.content)
        }
    }


    // 通过id获取会员等级记录详情
    get("/grade/recordDetail") {
        val parameters = call.request.queryParameters
        val gradeHierarchyId = parameters["gradeHierarchyId"]?.toLong() ?: throw BadParamRequestException("gradeHierarchyId参数为必填")
        val recordId = parameters["recordId"] ?: throw BadParamRequestException("recordId参数为必填")
        LOGGER.info("获取会员等级记录详情: gradeHierarchyId: {}, recordId: {}", gradeHierarchyId, recordId)
        ProgramService.getGradeHierarchyById(gradeHierarchyId) ?: throw BadParamRequestException("gradeHierarchyId参数无效")
        val res = noneTransaction {
            val record = GradeRecordRepository.queryRecordById(it, gradeHierarchyId, recordId)
                ?: throw GradeRecordNotExistException()
            val response = Json.convert<MemberGradeRecordDetailResponse>(record)
            response
        }
        call.respond(res)
    }
}


private data class MemberGradeRecordRequest(
    var gradeHierarchyId: Long?,
    var memberId: String? = null,
    var channelType: String? = null,
    var sortType: QuerySortType? = null,
    var recordType: GradeRecordType? = null,
    var currentGradeId: Long? = null,
    var originalGradeId: Long? = null,
    var startTime: String? = null,
    var endTime: String? = null,
    val number: Int,
    val pageSize: Int
)

private fun MemberGradeRecordRequest.validate(): MemberGradeRecordRequest {
    if (gradeHierarchyId == null) throw BadParamRequestException("gradeHierarchyId参数为必填")
    startTime?.checkISODate("startTime参数格式错误")
    endTime?.checkISODate("endTime参数格式错误")
    ProgramService.getGradeHierarchyById(gradeHierarchyId!!) ?: throw BadParamRequestException("gradeHierarchyId参数无效")
    return this
}



