package com.shuyun.loyalty.facade.repository.grade

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.MemberGradeRecord
import com.shuyun.loyalty.sdk.api.model.GradeRecordType
import com.shuyun.loyalty.sdk.api.model.Page
import com.shuyun.loyalty.sdk.utcStr
import com.shuyun.loyalty.sdk.Json
import java.time.ZonedDateTime

object GradeRecordRepository {

    private const val COLUMNS = " `id`,`planId`,`planName`,`memberGradeId`,`gradeHierarchyId`,`gradeHierarchyName`, `memberId`,`originalGradeId`,`originalGradeName`,`originalEffectDate`,`originalOverdueDate`, `currentGradeId`,`currentGradeName`,`currentEffectDate`,`currentOverdueDate`, `recordType`,`changeWay`,`triggerId`,`traceId`,`recordSourceDetail`,`operator`, `subjectFqn`,`description`,`extraInfo`,`recordDetail`,`created`,`channel`, `eventTypeName`,`ruleId`,`ruleName` "
    private const val COLUMNS_WITH_REF = "${COLUMNS}, `memberGrade` "

    @Suppress("UNCHECKED_CAST")
    fun queryMemberGradeRecord(
        sdk: DataapiWebSocketSdk,
        gradeHierarchyId: Long,
        memberId: String? = null,
        channelType: String? = null,
        sortType: String? = null,
        recordType: GradeRecordType? = null,
        currentGradeId: Long? = null,
        originalGradeId: Long? = null,
        startTime: ZonedDateTime? = null,
        endTime: ZonedDateTime? = null,
        number: Int = 0,
        pageSize: Int = 20,
        withTotalPages: Boolean = false
    ): Page<MemberGradeRecord> {
        val fromWhere = """
            FROM data.loyalty.member.hierarchy.GradeRecord${gradeHierarchyId} 
            WHERE gradeHierarchyId = :gradeHierarchyId
            ${if (memberId != null) " AND memberId = :memberId" else ""}
            ${if (channelType != null) " AND channel = :channelType" else ""}
            ${if (recordType != null) " AND recordType = :recordType" else ""}
            ${if (currentGradeId != null) " AND currentGradeId = :currentGradeId" else ""}
            ${if (originalGradeId != null) " AND originalGradeId = :originalGradeId" else ""}
            ${if (startTime != null) " AND created >= :startTime" else ""}
            ${if (endTime != null) " AND created <= :endTime" else ""}
        """.trimIndent().lines().filter { it.isNotBlank() }.joinToString(" ")
        val pageWhere = fromWhere + " ORDER BY created ${sortType ?: "DESC"} LIMIT $pageSize OFFSET ${number * pageSize}"
        val params = mapOf(
            "gradeHierarchyId" to gradeHierarchyId,
            "memberId" to memberId,
            "channelType" to channelType,
            "recordType" to recordType?.name,
            "currentGradeId" to currentGradeId,
            "originalGradeId" to originalGradeId,
            "startTime" to startTime?.utcStr(),
            "endTime" to endTime?.utcStr()
        )
        val elementCount = if (withTotalPages) {
            val countSql = "SELECT count(*) as count $fromWhere"
            val response = sdk.execute(countSql, params)
            val map = (response.data.firstOrNull() as Map<String, Any?>?) ?: mapOf("count" to 0)
            map["count"].toString().toLong()
        } else null

        val columnSql = "SELECT $COLUMNS $pageWhere"
        val response = sdk.execute(columnSql, params)
        val records: List<MemberGradeRecord> = Json.convert(response.data)

        return Page(
            content = records,
            number = number,
            size = pageSize,
            totalPages = elementCount?.let { (it + pageSize - 1) / pageSize },
            totalElements = elementCount
        )
    }


    fun queryRecordById(sdk: DataapiWebSocketSdk, gradeHierarchyId: Long, id: String): MemberGradeRecord? {
        val sql = """
            SELECT $COLUMNS
            FROM data.loyalty.member.hierarchy.GradeRecord${gradeHierarchyId}
            WHERE id = :id
        """.trimIndent()
        val response = sdk.execute(sql, mapOf("id" to id))
        return response.data.firstOrNull()?.let { Json.convert<MemberGradeRecord>(it) }
    }

    fun insertGradeRecord(sdk: DataapiWebSocketSdk, record: MemberGradeRecord) {
        val sql = """
            INSERT INTO data.loyalty.member.hierarchy.GradeRecord${record.gradeHierarchyId} (
                $COLUMNS_WITH_REF
            ) VALUES (
                :id, :planId, :planName, :memberGradeId, :gradeHierarchyId, :gradeHierarchyName, :memberId, :originalGradeId, :originalGradeName, :originalEffectDate, :originalOverdueDate, :currentGradeId, :currentGradeName, :currentEffectDate, :currentOverdueDate, :recordType, :changeWay, :triggerId, :traceId, :recordSourceDetail, :operator, :subjectFqn, :description, :extraInfo, :recordDetail, :created, :channel, :eventTypeName, :ruleId, :ruleName, :memberGrade
            )
        """.trimIndent()
        val params = mapOf(
            "id" to record.id,
            "planId" to record.planId,
            "planName" to record.planName,
            "memberGradeId" to record.memberGradeId,
            "gradeHierarchyId" to record.gradeHierarchyId,
            "gradeHierarchyName" to record.gradeHierarchyName,
            "memberId" to record.memberId,
            "originalGradeId" to record.originalGradeId,
            "originalGradeName" to record.originalGradeName,
            "originalEffectDate" to record.originalEffectDate?.utcStr(),
            "originalOverdueDate" to record.originalOverdueDate?.utcStr(),
            "currentGradeId" to record.currentGradeId,
            "currentGradeName" to record.currentGradeName,
            "currentEffectDate" to record.currentEffectDate?.utcStr(),
            "currentOverdueDate" to record.currentOverdueDate?.utcStr(),
            "recordType" to record.recordType,
            "changeWay" to record.changeWay,
            "triggerId" to record.triggerId,
            "traceId" to record.traceId,
            "recordSourceDetail" to record.recordSourceDetail,
            "operator" to record.operator,
            "subjectFqn" to record.subjectFqn,
            "description" to record.description,
            "extraInfo" to record.extraInfo,
            "recordDetail" to record.recordDetail,
            "created" to record.created,
            "channel" to record.channel,
            "eventTypeName" to record.eventTypeName,
            "ruleId" to record.ruleId,
            "ruleName" to record.ruleName,
            "memberGrade" to record.memberGradeId
        )
        sdk.execute(sql, params)
    }


    fun batchInsertGradeRecord(sdk: DataapiWebSocketSdk, gradeHierarchyId: Long, records: List<MemberGradeRecord>) {
        var sql = "INSERT INTO data.loyalty.member.hierarchy.GradeRecord$gradeHierarchyId ( $COLUMNS_WITH_REF ) VALUES "
        for (record in records) {
            sql += """
                (
                    ${record.id.c()}, 
                    ${record.planId}, 
                    ${record.planName.c()}, 
                    ${record.memberGradeId.c()}, 
                    ${record.gradeHierarchyId}, 
                    ${record.gradeHierarchyName.c()}, 
                    ${record.memberId.c()}, 
                    ${record.originalGradeId.c()}, 
                    ${record.originalGradeName.c()}, 
                    ${record.originalEffectDate.utcStr().c()}, 
                    ${record.originalOverdueDate.utcStr().c()}, 
                    ${record.currentGradeId}, 
                    ${record.currentGradeName.c()}, 
                    ${record.currentEffectDate.utcStr().c()}, 
                    ${record.currentOverdueDate.utcStr().c()}, 
                    ${record.recordType.c()}, 
                    ${record.changeWay.c()}, 
                    ${record.triggerId.c()}, 
                    ${record.traceId.c()}, 
                    ${record.recordSourceDetail.c()}, 
                    ${record.operator.c()}, 
                    ${record.subjectFqn.c()}, 
                    ${record.description.c()}, 
                    ${record.extraInfo.c()}, 
                    ${record.recordDetail.c()}, 
                    ${record.created.utcStr().c()}, 
                    ${record.channel.c()}, 
                    ${record.eventTypeName.c()}, 
                    ${record.ruleId.c()}, 
                    ${record.ruleName.c()}, 
                    ${record.memberGradeId.c()}
                ),
            """.trimIndent()
        }
        sql = sql.removeSuffix(",")
        sdk.execute(sql, emptyMap())
    }


    fun deleteGradeRecordByMemberId(sdk: DataapiWebSocketSdk, gradeHierarchyId: Long, memberId: String) {
        val sql = """
            DELETE FROM data.loyalty.member.hierarchy.GradeRecord$gradeHierarchyId
            WHERE memberId = :memberId
        """.trimIndent()
        sdk.execute(sql, mapOf("memberId" to memberId))
    }

    private fun String?.c(): String {
        return when (this) {
            null -> "NULL"
            else -> "'${this.replace("'", "\\'").replace("\"", "\\\"")}'"
        }
    }

    private fun Number?.c(): Any {
        return when (this) {
            null -> "NULL"
            else -> this
        }
    }
}