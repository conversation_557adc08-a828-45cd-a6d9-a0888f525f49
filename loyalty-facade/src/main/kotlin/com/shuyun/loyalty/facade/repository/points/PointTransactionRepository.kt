package com.shuyun.loyalty.facade.repository.points

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.PointTransaction
import com.shuyun.loyalty.facade.model.PointTransactionCalc
import com.shuyun.loyalty.facade.model.PointTransactionStatement
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.utcStr
import java.math.BigDecimal
import java.time.ZonedDateTime

object PointTransactionRepository {


    fun insertPointTransaction(sdk: DataapiWebSocketSdk, pointAccountTypeId: Long, transaction: PointTransaction) {
        val sql = """
            INSERT INTO data.loyalty.member.account.PointTransaction${pointAccountTypeId} 
            (`id`,`businessId`,`memberId`,`recordType`,`point`,`modified`,`created`)
            VALUES 
            (:id, :businessId, :memberId, :recordType, :point, :modified, :created)
        """.trimIndent()
        val params = mapOf(
            "id" to transaction.id,
            "businessId" to transaction.businessId,
            "memberId" to transaction.memberId,
            "recordType" to transaction.recordType,
            "point" to transaction.point,
            "modified" to transaction.modified.utcStr(),
            "created" to transaction.created.utcStr()
        )
        sdk.execute(sql, params)
    }


    fun deletePointTransactionById(sdk: DataapiWebSocketSdk, pointAccountTypeId: Long, id: String) {
        val sql = """
            DELETE FROM data.loyalty.member.account.PointTransaction$pointAccountTypeId
            WHERE id = :id
        """.trimIndent()
        val params = mapOf("id" to id)
        sdk.execute(sql, params)
    }


    fun updatePointTransactionById(sdk: DataapiWebSocketSdk, pointAccountTypeId: Long, id: String, newPoints: BigDecimal) {
        val sql = """
            UPDATE data.loyalty.member.account.PointTransaction$pointAccountTypeId
            SET point = :newPoints, modified = :modified
            WHERE id = :id
        """.trimIndent()
        val params = mapOf(
            "newPoints" to newPoints,
            "modified" to ZonedDateTime.now().utcStr(),
            "id" to id
        )
        sdk.execute(sql, params)
    }

    fun selectPointTransaction(
        sdk: DataapiWebSocketSdk,
        pointAccountTypeId: Long,
        memberId: String,
        businessId: String,
        recordTypes: List<String>,
    ): PointTransaction? {
        val sql = """
            SELECT `id`,`businessId`,`memberId`,`recordType`,`point`,`modified`,`created` 
            FROM data.loyalty.member.account.PointTransaction$pointAccountTypeId 
            WHERE businessId = :businessId AND memberId = :memberId AND recordType in (:recordTypes)
            LIMIT 1
        """.trimIndent()
        val params = mapOf(
            "businessId" to businessId,
            "memberId" to memberId,
            "recordTypes" to recordTypes
        )
        val data = sdk.execute(sql, params).data.firstOrNull()
        return data?.let { Json.convert(it) }
    }


    fun insertPointTransactionCalc(sdk: DataapiWebSocketSdk, pointAccountTypeId: Long, calc: PointTransactionCalc) {
        val sql = """
            INSERT INTO data.loyalty.member.account.PointTransactionCalc$pointAccountTypeId
            (`id`,`businessId`,`gainStatementId`,`point`,`effectiveDate`,`overdueDate`,`sourceId`,`sort`,`recordId`,`shopId`,`backId`,`ext1`,`ext2`,`ext3`,`modified`,`created`)
            VALUES 
            (:id, :businessId, :gainStatementId, :point, :effectiveDate, :overdueDate, :sourceId, :sort, :recordId, :shopId, :backId, :ext1, :ext2, :ext3, :modified, :created)
        """.trimIndent()
        val params = mapOf(
            "id" to calc.id,
            "businessId" to calc.businessId,
            "gainStatementId" to calc.gainStatementId,
            "point" to calc.point,
            "effectiveDate" to calc.effectiveDate?.utcStr(),
            "overdueDate" to calc.overdueDate?.utcStr(),
            "sourceId" to calc.sourceId,
            "sort" to calc.sort,
            "recordId" to calc.recordId,
            "shopId" to calc.shopId,
            "backId" to calc.backId,
            "ext1" to calc.ext1,
            "ext2" to calc.ext2,
            "ext3" to calc.ext3,
            "modified" to calc.modified.utcStr(),
            "created" to calc.created.utcStr()
        )
        sdk.execute(sql, params)
    }


    fun insertPointTransactionStatement(sdk: DataapiWebSocketSdk, pointAccountTypeId: Long, statement: PointTransactionStatement) {
        val sql = """
            INSERT INTO data.loyalty.member.account.PointTransactionStatement$pointAccountTypeId
            (`id`,`gainStatementId`,`pointTransactionId`,`point`,`effectiveDate`,`overdueDate`,`sort`,`shopId`,`backId`,`ext1`,`ext2`,`ext3`,`modified`,`created`)
            VALUES 
            (:id, :gainStatementId, :pointTransactionId, :point, :effectiveDate, :overdueDate, :sort, :shopId, :backId, :ext1, :ext2, :ext3, :modified, :created)
        """.trimIndent()
        val params = mapOf(
            "id" to statement.id,
            "gainStatementId" to statement.gainStatementId,
            "pointTransactionId" to statement.pointTransactionId,
            "point" to statement.point,
            "effectiveDate" to statement.effectiveDate?.utcStr(),
            "overdueDate" to statement.overdueDate?.utcStr(),
            "sort" to statement.sort,
            "shopId" to statement.shopId,
            "backId" to statement.backId,
            "ext1" to statement.ext1,
            "ext2" to statement.ext2,
            "ext3" to statement.ext3,
            "modified" to statement.modified.utcStr(),
            "created" to statement.created.utcStr()
        )
        sdk.execute(sql, params)
    }
}