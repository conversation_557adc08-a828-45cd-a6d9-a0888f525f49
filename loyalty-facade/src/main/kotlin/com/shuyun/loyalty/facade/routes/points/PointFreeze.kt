package com.shuyun.loyalty.facade.routes.points

import com.shuyun.loyalty.facade.common.Kafka
import com.shuyun.loyalty.facade.common.withMemberPointLock
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.service.BlockListService
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.points.PointModifyService
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointFreezeRequest
import com.shuyun.loyalty.sdk.Json
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import java.math.BigDecimal
import java.time.Duration
import java.time.ZonedDateTime

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.points.PointFreeze.kt")


fun Route.pointFreeze() {
    post(Regex("/point:frozen")) {
        val req = call.receive<MemberPointFreezeRequest>().also {
            LOGGER.info("冻结会员积分: {}", Json.toJson(it))
        }.validate()
        val point = req.point!!.scale(req.pointAccountId!!)
        val pointAccountType = ProgramService.getPointAccountTypeById(req.pointAccountId!!)!!
        if (pointAccountType.negativeStrategy == NegativeStrategy.NOT_ALLOWED.name) {
            throw PointFreezeNotAllowedException()
        }
        // 判断会员是否在会员表中存在 和 检查特殊名单
        BlockListService.check(
            TypeEnum.POINT,
            pointAccountType.subject.fqn,
            pointAccountType.id,
            req.memberId!!,
            ForbiddenOperation.POINT_FREEZE
        )

        val now = ZonedDateTime.now()
        val timeout = req.lockWaitTime ?: Constants.DEFAULT_WAIT_TIME
        withMemberPointLock(req.pointAccountId!!, req.memberId!!, Duration.ofMillis(timeout)) {
            val memberPoint = PointModifyService.freezeMemberPoint(req, point, now)
            Kafka.sendPointCalc(memberPoint.pointPlanId, memberPoint.id, memberPoint.memberId)
        }
        call.respond(HttpStatusCode.Accepted)
    }
}


private fun MemberPointFreezeRequest.validate(): MemberPointFreezeRequest {
    ProgramService.basicValidate(pointAccountId, memberId, channelType, changeMode, businessId)
    if (point == null || point!! <= BigDecimal.ZERO) throw BadParamRequestException("积分值必须大于0")
    return this
}


