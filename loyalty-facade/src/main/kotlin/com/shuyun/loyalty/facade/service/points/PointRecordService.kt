package com.shuyun.loyalty.facade.service.points

import com.shuyun.loyalty.facade.model.PointRecord
import com.shuyun.loyalty.facade.model.PointRecordItem
import com.shuyun.loyalty.facade.repository.points.PointRecordRepository
import com.shuyun.loyalty.sdk.api.model.Page
import com.shuyun.loyalty.sdk.Dataapi.noneTransaction
import java.time.ZonedDateTime

object PointRecordService {

    suspend fun queryPointRecord(
        pointAccountId: Long,
        recordTypes: List<String>? = null,
        status: List<String>? = null,
        memberId: String? = null,
        shopId: String? = null,
        channelType: String? = null,
        startTime: ZonedDateTime? = null,
        endTime: ZonedDateTime? = null,
        traceId: String? = null,
        sortType: String? = null,
        changeMode: String? = null,
        recordId: String? = null,
        number: Int = 0,
        pageSize: Int = 20,
    ): List<PointRecord> {
        val page = pagePointRecord(
            pointAccountId = pointAccountId,
            recordTypes = recordTypes,
            status = status,
            memberId = memberId,
            shopId = shopId,
            channelType = channelType,
            startTime = startTime,
            endTime = endTime,
            traceId = traceId,
            sortType = sortType,
            changeMode = changeMode,
            recordId = recordId,
            number = number,
            pageSize = pageSize,
            withTotal = false
        )
        return page.content
    }


    suspend fun pagePointRecord(
        pointAccountId: Long,
        recordTypes: List<String>?,
        status: List<String>?,
        memberId: String?,
        shopId: String?,
        channelType: String?,
        startTime: ZonedDateTime?,
        endTime: ZonedDateTime?,
        traceId: String?,
        sortType: String?,
        changeMode: String?,
        recordId: String? = null,
        number: Int = 0,
        pageSize: Int = 20,
        withTotal: Boolean = false,
        withoutPageContent: Boolean = false,
    ): Page<PointRecord> {
        return noneTransaction {
            val page = PointRecordRepository.queryPointRecord(
                it,
                pointAccountId = pointAccountId,
                recordTypes = recordTypes,
                status = status,
                memberId = memberId,
                shopId = shopId,
                channelType = channelType,
                startTime = startTime,
                endTime = endTime,
                traceId = traceId,
                sortType = sortType,
                changeMode = changeMode,
                recordId = recordId,
                number = number,
                pageSize = pageSize,
                withTotal = withTotal,
                withoutPageContent = withoutPageContent
            )
            page
        }
    }


    suspend fun queryPointRecordItemByTraceId(pointAccountId: Long, traceId: String): List<PointRecordItem> {
        return noneTransaction { PointRecordRepository.queryPointRecordItems(it, pointAccountId, traceId) }
    }
}