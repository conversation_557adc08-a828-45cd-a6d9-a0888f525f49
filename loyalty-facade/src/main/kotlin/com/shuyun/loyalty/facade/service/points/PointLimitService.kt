package com.shuyun.loyalty.facade.service.points

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.repository.points.SendLimitRepository
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.sdk.Json
import java.math.BigDecimal
import java.time.*
import java.time.temporal.TemporalAdjusters

object PointLimitService {


    fun filter(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, point: BigDecimal, now: ZonedDateTime): PointLimitResult? {
        if (point <= BigDecimal.ZERO) {
            return PointLimitResult(
                beforeTotalPoints = memberPoint.point,
                beforePoints = point,
                afterPoints = BigDecimal.ZERO,
                limitRuleType = PointLimitRuleType.NONE,
            )
        }

        var result: PointLimitResult? = null
        val pointAccountType = ProgramService.getPointAccountTypeById(memberPoint.pointPlanId)!!
        if (pointAccountType.singleTopLimit != null) {
            if (point > pointAccountType.singleTopLimit) {
                result = PointLimitResult(
                    beforeTotalPoints = memberPoint.point,
                    beforePoints = point,
                    afterPoints = pointAccountType.singleTopLimit,
                    limitRuleType = PointLimitRuleType.SINGLE,
                    singleTopLimit = pointAccountType.singleTopLimit,
                )
            }
        }

        if (pointAccountType.topLimit != null) {
            val p = result?.afterPoints ?: point
            if (p <= BigDecimal.ZERO) {
                return result
            }
            if (memberPoint.point + p > pointAccountType.topLimit) {
                result = PointLimitResult(
                    beforeTotalPoints = memberPoint.point,
                    beforePoints = point,
                    afterPoints = pointAccountType.topLimit - memberPoint.point,
                    limitRuleType = PointLimitRuleType.TOTAL,
                    topLimit = pointAccountType.topLimit,
                )
            }
        }

        val plan = pointAccountType.plan
        val enabledTimeBasedRule = plan.sendLimitRuleSwitch
        val timeBasedLimitRules = pointAccountType.sendLimitRule?.let {
            val list: List<TimeBasedLimitRule> = Json.parse(it)
            list.sortedBy { x -> x.cycle.ordinal }
        } ?: emptyList()

        val sendLimitCalc = findLimitCalc(sdk, enabledTimeBasedRule, memberPoint)
        var sendLimitCalcValues: MutableMap<String,Number> = mutableMapOf()
        if (sendLimitCalc != null && timeBasedLimitRules.isNotEmpty()) {
            sendLimitCalcValues = sendLimitCalc.value.toMutableMap()
        } else {
            TimeBasedLimitUnit.entries.forEach {
                val dateStr = getStartDateByUnit(now.shDate(), it)
                val pointK = "$dateStr|$it|POINT"
                val countK = "$dateStr|$it|COUNT"
                sendLimitCalcValues[pointK] = BigDecimal.ZERO
                sendLimitCalcValues[countK] = 0
            }
        }
        for (timeBasedLimitRule in timeBasedLimitRules) {
            val p = result?.afterPoints ?: point
            if (p <= BigDecimal.ZERO) {
                break
            }
            val dateStr = getStartDateByUnit(now.shDate(), timeBasedLimitRule.cycle)
            val k = "$dateStr|${timeBasedLimitRule.cycle}|POINT"
            val v = sendLimitCalcValues[k]?.toString()?.toBigDecimal() ?: BigDecimal.ZERO
            if (v + p > timeBasedLimitRule.value) {
                result = PointLimitResult(
                    beforeTotalPoints = memberPoint.point,
                    beforePoints = point,
                    afterPoints = timeBasedLimitRule.value - v,
                    limitRuleType = PointLimitRuleType.TIME_BASED_RULE_POINT_LIMIT,
                    timeBasedRule = timeBasedLimitRule,
                    sendLimitCalc = mapOf(k to v),
                )
                break
            }
        }
        // 通过上限规则过滤后的积分值
        val p = result?.afterPoints ?: point
        if (p > BigDecimal.ZERO) {
            // 累计积分
            val map = HashMap<String,Number>()
            TimeBasedLimitUnit.entries.forEach {
                val dateStr = getStartDateByUnit(now.shDate(), it)
                val pointK = "$dateStr|$it|POINT"
                val countK = "$dateStr|$it|COUNT"
                map[pointK] = (sendLimitCalcValues[pointK]?.toString()?.toBigDecimal() ?: BigDecimal.ZERO) + p
                map[countK] = (sendLimitCalcValues[countK]?.toInt() ?: 0) + 1
            }
            val value = Json.toJson(map)
            if (sendLimitCalc == null) {
                SendLimitRepository.insert(sdk, memberPoint, value)
            } else {
                SendLimitRepository.updateById(sdk, memberPoint.pointPlanId, sendLimitCalc.id, value)
            }
        }
        return result
    }


    private fun findLimitCalc(sdk: DataapiWebSocketSdk, enabledTimeBasedRule: Boolean, memberPoint: MemberPoint): SendLimitCalc? {
        return if (enabledTimeBasedRule) SendLimitRepository.findLimitCalc(sdk, memberPoint.pointPlanId, memberPoint.id) else null
    }


    private fun getStartDateByUnit(now: LocalDate, unit: TimeBasedLimitUnit): String {
        val date = when(unit) {
            TimeBasedLimitUnit.DAY -> now
            TimeBasedLimitUnit.WEEK -> LocalDateTime.of(now.minusDays(now.dayOfWeek.value - 1L), LocalTime.MIN).toLocalDate()
            TimeBasedLimitUnit.MONTH -> LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN).toLocalDate()
            TimeBasedLimitUnit.QUARTER -> LocalDate.of(now.year,now.month.firstMonthOfQuarter(),1)
            TimeBasedLimitUnit.YEAR -> LocalDate.of(now.year, Month.JANUARY, 1)
            TimeBasedLimitUnit.MAX -> "3000-12-31"
        }
        return date.toString().replace("-","")
    }


}


