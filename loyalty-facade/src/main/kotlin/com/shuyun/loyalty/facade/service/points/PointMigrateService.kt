package com.shuyun.loyalty.facade.service.points

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.BasicSegment
import com.shuyun.loyalty.facade.model.Constants.LONG_TERM_OVERDUE_DATE
import com.shuyun.loyalty.facade.model.MemberPoint
import com.shuyun.loyalty.facade.model.shDate
import com.shuyun.loyalty.facade.repository.points.MemberPointRepository
import com.shuyun.loyalty.facade.repository.points.PointStatementRepository
import com.shuyun.loyalty.facade.repository.points.SegmentRepository
import com.shuyun.loyalty.sdk.Uuid
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.time.ZonedDateTime

object PointMigrateService {

    private val logger = LoggerFactory.getLogger(PointMigrateService::class.java)

    /**
     * 重建积分块数据 reference: 0-以当前会员积分总账为准，1-以有效积分记录为准
     */
    fun rebuildSegment(sdk: DataapiWebSocketSdk, pointAccountId: Long, memberId: String, reference: Int = 0): MemberPoint? {

        val memberPoints = MemberPointRepository.selectMemberPoints(sdk, pointAccountId, memberId)
        if (memberPoints.size > 1) {
            throw IllegalStateException("同一个会员ID(${memberId})在(${pointAccountId})中存在多个账户")
        }

        val memberPoint = memberPoints.firstOrNull() ?: return null

        logger.info("重建积分块数据 memberId: {} point: {}", memberPoint.memberId, memberPoint.point)

        SegmentRepository.deleteByMemberId(sdk, memberPoint.pointPlanId, memberPoint.memberId)

        adjustRecords(sdk, memberPoint, reference)

        memberPoint.openSegmentFlag = true

        val segments = mutableListOf<BasicSegment>()
        if (memberPoint.point > BigDecimal.ZERO) {
            val validPoints = PointStatementRepository.queryValidPoints(sdk, memberPoint.pointPlanId, memberPoint.memberId)
            if (memberPoint.point > validPoints.sumOf { it.point } && reference ==0) {
                val p1 = PointStatementRepository
                    .queryValidPoints(sdk, memberPoint.pointPlanId, memberPoint.memberId, overdueDate = ZonedDateTime.now().minusYears(5))
                    .sumOf { it.point }
                if (memberPoint.point.compareTo(p1) != 0) {
                    segments.add(BasicSegment(Uuid.uuid, memberPoint.id, memberPoint.point-validPoints.sumOf { it.point }, LONG_TERM_OVERDUE_DATE.shDate()))
                }
            }
            for (v in validPoints) {
                val date = v.overdueDate?.shDate() ?: LONG_TERM_OVERDUE_DATE.shDate()
                var last: BasicSegment? = null
                for (segment in segments) {
                    if (segment.expireDate.isAfter(date)) {
                        continue
                    }
                    segment.point += v.point
                    last = segment
                }
                if (last == null) {
                    segments.add(BasicSegment(Uuid.uuid, memberPoint.id, v.point, date))
                } else {
                    if (!last.expireDate.isEqual(date)) {
                        segments.add(BasicSegment(Uuid.uuid, memberPoint.id, v.point, date))
                    }
                }
            }
        }
        if (memberPoint.point < BigDecimal.ZERO) {
            segments.add(BasicSegment(Uuid.uuid, memberPoint.id, memberPoint.point, LONG_TERM_OVERDUE_DATE.shDate()))
        }

        SegmentRepository.batchInsert(sdk, memberPoint, segments)
        MemberPointRepository.updateMemberPoint(sdk, memberPoint)
        return memberPoint
    }


    private fun adjustRecords(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, reference: Int) {
        val negativePoints = PointStatementRepository.queryNegativePoints(sdk, memberPoint.pointPlanId, memberPoint.memberId)
        val validPoints = PointStatementRepository.queryValidPoints(sdk, memberPoint.pointPlanId, memberPoint.memberId)

        val sumValidPoints = validPoints.sumOf { x -> x.point }
        val sumNegativePoints = negativePoints.sumOf { x -> x.point }
        val sumPoints = if (reference == 0) memberPoint.point else (sumValidPoints - sumNegativePoints)
        if (sumPoints > BigDecimal.ZERO) {
            PointStatementRepository.deleteNegativePoints(sdk, memberPoint.pointPlanId, memberPoint.memberId)
            if (sumPoints < sumValidPoints) {//40    124
                var p = sumValidPoints - sumPoints
                for (validPoint in validPoints) {
                    if (validPoint.point > p) {
                        validPoint.point -= p
                        PointStatementRepository.updateValidPointsById(sdk, memberPoint.pointPlanId, validPoint.id, validPoint.point)
                        p = BigDecimal.ZERO
                    } else {
                        PointStatementRepository.deleteValidPointsById(sdk, memberPoint.pointPlanId, validPoint.id)
                        p -= validPoint.point
                        validPoint.point = BigDecimal.ZERO
                    }
                    if (p <= BigDecimal.ZERO) {
                        break
                    }
                }
            }
        } else if (sumPoints < BigDecimal.ZERO) {
            PointStatementRepository.deleteValidPoints(sdk, memberPoint.pointPlanId, memberPoint.memberId)
            if (sumPoints.abs() < sumNegativePoints) {
                var p = sumNegativePoints - sumPoints.abs()
                for (negativePoint in negativePoints) {
                    if (negativePoint.point > p) {
                        negativePoint.point -= p
                        PointStatementRepository.updateNegativePointsById(sdk, memberPoint.pointPlanId, negativePoint.id, negativePoint.point)
                        p = BigDecimal.ZERO
                    } else {
                        PointStatementRepository.deleteNegativePointsById(sdk, memberPoint.pointPlanId, negativePoint.id)
                        p -= negativePoint.point
                        negativePoint.point = BigDecimal.ZERO
                    }
                    if (p <= BigDecimal.ZERO) {
                        break
                    }
                }
            }
            if (sumPoints.abs() > sumNegativePoints) {
                val p = sumPoints.abs() - sumNegativePoints
                PointStatementRepository.insertNegativePoints(sdk, memberPoint, p, Uuid.uuid)
            }
        } else {
            PointStatementRepository.deleteNegativePoints(sdk, memberPoint.pointPlanId, memberPoint.memberId)
            PointStatementRepository.deleteValidPoints(sdk, memberPoint.pointPlanId, memberPoint.memberId)
        }

        memberPoint.point = sumPoints
    }

}