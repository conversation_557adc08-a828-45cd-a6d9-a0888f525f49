package com.shuyun.loyalty.facade.repository.points

import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.facade.model.MemberPoint
import com.shuyun.loyalty.facade.model.utcStr
import com.shuyun.loyalty.sdk.Uuid
import java.time.ZonedDateTime

object PointMergeRepository {

    fun updateValidStatement(
        sdk: DataapiWebSocketSdk,
        pointAccountTypeId: Long,
        memberId: String,
        newMemberId: String,
        newMemberPointId: String,
        now: ZonedDateTime = ZonedDateTime.now()
    ) {
        val sql = """
            update data.loyalty.member.account.ValidStatement$pointAccountTypeId
            set memberId = :newMemberId , memberPointId = :newMemberPointId, memberPoint = :newMemberPointId, modified = :modified
            where memberId = :memberId 
        """.trimIndent()
        val params = mapOf(
            "newMemberId" to newMemberId,
            "newMemberPointId" to newMemberPointId,
            "memberId" to memberId,
            "modified" to now.utcStr()
        )
        sdk.execute(sql, params)
    }


    fun updateGainStatement(
        sdk: DataapiWebSocketSdk,
        pointAccountTypeId: Long,
        memberId: String,
        newMemberId: String,
        newMemberPointId: String,
        now: ZonedDateTime = ZonedDateTime.now()
    ) {
        val sql = """
            update data.loyalty.member.account.GainStatement$pointAccountTypeId
            set memberId = :newMemberId, memberPointId = :newMemberPointId, memberPoint = :newMemberPointId, modified = :modified
            where memberId = :memberId
        """.trimIndent()
        val params = mapOf(
            "newMemberId" to newMemberId,
            "newMemberPointId" to newMemberPointId,
            "memberId" to memberId,
            "modified" to now.utcStr()
        )
        sdk.execute(sql, params)
    }


    fun updateNegativeStatement(
        sdk: DataapiWebSocketSdk,
        pointAccountTypeId: Long,
        memberId: String,
        newMemberId: String,
        newMemberPointId: String,
        now: ZonedDateTime = ZonedDateTime.now()
    ) {
        val sql = """
            update data.loyalty.member.account.NegativeStatement$pointAccountTypeId
            set memberId = :newMemberId, memberPointId = :newMemberPointId, memberPoint = :newMemberPointId, modified = :modified
            where memberId = :memberId
        """.trimIndent()
        val params = mapOf(
            "newMemberId" to newMemberId,
            "newMemberPointId" to newMemberPointId,
            "memberId" to memberId,
            "modified" to now.utcStr()
        )
        sdk.execute(sql, params)
    }


    fun updateFrozenPoints(
        sdk: DataapiWebSocketSdk,
        pointAccountTypeId: Long,
        memberId: String,
        newMemberId: String,
        newMemberPointId: String
    ) {
        val sql = """
            update data.loyalty.member.account.FrozenPoint$pointAccountTypeId
            set memberId = :newMemberId, memberPointId = :newMemberPointId, memberPoint = :newMemberPointId
            where memberId = :memberId
        """.trimIndent()
        val params = mapOf(
            "newMemberId" to newMemberId,
            "newMemberPointId" to newMemberPointId,
            "memberId" to memberId
        )
        sdk.execute(sql, params)
    }


    fun updateFrozenStatement(
        sdk: DataapiWebSocketSdk,
        pointAccountTypeId: Long,
        memberId: String,
        newMemberId: String,
        newMemberPointId: String,
        now: ZonedDateTime = ZonedDateTime.now()
    ) {
        val sql = """
            update data.loyalty.member.account.FrozenStatement$pointAccountTypeId
            set memberId = :newMemberId, memberPointId = :newMemberPointId, memberPoint = :newMemberPointId, modified = :modified
            where memberId = :memberId
        """.trimIndent()
        val params = mapOf(
            "newMemberId" to newMemberId,
            "newMemberPointId" to newMemberPointId,
            "memberId" to memberId,
            "modified" to now.utcStr()
        )
        sdk.execute(sql, params)
    }


    fun deletePointLog(
        sdk: DataapiWebSocketSdk,
        pointAccountTypeId: Long,
        memberIds: List<String>
    ) {
        if (memberIds.isEmpty()) return
        val sql = """
            delete from data.loyalty.member.account.point.Log$pointAccountTypeId
            where memberId in (:memberIds)
        """.trimIndent()
        val params = mapOf(
            "memberIds" to memberIds,
        )
        sdk.execute(sql, params)
    }


    private fun batchInsertPointLog(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, data: List<Map<String, Any>>) {
        if (data.isEmpty()) return
        var sql = """
            INSERT INTO data.loyalty.member.account.point.Log${memberPoint.pointPlanId} (
                `id`,`planId`,`pointPlanId`,`memberId`,`subjectFqn`,`point`,`created`, `member`
            ) VALUES 
        """.trimIndent()
        for (d in data) {
            sql += """
                (
                '${Uuid.uuid}',
                ${memberPoint.planId},
                ${memberPoint.pointPlanId},
                '${memberPoint.memberId}',
                '${memberPoint.subjectFqn}',
                ${d["point"]},
                '${d["created"]}',
                '${memberPoint.memberId}'
                ),
            """.trimIndent()
        }
        sql = sql.removeSuffix(",")
        sdk.execute(sql, emptyMap())
    }


    @Suppress("UNCHECKED_CAST")
    fun batchInsertPointLog(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint) {
        val sql = """
            select changePoint, created 
            from data.loyalty.member.account.PointRecord${memberPoint.pointPlanId} 
            where memberId = :memberId
            order by created desc
        """.trimIndent()
        val response = sdk.execute(sql, mapOf("memberId" to memberPoint.memberId))
        val chunked = response.data.chunked(200)
        var p = memberPoint.point
        for (list in chunked) {
            val data = ArrayList<Map<String, Any>>()
            for (m in list) {
                val row = m as Map<String, Any>
                val changePoint = row["changePoint"].toString().toBigDecimal()
                val point = p - changePoint
                p -= changePoint
                data.add(mapOf("point" to point, "created" to row["created"]!!))
            }
            data.add(mapOf("point" to memberPoint.point,"created" to ZonedDateTime.now().utcStr())) //增加当前变更的积分
            batchInsertPointLog(sdk,memberPoint,data)
        }
    }


    fun updatePointRecord(
        sdk: DataapiWebSocketSdk,
        pointAccountTypeId: Long,
        memberId: String,
        newMemberId: String,
        newMemberPointId: String,
        now: ZonedDateTime = ZonedDateTime.now()
    ) {
        val sql = """
            update data.loyalty.member.account.PointRecord$pointAccountTypeId
            set memberId = :newMemberId, memberPointId = :newMemberPointId, memberPoint = :newMemberPointId, modified = :modified
            where memberId = :memberId
        """.trimIndent()
        val params = mapOf(
            "newMemberId" to newMemberId,
            "newMemberPointId" to newMemberPointId,
            "memberId" to memberId,
            "modified" to now.utcStr()
        )
        sdk.execute(sql, params)
    }


    fun updatePointRecordItem(
        sdk: DataapiWebSocketSdk,
        pointAccountTypeId: Long,
        memberId: String,
        newMemberId: String,
    ) {
        val sql = """
            update data.loyalty.member.account.PointRecordItem$pointAccountTypeId
            set memberId = :newMemberId, member = :newMemberId
            where memberId = :memberId
        """.trimIndent()
        val params = mapOf(
            "newMemberId" to newMemberId,
            "memberId" to memberId,
        )
        sdk.execute(sql, params)
    }


    fun updateMemberPointEvent(
        sdk: DataapiWebSocketSdk,
        hierarchyId: Long,
        memberId: String,
        newMemberId: String
    ) {
        val sql = """
            update data.loyalty.member.MemberPointEvent$hierarchyId
            set memberId = :newMemberId
            where memberId = :memberId
        """.trimIndent()
        val params = mapOf(
            "newMemberId" to newMemberId,
            "memberId" to memberId,
        )
        sdk.execute(sql, params)
    }


    fun updateCalculateJournal(
        sdk: DataapiWebSocketSdk,
        hierarchyId: Long,
        memberId: String,
        newMemberId: String,
        newMemberPointId: String,
        now: ZonedDateTime = ZonedDateTime.now()
    ) {
        val sql = """
            update data.loyalty.member.PointCalculateJournal$hierarchyId
            set memberId = :newMemberId, memberPointId = :newMemberPointId, comment = :comment, modified = :modified
            where memberId = :memberId and status = 'COMPLETED'
        """.trimIndent()
        val params = mapOf(
            "newMemberId" to newMemberId,
            "newMemberPointId" to newMemberPointId,
            "comment" to "merge from $memberId",
            "memberId" to memberId,
            "modified" to now.utcStr()
        )
        sdk.execute(sql, params)
    }


    fun deleteMemberPoint(sdk: DataapiWebSocketSdk, pointAccountTypeId: Long, memberIds: List<String>) {
        if (memberIds.isEmpty()) return
        val sql = """
            delete from data.loyalty.member.account.Point$pointAccountTypeId
            where memberId in (:memberIds)
        """.trimIndent()
        val params = mapOf("memberIds" to memberIds)
        sdk.execute(sql, params)
    }


    fun updateMemberPointToZero(sdk: DataapiWebSocketSdk, pointAccountTypeId: Long, memberIds: List<String>) {
        val sql = """
            update data.loyalty.member.account.Point$pointAccountTypeId
            set point = 0
            where memberId in (:memberIds)
        """.trimIndent()
        val params = mapOf(
            "memberIds" to memberIds
        )
        sdk.execute(sql, params)
    }
}