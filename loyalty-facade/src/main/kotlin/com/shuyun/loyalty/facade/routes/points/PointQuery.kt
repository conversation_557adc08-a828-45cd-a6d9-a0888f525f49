package com.shuyun.loyalty.facade.routes.points

import com.shuyun.loyalty.facade.model.BadParamRequestException
import com.shuyun.loyalty.facade.model.PointAccountNotFoundException
import com.shuyun.loyalty.facade.repository.points.PointStatementRepository
import com.shuyun.loyalty.facade.service.MemberService
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.points.MemberPointService
import com.shuyun.loyalty.sdk.api.model.http.points.MemberNotYetValidPointsResponse
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointItemResponse
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointResponse
import com.shuyun.loyalty.sdk.api.model.http.points.MemberValidPointResponse
import com.shuyun.loyalty.sdk.Dataapi.noneTransaction
import com.shuyun.loyalty.sdk.Json
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.points.PointQuery.kt")


fun Route.pointQuery() {

    // 查询会员积分值
    get("/point") {
        val pointAccountId = call.request.queryParameters["pointAccountId"]
        val memberId = call.request.queryParameters["memberId"]
        val fields = call.request.queryParameters["fields"]
        val req = MemberPointQueryReq(
            pointAccountId = pointAccountId?.toLong(),
            memberId = memberId,
            fields = fields
        ).also { LOGGER.debug("查询会员积分:({}-{}-{})", it.pointAccountId, it.memberId, it.fields) }.validate()

        val accountType = ProgramService.getPointAccountTypeById(req.pointAccountId!!)!!
        MemberService.checkMemberId(accountType.subject.fqn, req.memberId!!)

        val queryFields = (req.fields ?: "point").trim()
        if (queryFields == "point") {
            val memberPoint = MemberPointService.findMemberPoint(req.pointAccountId, req.memberId)
            val point = memberPoint?.point ?: BigDecimal.ZERO
            call.respond(MemberPointResponse().apply { this.point = point })
            return@get
        }
        val memberPoint = MemberPointService.findMemberPoint(req.pointAccountId, req.memberId)
        if (memberPoint == null) {
            LOGGER.warn("会员积分账户不存在 memberAccountId={}, memberId={}", req.pointAccountId, req.memberId)
            throw PointAccountNotFoundException("会员积分账户不存在")
        }
        val map: Map<String, Any?> = Json.convert(memberPoint)
        val resultMap = mutableMapOf<String, Any?>()
        queryFields.split(",").filter { it.trim().isNotEmpty() }.forEach {
            map[it]?.let { value ->
                resultMap[it] = value
            }
        }
        call.respond(resultMap)
    }


    // 查询积分账户记录列表
    get("/point:list") {
        val pointAccountId = call.parameters["pointAccountId"]?.toLong()
        val memberId = call.parameters["memberId"]
        val number = call.parameters["number"]?.toInt() ?: 0
        val pageSize = call.parameters["pageSize"]?.toInt() ?: 20

        if (pointAccountId == null) {
            throw BadParamRequestException("pointAccountId参数为必填")
        }

        val hierarchy = ProgramService.getPointAccountTypeById(pointAccountId) ?: throw BadParamRequestException("pointAccountId参数错误")

        val list = MemberPointService.queryMemberPoints(pointAccountId, memberId, number, pageSize)

        val response = list.map {
            MemberPointItemResponse().apply {
                this.planId = it.planId
                this.planName = hierarchy.plan.name
                this.subjectId = hierarchy.subject.id
                this.subjectName = hierarchy.subject.name
                this.subjectFqn = it.subjectFqn
                this.pointAccountTypeId = it.pointPlanId
                this.pointAccountTypeName = hierarchy.name
                this.memberId = it.memberId
                this.point = it.point
            }
        }

        call.respond(response)
    }

    // 查询指定失效时间范围内有效积分列表
    get("/point:valid") {
        val pointAccountId = call.parameters["pointAccountId"] ?: throw BadParamRequestException("pointAccountId参数为必填")
        val memberId = call.parameters["memberId"] ?: throw BadParamRequestException("memberId参数为必填")
        val timeUnit = call.parameters["timeUnit"] ?: throw BadParamRequestException("timeUnit参数为必填")
        val timeValue = call.parameters["timeValue"] ?: throw BadParamRequestException("timeValue参数为必填")
        val number = call.parameters["number"]?.toInt() ?: 0
        val pageSize = call.parameters["pageSize"]?.toInt() ?: 100

        val hierarchy = ProgramService.getPointAccountTypeById(pointAccountId.toLong()) ?: throw BadParamRequestException("pointAccountId参数错误")

        val shanghaiZoneId = ZoneId.of("Asia/Shanghai")
        val start = ZonedDateTime.of(LocalDate.now(shanghaiZoneId), LocalTime.MIDNIGHT, shanghaiZoneId)
        val overdueDate = when (timeUnit) {
            "YEAR" -> start.plusYears(timeValue.toLong())
            "MONTH" -> start.plusMonths(timeValue.toLong())
            "DAY" -> start.plusDays(timeValue.toLong())
            else -> throw BadParamRequestException("timeUnit参数错误")
        }

        LOGGER.info("查询过期时间小于{}的有效积分记录", overdueDate)

        val validPointRecords = noneTransaction { PointStatementRepository.queryValidPointsExpiredDateBefore(it, hierarchy.id, memberId, overdueDate, number, pageSize) }

        val response = validPointRecords.map { Json.convert<MemberValidPointResponse>(it) }

        call.respond(response)
    }

    // 查询尚未生效的积分值
    get("/point:not-yet-valid") {
        val pointAccountId = call.parameters["pointAccountId"] ?: throw BadParamRequestException("pointAccountId参数为必填")
        val memberId = call.parameters["memberId"] ?: throw BadParamRequestException("memberId参数为必填")
        val hierarchy = ProgramService.getPointAccountTypeById(pointAccountId.toLong()) ?: throw BadParamRequestException("pointAccountId参数错误")

        MemberService.checkMemberId(hierarchy.subject.fqn, memberId)

        LOGGER.info("查询尚未生效的积分值 {} {} {}", hierarchy.id, hierarchy.name, memberId)

        val points = noneTransaction {
            PointStatementRepository.queryNotYetValidPointsPoints(
                it,
                pointAccountId.toLong(),
                memberId,
                ZonedDateTime.now()
            )
        }
        call.respond(MemberNotYetValidPointsResponse().apply { this.points = points })
    }
}


private data class MemberPointQueryReq(val pointAccountId: Long?, val memberId: String?, val fields: String?)

private fun MemberPointQueryReq.validate(): MemberPointQueryReq {
    ProgramService.basicValidate(pointAccountId, memberId)
    return this
}



