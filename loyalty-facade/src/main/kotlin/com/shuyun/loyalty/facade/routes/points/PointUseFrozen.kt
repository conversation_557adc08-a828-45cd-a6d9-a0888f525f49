package com.shuyun.loyalty.facade.routes.points

import com.shuyun.loyalty.facade.common.Kafka
import com.shuyun.loyalty.facade.common.withMemberPointLock
import com.shuyun.loyalty.facade.model.Constants
import com.shuyun.loyalty.facade.model.ForbiddenOperation
import com.shuyun.loyalty.facade.model.TypeEnum
import com.shuyun.loyalty.facade.service.BlockListService
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.points.PointModifyService
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointUseFrozenRequest
import com.shuyun.loyalty.sdk.Json
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import java.time.Duration
import java.time.ZonedDateTime

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.points.PointUseFrozen.kt")



fun Route.pointUseFrozen() {

    post("/point:frozenDeduct") {
        val req = call.receive<MemberPointUseFrozenRequest>().also { LOGGER.info("使用冻结会员积分: {}", Json.toJson(it)) }.validate()

        val now = ZonedDateTime.now()
        val timeout = req.lockWaitTime ?: Constants.DEFAULT_WAIT_TIME
        val accountType = ProgramService.getPointAccountTypeById(req.pointAccountId!!)!!
        // 判断会员是否在会员表中存在 和 检查特殊名单
        BlockListService.check(
            TypeEnum.POINT,
            accountType.subject.fqn,
            accountType.id,
            req.memberId!!,
            ForbiddenOperation.POINT_USE_FREEZE,
        )
        val res = withMemberPointLock(req.pointAccountId!!, req.memberId!!, Duration.ofMillis(timeout)) {
            PointModifyService.useFrozenMemberPoint(req, now, req.tx)
        }
        Kafka.sendPointCalc(req.pointAccountId!!, res.memberPointId!!, req.memberId!!)
        call.respond(res)
    }
}


private fun MemberPointUseFrozenRequest.validate(): MemberPointUseFrozenRequest {
    ProgramService.basicValidate(pointAccountId, memberId, channelType, changeMode, businessId)
    return this
}


