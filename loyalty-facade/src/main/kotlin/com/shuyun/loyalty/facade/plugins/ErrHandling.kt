package com.shuyun.loyalty.facade.plugins

import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.facade.model.FacadeException
import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.plugins.statuspages.*
import io.ktor.server.response.*
import io.ktor.util.logging.*

internal val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.plugins.ErrHandling.kt")
fun Application.configureExceptionHandling() {
    install(StatusPages) {
        exception<FacadeException> { call, cause ->
            LOGGER.warn("请求处理: {}", cause.message)
            call.respond(HttpStatusCode.BadRequest,  cause.toJson())
        }
        exception<Throwable> { call, cause ->
            val error = mapOf(
                "error_code" to "040500",
                "msg" to "请求处理异常",
                "module" to "loyalty4",
                "service" to GlobalContext.serviceName()
            )
            LOGGER.error("请求处理异常: {}", cause.message, cause)
            call.respond(HttpStatusCode.InternalServerError,  error)
        }
    }
}
