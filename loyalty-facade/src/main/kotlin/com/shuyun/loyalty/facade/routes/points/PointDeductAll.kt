package com.shuyun.loyalty.facade.routes.points

import com.shuyun.loyalty.facade.common.withMemberPointLock
import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.service.BlockListService
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.points.MemberPointService
import com.shuyun.loyalty.facade.service.points.PointModifyService
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointDeductAllRequest
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointDeductAllResponse
import com.shuyun.loyalty.sdk.Json
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import java.math.BigDecimal
import java.time.ZonedDateTime

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.points.PointDeductAll.kt")


// 作废剩余积分
fun Route.pointDeductAll() {

    post("/point:deduct-all") {
        val req = call.receive<MemberPointDeductAllRequest>().also { LOGGER.info("作废扣减全部剩余积分: {}", Json.toJson(it)) }.validate()
        val now = ZonedDateTime.now()
        val success = ArrayList<Long>()
        val error = ArrayList<Long>()
        for (pointPlanId in req.pointPlanIds!!) {
            try {
                val pointAccountType = ProgramService.getPointAccountTypeById(pointPlanId)
                if (pointAccountType == null) {
                    error.add(pointPlanId)
                    LOGGER.warn("积分账户ID不存在: {}", pointPlanId)
                    continue
                }
                if (pointAccountType.negativeStrategy == NegativeStrategy.NOT_ALLOWED.name) {
                    error.add(pointPlanId)
                    LOGGER.warn("积分账户ID不允许扣减: {}", pointPlanId)
                    continue
                }
                BlockListService.check(
                    TypeEnum.POINT,
                    pointAccountType.subject.fqn,
                    pointPlanId,
                    req.memberId!!,
                    ForbiddenOperation.POINT_DEDUCT_BY_ABOLISH
                )
                withMemberPointLock(pointPlanId, req.memberId!!)  {
                    val memberPoint = MemberPointService.findMemberPoint(pointPlanId, req.memberId!!)
                    if (memberPoint == null || memberPoint.point <= BigDecimal.ZERO) {
                        return@withMemberPointLock
                    }
                    PointModifyService.deductAll(pointAccountType, memberPoint, req.toPointAttr(), now)
                }
                success.add(pointPlanId)
            } catch (e: Exception) {
                error.add(pointPlanId)
                LOGGER.error("作废剩余积分失败: pointPlanId: {}, memberId: {} ", pointPlanId, req.memberId, e)
            }
        }
        call.respond(MemberPointDeductAllResponse().apply {
            this.successPointPlanIds = success
            this.errorPointPlanIds = error
        })
    }
}


private fun MemberPointDeductAllRequest.toPointAttr(): PointAttr {
    return PointAttr(
        traceId = uniqueId!!,
        uniqueId = uniqueId!!,
        changeMode = changeMode ?: Constants.DEFAULT_CHANGE_MODE,
        channel = channelType ?: Constants.DEFAULT_CHANNEL_TYPE,
        operatorId = Constants.DEFAULT_OPERATOR,
        operator = Constants.DEFAULT_OPERATOR,
        shopId = shopId,
        kzzd1 = kzzd1,
        kzzd2 = kzzd2,
        kzzd3 = kzzd3,
        desc = desc,
    )
}


private fun MemberPointDeductAllRequest.validate(): MemberPointDeductAllRequest {
    if (planId == null) throw BadParamRequestException("计划ID不能为空")
    if (pointPlanIds.isNullOrEmpty()) throw BadParamRequestException("积分账户ID列表不能为空")
    if (memberId == null) throw BadParamRequestException("会员ID不能为空")
    if (uniqueId == null) throw BadParamRequestException("业务唯一ID不能为空")
    if (channelType == null) throw BadParamRequestException("渠道类型不能为空")
    if (changeMode != null) {
        ProgramService.findChangeModeByCode(changeMode!!) ?: throw BadParamRequestException("无效的变更方式")
    } else {
        changeMode = "INTERFACE"
    }
    pointPlanIds = pointPlanIds!!.distinct()
    return this
}



