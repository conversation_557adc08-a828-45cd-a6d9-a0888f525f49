package com.shuyun.loyalty.facade.service.points

import com.shuyun.loyalty.facade.common.Kafka
import com.shuyun.loyalty.facade.model.Constants.RANDOM_CHAR_DICTIONARY
import com.shuyun.loyalty.facade.model.ImportPointRecord
import com.shuyun.loyalty.facade.model.isSdkDuplicate
import com.shuyun.loyalty.facade.repository.points.PointImportRepository
import com.shuyun.loyalty.sdk.Dataapi
import com.shuyun.loyalty.sdk.Dataapi.noneTransaction
import com.shuyun.loyalty.sdk.Dataapi.withTransaction
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.withContext
import org.apache.commons.lang3.RandomStringUtils
import org.slf4j.LoggerFactory

object PointImportService {

    private val logger = LoggerFactory.getLogger(PointImportService::class.java)


    suspend fun saveImportRecords(records: List<ImportPointRecord>) {
        withTransaction {
            PointImportRepository.batchInsertImportRecord(it, records)
        }
    }


    suspend fun applyImportRecords(importId: String, pointAccountTypeId: Long) {
        withContext(Dispatchers.IO + MDCContext()) {
            PointImportRepository.fetchImportMember(importId, pointAccountTypeId) { importMember ->
                if (importMember.migrationId != importId) return@fetchImportMember
                try {
                    Dataapi.sdk.use { PointImportRepository.insertImportMember(it, importMember) }
                    val mmid = "MMMID-${importMember.id}"
                    val reqId = RandomStringUtils.random(8, RANDOM_CHAR_DICTIONARY)
                    Kafka.sendPointCalc(importMember.pointId, mmid, importMember.memberId, reqId)
                    logger.info(
                        "发送迁移会员消息 migrationId: {} memberId: {} reqId: {}",
                        importId, importMember.memberId, reqId
                    )
                } catch (e: Exception) {
                    if (!e.isSdkDuplicate()) {
                        logger.error("迁移会员失败 migrationId: {} memberId: {} id: {}", importId, importMember.memberId, importMember.id, e)
                        //logger.info("迁移会员已存在 migrationId: {} memberId: {} id: {}", importId, importMember.memberId, importMember.id)
                    }
                }
            }
        }
    }


    suspend fun countImportMember(migrationId: String, pointAccountTypeId: Long): Long {
        return noneTransaction {
            PointImportRepository.countImportMember(it, migrationId, pointAccountTypeId)
        }
    }
}