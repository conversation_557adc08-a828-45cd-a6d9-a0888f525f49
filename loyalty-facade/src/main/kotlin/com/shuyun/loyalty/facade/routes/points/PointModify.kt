package com.shuyun.loyalty.facade.routes.points

import com.shuyun.loyalty.facade.model.*
import com.shuyun.loyalty.facade.service.ProgramService
import com.shuyun.loyalty.facade.service.points.MemberPointService
import com.shuyun.loyalty.facade.service.points.PointModifyService
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointBatchModifyRequest
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointBatchModifyResponse
import com.shuyun.loyalty.sdk.api.model.http.points.MemberPointBatchModifyResponseItem
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.sdk.Uuid
import io.ktor.http.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import io.ktor.util.logging.*
import java.math.BigDecimal
import java.time.ZonedDateTime

private val LOGGER = KtorSimpleLogger("com.shuyun.loyalty.facade.routes.points.PointModify.kt")


fun Route.pointModify() {

    post("/point:batch:modify") {
        val req = call.receive<MemberPointBatchModifyRequest>().also {
            LOGGER.info("批量变更会员积分: {}", Json.toJson(it))
        }.validate()
        val maxBatchSize = Property.getSysOrEnv("point.batch.max.size", 1000)
        if (req.openPointBatchItems!!.size > maxBatchSize) {
            throw BadParamRequestException("批量变更积分数量不能超过$maxBatchSize")
        }

        val uniqueRecord = InterfaceRecord(
            id = req.triggerId,
            sequence = req.triggerId,
            content = Json.toJson(Json.copy(req).apply { this.openPointBatchItems = null }),
            created = ZonedDateTime.now()
        )

        val batchLog = BatchModifyLog(
            parentId = 0,
            triggerId = req.triggerId,
            processStatus = "WAIT",
            openType = TypeEnum.POINT.name,
            totalNumber = req.openPointBatchItems!!.size,
            failNumber = 0,
            successNumber = 0,
            createTime = ZonedDateTime.now()
        )

        val hierarchy = ProgramService.getPointAccountTypeById(req.pointAccountId!!) ?: throw BadParamRequestException("积分账户ID错误")

        val pointRecords = req.openPointBatchItems!!.map {
            PointRecord(
                planId = hierarchy.plan.id,
                planName = hierarchy.plan.name,
                pointPlanId = req.pointAccountId!!,
                pointPlanName = hierarchy.name,
                memberPointId = "",
                memberId = it.memberId!!,
                subjectFqn = hierarchy.subject.fqn,
                recordType = req.recordType,
                point = it.point,
                effectiveDate = it.effectDate,
                overdueDate = it.overdueDate,
                traceId = req.triggerId,
                key = Uuid.uuid,
                changeMode = it.changeMode ?: "INTERFACE_BATCH",
                channel = it.channelType,
                desc = it.desc,
                shopId = it.shopId,
                kzzd1 = it.kzzd1,
                kzzd2 = it.kzzd2,
                kzzd3 = it.kzzd3,
                actionId = it.actionId,
                actionName = it.actionName,
                actionNodeId = it.actionNodeId,
                actionNodeName = it.actionNodeName,
                modified = ZonedDateTime.now(),
                created = ZonedDateTime.now(),
            )
        }

        PointModifyService.batchModifyMemberPoint(pointRecords, batchLog, uniqueRecord)

        call.respond(HttpStatusCode.OK)
    }


    // 查询批量变更积分结果
    get("/point:batch:log") {
        val triggerId = call.parameters["triggerId"] ?: throw BadParamRequestException("追溯ID不能为空")
        val batchLog = MemberPointService.findParentBatchModifyLog(triggerId)
        if (batchLog == null) {
            LOGGER.warn("追溯ID: {} 无对应记录", triggerId)
            call.respond(HttpStatusCode.OK)
        } else {
            val items = MemberPointService.findChildBatchModifyLog(batchLog.id!!)
            val children = items.map {
                MemberPointBatchModifyResponseItem().apply {
                    this.memberId = it.memberId
                    this.responseErrorData = it.responseErrorData
                }
            }
            val response = MemberPointBatchModifyResponse().apply {
                this.openType = batchLog.openType
                this.triggerId = batchLog.triggerId
                this.processStatus = batchLog.processStatus
                this.totalNumber = batchLog.totalNumber
                this.failNumber = batchLog.failNumber
                this.successNumber = batchLog.successNumber
                this.memberPointModifyBatchItemResponse = children
            }
            call.respond(response)
        }
    }
}



private fun MemberPointBatchModifyRequest.validate(): MemberPointBatchModifyRequest {
    if (pointAccountId == null) throw BadParamRequestException("积分账户ID不能为空")
    if (recordType == null) throw BadParamRequestException("触发动作不能为空")
    if (recordType != "SEND" && recordType != "DEDUCT") throw BadParamRequestException("无效的触发动作")
    if (triggerId == null) throw BadParamRequestException("追溯ID不能为空")
    if (openPointBatchItems == null || openPointBatchItems!!.isEmpty()) {
        throw BadParamRequestException("积分变更信息不能为空")
    }
    for (item in openPointBatchItems!!) {
        if (item.memberId == null || item.memberId!!.isEmpty()) {
            throw BadParamRequestException("会员ID不能为空")
        }
        if (item.point == null || item.point!! <= BigDecimal.ZERO) {
            throw BadParamRequestException("积分值必须大于0")
        }
        if (item.channelType == null || item.channelType!!.isEmpty()) {
            throw BadParamRequestException("渠道类型不能为空")
        }
    }

    return this
}






