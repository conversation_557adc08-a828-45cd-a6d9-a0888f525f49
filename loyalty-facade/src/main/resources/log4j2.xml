<?xml version="1.0" encoding="UTF-8"?>
<!-- Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，
 你会看到log4j2内部各种详细输出。可以设置成OFF(关闭)或Error(只输出错误信息)
-->
<Configuration status="WARN" monitorInterval="30">
    <Properties>
        <Property name="LOG_SERVICE_NAME" value="loyalty-facade"/>
        <Property name="LOG_APP_VERSION" value="${sys:APP_VERSION:-${env:APP_VERSION:-0.0.1}}"/>
        <Property name="LOG_ENVIRONMENT" value="${sys:ENVIRONMENT:-${env:ENVIRONMENT:-dev}}"/>
        <Property name="LOG_SANDBOX" value="${sys:SANDBOX:-${env:SANDBOX:-base}}"/>
        <Property name="LOG_MESOS_TASK_ID" value="${sys:MESOS_TASK_ID:-${env:MESOS_TASK_ID:-1}}"/>
        <Property name="LOGGING_BASE_DIR" value="${sys:system.logging.dir:-${env:system.logging.dir:-/var/log}}"/>
        <Property name="LOGGING_BAK_FILE" value="${LOGGING_BASE_DIR}/${LOG_SERVICE_NAME}/${LOG_APP_VERSION}/${LOG_ENVIRONMENT}/${LOG_SANDBOX}/${LOG_MESOS_TASK_ID}/${LOG_SERVICE_NAME}.log.%d{yyyyMMddHH}"/>
        <Property name="LOGGING_FILE" value="${LOGGING_BASE_DIR}/${LOG_SERVICE_NAME}/${LOG_APP_VERSION}/${LOG_ENVIRONMENT}/${LOG_SANDBOX}/${LOG_MESOS_TASK_ID}/${LOG_SERVICE_NAME}.log"/>
        <property name="DEFAULT_LOG_PATTERN" value="[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%-5level] [%logger{36}:%line] [%thread] [%X{loyalty_trace_id}] [-[%msg]-] %n"/>
        <property name="LOG_PATTERN" value="${sys:system.logging.format1:-${env:system.logging.format1:-${DEFAULT_LOG_PATTERN:-}}}"/>
    </Properties>

    <Appenders>
        <!-- 输出控制台日志的配置 -->
        <Console name="console" target="SYSTEM_OUT">
            <PatternLayout pattern="${LOG_PATTERN}"/>
        </Console>

        <!-- 1️⃣  滚动文件 Appender -->
        <RollingFile name="file" fileName="${LOGGING_FILE}" filePattern="${LOGGING_BAK_FILE}">
            <PatternLayout pattern="${LOG_PATTERN}" />
            <!-- 2️⃣  触发策略：大小 + 日期 -->
            <Policies>
                <!-- 文件 ≥ 1 GiB 立即滚动 -->
                <SizeBasedTriggeringPolicy size="1GB"/>
                <!-- 每天 0 点再滚动一次，使序号重置 -->
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
            </Policies>
            <!-- 3️⃣  归档策略：数量 + 清理 -->
            <!-- fileIndex="min" 让序号从 1 开始；max=10 控制每天最多 10 个文件 -->
            <DefaultRolloverStrategy fileIndex="min" max="10">
                <!-- Delete 动作在每次滚动后执行 -->
                <Delete basePath="${LOGGING_BASE_DIR}/archive" maxDepth="1">
                    <!-- 仅匹配本应用归档文件 -->
                    <IfFileName glob="${LOG_SERVICE_NAME}-*.log"/>
                    <!-- 只删除 7 天以前的文件 -->
                    <IfLastModified age="7d"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingFile>
    </Appenders>


    <Loggers>
        <!-- root logger 配置,全局配置，默认所有的Logger都继承此配置 -->
        <!-- AsyncRoot - 异步记录日志 - 需要LMAX Disruptor的支持 -->
        <AsyncRoot level="INFO" includeLocation="true" additivity="true">
            <AppenderRef ref="file"/>
            <AppenderRef ref="console"/>
        </AsyncRoot>

        <!--第三方的软件日志级别 -->
        <AsyncLogger name="com.shuyun.loyalty" level="INFO" includeLocation="true" additivity="false">
            <AppenderRef ref="file"/>
            <AppenderRef ref="console"/>
        </AsyncLogger>

        <logger name="com.shuyun.dm" level="WARN"/>
        <logger name="org.apache.kafka.clients" level="WARN"/>
        <logger name="com.shuyun.dm.metadata.sdk.client.DefaultMetadataHttpSdk" level="WARN"/>
        <logger name="com.shuyun.spectrum" level="error"/>
        <logger name="com.shuyun.motor" level="error"/>
        <logger name="com.shuyun.pip" level="info"/>
        <logger name="com.shuyun.pip.motor" level="warn"/>
    </Loggers>
</Configuration>