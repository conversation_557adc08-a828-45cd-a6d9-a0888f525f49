---
openapi: 3.0.3
info:
  title: loyalty-facade
  description: loyalty-facade
  version: '1.0'
paths:
  "/loyalty-facade/v1/ops/ping":
    get:
      tags:
        - 运维接口
      summary: 健康检查
      x-visibility: "Service"
      responses:
        '200':
          description: Server Success
          content:
            text/plain:
              schema:
                type: string
                description: 健康状态
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/ops/loggers":
    post:
      tags:
        - 运维接口
      summary: 修改日志打印级别
      x-visibility: "Service"
      requestBody:
        content:
          application/json:
            schema:
              type: object
              required:
                - packageName
                - configuredLevel
              properties:
                packageName:
                  type: string
                  description: 日志包名
                configuredLevel:
                  type: string
                  description: 日志级别
      responses:
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/plan:info":
    get:
      tags:
        - 计划方案信息
      summary: 计划方案信息
      x-visibility: "Service"
      operationId: findLoyaltyPlanInfo
      parameters:
        - name: planId
          in: query
          required: true
          description: 计划ID
          schema:
            type: integer
      responses:
        '200':
          description: 计划方案信息
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/PlanResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/plan:all":
    get:
      tags:
        - 计划方案信息
      summary: 计划方案信息
      x-visibility: "Service"
      operationId: findLoyaltyPlanAll
      responses:
        '200':
          description: 计划方案信息
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/PlanResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:init":
    post:
      tags:
        - 积分
      summary: 积分账户初始化
      x-visibility: "Service"
      description: 积分账户初始化，如果已存在抛出异常
      operationId: pointInit
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberPointInitRequest"
      responses:
        '200':
          description: Server Success
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/MemberPointInitResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:send":
    post:
      tags:
        - 积分
      summary: 积分发放
      x-visibility: "External"
      description: 如果积分账户不存在、积分不足或积分上限等异常则报异常。如果要自动初始化积分账户则需要在配置中心添加:member.pointAccount.autoInit=true(但会影响性能)。如果想获取会员不存在异常，则配置中心添加：member.pointAccount.checkExist=true(积分发放失败后，会查询会员确认是否因不存在导致。考虑性能，不建议开启)
      operationId: pointSend
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberPointSendRequest"
      responses:
        '200':
          description: 请求已接收
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/MemberPointSendResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:deduct":
    post:
      tags:
        - 积分
      summary: 积分扣减
      x-visibility: "External"
      operationId: pointDeduct
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberPointDeductRequest"
      responses:
        '202':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:allowedNegativeDetect":
    post:
      tags:
        - 积分
      summary: 积分扣减-允许除到负数
      x-visibility: "Service"
      description: 积分账户需要设置成允许负数且配置中心loyalty-facade目录需要配置：point.over.use.enabled=true
      operationId: allowedNegativeDetect
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberPointDeductRequest"
      responses:
        '202':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:frozen":
    post:
      tags:
        - 积分
      summary: 积分冻结
      x-visibility: "External"
      operationId: pointFrozen
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberPointFreezeRequest"
      responses:
        '202':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:frozenDeduct":
    post:
      tags:
        - 积分
      summary: 积分冻结消耗
      x-visibility: "External"
      description: 积分冻结消耗
      operationId: pointFrozenDeduct
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberPointUseFrozenRequest"
      responses:
        '200':
          description: 请求已接收
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/MemberPointUseFrozenResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:unfrozen":
    post:
      tags:
        - 积分
      summary: 积分解冻
      x-visibility: "External"
      description: 积分解冻
      operationId: pointUnFrozen
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberPointUnfreezeRequest"
      responses:
        '200':
          description: 请求已接收
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/MemberPointUnfreezeResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:batch:modify":
    post:
      tags:
        - 积分
      summary: 批量变更会员积分
      x-visibility: "Service"
      description: 批量变更会员积分
      operationId: batchModifyPoint
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberPointBatchModifyRequest"
      responses:
        '200':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:batch:log":
    get:
      tags:
        - 积分
      summary: 查询批量变更积分结果
      x-visibility: "Service"
      description: 查询批量变更积分结果
      operationId: getBatchModifyPointLog
      parameters:
        - name: "triggerId"
          in: "query"
          required: true
          description: "批量操作的追溯Id"
          schema:
            type: string
      responses:
        '200':
          description: 请求已接收
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/MemberPointBatchModifyResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:revert":
    post:
      tags:
        - 积分
      summary: 积分撤销
      x-visibility: "External"
      description: 积分撤销
      operationId: pointRevert
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberPointRevertRequest"
      responses:
        '200':
          description: 请求已接收
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/MemberPointRevertResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:import:save-record":
    post:
      tags:
        - 积分
      summary: 积分迁移导入-保存积分导入记录
      x-visibility: "Service"
      description: 积分迁移导入-保存积分导入记录
      operationId: savePointImportRecord
      parameters:
        - name: "importId"
          in: "query"
          required: true
          description: "导入ID"
          schema:
            type: string
        - name: "pointAccountTypeId"
          in: "query"
          required: true
          description: "积分账户类型ID"
          schema:
              type: "integer"
        - name: "overrideHistoryPoint"
          in: "query"
          required: false
          description: "是否覆盖历史积分"
          schema:
            type: "boolean"
            default: false
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                "$ref": "#/components/schemas/MemberPointImportRequest"
      responses:
        '200':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:import:apply-record":
    post:
      tags:
        - 积分
      summary: 积分迁移导入-将记录转化为积分
      x-visibility: "Service"
      description: 积分迁移导入-将记录转化为积分
      operationId: applyPointImportRecord
      parameters:
        - name: "importId"
          in: "query"
          required: true
          description: "导入ID"
          schema:
            type: string
        - name: "pointAccountTypeId"
          in: "query"
          required: true
          description: "积分账户类型ID"
          schema:
            type: "integer"
      responses:
        '202':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:import:result":
    get:
      tags:
        - 积分
      summary: 积分迁移导入-查询导入结果
      x-visibility: "Service"
      description: 积分迁移导入-查询导入结果
      operationId: getPointImportResult
      parameters:
        - name: "importId"
          in: "query"
          required: true
          description: "导入ID"
          schema:
            type: string
        - name: "pointAccountTypeId"
          in: "query"
          required: true
          description: "积分账户类型ID"
          schema:
            type: "integer"
      responses:
        '200':
          description: 请求已接收
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/MemberPointImportResult"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point":
    get:
      tags:
        - 积分
      summary: 单个会员积分查询
      x-visibility: "External"
      operationId: findMemberPoint
      parameters:
        - name: memberId
          in: query
          required: true
          schema:
            type: string
        - name: pointAccountId
          in: query
          description: 积分账户id
          required: true
          schema:
            type: string
        - name: fields
          in: query
          description: '要查询的字段，以逗号分隔, default: point'
          schema:
            type: string
      responses:
        '200':
          description: 积分值对象
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/MemberPointResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:list":
    get:
      tags:
        - 积分
      summary: 查询积分账户记录列表
      x-visibility: "Service"
      operationId: findMemberPointList
      parameters:
        - name: pointAccountId
          in: query
          description: 积分账户id
          required: true
          schema:
            type: integer
        - name: memberId
          in: query
          description: 会员id
          required: true
          schema:
            type: string
        - name: number
          in: query
          description: '第几页，从0开始'
          schema:
            type: integer
        - name: pageSize
          in: query
          description: '每页大小，默认20'
          schema:
            type: integer
      responses:
        '200':
          description: 积分账户记录对象列表
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/MemberPointItemResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:valid":
    get:
      tags:
        - 积分
      summary: 查询指定失效时间范围内有效积分列表
      x-visibility: "External"
      operationId: findMemberPointValid
      parameters:
        - name: pointAccountId
          in: query
          description: 积分账户id
          required: true
          schema:
            type: integer
        - name: memberId
          in: query
          required: true
          schema:
            type: string
        - name: timeUnit
          in: query
          description: 时间单位
          required: true
          schema:
            type: string
            enum:
              - YEAR
              - MONTH
              - DAY
        - name: timeValue
          in: query
          description: 时间值
          required: true
          schema:
            type: integer
        - name: number
          in: query
          description: '第几页，从0开始'
          schema:
            type: integer
        - name: pageSize
          in: query
          description: '每页大小，默认20'
          schema:
            type: integer
      responses:
        '200':
          description: 查询指定失效时间范围内有效积分列表
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/MemberValidPointResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:not-yet-valid":
    get:
      tags:
        - 积分
      summary: 查询尚未生效的积分值
      x-visibility: "Service"
      operationId: findMemberPointNotYetValid
      parameters:
        - name: pointAccountId
          in: query
          description: 积分账户id
          required: true
          schema:
            type: integer
        - name: memberId
          in: query
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 查询尚未生效的积分值
          content:
            application/json:
              schema:
                type: object
                properties:
                  points:
                    type: number
                    description: 未生效的积分值
                    format: double
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:record:page":
    get:
      tags:
        - 积分
      summary: 查询积分变更记录分页
      x-visibility: "External"
      operationId: findMemberPointRecordPage
      parameters:
        - name: pointAccountId
          in: query
          description: 积分账户id
          required: true
          schema:
            type: integer
        - name: recordType
          in: query
          description: 记录类型
          schema:
            type: string
            enum:
              - "SEND"
              - "DELAY_SEND"
              - "EXPIRE"
              - "FREEZE"
              - "UNFREEZE"
              - "DEDUCT"
              - "ABOLISH"
              - "TIMER"
              - "RECALCULATE"
              - "SPECIAL_DEDUCT"
              - "SPECIAL_FREEZE"
              - "SPECIAL_UNFREEZE"
              - "SPECIAL_ABOLISH"
              - "MANUAL_ABOLISH"
              - "OPEN_FREEZE"
              - "OPEN_UNFREEZE"
              - "REVERSE_SEND"
              - "REVERSE_DEDUCT"
        - name: recordTypes
          in: query
          description: 记录类型列表
          schema:
            type: array
            items:
              type: string
              enum:
                - "SEND"
                - "DELAY_SEND"
                - "EXPIRE"
                - "FREEZE"
                - "UNFREEZE"
                - "DEDUCT"
                - "ABOLISH"
                - "TIMER"
                - "RECALCULATE"
                - "SPECIAL_DEDUCT"
                - "SPECIAL_FREEZE"
                - "SPECIAL_UNFREEZE"
                - "SPECIAL_ABOLISH"
                - "MANUAL_ABOLISH"
                - "OPEN_FREEZE"
                - "OPEN_UNFREEZE"
                - "REVERSE_SEND"
                - "REVERSE_DEDUCT"
        - name: status
          in: query
          description: 记录状态列表
          schema:
            type: array
            items:
              type: string
              enum:
                - "DELAY"
                - "DELAY_FROZEN"
                - "DELAY_ABOLISH"
                - "EXPIRE"
                - "VALID"
                - "FROZEN"
                - "USED"
                - "ABOLISH"
                - "FROZEN_ABOLISH"
        - name: memberId
          in: query
          description: 会员Id
          schema:
            type: string
        - name: shopId
          in: query
          description: 店铺Id
          schema:
            type: string
        - name: channelType
          in: query
          description: 渠道类型
          schema:
            type: string
        - name: startTime
          in: query
          description: 开始时间
          example: '2021-01-01T12:17:00+08:00'
          schema:
            type: string
            format: date-time
        - name: endTime
          in: query
          description: 结束时间
          example: '2021-01-01T12:17:00+08:00'
          schema:
            type: string
            format: date-time
        - name: traceId
          in: query
          description: 追溯Id
          schema:
            type: string
        - name: sortType
          in: query
          description: 排序类型
          schema:
            type: string
            enum:
              - "CREATED_DESC"
              - "CREATED_ASC"
        - name: changeMode
          in: query
          description: 变更方式
          schema:
            type: string
        - name: number
          in: query
          description: '第几页，从0开始'
          schema:
            type: integer
        - name: pageSize
          in: query
          description: '每页大小，默认20'
          schema:
            type: integer
      responses:
        '200':
          description: 积分变更记录
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/PointPage"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:record:list":
    get:
      tags:
        - 积分
      summary: 查询积分变更记录列表
      x-visibility: "External"
      operationId: findMemberPointRecordList
      parameters:
        - name: pointAccountId
          in: query
          description: 积分账户id
          required: true
          schema:
            type: integer
        - name: recordType
          in: query
          description: 记录类型
          schema:
            type: string
            enum:
              - "SEND"
              - "DELAY_SEND"
              - "EXPIRE"
              - "FREEZE"
              - "UNFREEZE"
              - "DEDUCT"
              - "ABOLISH"
              - "TIMER"
              - "RECALCULATE"
              - "SPECIAL_DEDUCT"
              - "SPECIAL_FREEZE"
              - "SPECIAL_UNFREEZE"
              - "SPECIAL_ABOLISH"
              - "MANUAL_ABOLISH"
              - "OPEN_FREEZE"
              - "OPEN_UNFREEZE"
              - "REVERSE_SEND"
              - "REVERSE_DEDUCT"
        - name: recordTypes
          in: query
          description: 记录类型列表
          schema:
            type: array
            items:
              type: string
              enum:
                - "SEND"
                - "DELAY_SEND"
                - "EXPIRE"
                - "FREEZE"
                - "UNFREEZE"
                - "DEDUCT"
                - "ABOLISH"
                - "TIMER"
                - "RECALCULATE"
                - "SPECIAL_DEDUCT"
                - "SPECIAL_FREEZE"
                - "SPECIAL_UNFREEZE"
                - "SPECIAL_ABOLISH"
                - "MANUAL_ABOLISH"
                - "OPEN_FREEZE"
                - "OPEN_UNFREEZE"
                - "REVERSE_SEND"
                - "REVERSE_DEDUCT"
        - name: status
          in: query
          description: 记录状态列表
          schema:
            type: array
            items:
              type: string
              enum:
                - "DELAY"
                - "DELAY_FROZEN"
                - "DELAY_ABOLISH"
                - "EXPIRE"
                - "VALID"
                - "FROZEN"
                - "USED"
                - "ABOLISH"
                - "FROZEN_ABOLISH"
        - name: memberId
          in: query
          description: 会员Id
          schema:
            type: string
        - name: shopId
          in: query
          description: 店铺Id
          schema:
            type: string
        - name: channelType
          in: query
          description: 渠道类型
          schema:
            type: string
        - name: startTime
          in: query
          description: 开始时间
          example: '2021-01-01T12:17:00+08:00'
          schema:
            type: string
            format: date-time
        - name: endTime
          in: query
          description: 结束时间
          example: '2021-01-01T12:17:00+08:00'
          schema:
            type: string
            format: date-time
        - name: traceId
          in: query
          description: 追溯Id
          schema:
            type: string
        - name: sortType
          in: query
          description: 排序类型
          schema:
            type: string
            enum:
              - "CREATED_DESC"
              - "CREATED_ASC"
        - name: changeMode
          in: query
          description: 变更方式
          schema:
            type: string
        - name: number
          in: query
          description: '第几页，从0开始'
          schema:
            type: integer
        - name: pageSize
          in: query
          description: '每页大小，默认20'
          schema:
            type: integer
      responses:
        '200':
          description: 积分变更记录
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/MemberPointRecordResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:record:count":
    get:
      tags:
        - 积分
      summary: 查询积分变更记录总数
      x-visibility: "Service"
      operationId: findMemberPointRecordListCount
      parameters:
        - name: pointAccountId
          in: query
          description: 积分账户id
          required: true
          schema:
            type: integer
        - name: recordType
          in: query
          description: 记录类型
          schema:
            type: string
            enum:
              - "SEND"
              - "DELAY_SEND"
              - "EXPIRE"
              - "FREEZE"
              - "UNFREEZE"
              - "DEDUCT"
              - "ABOLISH"
              - "TIMER"
              - "RECALCULATE"
              - "SPECIAL_DEDUCT"
              - "SPECIAL_FREEZE"
              - "SPECIAL_UNFREEZE"
              - "SPECIAL_ABOLISH"
              - "MANUAL_ABOLISH"
              - "OPEN_FREEZE"
              - "OPEN_UNFREEZE"
              - "REVERSE_SEND"
              - "REVERSE_DEDUCT"
        - name: recordTypes
          in: query
          description: 记录类型列表
          schema:
            type: array
            items:
              type: string
              enum:
                - "SEND"
                - "DELAY_SEND"
                - "EXPIRE"
                - "FREEZE"
                - "UNFREEZE"
                - "DEDUCT"
                - "ABOLISH"
                - "TIMER"
                - "RECALCULATE"
                - "SPECIAL_DEDUCT"
                - "SPECIAL_FREEZE"
                - "SPECIAL_UNFREEZE"
                - "SPECIAL_ABOLISH"
                - "MANUAL_ABOLISH"
                - "OPEN_FREEZE"
                - "OPEN_UNFREEZE"
                - "REVERSE_SEND"
                - "REVERSE_DEDUCT"
        - name: status
          in: query
          description: 记录状态列表
          schema:
            type: array
            items:
              type: string
              enum:
                - "DELAY"
                - "DELAY_FROZEN"
                - "DELAY_ABOLISH"
                - "EXPIRE"
                - "VALID"
                - "FROZEN"
                - "USED"
                - "ABOLISH"
                - "FROZEN_ABOLISH"
        - name: memberId
          in: query
          description: 会员Id
          schema:
            type: string
        - name: shopId
          in: query
          description: 店铺Id
          schema:
            type: string
        - name: channelType
          in: query
          description: 渠道类型
          schema:
            type: string
        - name: startTime
          in: query
          description: 开始时间
          example: '2021-01-01T12:17:00+08:00'
          schema:
            type: string
            format: date-time
        - name: endTime
          in: query
          description: 结束时间
          example: '2021-01-01T12:17:00+08:00'
          schema:
            type: string
            format: date-time
        - name: traceId
          in: query
          description: 追溯Id
          schema:
            type: string
        - name: sortType
          in: query
          description: 排序类型
          schema:
            type: string
            enum:
              - "CREATED_DESC"
              - "CREATED_ASC"
        - name: changeMode
          in: query
          description: 变更方式
          schema:
            type: string
        - name: number
          in: query
          description: '第几页，从0开始'
          schema:
            type: integer
            default: 0
        - name: pageSize
          in: query
          description: '每页大小，默认20'
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: 积分变更记录
          content:
            application/json:
              schema:
                type: object
                required:
                  - totalElements
                properties:
                  totalElements:
                    type: integer
                    description: 总记录数
                    format: int64
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:record:detail":
    get:
      tags:
        - 积分
      summary: 查询积分变更记录详情
      x-visibility: "Service"
      operationId: findMemberPointRecordDetail
      parameters:
        - name: pointAccountId
          in: query
          description: 积分账户体系id
          required: true
          schema:
            type: string
        - name: recordId
          in: query
          description: 记录ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 积分变更记录详情
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/MemberPointRecordDetailResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:record:item":
    get:
      tags:
        - 积分
      summary: 查询积分变更记录明细
      x-visibility: "Service"
      operationId: findMemberPointRecordItem
      parameters:
        - name: pointAccountId
          in: query
          description: 积分账户体系id
          required: true
          schema:
            type: string
        - name: triggerId
          in: query
          description: 追溯Id(traceId)
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 积分变更记录明细
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/MemberPointRecordItemResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/point:api-definition":
    get:
      tags:
        - 积分
      summary: 查询积分API定义
      x-visibility: "Service"
      operationId: findPointApiDefinition
      parameters:
        - name: apiName
          in: query
          description: 接口名称
          required: true
          schema:
            type: string
            enum:
              - OPEN_POINT_MEMBER_RECORD
              - OPEN_GRADE_MEMBER_RECORD
      responses:
        '200':
          description: 积分API定义
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: object
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  # 等级
  "/loyalty-facade/v1/grade/import":
    post:
      tags:
        - 等级
      summary: 等级记录迁移导入
      x-visibility: "Service"
      operationId: importGrade
      parameters:
        - name: importId
          in: query
          required: true
          description: 导入Id
          schema:
            type: string
        - name: gradeHierarchyId
          in: query
          required: true
          description: 等级体系ID
          schema:
            type: integer
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                "$ref": "#/components/schemas/MemberGradeImportRequest"
      responses:
        '200':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 等级处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/grade/init":
    post:
      tags:
        - 等级
      summary: 初始化为最低等级, 仅当等级账户不存在时
      x-visibility: "External"
      operationId: modifyGrade
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberGradeInitRequest"
      responses:
        '200':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 等级处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/grade/modify":
    post:
      tags:
        - 等级
      summary: 修改会员等级
      x-visibility: "External"
      operationId: modifyGrade
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberGradeModifyRequest"
      responses:
        '200':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 等级处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/grade/modify:batch":
    post:
      tags:
        - 等级
      summary: 批量等级变更到最高或者最低
      x-visibility: "Service"
      operationId: batchModifyGrade
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberGradeBatchModifyRequest"
      responses:
        '200':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 等级处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/grade":
    get:
      tags:
        - 等级
      summary: 查询会员等级
      x-visibility: "Service"
      operationId: findGrade
      parameters:
        - name: gradeHierarchyId
          in: query
          description: 等级体系ID
          required: true
          schema:
            type: integer
        - name: memberId
          in: query
          description: 会员ID
          schema:
            type: string
        - name: gradeDefinitionId
          in: query
          description: 等级定义ID
          schema:
            type: integer
        - name: number
          in: query
          description: '第几页，从0开始'
          schema:
            type: integer
            default: 0
        - name: pageSize
          in: query
          description: '每页大小，默认20'
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: 会员等级列表
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/MemberGradeResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 等级处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/currentMemberGrade":
    get:
      tags:
        - 等级
      summary: 查询当前会员等级
      x-visibility: "External"
      operationId: findMemberGrade
      parameters:
        - name: gradeHierarchyId
          in: query
          description: 等级体系ID
          required: true
          schema:
            type: integer
        - name: memberId
          in: query
          description: 会员ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 当前会员等级
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/MemberGradeResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 等级处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/grade/recordPage":
    get:
      tags:
        - 等级
      summary: 查询会员等级变更记录
      x-visibility: "External"
      operationId: findMemberGradeRecordPage
      parameters:
        - name: gradeHierarchyId
          in: query
          description: 等级体系ID
          required: true
          schema:
            type: integer
        - name: memberId
          in: query
          description: 会员ID
          schema:
            type: string
        - name: channelType
          in: query
          description: 渠道类型
          schema:
            type: string
        - name: sortType
          in: query
          description: 排序类型
          schema:
            type: string
            enum:
              - "CREATED_DESC"
              - "CREATED_ASC"
        - name: recordType
          in: query
          description: 记录类型
          schema:
              type: string
              enum:
              - "UPGRADE"
              - "DEGRADE"
              - "HOLD_BACK_GRADE"
              - "GRADE_RECALCULATE"
        - name: currentGradeId
          in: query
          description: 当前等级定义ID
          schema:
            type: integer
        - name: originalGradeId
          in: query
          description: 原等级定义ID
          schema:
              type: integer
        - name: startTime
          in: query
          description: 记录创建时间筛选：大于等于此时间
          example: '2021-01-01T12:17:00+08:00'
          schema:
            type: string
            format: date-time
        - name: endTime
          in: query
          description: 记录创建时间筛选：小于等于此时间
          example: '2021-01-01T12:17:00+08:00'
          schema:
            type: string
            format: date-time
        - name: number
          in: query
          description: '第几页，从0开始'
          schema:
            type: integer
            default: 0
        - name: pageSize
          in: query
          description: '每页大小，默认20'
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: 会员等级变更记录
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/MemberGradeRecordPage"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 等级处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/grade/records":
    get:
      tags:
        - 等级
      summary: 查询会员等级变更记录
      x-visibility: "External"
      operationId: findMemberGradeRecords
      parameters:
        - name: gradeHierarchyId
          in: query
          description: 等级体系ID
          required: true
          schema:
            type: integer
        - name: memberId
          in: query
          description: 会员ID
          schema:
            type: string
        - name: channelType
          in: query
          description: 渠道类型
          schema:
            type: string
        - name: sortType
          in: query
          description: 排序类型
          schema:
            type: string
            enum:
              - "CREATED_DESC"
              - "CREATED_ASC"
        - name: recordType
          in: query
          description: 记录类型
          schema:
            type: string
            enum:
              - "UPGRADE"
              - "DEGRADE"
              - "HOLD_BACK_GRADE"
              - "GRADE_RECALCULATE"
        - name: currentGradeId
          in: query
          description: 当前等级定义ID
          schema:
            type: integer
        - name: originalGradeId
          in: query
          description: 原等级定义ID
          schema:
            type: integer
        - name: startTime
          in: query
          description: 记录创建时间筛选：大于等于此时间
          example: '2021-01-01T12:17:00+08:00'
          schema:
            type: string
            format: date-time
        - name: endTime
          in: query
          description: 记录创建时间筛选：小于等于此时间
          example: '2021-01-01T12:17:00+08:00'
          schema:
            type: string
            format: date-time
        - name: number
          in: query
          description: '第几页，从0开始'
          schema:
            type: integer
            default: 0
        - name: pageSize
          in: query
          description: '每页大小，默认20'
          schema:
            type: integer
            default: 20
      responses:
        '200':
          description: 会员等级变更记录
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/MemberGradeRecordResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 等级处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/grade/recordDetail":
    get:
      tags:
        - 等级
      summary: 查询会员等级变更记录详情
      x-visibility: "Service"
      operationId: findMemberGradeRecordDetail
      parameters:
        - name: gradeHierarchyId
          in: query
          description: 等级体系ID
          required: true
          schema:
            type: integer
        - name: recordId
          in: query
          description: 记录ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: 会员等级变更记录详情
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/MemberGradeRecordDetailResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 等级处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"

  #勋章
  "/loyalty-facade/v1/medal/obtain":
    post:
      tags:
        - 勋章
      summary: 颁发会员勋章
      x-visibility: "Service"
      operationId: medalObtain
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberMedalModifyRequest"
      responses:
        '200':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 勋章处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/medal/recycle":
    post:
      tags:
        - 勋章
      summary: 回收会员勋章
      x-visibility: "Service"
      operationId: medalRecycle
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberMedalRecycleRequest"
      responses:
        '200':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 勋章处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/medal/reset":
    post:
      tags:
        - 勋章
      summary: 重置会员勋章
      x-visibility: "Service"
      operationId: medalReset
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberMedalResetRequest"
      responses:
        '200':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 勋章处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/medal/batchRecycle":
    post:
      tags:
        - 勋章
      summary: 批量回收会员勋章
      x-visibility: "Service"
      operationId: batchMedalRecyc
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberMedalBatchRecycleRequest"
      responses:
        '200':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 勋章处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/medal/query":
    get:
      tags:
        - 勋章
      summary: 分页勋章查询
      x-visibility: "External"
      operationId: findMemberMedalQuery
      parameters:
        - name: medalHierarchyId
          in: query
          description: 勋章体系id
          required: true
          schema:
            type: integer
        - name: planId
          in: query
          description: 计划id
          required: true
          schema:
            type: integer
        - name: memberId
          in: query
          required: true
          description: 会员Id
          schema:
            type: string
        - name: number
          in: query
          description: '第几页，从0开始'
          schema:
            default: 0
            type: integer
        - name: pageSize
          in: query
          description: '每页大小，默认20'
          schema:
            default: 20
            type: integer
      responses:
        '200':
          description: 勋章记录
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/MemberMedalQueryResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 勋章处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/medal/record":
    get:
      tags:
        - 勋章
      summary: 分页勋章变更记录查询
      x-visibility: "External"
      operationId: findMemberMedalRecordQuery
      parameters:
        - name: medalHierarchyId
          in: query
          description: 勋章体系id
          required: true
          schema:
            type: integer
        - name: planId
          in: query
          description: 计划id
          required: true
          schema:
            type: integer
        - name: memberId
          in: query
          description: 会员Id
          required: true
          schema:
            type: string
        - name: medalDefinitionId
          in: query
          description: 勋章id
          schema:
            type: integer
        - name: channelType
          in: query
          description: 变更渠道
          schema:
            type: string
        - name: sortType
          in: query
          description: 排序方式
          schema:
            type: string
            enum:
              - "CREATED_DESC"
              - "CREATED_ASC"
        - name: recordType
          in: query
          description: 变更类型
          schema:
            type: string
            enum:
              - "OBTAIN"
              - "KEEP"
              - "RECYCLE"
        - name: startTime
          in: query
          description: 开始时间
          example: '2021-01-01T12:17:00+08:00'
          schema:
            type: string
            format: date-time
        - name: endTime
          in: query
          description: 结束时间
          example: '2021-01-01T12:17:00+08:00'
          schema:
            type: string
            format: date-time
        - name: number
          in: query
          description: '第几页，从0开始'
          schema:
            type: integer
        - name: pageSize
          in: query
          description: '每页大小，默认20'
          schema:
            type: integer
      responses:
        '200':
          description: 勋章记录
          content:
            application/json:
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/MemberMedalRecordResponse"
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 勋章处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"

  #合卡
  "/loyalty-facade/v1/merge":
    post:
      tags:
        - 合卡
      summary: 会员合卡
      x-visibility: "Service"
      operationId: memberMerge
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/MemberMergeRequest"
      responses:
        '200':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 会员合并处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/merge:point":
    post:
      tags:
        - 合卡
      summary: 会员积分合并
      x-visibility: "Service"
      operationId: memberMergePoint
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/OnlyMergePointRequest"
      responses:
        '200':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 积分合并处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/merge:grade":
    post:
      tags:
        - 合卡
      summary: 会员等级合并
      x-visibility: "Service"
      operationId: memberMergeGrade
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/OnlyMergeGradeRequest"
      responses:
        '200':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 等级合并处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
  "/loyalty-facade/v1/merge:medal":
    post:
      tags:
        - 合卡
      summary: 会员勋章合并
      x-visibility: "Service"
      operationId: memberMergeMedal
      requestBody:
        content:
          application/json:
            schema:
              "$ref": "#/components/schemas/OnlyMergeMedalRequest"
      responses:
        '200':
          description: 请求已接收
        '4XX':
          description: 请求参数错误
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
        '5XX':
          description: 勋章处理异常
          content:
            application/json:
              schema:
                "$ref": "#/components/schemas/ErrorResponse"
components:
  schemas:
    ErrorResponse:
      required:
        - module
        - msg
        - service
        - error_code
      type: object
      properties:
        error_code:
          type: string
          description: 错误码
          example: 060400
        msg:
          type: string
          description: 错误信息
          example: 参数不能为空
        module:
          type: string
          description: 模块
          example: loyalty4
        service:
          type: string
          description: 服务
          example: loyalty-facade
    PlanResponse:
      type: object
      properties:
        id:
          type: integer
          description: 计划ID
          format: int64
        name:
          type: string
          description: 计划名称
        sendLimitRuleSwitch:
          type: boolean
          description: 发放限制规则开关
        subjects:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                description: 主体ID
                format: int64
              name:
                type: string
                description: 主体名称
              fqn:
                type: string
                description: 主体FQN
              subjectEventTypes:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      description: 时机Id
                      format: int64
                    name:
                      type: string
                      description: 时机名称
                    eventStream:
                      type: string
                      description: 时机事件流
                      example: "event.loyalty.point.order.refund"
              pointAccountTypes:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      description: 积分账户类型ID
                      format: int64
                    name:
                      type: string
                      description: 积分账户类型名称
                    pointAccountType:
                      type: string
                      description: 积分账户类型
                      example: "POINT"
                    priorityDeduction:
                      type: string
                      description: 扣减优先级配置
                    negativeStrategy:
                      type: string
                      description: 负数策略
                      enum:
                        - "TO_ZERO"
                        - "TO_NEGATIVE"
                        - "NOT_ALLOWED"
                    topLimit:
                      type: number
                      description: 累计积分发放上限
                      format: double
                    singleTopLimit:
                      type: number
                      description: 单次积分发放上限
                      format: double
                    sendLimitRule:
                      type: string
                      description: 积分发放上限规则配置
                    precision:
                      type: integer
                      description: 积分精度
                      format: int64
                    unit:
                      type: string
                      description: 积分单位
                    rounding:
                      type: string
                      description: 积分取整方式
                      enum:
                        - "UP"
                        - "DOWN"
                        - "CEILING"
                        - "HALF_UP"
                        - "HALF_DOWN"
                        - "HALF_EVEN"
                        - "UNNECESSARY"
              gradeHierarchies:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      description: 等级体系ID
                      format: int64
                    name:
                      type: string
                      description: 等级体系名称
                    gradeDefinitions:
                      type: array
                      items:
                        type: object
                        properties:
                          id:
                            type: integer
                            description: 等级定义ID
                            format: int64
                          name:
                            type: string
                            description: 等级定义名称
                          sort:
                            type: integer
                            description: 排序
                            format: int64
              medalHierarchies:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      description: 勋章体系ID
                    name:
                      type: string
                      description: 勋章体系名称
                    medalDefinitions:
                      type: array
                      items:
                        type: object
                        properties:
                          id:
                            type: integer
                            description: 勋章定义ID
                            format: int64
                          name:
                            type: string
                            description: 勋章定义名称
                          sort:
                            type: integer
                            description: 排序
    MemberPointDeductRequest:
      required:
        - pointAccountId
        - memberId
        - point
        - channelType
      type: object
      properties:
        pointAccountId:
          type: integer
          description: 积分账户体系id
          format: int64
        memberId:
          type: string
          description: 会员id
        channelType:
          type: string
          description: 渠道类型
        shopId:
          type: string
          description: 店铺ID
        uniqueId:
          type: string
          description: 业务唯一id
        lockWaitTime:
          type: integer
          description: 锁等待时间(ms),为空使用配置中心，配置中心为空默认5000ms
          format: int64
        desc:
          type: string
          description: 描述
        actionName:
          type: string
          description: 活动名称
        actionId:
          description: 活动ID
          type: string
        actionNodeId:
          description: 发放节点id
          type: string
        actionNodeName:
          description: 发放节点名称
          type: string
        changeMode:
          type: string
          description: 变更方式
        point:
          type: number
          description: 要操作的积分
        kzzd1:
          type: string
          description: 扩展字段1
        kzzd2:
          type: string
          description: 扩展字段2
        kzzd3:
          type: string
          description: 扩展字段3
        tx:
          type: boolean
          description: 是否允许撤销，默认不允许
        triggerId:
          type: string
          description: 撤销业务Id，当tx=true时必传
      description: 积分扣减请求对象
    MemberPointSendRequest:
      required:
        - pointAccountId
        - memberId
        - uniqueId
        - channelType
        - point
      type: object
      description: 积分发放请求对象
      properties:
        pointAccountId:
          type: integer
          description: 积分账户id
          format: int64
        memberId:
          type: string
          description: 会员id
        channelType:
          type: string
          description: 渠道类型
        shopId:
          type: string
          description: 店铺Id
        uniqueId:
          type: string
          description: 业务唯一id
        lockWaitTime:
          type: integer
          description: 锁等待时间(ms),为空使用配置中心，配置中心为空默认5000ms
          format: int64
        desc:
          type: string
          description: 描述
        actionName:
          type: string
          description: 活动名称
        actionId:
          description: 活动ID
          type: string
        actionNodeId:
          description: 发放节点id
          type: string
        actionNodeName:
          description: 发放节点名称
          type: string
        changeMode:
          type: string
          description: 变更方式
        point:
          type: number
          description: 要操作的积分
        effectiveDate:
          type: string
          description: 积分生效时间
          format: date-time
        overdueDate:
          type: string
          description: 过期时间，永不过期或者扣减时不用传
          format: date-time
        kzzd1:
          type: string
          description: 扩展字段1
        kzzd2:
          type: string
          description: 扩展字段2
        kzzd3:
          type: string
          description: 扩展字段3
        tx:
          type: boolean
          description: 是否允许撤销，默认不允许
        triggerId:
          type: string
          description: 撤销业务Id，当tx=true时必传
    MemberPointSendResponse:
      required:
        - point
        - realPoint
      type: object
      properties:
        point:
          type: number
          description: 积分值
          format: double
        realPoint:
          type: number
          description: 实际积分值
          format: double
        type:
          type: string
          enum:
            - SEND
            - DELAY_SEND
          description: 积分类型
    MemberPointUseFrozenRequest:
      required:
        - channelType
        - memberId
        - pointAccountId
        - businessId
      type: object
      properties:
        memberId:
          type: string
          description: 会员id
        channelType:
          type: string
          description: 渠道类型
        pointAccountId:
          type: integer
          description: 积分账户id
          format: int64
        shopId:
          type: string
          description: 店铺ID
        businessId:
          type: string
          description: 业务唯一id
        lockWaitTime:
          type: integer
          description: 锁等待时间(ms),为空使用配置中心，配置中心为空默认5000ms
          format: int64
        desc:
          type: string
          description: 描述
        actionName:
          type: string
          description: 活动名称
        actionId:
          description: 活动ID
          type: string
        actionNodeId:
          description: 发放节点id
          type: string
        actionNodeName:
          description: 发放节点名称
          type: string
        changeMode:
          type: string
          description: 变更方式
        autoFillShopId:
          description: 是否获取冻结时店铺ID,默认false
          type: boolean
          default: false
        kzzd1:
          type: string
          description: 扩展字段1
        kzzd2:
          type: string
          description: 扩展字段2
        kzzd3:
          type: string
          description: 扩展字段3
        tx:
          type: boolean
          description: 是否允许撤销，默认不允许
    MemberPointUnfreezeRequest:
      required:
        - pointAccountId
        - memberId
        - businessId
        - channelType
      type: object
      description: 积分解冻请求对象
      properties:
        memberId:
          type: string
          description: 会员id
        channelType:
          type: string
          description: 渠道类型
        pointAccountId:
          type: integer
          description: 积分账户id
          format: int64
        shopId:
          type: string
          description: 店铺ID
        businessId:
          type: string
          description: 业务唯一id
        lockWaitTime:
          type: integer
          description: 锁等待时间(ms),为空使用配置中心，配置中心为空默认5000ms
          format: int64
        desc:
          type: string
          description: 描述
        actionName:
          type: string
          description: 活动名称
        actionId:
          description: 活动ID
          type: string
        actionNodeId:
          description: 发放节点id
          type: string
        actionNodeName:
          description: 发放节点名称
          type: string
        changeMode:
          type: string
          description: 变更方式
        autoFillShopId:
          description: 是否获取冻结时店铺ID,默认false
          type: boolean
        kzzd1:
          type: string
          description: 扩展字段1
        kzzd2:
          type: string
          description: 扩展字段2
        kzzd3:
          type: string
          description: 扩展字段3
    MemberPointUnfreezeResponse:
      required:
        - point
        - type
      type: object
      description: 积分解冻返回对象
      properties:
        point:
          type: number
          description: 积分值
          format: double
        type:
          type: string
          enum:
            - UNFREEZE
          description: 积分类型
    MemberPointFreezeRequest:
      required:
        - pointAccountId
        - memberId
        - point
        - channelType
      type: object
      properties:
        memberId:
          type: string
          description: 会员id
        channelType:
          type: string
          description: 渠道类型
        pointAccountId:
          type: integer
          description: 积分账户id
          format: int64
        shopId:
          type: string
          description: 店铺ID
        businessId:
          type: string
          description: 业务唯一id
        lockWaitTime:
          type: integer
          description: 锁等待时间(ms),为空使用配置中心，配置中心为空默认5000ms
          format: int64
        desc:
          type: string
          description: 描述
        actionName:
          type: string
          description: 活动名称
        actionId:
          description: 活动ID
          type: string
        actionNodeId:
          description: 发放节点id
          type: string
        actionNodeName:
          description: 发放节点名称
          type: string
        changeMode:
          type: string
          description: 变更方式
        point:
          type: number
          description: 要操作的积分
        kzzd1:
          type: string
          description: 扩展字段1
        kzzd2:
          type: string
          description: 扩展字段2
        kzzd3:
          type: string
          description: 扩展字段3
      description: 积分冻结
    MemberPointUseFrozenResponse:
      required:
        - point
        - type
      type: object
      description: 积分冻结消耗返回对象
      properties:
        point:
          type: number
          description: 积分值
          format: double
        type:
          type: string
          enum:
            - USE_FREEZE
          description: 积分类型
    MemberPointInitRequest:
      description: MemberPointInitRequest
      required:
        - pointAccountIds
        - memberId
        - channelType
      type: object
      properties:
        memberId:
          type: string
          description: 会员id
        channelType:
          type: string
          description: 渠道类型
        businessId:
          type: string
          description: 业务唯一id
        lockWaitTime:
          type: integer
          description: 锁等待时间(ms),为空使用配置中心，配置中心为空默认5000ms
          format: int64
        pointAccountIds:
          description: 积分账户ID集合
          type: integer
          format: int64
    MemberPointInitResponse:
      required:
        - memberPointInitResults
      type: object
      properties:
        memberPointInitResults:
          description: 积分账户初始化结果
          type: array
          items:
            "$ref": "#/components/schemas/MemberPointInitResult"
    MemberPointInitResult:
      required:
        - initResult
        - pointAccountId
      type: object
      properties:
        pointAccountId:
          description: 积分账户ID
          type: integer
          format: int64
        initResult:
          description: 初始化结果
          type: boolean
        desc:
          description: 初始化失败原因
          type: string
    MemberPointResponse:
      required:
        - point
      type: object
      properties:
        point:
          description: 积分值
          type: number
          format: double
    MemberPointBatchModifyRequest:
      title: MemberPointBatchModifyReq
      description: 批量变更会员积分请求对象
      required:
        - pointAccountId
        - recordType
        - triggerId
        - openPointBatchItems
      type: object
      properties:
        pointAccountId:
          description: 积分账户体系ID
          type: integer
          format: int64
        recordType:
          description: 批量动作类型
          type: string
          enum:
            - SEND
            - DEDUCT
        triggerId:
          description: 追溯ID
          type: string
        openPointBatchItems:
          description: 批量操作项
          type: array
          items:
            "$ref": "#/components/schemas/MemberPointBatchModifyItem"
    MemberPointBatchModifyItem:
      required:
        - memberId
        - point
        - channelType
      type: object
      properties:
        memberId:
          description: 会员ID
          type: string
        point:
          description: 积分值
          type: number
          format: double
        overdueDate:
          description: 积分过期时间
          type: string
          format: date-time
        desc:
          description: 描述
          type: string
        channelType:
          description: 渠道类型
          type: string
        effectDate:
          description: 积分生效时间
          type: string
          format: date-time
        shopId:
          description: 店铺ID
          type: string
        actionName:
          description: 活动名称
          type: string
        actionId:
          description: 活动ID
          type: string
        actionNodeId:
          description: 发放节点ID
          type: string
        actionNodeName:
          description: 发放节点名称
          type: string
        changeMode:
          description: 变更方式
          type: string
        kzzd1:
          description: 扩展字段1
          type: string
        kzzd2:
          description: 扩展字段2
          type: string
        kzzd3:
          description: 扩展字段3
          type: string
    MemberPointBatchModifyResponse:
      required:
        - openType
        - triggerId
        - processStatus
        - totalNumber
        - memberPointModifyBatchItemResponse
      type: object
      properties:
        openType:
          description: 类型
          type: string
          enum:
            - POINT
            - GRADE
            - MEDAL
        triggerId:
          description: 追溯ID
          type: string
        processStatus:
          description: 处理状态
          type: string
          enum:
            - WAIT
            - RUNNING
            - FAIL
            - SUCCESS
        totalNumber:
          description: 总数
          type: integer
          format: int64
        failNumber:
          description: 失败数
          type: integer
          format: int64
        successNumber:
          description: 成功数
          type: integer
          format: int64
        memberPointModifyBatchItemResponse:
          description: 批量操作项
          type: array
          items:
            "$ref": "#/components/schemas/MemberPointModifyBatchItemResponse"
    MemberPointModifyBatchItemResponse:
      required:
        - memberId
      type: object
      properties:
        memberId:
          description: 会员ID
          type: string
        responseErrorData:
          description: 错误信息
          type: string
    MemberPointRevertRequest:
      required:
        - pointAccountId
        - subMemberIds
        - channelType
        - uniqueId
        - tradeId
      type: object
      properties:
        pointAccountId:
          description: 积分账户ID
          type: integer
          format: int64
        memberId:
          description: 会员ID
          type: string
        recordType:
          description: 操作类型
          type: string
          enum:
            - SEND
            - DEDUCT
            - USE_FREEZE
        channelType:
          description: 渠道类型
          type: string
        point:
          description: 积分值
          type: number
          format: double
        changeMode:
          description: 变更方式
          type: string
        desc:
          description: 描述
          type: string
        uniqueId:
          description: 业务唯一ID
          type: string
        tradeId:
          description: 正向交易ID
          type: string
        shopId:
          description: 店铺ID
          type: string
        actionName:
          description: 活动名称
          type: string
        actionId:
          description: 活动ID
          type: string
        actionNodeId:
          description: 发放节点ID
          type: string
        actionNodeName:
          description: 发放节点名称
          type: string
        kzzd1:
          description: 扩展字段1
          type: string
        kzzd2:
          description: 扩展字段2
          type: string
        kzzd3:
          description: 扩展字段3
          type: string
    MemberPointRevertResponse:
      type: object
      properties:
        requestId:
          description: 请求ID
          type: string
    MemberPointImportRequest:
      required:
        - memberId
        - action
        - point
        - createdDate
        - key
      type: object
      properties:
        memberId:
          description: 会员ID
          type: string
        action:
          description: 动作类型
          type: string
          enum:
            - SEND
            - DEDUCT
        channelType:
          description: 渠道类型
          type: string
        point:
          description: 积分值
          type: number
          format: double
        effectiveDate:
          description: 生效时间
          type: string
          format: date-time
        overdueDate:
          description: 过期时间
          type: string
          format: date-time
        key:
          description: 唯一Id
          type: string
        description:
          description: 描述
          type: string
        createdDate:
          description: 创建时间
          type: string
          format: date-time
        shopId:
          description: 店铺ID
          type: string
        kzzd1:
          description: 扩展字段1
          type: string
        kzzd2:
          description: 扩展字段2
          type: string
        kzzd3:
          description: 扩展字段3
          type: string
    MemberPointImportResult:
      required:
        - progress
      type: object
      properties:
        progress:
          description: 进度
          type: string
          example: "已完成"
    MemberPointItemResponse:
      required:
        - planId
        - planName
        - subjectId
        - subjectName
        - subjectFqn
        - pointAccountTypeId
        - pointAccountTypeName
        - memberId
        - point
      type: object
      properties:
        planId:
          description: 计划ID
          type: integer
          format: int64
        planName:
          description: 计划名称
          type: string
        subjectId:
          description: 主体ID
          type: integer
          format: int64
        subjectName:
          description: 主体名称
          type: string
        subjectFqn:
          description: 主体模型FQN
          type: string
        pointAccountTypeId:
          description: 积分账户类型ID
          type: integer
          format: int64
        pointAccountTypeName:
          description: 积分账户类型名称
          type: string
        memberId:
          description: 会员ID
          type: string
        point:
          description: 积分值
          type: number
          format: double
    MemberValidPointResponse:
      required:
        - id
        - planId
        - pointPlanId
        - memberPointId
        - memberId
        - point
        - gainStatementId
        - effectiveDate
        - fromStatus
        - created
        - modified
      type: object
      properties:
        id:
          description: ID
          type: integer
          format: int64
        planId:
          description: 计划ID
          type: integer
          format: int64
        pointPlanId:
          description: 积分账户体系ID
          type: integer
          format: int64
        memberPointId:
          description: 会员积分账户ID
          type: integer
          format: int64
        memberId:
          description: 会员ID
          type: string
        point:
          description: 积分值
          type: number
          format: double
        gainStatementId:
          description: 积分获取记录ID
          type: integer
          format: int64
        effectiveDate:
          description: 生效时间
          type: string
          format: date-time
        overdueDate:
          description: 过期时间
          type: string
          format: date-time
        fromStatus:
          description: 状态
          type: string
          enum:
            - START
            - VALID
            - FURTHER_VALID
            - FROZEN
            - EXPIRED
            - USED
            - SPECIAL_FROZE
            - SPECIAL_ABOLISH
            - OPEN_FROZE
        created:
          description: 创建时间
          type: string
          format: date-time
        modified:
          description: 修改时间
          type: string
          format: date-time
    PointPage:
      required:
        - totalElements
        - totalPages
        - number
        - size
        - content
      type: object
      properties:
        totalElements:
          description: 总数
          type: integer
          format: int64
        totalPages:
          description: 总页数
          type: integer
          format: int64
        number:
          description: 当前页
          type: integer
          format: int64
        size:
          description: 每页大小
          type: integer
          format: int64
        content:
          description: 积分变更记录
          type: array
          items:
            "$ref": "#/components/schemas/MemberPointRecordResponse"
    MemberPointRecordResponse:
      type: object
      properties:
        id:
          description: "id主键"
          type: string
        planId:
          description: "计划Id"
          type: integer
          format: int64
        planName:
          description: "计划名称"
          type: string
        subjectId:
          description: "主体Id"
          type: integer
          format: int64
        subjectName:
          description: "主体名称"
          type: string
        subjectFqn:
          description: "主体FQN"
          type: string
        pointAccountTypeId:
          description: "积分账户Id"
          type: integer
          format: int64
        pointAccountTypeName:
          description: "积分账户名称"
          type: string
        memberId:
          description: "会员ID"
          type: string
        point:
          description: "变更积分"
          type: number
        recordType:
          description: "触发动作"
          type: string
          enum:
            - "SEND"
            - "DELAY_SEND"
            - "EXPIRE"
            - "FREEZE"
            - "UNFREEZE"
            - "DEDUCT"
            - "ABOLISH"
            - "TIMER"
            - "RECALCULATE"
            - "SPECIAL_DEDUCT"
            - "SPECIAL_FREEZE"
            - "SPECIAL_UNFREEZE"
            - "SPECIAL_ABOLISH"
            - "MANUAL_ABOLISH"
            - "OPEN_FREEZE"
            - "OPEN_UNFREEZE"
            - "REVERSE_SEND"
            - "REVERSE_DEDUCT"
        effectiveDate:
          description: "积分生效时间"
          type: string
          format: date-time
        overdueDate:
          description: "积分过期时间"
          type: string
          format: date-time
        desc:
          description: "备注信息"
          type: string
        totalPoint:
          description: "历史总积分"
          type: number
        created:
          description: "变更时间"
          type: string
          format: date-time
        changeMode:
          description: "变更方式"
          type: string
        traceId:
          description: "原单Id"
          type: string
        key:
          description: "请求唯一Id"
          type: string
        channelType:
          description: "渠道"
          type: string
        shopId:
          description: "店铺Id"
          type: string
        actionName:
          description: "活动名称"
          type: string
        status:
          description: "状态"
          type: string
          enum:
            - "DELAY"
            - "DELAY_FROZEN"
            - "DELAY_ABOLISH"
            - "EXPIRE"
            - "VALID"
            - "FROZEN"
            - "USED"
            - "ABOLISH"
            - "FROZEN_ABOLISH"
        ruleId:
          description: "规则Id"
          type: integer
          format: int64
        ruleName:
          description: "规则名称"
          type: string
        eventTypeName:
          description: "时机名称"
          type: string
        signedPoint:
          description: "变更积分"
          type: number
        kzzd1:
          description: "扩展字段1"
          type: string
        kzzd2:
          description: "扩展字段2"
          type: string
        kzzd3:
          description: "扩展字段3"
          type: string
    MemberPointRecordDetailResponse:
      type: object
      properties:
        planId:
          description: "计划ID"
          type: integer
          format: int64
        planName:
          description: "计划名称"
          type: string
        subjectFqn:
          description: "主体FQN"
          type: string
        pointPlanId:
          description: "积分账户体系ID"
          type: integer
          format: int64
        pointPlanName:
          description: "积分账户体系名称"
          type: string
        memberPointId:
          description: "会员积分账户ID"
          type: integer
          format: int64
        memberId:
          description: "会员ID"
          type: string
        point:
          description: "变更积分"
          type: number
        effectiveDate:
          description: "积分生效时间"
          type: string
          format: date-time
        overdueDate:
          description: "积分过期时间"
          type: string
          format: date-time
        traceId:
          description: "原单Id"
          type: string
        totalPoint:
          description: "总积分"
          type: number
        recordType:
          description: "触发动作"
          type: string
          enum:
            - "SEND"
            - "DELAY_SEND"
            - "EXPIRE"
            - "FREEZE"
            - "UNFREEZE"
            - "DEDUCT"
            - "ABOLISH"
            - "TIMER"
            - "RECALCULATE"
            - "SPECIAL_DEDUCT"
            - "SPECIAL_FREEZE"
            - "SPECIAL_UNFREEZE"
            - "SPECIAL_ABOLISH"
            - "MANUAL_ABOLISH"
            - "OPEN_FREEZE"
            - "OPEN_UNFREEZE"
            - "REVERSE_SEND"
            - "REVERSE_DEDUCT"
        desc:
          description: "备注信息"
          type: string
        key:
          description: "请求唯一Id"
          type: string
        ruleGroup:
          description: "规则组"
          type: string
        changeMode:
          description: "变更方式"
          type: string
        channel:
          description: "渠道"
          type: string
        operatorId:
          description: "操作人ID"
          type: string
        operator:
          description: "操作人"
          type: string
        created:
          description: "创建时间"
          type: string
          format: date-time
        modified:
          description: "修改时间"
          type: string
          format: date-time
    MemberPointRecordItemResponse:
      type: object
      properties:
        planId:
          description: "计划ID"
          type: integer
          format: int64
        pointPlanId:
          description: "积分账户体系ID"
          type: integer
          format: int64
        traceId:
          description: "原单Id"
          type: string
        memberId:
          description: "会员ID"
          type: string
        point:
          description: "变更积分"
          type: number
          format: double
        created:
          description: "创建时间"
          type: string
          format: date-time
        effectiveDate:
          description: "积分生效时间"
          type: string
          format: date-time
        overdueDate:
          description: "积分过期时间"
          type: string
          format: date-time
        sort:
          description: "排序"
          type: integer
          format: int64
        status:
          description: "状态"
          type: string
    # 等级
    MemberGradeImportRequest:
      required:
        - memberId
        - targetGradeDefinitionId
        - description
        - createdDate
        - key
        - channelType
      type: object
      properties:
        memberId:
          description: 会员ID
          type: string
        overdueDate:
          description: 过期时间
          type: string
          format: date-time
        targetGradeDefinitionId:
          description: 目标等级定义ID
          type: integer
          format: int64
        description:
          description: 描述
          type: string
        createdDate:
          description: 创建时间
          type: string
          format: date-time
        key:
          description: 唯一Id
          type: string
        channelType:
          description: 渠道类型
          type: string
    MemberGradeInitRequest:
      required:
        - gradeHierarchyId
        - memberId
        - overdueDate
        - description
        - channelType
        - triggerId
      type: object
      properties:
        gradeHierarchyId:
          description: 等级体系ID
          type: integer
          format: int64
        memberId:
          description: 会员ID
          type: string
        overdueDate:
          description: 过期时间
          type: string
          format: date-time
        description:
          description: 描述
          type: string
        channelType:
          description: 渠道类型
          type: string
        triggerId:
          description: 追溯ID
          type: string
        changeWayType:
          description: 变更方式
          type: string
    MemberGradeModifyRequest:
      required:
        - gradeHierarchyId
        - memberId
        - gradeDefinitionId
        - overdueDate
        - description
        - channelType
        - triggerId
      type: object
      properties:
        gradeHierarchyId:
          description: 等级体系ID
          type: integer
          format: int64
        memberId:
          description: 会员ID
          type: string
        gradeDefinitionId:
          description: 等级定义ID
          type: integer
          format: int64
        overdueDate:
          description: 过期时间
          type: string
          format: date-time
        description:
          description: 描述
          type: string
        useOriginalEffectTime:
          description: 是否使用原生效时间
          type: boolean
          default: false
        channelType:
          description: 渠道类型
          type: string
        triggerId:
          description: 追溯ID
          type: string
        changeWayType:
          description: 变更方式
          type: string
    MemberGradeBatchModifyRequest:
      required:
        - gradeHierarchyIds
        - memberId
        - channelType
        - triggerId
        - changeWayType
        - gradeModifyType
        - description
      type: object
      properties:
        gradeHierarchyIds:
          description: 等级体系ID列表
          type: array
          items:
            type: integer
            format: int64
        memberId:
          description: 会员ID
          type: string
        channelType:
          description: 渠道类型
          type: string
        triggerId:
          description: 追溯ID
          type: string
        changeWayType:
          description: 变更方式
          type: string
        gradeModifyType:
          description: 变更类型
          type: string
          enum:
            - "LOWEST_GRADE"
            - "HIGHEST_GRADE"
            - "NO_GRADE"
        overdueDate:
          description: 过期时间
          type: string
          format: date-time
        description:
          description: 描述
          type: string
    MemberGradeResponse:
      type: object
      properties:
        planId:
          description: 计划ID
          type: integer
          format: int64
        planName:
          description: 计划名称
          type: string
        subjectId:
          description: 主体ID
          type: integer
          format: int64
        subjectName:
          description: 主体名称
          type: string
        subjectFqn:
          description: 主体模型FQN
          type: string
        gradeHierarchyId:
          description: 等级体系ID
          type: integer
          format: int64
        gradeHierarchyName:
          description: 等级体系名称
          type: string
        memberId:
          description: 会员ID
          type: string
        currentGradeDefinitionId:
          description: 当前等级定义ID
          type: integer
          format: int64
        gradeDefinitionName:
          description: 等级定义名称
          type: string
        effectDate:
          description: 生效时间
          type: string
          format: date-time
        overdueDate:
          description: 过期时间
          type: string
          format: date-time
    MemberGradeRecordPage:
        required:
            - number
            - size
            - content
        type: object
        properties:
            totalElements:
              description: 总数
              type: integer
              format: int64
            totalPages:
              description: 总页数
              type: integer
              format: int64
            number:
              description: 当前页
              type: integer
              format: int64
            size:
              description: 每页大小
              type: integer
              format: int64
            content:
              description: 等级变更记录
              type: array
              items:
                  "$ref": "#/components/schemas/MemberGradeRecordResponse"
    MemberGradeRecordResponse:
      type: object
      properties:
        planId:
          description: "计划Id"
          type: integer
          format: int64
        planName:
          description: "计划名称"
          type: string
        subjectId:
          description: "主体Id"
          type: integer
          format: int64
        subjectName:
          description: "主体名称"
          type: string
        subjectFqn:
          description: "主体FQN"
          type: string
        gradeHierarchyId:
          description: "等级体系Id"
          type: integer
          format: int64
        gradeHierarchyName:
          description: "等级体系名称"
          type: string
        memberId:
          description: "会员ID"
          type: string
        originalGradeId:
          description: "原等级定义ID"
          type: integer
          format: int64
        originalGradeName:
          description: "原等级定义名称"
          type: string
        originalEffectDate:
          description: "原生效时间"
          type: string
          format: date-time
        originalOverdueDate:
          description: "原过期时间"
          type: string
          format: date-time
        currentGradeId:
          description: "当前等级定义ID"
          type: integer
          format: int64
        currentGradeName:
          description: "当前等级定义名称"
          type: string
        currentEffectDate:
          description: "当前生效时间"
          type: string
          format: date-time
        currentOverdueDate:
          description: "当前过期时间"
          type: string
          format: date-time
        recordType:
          description: "触发动作"
          type: string
        description:
          description: "备注信息"
          type: string
        created:
          description: "创建时间"
          type: string
          format: date-time
    MemberGradeRecordDetailResponse:
      type: object
      properties:
        id:
          description: "id主键"
          type: string
        planId:
          description: "计划Id"
          type: integer
          format: int64
        planName:
          description: "计划名称"
          type: string
        gradeHierarchyId:
          description: "等级体系Id"
          type: integer
          format: int64
        gradeHierarchyName:
          description: "等级体系名称"
          type: string
        memberGradeId:
          description: "会员等级ID"
          type: integer
          format: int64
        memberId:
          description: "会员ID"
          type: string
        originalGradeId:
          description: "原等级定义ID"
          type: integer
          format: int64
        originalGradeName:
          description: "原等级定义名称"
          type: string
        originalEffectDate:
          description: "原生效时间"
          type: string
          format: date-time
        originalOverdueDate:
          description: "原过期时间"
          type: string
          format: date-time
        currentGradeId:
          description: "当前等级定义ID"
          type: integer
          format: int64
        currentGradeName:
          description: "当前等级定义名称"
          type: string
        currentEffectDate:
          description: "当前生效时间"
          type: string
          format: date-time
        currentOverdueDate:
          description: "当前过期时间"
          type: string
          format: date-time
        recordType:
          description: "触发动作"
          type: string
        changeWay:
          description: "变更方式"
          type: string
        triggerId:
          description: "追溯ID"
          type: string
        traceId:
          description: "原单Id"
          type: string
        recordSourceDetail:
          description: "记录来源"
          type: string
        subjectFqn:
          description: "主体FQN"
          type: string
        description:
          description: "备注信息"
          type: string
        created:
          description: "创建时间"
          type: string
          format: date-time
        channel:
          description: "渠道"
          type: string
        operator:
          description: "操作人"
          type: string
        extraInfo:
          description: "扩展信息"
          type: string
        recordDetail:
          description: "记录详情"
          type: string
    #勋章
    MemberMedalModifyRequest:
      required:
        - memberId
        - medalDefinitionId
        - channelType
        - medalHierarchyId
      type: object
      properties:
        memberId:
          description: 会员Id
          type: string
        medalDefinitionId:
          description: 勋章id
          type: integer
          format: int64
        medalHierarchyId:
          description: 勋章体系id
          type: integer
          format: int64
        overdueDate:
          description: 勋章过期时间
          type: string
          format: date-time
        channelType:
          description: 变更渠道
          type: string
        description:
          description: 备注
          type: string
        kzzd1:
          description: 扩展字段1
          type: string
        kzzd2:
          description: 扩展字段2
          type: string
        kzzd3:
          description: 扩展字段3
          type: string
    MemberMedalRecycleRequest:
      required:
        - memberId
        - medalDefinitionId
        - channelType
        - medalHierarchyId
      type: object
      properties:
        memberId:
          description: 会员Id
          type: string
        medalDefinitionId:
          description: 勋章id
          type: integer
          format: int64
        medalHierarchyId:
          description: 勋章体系id
          type: integer
          format: int64
        channelType:
          description: 变更渠道
          type: string
        description:
          description: 备注
          type: string
        kzzd1:
          description: 扩展字段1
          type: string
        kzzd2:
          description: 扩展字段2
          type: string
        kzzd3:
          description: 扩展字段3
          type: string
    MemberMedalResetRequest:
      required:
        - memberId
        - medalDefinitionId
        - channelType
        - medalHierarchyId
      type: object
      properties:
        memberId:
          description: 会员Id
          type: string
        medalDefinitionId:
          description: 勋章id
          type: integer
          format: int64
        medalHierarchyId:
          description: 勋章体系id
          type: integer
          format: int64
        overdueDate:
          description: 勋章过期时间
          type: string
          format: date-time
        channelType:
          description: 变更渠道
          type: string
        description:
          description: 备注
          type: string
        kzzd1:
          description: 扩展字段1
          type: string
        kzzd2:
          description: 扩展字段2
          type: string
        kzzd3:
          description: 扩展字段3
          type: string
        requestType:
          description: 变更方式 SYNC同步 ASYNC 异步 默认同步
          type: string
    MemberMedalBatchRecycleRequest:
      required:
        - memberId
        - channelType
        - medalHierarchyIds
      type: object
      properties:
        memberId:
          description: 会员Id
          type: string
        medalHierarchyIds:
          description: 勋章体系集合
          type: array
          items:
            type: integer
            format: int64
        channelType:
          description: 变更渠道
          type: string
        description:
          description: 备注
          type: string
        kzzd1:
          description: 扩展字段1
          type: string
        kzzd2:
          description: 扩展字段2
          type: string
        kzzd3:
          description: 扩展字段3
          type: string
        triggerId:
          description: 业务id
          type: string
        changeWayType:
          description: 变更方式
          type: string
          enum:
            - INTERFACE
            - OBTAIN
            - KEEP
            - MANUAL
    MemberMedalQueryResponse:
      type: object
      properties:
        planId:
          description: "计划Id"
          type: integer
          format: int64
        planName:
          description: "计划名称"
          type: string
        subjectId:
          description: "主体Id"
          type: integer
          format: int64
        subjectName:
          description: "主体名称"
          type: string
        subjectFqn:
          description: "主体FQN"
          type: string
        medalHierarchyId:
          description: "勋章体系Id"
          type: integer
          format: int64
        medalHierarchyName:
          description: "勋章体系名称"
          type: string
        memberId:
          description: "会员ID"
          type: string
        medalDefinitionId:
          description: "勋章Id"
          type: integer
          format: int64
        medalDefinitionName:
          description: "勋章名称"
          type: string
        effectDate:
          description: "勋章生效时间"
          type: string
          format: date-time
        overdueDate:
          description: "勋章过期时间"
          type: string
          format: date-time
    MemberMedalRecordResponse:
      type: object
      properties:
        planId:
          description: "计划Id"
          type: integer
          format: int64
        planName:
          description: "计划名称"
          type: string
        medalHierarchyId:
          description: "勋章体系Id"
          type: integer
          format: int64
        medalHierarchyName:
          description: "勋章体系名称"
          type: string
        memberId:
          description: "会员ID"
          type: string
        medalDefinitionId:
          description: "勋章Id"
          type: integer
          format: int64
        medalDefinitionName:
          description: "勋章名称"
          type: string
        currentOverdueDate:
          description: "当前过期时间"
          type: string
          format: date-time
        created:
          description: "变更时间"
          type: string
          format: date-time
        recordType:
          description: "变更类型"
          type: string
          enum:
            - "OBTAIN"
            - "KEEP"
            - "RECYCLE"
        channelType:
          description: "渠道"
          type: string
        ruleName:
          description: "规则名称"
          type: string
        description:
          description: "备注"
          type: string
        changeWayType:
          description: "变更方式"
          type: string
        kzzd1:
          description: "扩展字段1"
          type: string
        kzzd2:
          description: "扩展字段2"
          type: string
        kzzd3:
          description: "扩展字段3"
          type: string
    #合卡
    MemberMergeRequest:
      required:
        - memberId
        - subMemberIds
        - triggerId
      type: object
      properties:
        memberId:
          description: 会员ID
          type: string
        subMemberIds:
          description: 合并会员ID集合
          type: array
          items:
            type: string
        triggerId:
          description: 业务唯一ID
          type: string
        channelType:
          description: 渠道
          type: string
          default: loyalty
        mergePointRequest:
          "$ref": "#/components/schemas/MergePointRequest"
        mergeGradeRequest:
          "$ref": "#/components/schemas/MergeGradeRequest"
        mergeMedalRequest:
          "$ref": "#/components/schemas/MergeMedalRequest"
        synchronized:
          description: 是否同步执行接口 默认true
          type: boolean
          default: true
        generateMergeRecord:
          description: 是否生成合卡记录 默认true
          type: boolean
          default: true
        description:
          description: 备注
          type: string
        txType:
          description: 积分等级勋章是否同一事务执行  WHOLE 同一事务，INDIVIDUAL 积分等级勋章独立事务执行 默认 WHOLE
          type: string
          default: WHOLE
          enum:
            - WHOLE
            - INDIVIDUAL
    OnlyMergePointRequest:
      required:
        - memberId
        - subMemberIds
        - triggerId
        - mergePointRequest
      type: object
      properties:
        memberId:
          description: 会员ID
          type: string
        subMemberIds:
          description: 合并会员ID集合
          type: array
          items:
            type: string
        triggerId:
          description: 业务唯一ID
          type: string
        mergePointRequest:
          "$ref": "#/components/schemas/MergePointRequest"
        channelType:
          description: 渠道
          type: string
          default: loyalty
        generateMergeRecord:
          description: 是否生成合卡记录 默认true
          type: boolean
        description:
          description: 备注
          type: string
    OnlyMergeGradeRequest:
      required:
        - memberId
        - subMemberIds
        - triggerId
        - mergeGradeRequest
      type: object
      properties:
        memberId:
          description: 会员ID
          type: string
        subMemberIds:
          description: 合并会员ID集合
          type: array
          items:
            type: string
        triggerId:
          description: 业务唯一ID
          type: string
        mergeGradeRequest:
          "$ref": "#/components/schemas/MergeGradeRequest"
        channelType:
          description: 渠道
          type: string
          default: loyalty
        generateMergeRecord:
          description: 是否生成合卡记录 默认true
          default: true
          type: boolean
        description:
          description: 备注
          type: string
    OnlyMergeMedalRequest:
      required:
        - memberId
        - subMemberIds
        - triggerId
        - mergeMedalRequest
      type: object
      properties:
        memberId:
          description: 会员ID
          type: string
        subMemberIds:
          description: 合并会员ID集合
          type: array
          items:
            type: string
        triggerId:
          description: 业务唯一ID
          type: string
        mergeMedalRequest:
          "$ref": "#/components/schemas/MergeMedalRequest"
        channelType:
          description: 渠道
          type: string
          default: loyalty
        generateMergeRecord:
          description: 是否生成合卡记录 默认true
          type: boolean
          default: true
        description:
          description: 备注
          type: string
    MergePointRequest:
      type: object
      required:
        - pointPlanIds
      properties:
        pointPlanIds:
          type: array
          description: 积分账号id集合
          items:
            type: integer
            format: int64
        deleteSubMemberPointAccount:
          type: boolean
          default: true
          description: 是否删除副卡积分账号 默认 true 删除
    MergeGradeRequest:
      type: object
      required:
        - gradeHierarchyIds
        - gradeMergeType
      properties:
        gradeHierarchyIds:
          type: array
          description: 等级体系id集合
          items:
            type: integer
            format: int64
        gradeMergeType:
          description: 等级合并规则类型 HIGHEST:取主副中最高等级 RECALCULATE:基于等级重算规则重算
          type: string
          enum:
            - HIGHEST
            - RECALCULATE
        useHighestEffectTimeType:
          description: 取高后等级生效时间 RESET:取系统当前时间 UNCHANGE:取主卡有效期 默认RESET
          type: string
          default: RESET
          enum:
            - RESET
            - UNCHANGE
        deleteSubMemberGradeAccount:
          type: boolean
          default: false
          description: 是否删除副卡等级 默认 false 不删除
    MergeMedalRequest:
      type: object
      required:
          - medalHierarchyIds
      properties:
        medalHierarchyIds:
          type: array
          description: 勋章体系id集合
          items:
            type: integer
            format: int64
        deleteSubMemberMedalAccount:
          type: boolean
          default: false
          description: 是否删除副卡勋章 默认 false 不删除