<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.shuyun.loyalty4</groupId>
        <artifactId>loyalty-engine</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>loyalty-service</artifactId>
    <packaging>jar</packaging>

    <properties>
        <test.arg.SERVICE_NAME>loyalty-stream</test.arg.SERVICE_NAME>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-bom</artifactId>
                <version>${log4j2.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-bom</artifactId>
                <version>${slf4j.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>pip-feign-client-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>${redisson.version}</version>
        </dependency>


        <dependency>
            <groupId>com.shuyun.kylin.es</groupId>
            <artifactId>es-sdk</artifactId>
        </dependency>


        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>mybatis-plus-ds-boot-starter</artifactId>
            <version>${pip.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuyun.dm</groupId>
                    <artifactId>dm-metadata-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.shuyun.dm</groupId>
                    <artifactId>dm-dataapi-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!--判断文件编码-->
        <dependency>
            <groupId>com.github.albfernandez</groupId>
            <artifactId>juniversalchardet</artifactId>
        </dependency>

        <!-- https://search.maven.org/artifact/com.github.f4b6a3/ulid-creator -->
        <dependency>
            <groupId>com.github.f4b6a3</groupId>
            <artifactId>tsid-creator</artifactId>
        </dependency>

        <!-- ds -->
        <dependency>
            <groupId>com.shuyun.dm</groupId>
            <artifactId>dm-dataapi-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.shuyun.dm</groupId>
                    <artifactId>dm-metadata-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuyun.dm</groupId>
            <artifactId>dm-metadata-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dm-sdk-common</artifactId>
                    <groupId>com.shuyun.dm</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>dm-pool</artifactId>
            <version>${pip.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuyun.dm</groupId>
                    <artifactId>dm-metadata-sdk</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.shuyun.dm</groupId>
                    <artifactId>dm-dataapi-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.shuyun.fx</groupId>
            <artifactId>fx-box</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fulgur-exception</artifactId>
                    <groupId>org.maqian.fulgur</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.shuyun.kylin.crm</groupId>
            <artifactId>data-selector-sdk</artifactId>
            <version>${data-selector.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-json</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- kafka -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-stream</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.kafka</groupId>
            <artifactId>kafka-clients</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.lz4</groupId>
                    <artifactId>lz4-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-stream-binder-kafka</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.lz4</groupId>
                    <artifactId>lz4-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.kafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--强制走数据库主节点-->
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>druid-force-master</artifactId>
            <version>${pip.version}</version>
        </dependency>
        <!--多租户sql重写-->
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>druid-tenant</artifactId>
            <version>${pip.version}</version>
        </dependency>
        <!--starter-->
        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>druid-tenant-stater</artifactId>
            <version>${pip.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuyun.pip</groupId>
            <artifactId>pip-exception</artifactId>
            <version>${pip.version}</version>
        </dependency>

        <dependency>
            <groupId>com.shuyun</groupId>
            <artifactId>eawf-sdk</artifactId>
            <version>${eawf.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.shuyun.pip</groupId>
                    <artifactId>pip-dependencies</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>powermock-reflect</artifactId>
                    <groupId>org.powermock</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>powermock-core</artifactId>
                    <groupId>org.powermock</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.jetbrains.kotlin</groupId>
                    <artifactId>kotlin-test-junit</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
        </dependency>

        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
        </dependency>

        <dependency>
            <groupId>org.objenesis</groupId>
            <artifactId>objenesis</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.shuyun</groupId>
            <artifactId>motor-common</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.transaction</groupId>
            <artifactId>javax.transaction-api</artifactId>
        </dependency>

        <dependency>
            <groupId>javax.persistence</groupId>
            <artifactId>javax.persistence-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.shuyun.loyalty4</groupId>
            <artifactId>loyalty-sdk-ds</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j2-impl</artifactId>
        </dependency>

        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-core</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
        </dependency>

    </dependencies>
</project>