package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.meta.PropertyBelongerTypeEnum
import com.shuyun.loyalty.service.model.PropertyTemplate
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.util.converter.ZonedDateTime2StringConverter
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component
import java.time.ZonedDateTime

@Component
class PropertyTemplateRepository : DataModelCarryRepository<PropertyTemplate>() {

    private val log = LogManager.getLogger(PropertyTemplateRepository::class.java)
    override fun log(): Logger = log

    fun findByBelongerVersionIdAndBelongerTypeAndDisabled(belongerVersionId: Long, belongerType: PropertyBelongerTypeEnum, disabled: Boolean): List<PropertyTemplate>{
        val params = HashMap<String, Any?>()
        params["belongerVersionId"] = belongerVersionId
        params["belongerType"] = belongerType.name
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params))
    }


    fun findByVersionNameList(i18n: String, updateTime: ZonedDateTime): List<PropertyTemplate> {
        log.debug("findByVersionNameList i18n: {}, updateTime: {}", i18n, updateTime)
        val sql =
            "select id,name,versionId,updateTime from data.loyalty.manager.propertyTemplate where status != 'DRAFT' and updateTime <= :updateTime order by updateTime desc "
        val params = HashMap<String, Any>()
        params["updateTime"] = ZonedDateTime2StringConverter().convert(updateTime)!!
        return executeList(sql, params)
    }

}