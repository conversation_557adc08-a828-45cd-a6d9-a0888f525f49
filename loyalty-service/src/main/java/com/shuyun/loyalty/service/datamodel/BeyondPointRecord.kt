package com.shuyun.loyalty.service.datamodel

import com.fasterxml.jackson.annotation.JsonProperty
import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.meta.PlanStatusEnum
import com.shuyun.loyalty.service.service.BeyondPointRecordService
import com.shuyun.loyalty.service.service.PlanBaseService
import com.shuyun.loyalty.service.service.PointAccountTypeBaseService
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.pip.ApplicationContextHolder
import java.math.BigDecimal
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne
import javax.persistence.Table
import javax.validation.constraints.NotEmpty


/** 积分超出记录 */
@DataServiceModel
@FqnVariableModel
@Table(name = "data.loyalty.member.account.BeyondPointRecord{*}")
class BeyondPointRecord : BaseMemberPoint() {

    /** 会员积分账户id */
    @Column
    @NotEmpty
    var memberPointId: String? = null

    /** 触发动作 */
    @Column
    lateinit var recordType: FSMPointEvent

    /** 业务模板描述信息，时机名称+规则组名称 */
    @Column
    var recordSourceDetail: String? = null

    /** 积分生效时间，如果是锁定、冻结则没有有效期 */
    @Column
    var effectiveDate: ZonedDateTime? = null

    /** 积分过期时间，如果是锁定、冻结则没有有效期 */
    @Column
    var overdueDate: ZonedDateTime? = null

    /** 操作人、操作原记录id，订单id，规则id，规则组id，参与规则组条件、参与规则计算数据，interfaceSource：接口调用来源 */
    @Column
    var extralInfo: String? = null

    /** 用于显示描述记录变更信息 */
    @Column
    var recordDetail: String? = null

    /** recordDetail为系统自动根据规则生成的明细信息。而desc为可接口，或特殊场景添加ras */
    @Column
    var desc: String? = null

    /** 操作人，默认系统 */
    @Column
    var operator: String = ConstantValue.DEFAULT_OPERATOR

    /** 操作人用户ID */
    @Column
    var operatorId: String = ConstantValue.DEFAULT_OPERATOR

    /**数据来源*/
    @Column
    var channel: String? = null

    /** 最后一次修改时间 */
    @Column
    var modified: ZonedDateTime = ZonedDateTime.now()

    /**事件ID*/
    @Column
    var key: String? = null

    /**规则组*/
    @Column
    var ruleGroup: String? = null

    /**变更方式*/
    @Column
    var changeMode: String? = null

    /**计划名称*/
    @Column
    var planName: String? = null

    /**积分帐号名称*/
    @Column
    var pointPlanName: String? = null

    /**原单ID*/
    @Column
    var traceId: String? = null

    /** 总积分 */
    @Column
    var totalPoint: Double = 0.0

    /**时机名称*/
    @Column
    var eventTypeName: String? = null

    /**活动名称*/
    @Column
    var actionName: String? = null

    /** 规则Id */
    @Column
    var ruleId: Long? = null

    /**规则名称*/
    @Column
    var ruleName: String? = null

    /**店铺ID*/
    @Column
    var shopId: String? = null

    /**扩展字段1*/
    @Column
    @set:JsonProperty("KZZD1")
    @get:JsonProperty("KZZD1")
    var KZZD1: String? = null

    /**扩展字段2*/
    @Column
    @set:JsonProperty("KZZD2")
    @get:JsonProperty("KZZD2")
    var KZZD2: String? = null

    /**扩展字段3*/
    @Column
    @set:JsonProperty("KZZD3")
    @get:JsonProperty("KZZD3")
    var KZZD3: String? = null

    /**积分变动值,带正负号*/
    @Column
    var changePoint: BigDecimal = BigDecimal.ZERO

    /**
     * 积分差值
     */
    @Column
    var diffPoint: BigDecimal = BigDecimal.ZERO

    /**
     * 数据类型
     */
    @Column
    var type: String? = null
    /**积分状态，对账使用*/
    @Column
    var status: PCStatus? = null

    /**
     * 积分变更记录ID
     */
    @Column
    var pointRecordId: String? = null

    @ManyToOne
    @JoinColumn(name = "memberId")
    var member: Any? = null

    fun save(accountTypeId: Long) {
        buildExtralField(accountTypeId)
        ApplicationContextHolder.getBean(BeyondPointRecordService::class.java).saveOrUpdate(this, accountTypeId.toString())
    }

    private fun buildExtralField(accountTypeId: Long) {
        this.planName = ApplicationContextHolder.getBean(PlanBaseService::class.java).findPlanByName(this.planId!!, PlanStatusEnum.PUBLISHED)
        this.pointPlanName = ApplicationContextHolder.getBean(PointAccountTypeBaseService::class.java).findPointAccountType(accountTypeId).name
        this.modified = ZonedDateTime.now()
        this.created = ZonedDateTime.now()
    }
}