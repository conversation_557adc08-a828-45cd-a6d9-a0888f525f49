package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.datamodel.ReturnOrder
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

/**
 *
 * @Author: sky
 */
@Component
class ReturnOrderRepository : DataModelRepository<ReturnOrder>() {
    private val log = LogManager.getLogger(ReturnOrderRepository::class.java)
    override fun log(): Logger = this.log
}