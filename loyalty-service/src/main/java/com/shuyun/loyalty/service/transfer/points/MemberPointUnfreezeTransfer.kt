package com.shuyun.loyalty.service.transfer.points

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.entity.enums.FromTypeEnum
import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.service.datamodel.*
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.exception.PointException
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.service.MemberPointService
import com.shuyun.pip.ApplicationContextHolder
import org.apache.logging.log4j.LogManager
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.ZonedDateTime
import kotlin.jvm.optionals.getOrNull

@Service
class MemberPointUnfreezeTransfer: MemberPointBaseTransfer() {

    private val logger = LogManager.getLogger(MemberPointUnfreezeTransfer::class.java)


    fun unfreeze(lp: LoyaltyPoints, @Suppress("unused") p: BigDecimal, forbiddenPort: ForbiddenPort, operation: ForbiddenOperation, journalKey: String) {

        // 特殊名单检测
        checkSpecialList(lp, operation, forbiddenPort)

        val memberPoint = lp.member.memberPoint!!

        // 特殊操作-单笔积分解冻
        if (lp.type == LoyaltyRequestType.MANUAL && lp.attr.businessId != null) {
            val validStatementId = lp.attr.businessId!!
            val vs = validService.findById(validStatementId, lp.hierarchy.id) ?: return
            if (vs.fromStatus != PointStateEnum.SPECIAL_FROZE) {
                // 当前积分状态无法解冻
                throw PointException(LoyaltyExceptionCode.POINT_STATUS_CHANGED)
            }
            // 临时解冻，防止页面查出来继续解冻
            vs.fromStatus = PointStateEnum.FROZEN
            vs.modified = ZonedDateTime.now()
            vs.saveOrUpdate(lp.hierarchy.id)

            var point = vs.point
            // 检查解冻的时候是否已经过期了
            if (vs.overdueDate.shDate().isBefore(lp.date.shDate())) {
                // 解冻后已经过期了，解冻+0分
                point = BigDecimal.ZERO
            }
            // 发放总积分
            memberPoint.plus(point, vs.effectiveDate!!, vs.overdueDate, lp.date)

            lp.pointValue = vs.point
            lp.afterPoints = point
            lp.afterTotalPoints = memberPoint.point
        }
        // 事件解冻
        else if (lp.type == LoyaltyRequestType.EVENT && lp.attr.businessId != null) {
            val gainStatementId = lp.attr.businessId!!

            // 查找原单
            val gs = gainStatementService.findById(gainStatementId, lp.hierarchy.id.toString()).getOrNull() ?: return
            val frozenPoint = frozenPointService.findFrozenPointByGainStatementId(gs.id!!, gs.pointPlanId!!) ?: return

            unFrozenEventPoint(lp, gs, frozenPoint)
        }
        else {
            throw UnsupportedOperationException("不支持的类型 ${lp.type}")
        }
        // 保存
        lp.buildJournal(PointAction.UNFREEZE, journalKey).save()
    }


    // 解冻操作只处理明细，不处理总积分(前置已经处理过)
    fun transfer(lp: LoyaltyPoints) {

        // 解冻之前的剩余积分
        var totalPoint = lp.member.point

        // 会修改入参
        fun unfreezeVS(
            recordId: String,
            items: MutableList<PointRecordItem>,
            gss: List<MemberPointGainStatement>,
            vs: MemberPointValidStatement,
            segments: MutableList<MemberPointSegment>
        ) {
            val beforeVsPoint = vs.point
            offset(lp, totalPoint, vs.point, recordId, FSMPointEvent.UNFREEZE) { offsetItems, afterVsPoint ->
                items.addAll(offsetItems)
                // 抵消负积分之后剩余的积分如果还大于0
                if (afterVsPoint > BigDecimal.ZERO) {
                    val item = initRecordItem(
                        lp,
                        Uuid.uuid,
                        recordId,
                        afterVsPoint,
                        FSMPointEvent.UNFREEZE,
                        backId = Uuid.uuid,
                        parentBackId = vs.id,
                        effectiveDate = vs.effectiveDate,
                        overdueDate = vs.overdueDate
                    )
                    items.add(item)
                    vs.backId = item.backId
                    vs.point = afterVsPoint

                    // 修改原单获取记录的状态为发放
                    gss.find { it.id == vs.gainStatementId }?.status = FSMPointEvent.SEND
                } else {
                    vs.point = BigDecimal.ZERO
                    // 修改原单获取记录的状态为扣减
                    val fzGS = gss.find { it.id == vs.gainStatementId }
                    if (fzGS != null && fzGS.status == FSMPointEvent.SEND) {
                        fzGS.status = FSMPointEvent.DEDUCT
                    }
                }
            }
            totalPoint += beforeVsPoint
            if (vs.point <= BigDecimal.ZERO || totalPoint <= BigDecimal.ZERO) {
                return
            }
            val s = segments.find { it.expireDate!!.isEqual(vs.overdueDate.shDate()) }
            if (s == null && !vs.overdueDate.shDate().isBefore(lp.date.shDate())) {
                segments.add(
                    MemberPointSegment().apply {
                        this.id = Uuid.uuid
                        this.planId = lp.plan.id
                        this.pointPlanId = lp.hierarchy.id
                        this.memberPointId = lp.member.id
                        this.memberId = lp.member.memberId
                        this.subjectFqn = lp.subject.dataType
                        this.point = BigDecimal.ZERO
                        this.expireDate = vs.overdueDate.shDate()
                        this.modified = ZonedDateTime.now()
                        this.created = ZonedDateTime.now()
                        this.initial = true
                    }
                )
                segments.sortBy { it.expireDate!! }
            }

            for ((index, sgmt) in segments.withIndex()) {
                if (sgmt.initial) {
                    if (index + 1 < segments.size) {
                        sgmt.point = segments[index + 1].point
                    }
                }
                if (!sgmt.expireDate!!.isAfter(vs.overdueDate.shDate())) {
                    sgmt.point += vs.point
                    sgmt.modified = lp.date
                    sgmt.updated = true
                }
            }
        }

        when (lp.type) {
            LoyaltyRequestType.API -> {
                // 接口解冻
                lp.date = ZonedDateTime.now()
                val mp = ApplicationContextHolder.getBean(MemberPointService::class.java).getByMemberPointId(lp.hierarchy.id, lp.member.id) ?: return
                lp.member.apply {
                    this.point = mp.point
                    this.memberPoint = mp
                }
                val vss = validService.findByPriorities(
                    lp.hierarchy.id,
                    lp.member.id,
                    emptyList(),
                    lp.date.minusYears(100),
                    openTraceId = lp.attr.traceId,
                    statusIn = listOf(PointStateEnum.OPEN_FROZE),
                    limit = 10000
                )
                if (vss.isEmpty()) {
                    logger.warn("解冻积分记录不存在，解冻积分结束 requestId: {}", lp.attr.traceId)
                    lp.out = OutNotification(
                        requestId = lp.attr.uniqueId,
                        type = PointAction.UNFREEZE,
                        memberId = mp.memberId,
                        accountTypeId = mp.pointPlanId!!,
                        expectPoints = BigDecimal.ZERO,
                        actualPoints = BigDecimal.ZERO
                    )
                    return
                }
                val p = vss.sumOf { it.point }
                val gss = gainStatementService.findByIds(vss.map { it.gainStatementId }, lp.hierarchy.id)
                val recordId = Uuid.uuid
                val record = initPointRecord(
                    lp, recordId, FSMPointEvent.OPEN_UNFREEZE,
                    points = p,
                    changePoints = p,
                    totalPoints = mp.point + p,
                    pcStatus = PCStatus.VALID
                ).apply {
                    // 是否采用冻结时的shopId
                    if (lp.attr.autoFillShopId == true) {
                        // 查询冻结时的ShopId
                        val oldRecord = recordService.findByTraceIdList(lp.member.id, lp.attr.traceId, lp.hierarchy.id)
                            .filter { it.recordType == FSMPointEvent.OPEN_FREEZE }.maxByOrNull { it.created }
                        shopId = oldRecord?.shopId
                    }
                    recordDetail = "API操作-解冻积分"
                }

                // 查询大于扣减时间的所有积分块
                val segments = segmentService.findDateAfter(lp.hierarchy.id, lp.member.id, lp.date.shDate()).toMutableList()

                val items = ArrayList<PointRecordItem>()
                for (vs in vss) {
                    unfreezeVS(recordId, items, gss, vs, segments)
                }

                if (vss.size == 1) {
                    record.effectiveDate = vss.first().effectiveDate
                    record.overdueDate = vss.first().overdueDate
                }

                // 原有效积分先全部删除，再新建一份
                validService.deleteBatchValidPoint(vss.map { it.id!! }.toTypedArray(), lp.hierarchy.id)

                val newVss = vss.filter { it.point > BigDecimal.ZERO }.onEach {
                    it.id = it.backId
                    it.shopId = record.shopId
                    it.fromStatus = PointStateEnum.VALID
                    it.openTraceId = null
                }

                validService.batchInsert(newVss, lp.hierarchy.id.toString())

                // 更新原单获取记录状态
                gss.forEach {
                    it.modified = ZonedDateTime.now()
                    it.saveOrUpdate(lp.hierarchy.id)
                }

                batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), items, segments)

                // 解冻前的剩余积分值
                val beforePoints = mp.point
                mp.apply {
                    point = segments.firstOrNull()?.point ?: BigDecimal.ZERO
                    modified = ZonedDateTime.now()
                }.also {
                    it.update()
                    it.saveLog()
                    lp.out = OutNotification(
                        requestId = lp.attr.uniqueId,
                        type = PointAction.UNFREEZE,
                        memberId = mp.memberId,
                        accountTypeId = mp.pointPlanId!!,
                        expectPoints = p,
                        actualPoints = (it.point - beforePoints).abs()
                    )
                }
            }
            LoyaltyRequestType.MANUAL -> {
                // 特殊变更-解冻(单笔解冻)
                val recordId = Uuid.uuid
                val record = initPointRecord(
                    lp, recordId, FSMPointEvent.SPECIAL_UNFREEZE,
                    points = lp.afterPoints,
                    changePoints = lp.afterPoints,
                    totalPoints = lp.afterTotalPoints,
                    pcStatus = PCStatus.VALID
                ).apply {
                    recordDetail = "特殊变更操作-解冻单笔积分"
                }

                val items = ArrayList<PointRecordItem>()

                val validStatementId = lp.attr.businessId ?: return
                val vs = validService.findById(validStatementId, lp.hierarchy.id) ?: return

                record.effectiveDate = vs.effectiveDate
                record.overdueDate = vs.overdueDate

                val gss = gainStatementService.findById(vs.gainStatementId, lp.hierarchy.id.toString()).getOrNull()?.let { listOf(it) } ?: emptyList()

                unfreezeVS(recordId, items, gss, vs, mutableListOf())

                vs.delete(lp.hierarchy.id)
                if (vs.fromStatus != PointStateEnum.EXPIRED && vs.point > BigDecimal.ZERO) {
                    vs.id = vs.backId
                    vs.fromStatus = PointStateEnum.VALID
                    vs.openTraceId = null
                    vs.save(lp.hierarchy.id)
                }

                val gs = gss.firstOrNull()
                gs?.modified = ZonedDateTime.now()
                gs?.saveOrUpdate(lp.hierarchy.id)

                batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), items, listOf())
            }
            LoyaltyRequestType.EVENT -> {
                // 事件解冻
                val gainStatementId = lp.attr.businessId ?: return

                // 查找原单
                val gs = gainStatementService.findById(gainStatementId, lp.hierarchy.id.toString()).getOrNull()

                if (gs == null) {
                    logger.warn("原单积分不存在，解冻积分结束")
                    return
                }

                val frozenPoint = gs.findFrozenPoint(lp.hierarchy.id)

                // 原单关联的冻结积分存在
                if (frozenPoint == null) {
                    logger.debug("冻结积分记录不存在")
                    return
                }

                val isDelayEffective = frozenPoint.fromStatus == FSMPointEvent.DELAY_SEND

                // 解冻待生效的积分
                if (isDelayEffective) {
                    val recordId = Uuid.uuid
                    val record = initPointRecord(
                        lp, recordId, FSMPointEvent.UNFREEZE,
                        points = frozenPoint.point.abs(),
                        changePoints = BigDecimal.ZERO,
                        totalPoints = lp.afterTotalPoints,
                        pcStatus = PCStatus.DELAY,
                        effectiveDate = gs.effectiveDate,
                        overdueDate = gs.overdueDate
                    ).apply {
                        recordDetail = "事件解冻待生效的积分"
                    }

                    val items = ArrayList<PointRecordItem>()

                    if (gs.status == FSMPointEvent.EXPIRE) {
                        val unfreezeItem = initRecordItem(
                            lp,
                            Uuid.uuid,
                            recordId,
                            BigDecimal.ZERO,
                            FSMPointEvent.UNFREEZE,
                            backId = Uuid.uuid,
                            parentBackId = frozenPoint.id,
                            effectiveDate = gs.effectiveDate,
                            overdueDate = gs.overdueDate
                        )

                        val effectiveItem = initRecordItem(
                            lp,
                            Uuid.uuid,
                            recordId,
                            frozenPoint.point.abs(),
                            FSMPointEvent.SEND,
                            backId = Uuid.uuid,
                            parentBackId = unfreezeItem.backId,
                            effectiveDate = gs.effectiveDate,
                            overdueDate = gs.overdueDate
                        )

                        val expiredItem = initRecordItem(
                            lp,
                            Uuid.uuid,
                            recordId,
                            -frozenPoint.point.abs(),
                            FSMPointEvent.EXPIRE,
                            parentBackId = effectiveItem.backId,
                            effectiveDate = gs.effectiveDate,
                            overdueDate = gs.overdueDate
                        )
                        items.add(unfreezeItem)
                        items.add(effectiveItem)
                        items.add(expiredItem)
                    } else {
                        // 解冻后，依然是待生效
                        val item = initRecordItem(
                            lp,
                            Uuid.uuid,
                            recordId,
                            BigDecimal.ZERO,
                            FSMPointEvent.UNFREEZE,
                            backId = lp.attr.backId ?: Uuid.uuid,
                            parentBackId = frozenPoint.id,
                            effectiveDate = gs.effectiveDate,
                            overdueDate = gs.overdueDate
                        )
                        items.add(item)
                        // 重新生成一个原单积分获取记录(为了维持item链的完整性)
                        gs.delete(lp.hierarchy.id)
                        gs.id = item.backId
                        gs.modified = ZonedDateTime.now()
                        gs.save(lp.hierarchy.id)
                    }

                    batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), items, listOf())

                    return
                }

                // 解冻已生效的积分👇
                val recordId = Uuid.uuid
                val record = initPointRecord(
                    lp, recordId, FSMPointEvent.UNFREEZE,
                    points = lp.afterPoints,
                    changePoints = lp.afterPoints,
                    totalPoints = lp.afterTotalPoints,
                    pcStatus = PCStatus.VALID,
                    effectiveDate = gs.effectiveDate,
                    // 这里仅代表冻结对应的原单有效期，不是所解冻的所还回去的积分有效期，冻结的时候可能存在拆借，解冻后各回各家各找各妈
                    overdueDate = gs.overdueDate
                ).apply {
                    recordDetail = "事件解冻已生效的积分"
                }

                val vss = ArrayList<MemberPointValidStatement>()
                val items = ArrayList<PointRecordItem>()

                val frozenStatements = frozenStatementService.findByFrozenIds(lp.hierarchy.id, listOf(frozenPoint.id!!))
                // 先解冻的负积分
                val sortedFrozenStatements = frozenStatements.sortedBy { it.fromType != FromTypeEnum.NEGATIVE }

                val gsIds = sortedFrozenStatements.map { it.gainStatementId }
                val gss = gainStatementService.findByIds(gsIds, lp.hierarchy.id)

                for (fs in sortedFrozenStatements) {
                    // 解冻后没有过期
                    offset(lp, totalPoint, fs.point, recordId, FSMPointEvent.UNFREEZE) { offsetItems, offsetPoint ->
                        items.addAll(offsetItems)
                        // 抵消负积分之后剩余的积分如果还大于0
                        if (offsetPoint > BigDecimal.ZERO) {
                            val item = initRecordItem(
                                lp,
                                Uuid.uuid,
                                recordId,
                                offsetPoint,
                                FSMPointEvent.UNFREEZE,
                                backId = Uuid.uuid,
                                parentBackId = fs.id,
                                effectiveDate = fs.effectiveDate,
                                overdueDate = fs.overdueDate
                            )
                            items.add(item)
                            val vs = initValidPoints(lp,
                                item.backId!!,
                                fs.gainStatementId, offsetPoint.abs(),
                                PointStateEnum.VALID,
                                fs.effectiveDate!!,
                                fs.overdueDate
                            )
                            vss.add(vs)
                            // 更新对应的原单获取记录状态为过期
                            gss.find { it.id == fs.gainStatementId }?.status = FSMPointEvent.SEND
                        } else {
                            // 更新对应的原单获取记录状态为扣减
                            val fzGS = gss.find { it.id == fs.gainStatementId }
                            if (fzGS != null && fzGS.status == FSMPointEvent.FREEZE) {
                                fzGS.status = FSMPointEvent.DEDUCT
                            }
                        }
                    }
                    totalPoint += fs.point
                }

                validService.batchInsert(vss, lp.hierarchy.id.toString())

                gss.forEach {
                    it.modified = ZonedDateTime.now()
                    it.saveOrUpdate(lp.hierarchy.id)
                }

                // 处理完删除冻结记录
                frozenPoint.delete(lp.hierarchy.id)
                frozenPoint.deleteStatement(lp.hierarchy.id)

                batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), items, listOf())
            }
            else -> {}
        }
    }
}