package com.shuyun.loyalty.service.transfer.points

import com.github.f4b6a3.tsid.Tsid
import com.pip.mybatisplus.pools.DmPoolFactory
import com.pip.shuyun.pool.transaction.DmTransaction
import com.shuyun.loyalty.entity.api.constants.ChangeMode
import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.api.request.MemberPointImportRequest
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.enums.NegativeStrategyEnum
import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.sdk.Segments
import com.shuyun.loyalty.service.datamodel.*
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.extension.adjustToSHTimeEnd
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.model.Plan
import com.shuyun.loyalty.service.repository.MemberLimitPointRecordRepository
import com.shuyun.loyalty.service.service.LoyaltyPrograms
import com.shuyun.loyalty.service.service.MemberPointSendLimitCalcService
import com.shuyun.loyalty.service.service.MemberPointService
import com.shuyun.loyalty.service.service.PointMigrationService
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.ZonedDateTime
import java.util.*

@Service
class MemberPointMigrateTransfer: MemberPointBaseTransfer() {

    @Autowired
    private lateinit var memberPointSendLimitCalcService: MemberPointSendLimitCalcService

    @Autowired
    private lateinit var memberLimitPointRecordRepository: MemberLimitPointRecordRepository

    @Autowired
    private lateinit var memberPointService: MemberPointService

    @Autowired
    private lateinit var pointMigrationService: PointMigrationService

    companion object {
        private const val DATE_TIME_PADDING = 10_000_000_000_000 //补齐位数
        private const val BATCH_INSERT_SIZE = 200
    }

    @DmTransaction
    fun transfer(hierarchyId: Long, memberId: String, migMemberId: String, savePointLog: Boolean = true) {
        val migMember = pointMigrationService.findMigMemberById(migMemberId) ?: return
        val plan = LoyaltyPrograms.findPlanByAccountTypeId(hierarchyId) ?: throw LoyaltyException(LoyaltyExceptionCode.PLAN_NOT_FOUND, "- hierarchyId: $hierarchyId")
        val memberPoint = memberPointService.getOrCreate(hierarchyId, memberId)

         restoreMigrationLog(migMember.migrationId, memberPoint)

        val beforeTotalPoint = memberPoint.point
        val journalist = ArrayList<MemberPointCalculateTask>()
        val records = ArrayList<MemberPointRecord>()
        val items = ArrayList<PointRecordItem>()
        val gss = ArrayList<MemberPointGainStatement>()
        val vss = ArrayList<MemberPointValidStatement>()
        val nss = ArrayList<MemberPointNegativeStatement>()

        var currentTotalPoint = beforeTotalPoint

        // 检查是否存在负积分，存在则抵消
        fun offset(lp: LoyaltyPoints, recordId: String, gs: MemberPointGainStatement, vs: MemberPointValidStatement) {
            var p = gs.point
            for (negative in nss) {
                if (negative.point.compareTo(BigDecimal.ZERO) == 0) {
                    continue
                }
                var itemPoint = negative.point
                if (negative.point > p) {
                    negative.point -= p
                    itemPoint = p
                    p = BigDecimal.ZERO
                } else {
                    p -= negative.point
                    negative.point = BigDecimal.ZERO
                }
                val item = initRecordItem(
                    lp, Uuid.uuid,
                    recordId, itemPoint, FSMPointEvent.SEND,
                    parentBackId = negative.id,
                    effectiveDate = ZonedDateTime.now(),
                    overdueDate = ZonedDateTime.now()
                )
                items.add(item)
                if (p.compareTo(BigDecimal.ZERO) == 0) {
                    break
                }
            }
            vs.point = p
        }

        fun send(lp: LoyaltyPoints, recordId: String, gs: MemberPointGainStatement) {
            // 有效积分记录
            val vs = initValidPoints(
                lp,
                Uuid.uuid,
                gs.id!!,
                gs.point,
                PointStateEnum.VALID,
                lp.attr.effectiveDate!!,
                lp.attr.overdueDate!!
            )
            // 检查是否存在负积分，存在则抵消
            offset(lp, recordId, gs, vs)

            if (vs.point > BigDecimal.ZERO) {
                vss.add(vs)
                val item = initRecordItem(
                    lp, Uuid.uuid,
                    recordId, vs.point, FSMPointEvent.SEND,
                    backId = vs.id,
                    parentBackId = vs.backId,
                    effectiveDate = vs.effectiveDate,
                    overdueDate = vs.overdueDate
                )
                items.add(item)
                gs.status = FSMPointEvent.SEND
            } else {
                gs.status = FSMPointEvent.DEDUCT
            }
        }

        fun checkEffective(gs: MemberPointGainStatement) {
            val effectiveLP = buildLP(plan, memberPoint, traceId = gs.traceId, uniqueId = gs.id)
            effectiveLP.member.point = currentTotalPoint
            effectiveLP.date = gs.effectiveDate
            val recordId = Uuid.uuid
            val record = initPointRecord(
                effectiveLP, recordId, FSMPointEvent.TIMER,
                points = gs.point,
                changePoints = gs.point,
                totalPoints = currentTotalPoint + gs.point,
                pcStatus = PCStatus.VALID,
                effectiveDate = gs.effectiveDate,
                overdueDate = gs.overdueDate
            ).apply {
                val rd = records.firstOrNull { it.id == gs.id }
                shopId = rd?.shopId
                KZZD1 = rd?.KZZD1
                KZZD2 = rd?.KZZD2
                KZZD3 = rd?.KZZD3
                channel = rd?.channel
                recordDetail =  "迁移积分-${migMember.migrationId}"
                desc = "生效"
            }
            effectiveLP.attr.traceId = gs.traceId ?: effectiveLP.attr.traceId
            effectiveLP.attr.effectiveDate = gs.effectiveDate
            effectiveLP.attr.overdueDate = gs.overdueDate

            currentTotalPoint += gs.point
            records.add(record)
            send(effectiveLP, recordId, gs)
        }

        fun checkOverdue(vs: MemberPointValidStatement) {
            val traceId = gss.find { it.id == vs.gainStatementId }?.traceId
            val overdueLP = buildLP(plan, memberPoint, traceId = traceId, uniqueId = vs.id)
            overdueLP.date = vs.overdueDate.plusSeconds(1)
            overdueLP.member.point = currentTotalPoint
            overdueLP.attr.traceId = traceId ?: overdueLP.attr.traceId
            val recordId = Uuid.uuid
            val record = initPointRecord(
                overdueLP, recordId, FSMPointEvent.EXPIRE,
                points = vs.point,
                changePoints = -vs.point,
                totalPoints = currentTotalPoint - vs.point,
                pcStatus = PCStatus.EXPIRE,
                effectiveDate = vs.effectiveDate,
                overdueDate = vs.overdueDate,
            ).apply {
                shopId = vs.shopId
                KZZD1 = vs.kzzd1
                KZZD2 = vs.kzzd2
                KZZD3 = vs.kzzd3
                channel = vs.channel
                recordDetail =  "迁移积分-${migMember.migrationId}"
                desc = "过期"
            }

            val item = initRecordItem(
                overdueLP,
                Uuid.uuid,
                recordId,
                -vs.point,
                FSMPointEvent.EXPIRE,
                parentBackId = vs.id,
                effectiveDate = vs.effectiveDate,
                overdueDate = vs.overdueDate
            )

            currentTotalPoint -= vs.point
            records.add(record)
            items.add(item)

            vs.point = BigDecimal.ZERO

            val gs = gss.firstOrNull { it.id == vs.gainStatementId }
            gs?.status = FSMPointEvent.EXPIRE

            record.traceId = gs?.traceId
            item.traceId = gs?.traceId
        }

        var removedHistoryData = false
        val ids = HashSet<String>()
        val migrationRecords = pointMigrationService.findMigrationRecordList(migMember.migrationId, hierarchyId, memberId)
        while (migrationRecords.isNotEmpty()) {
            val migrationRecord = migrationRecords.removeFirst()
            if (!ids.add(migrationRecord.id) && migrationRecord.action != "CHECK") {
                continue
            }
            val lp = buildLP(plan, memberPoint, migrationRecord)
            var action = migrationRecord.action
            if (migrationRecord.overrideHistory) {
                if (!removedHistoryData) {
                    cleanData(memberPoint)
                    removedHistoryData = true
                }
                records.clear()
                items.clear()
                gss.clear()
                vss.clear()
                nss.clear()
                journalist.clear()
                currentTotalPoint = BigDecimal.ZERO
                lp.hierarchy.negativeStrategy = NegativeStrategyEnum.TO_NEGATIVE
                // 导入负积分
                if (lp.afterPoints < BigDecimal.ZERO) {
                    lp.pointValue = lp.pointValue.abs()
                    lp.afterPoints = lp.afterPoints.abs()
                    action = "DEDUCT"
                }
            }

            when (action) {
                "SEND" -> {
                    // 是否是延迟生效的积分
                    val delayed = lp.attr.effectiveDate!!.isAfter(lp.date)
                    val effectDateTime = lp.attr.effectiveDate!!
                    val overdueDateTime = lp.attr.overdueDate!!.adjustToSHTimeEnd()

                    // 延迟生效的积分
                    if (delayed) {
                        val id = Uuid.uuid
                        // 保存积分变更记录
                        val record = initPointRecord(
                            lp, id,
                            FSMPointEvent.DELAY_SEND,
                            points = lp.afterPoints,
                            changePoints = BigDecimal.ZERO,
                            totalPoints = currentTotalPoint,
                            PCStatus.DELAY,
                            effectDateTime,
                            overdueDateTime
                        )
                        lp.member.point = record.totalPoint
                        lp.afterTotalPoints = record.totalPoint

                        // 保存积分获取记录
                        val gs = initGainPoints(
                            lp,
                            id,
                            FSMPointEvent.DELAY_SEND,
                            lp.afterPoints,
                            effectDateTime,
                            overdueDateTime
                        )

                        val item = initRecordItem(
                            lp,
                            id = Uuid.uuid,
                            recordId = id,
                            points = BigDecimal.ZERO,
                            status = FSMPointEvent.DELAY_SEND,
                            backId = gs.id,
                            effectiveDate = effectDateTime,
                            overdueDate = overdueDateTime
                        )
                        journalist.add(buildJournal(lp, migrationRecord))
                        records.add(record)
                        gss.add(gs)
                        items.add(item)
                    } else {
                        val id = Uuid.uuid
                        // 保存积分变更记录
                        val record = initPointRecord(
                            lp, id,
                            FSMPointEvent.SEND,
                            points = lp.afterPoints,
                            changePoints = lp.afterPoints,
                            totalPoints = currentTotalPoint + lp.afterPoints,
                            PCStatus.VALID,
                            effectDateTime,
                            overdueDateTime
                        )
                        lp.member.point = currentTotalPoint
                        lp.afterTotalPoints = record.totalPoint
                        currentTotalPoint = record.totalPoint
                        // 保存积分获取记录
                        val gs = initGainPoints(
                            lp,
                            id,
                            FSMPointEvent.SEND,
                            lp.afterPoints,
                            effectDateTime,
                            overdueDateTime
                        )
                        journalist.add(buildJournal(lp, migrationRecord))
                        records.add(record)
                        gss.add(gs)
                        send(lp, record.id!!, gs)
                    }

                }
                "DEDUCT" -> {
                    var actualPoints = lp.afterPoints
                    var detail = ""
                    if (lp.hierarchy.negativeStrategy == NegativeStrategyEnum.TO_ZERO) {
                        if (currentTotalPoint <= BigDecimal.ZERO) {
                            actualPoints = BigDecimal.ZERO
                            detail = "预计扣除-${lp.afterPoints}"
                        } else {
                            if (currentTotalPoint < lp.afterPoints) {
                                actualPoints = currentTotalPoint
                            }
                        }
                    }
                    val recordId = Uuid.uuid
                    val record = initPointRecord(
                        lp, recordId, FSMPointEvent.DEDUCT,
                        points = actualPoints,
                        changePoints = -actualPoints,
                        totalPoints = currentTotalPoint - actualPoints,
                        pcStatus = PCStatus.USED,
                    ).apply {  recordDetail = detail }

                    lp.member.point = currentTotalPoint
                    lp.afterTotalPoints = record.totalPoint
                    currentTotalPoint = record.totalPoint
                    journalist.add(buildJournal(lp, migrationRecord))
                    records.add(record)

                    val sortedVss = vss.sortedBy { it.overdueDate.toInstant().toEpochMilli() }
                    var p = actualPoints
                    for (vs in sortedVss) {
                        if (vs.point.compareTo(BigDecimal.ZERO) == 0) {
                            continue
                        }
                        var itemPoint = vs.point
                        if (vs.point > p) {
                            itemPoint = p
                            vs.point -= p
                        } else {
                            vs.point = BigDecimal.ZERO
                        }
                        vs.subPoints = itemPoint
                        p -= itemPoint

                        // 积分追溯明细
                        val item = initRecordItem(lp,
                            Uuid.uuid,
                            recordId, -itemPoint, FSMPointEvent.DEDUCT, parentBackId = vs.id)
                        items.add(item)

                        if (p.compareTo(BigDecimal.ZERO) == 0) {
                            break
                        }
                    }
                    if (p > BigDecimal.ZERO) {
                        val negative = initNegativePoints(lp, Uuid.uuid, recordId, p)
                        val negativeItem = initRecordItem(lp, Uuid.uuid, recordId, -p, FSMPointEvent.DEDUCT, backId = negative.id)
                        items.add(negativeItem)
                        nss.add(negative)
                    }
                    gss.forEach { gs ->
                        val vs = vss.find { it.gainStatementId == gs.id && it.point > BigDecimal.ZERO }
                        if (vs == null) {
                            if (gs.status == FSMPointEvent.SEND) {
                                gs.status = FSMPointEvent.DEDUCT
                            }
                        }
                    }
                }
                "CHECK" -> {
                    var continuation: Boolean
                    do {
                        val map = TreeMap<ZonedDateTime, MutableList<Any>>()
                        // 检查是否存在待生效的积分
                        val effectiveGSS = gss.filter { it.status == FSMPointEvent.DELAY_SEND && !it.effectiveDate.isAfter(migrationRecord.createdDate) && it.point > BigDecimal.ZERO }
                        if (effectiveGSS.isNotEmpty()) {
                            effectiveGSS.forEach {
                                val lst = map[it.effectiveDate] ?: mutableListOf()
                                lst.add(it)
                                map[it.effectiveDate] = lst
                            }
                            continuation = true
                        } else {
                            continuation = false
                        }

                        // 检查是否存在已过期的积分
                        val overdueVss = vss.filter { it.overdueDate.shDate().isBefore(migrationRecord.createdDate.shDate()) && it.point > BigDecimal.ZERO }
                        if (overdueVss.isNotEmpty()) {
                            overdueVss.forEach {
                                val lst = map[it.overdueDate] ?: mutableListOf()
                                lst.add(it)
                                map[it.overdueDate] = lst
                            }
                        }
                        map.forEach { (_, items) ->
                            items.forEach {
                                when (it) {
                                    is MemberPointGainStatement -> checkEffective(it)
                                    is MemberPointValidStatement -> checkOverdue(it)
                                }
                            }
                        }
                    } while (continuation)
                }
                else -> {}
            }
            val isEqualNow = migrationRecord.createdDate.toLocalDate().isEqual(ZonedDateTime.now().toLocalDate())
            if (action != "CHECK" && !isEqualNow) {
                val date = migrationRecords.firstOrNull()?.createdDate ?: ZonedDateTime.now()
                val next = migrationRecord.copy(id = "-", action = "CHECK", overrideHistory = false, createdDate = date)
                migrationRecords.addFirst(next)
            }
        }

        val newGSS = gss.filter { it.point > BigDecimal.ZERO }
        val newVSS = vss.filter { it.point > BigDecimal.ZERO }
        val newNSS = nss.filter { it.point > BigDecimal.ZERO }

        gainStatementService.deleteByMemberPointId(memberPoint.id!!, memberPoint.pointPlanId!!)
        validService.deleteByMemberPointId(memberPoint.id!!, memberPoint.pointPlanId!!)

        chunked(newGSS) { gainStatementService.batchInsert(it, memberPoint.pointPlanId!!.toString()) }
        chunked(newVSS) { validService.batchInsert(it, memberPoint.pointPlanId!!.toString()) }
        chunked(records) { recordService.batchInsert(it, memberPoint.pointPlanId!!.toString()) }
        chunked(items) { itemService.batchInsert(it, memberPoint.pointPlanId!!.toString()) }
        chunked(newNSS) { negativeStatementService.batchInsert(it, memberPoint.pointPlanId!!.toString()) }
        chunked(journalist) { MemberPointCalculateTask.batchSave(memberPoint.pointPlanId!!, it) }

        pointMigrationService.delete(migMember)

        memberPoint.point = currentTotalPoint
        memberPoint.openSegmentFlag = false
        memberPoint.modified = ZonedDateTime.now()

        updatePoint(memberPoint, savePointLog)
    }


    private fun <T> chunked(list: List<T>, block: (List<T>) -> Unit) {
        list.chunked(BATCH_INSERT_SIZE).forEach { block(it) }
    }

    fun updatePoint(memberPoint: MemberPoint, savePointLog: Boolean = true) {
        DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
            val points = Segments.rebuildSegment(
                sdk,
                pointAccountId = memberPoint.pointPlanId!!,
                memberId = memberPoint.memberId,
                reference = 1
            )
            memberPoint.point = points
        }
        if (savePointLog) {
            memberPoint.saveLog()
        }
    }

    private fun cleanData(memberPoint: MemberPoint, journalIds: List<String>, includePointLimitCalc: Boolean = true, includePointLog: Boolean = true) {
        MemberPointCalculateTask.deleteByIds(memberPoint.pointPlanId!!, journalIds)
        cleanDataExcludeJournal(memberPoint, includePointLimitCalc, includePointLog)
    }

    fun cleanData(memberPoint: MemberPoint, includePointLimitCalc: Boolean = true, includePointLog: Boolean = true) {
        MemberPointCalculateTask.deleteBy(memberPoint.pointPlanId!!, memberPoint.id!!)
        cleanDataExcludeJournal(memberPoint, includePointLimitCalc, includePointLog)
    }

    private fun cleanDataExcludeJournal(memberPoint: MemberPoint, includePointLimitCalc: Boolean = true, includePointLog: Boolean = true) {
        recordService.deleteByMemberPointId(memberPoint.id!!, memberPoint.pointPlanId!!)
        gainStatementService.deleteByMemberPointId(memberPoint.id!!, memberPoint.pointPlanId!!)
        validService.deleteByMemberPointId(memberPoint.id!!, memberPoint.pointPlanId!!)
        segmentService.deleteByMemberId(memberPoint.memberId, memberPoint.pointPlanId!!)
        negativeStatementService.deleteByMemberPointId(memberPoint.id!!, memberPoint.pointPlanId!!)
        frozenStatementService.deleteByMemberPointId(memberPoint.id!!, memberPoint.pointPlanId!!)
        frozenPointService.deleteByMemberPointId(memberPoint.id!!, memberPoint.pointPlanId!!)
        itemService.deleteByMemberId(memberPoint.memberId, memberPoint.pointPlanId!!)
        memberLimitPointRecordRepository.deleteByMemberPointId(memberPoint.id!!, memberPoint.pointPlanId!!)
        if (includePointLimitCalc) {
            memberPointSendLimitCalcService.deleteByMemberId(memberPoint.memberId, memberPoint.pointPlanId!!)
        }
        if (includePointLog) {
            memberPointLogService.deleteByMemberId(memberPoint.memberId, memberPoint.pointPlanId!!)
        }
    }


    private fun buildLP(plan: Plan, memberPoint: MemberPoint, traceId: String? = null, uniqueId: String? = null, ): LoyaltyPoints {
        return LoyaltyPoints.of(
            plan,
            memberPoint,
            LoyaltyRequestType.AUTO,
            BigDecimal.ZERO,
            changeMode = ChangeMode.AUTO_CALC.name,
            channel = ConstantValue.DEFAULT_CHANNEL_TYPE,
            traceId = traceId ?: Tsid.fast().toString(),
            uniqueId = uniqueId ?: Tsid.fast().toString(),
            checkSpecialList = false
        )
    }


    private fun buildLP(plan: Plan, memberPoint: MemberPoint, migrationRecord: PointMigrationService.MigrationRecord): LoyaltyPoints {
        val lp = LoyaltyPoints.of(
            plan,
            memberPoint,
            LoyaltyRequestType.API,
            migrationRecord.point,
            date = migrationRecord.createdDate,
            changeMode = ChangeMode.MANUAL.name,
            channel = migrationRecord.channelType ?: ConstantValue.DEFAULT_CHANNEL_TYPE,
            traceId = migrationRecord.migrationId + "-" + migrationRecord.key,
            uniqueId = migrationRecord.key,
            shopId = migrationRecord.shopId,
            kzzd1 = migrationRecord.kzzd1,
            kzzd2 = migrationRecord.kzzd2,
            kzzd3 = migrationRecord.kzzd3,
            desc = migrationRecord.description,
            checkSpecialList = false
        ).apply {
            attr.migrationType = migrationRecord.action
            attr.recordDetail = "迁移积分-${migrationRecord.migrationId}"
            attr.overrideHistoryPoint = migrationRecord.overrideHistory
        }
        // 插入生效和过期
        if (migrationRecord.action == MemberPointImportRequest.Type.SEND.name) {
            lp.attr.effectiveDate = migrationRecord.effectiveDate
            lp.attr.overdueDate = migrationRecord.overdueDate
        }
        return lp
    }


    private fun buildJournal(lp: LoyaltyPoints,  migrationRecord: PointMigrationService.MigrationRecord): MemberPointCalculateTask {
        val j= lp.buildJournal(PointAction.MIGRATE, lp.attr.uniqueId).apply {
            status = PointStatus.COMPLETED
            created = migrationRecord.createdDate
            modified = migrationRecord.createdDate
        }
        return j
    }

    private fun restoreMigrationLog(migrationId: String, memberPoint: MemberPoint) {
        val status = listOf(PointStatus.NEW, PointStatus.COMPLETED)
        var idAfter = ""
        val requestList = ArrayList<MemberPointImportRequest>()
        val ids = ArrayList<String>()
        while (true) {
            val list = MemberPointCalculateTask.findList(memberPoint.pointPlanId!!, memberPoint.id!!, status, idAfter)
            for (journal in list) {
                idAfter = journal.id!!
                if (journal.action !in listOf(PointAction.MIGRATE, PointAction.EFFECT, PointAction.EXPIRE)) {
                    throw IllegalStateException("会员积分已经发生过变更不允许再次迁入数据 hierarchyId: ${journal.hierarchyId} memberId: ${journal.memberId}")
                }
                if (journal.action == PointAction.EFFECT || journal.action == PointAction.EXPIRE) {
                    continue
                }
                val lp = journal.lp!!
                val req = MemberPointImportRequest().apply {
                    this.myId = (DATE_TIME_PADDING+journal.created.toInstant().toEpochMilli()).toString().plus(journal.id)
                    this.memberId = journal.memberId
                    this.action = MemberPointImportRequest.Type.valueOf(lp.attr.migrationType!!)
                    this.point = lp.pointValue
                    this.createdDate = journal.created
                    this.key = lp.attr.uniqueId
                    this.channelType = lp.attr.channel
                    this.description = lp.attr.desc
                    this.shopId = lp.attr.shopId
                    this.KZZD1 = lp.attr.kzzd1
                    this.KZZD2 = lp.attr.kzzd2
                    this.KZZD3 = lp.attr.kzzd3
                    this.overrideHistory = lp.attr.overrideHistoryPoint ?: false
                    if (this.action == MemberPointImportRequest.Type.SEND) {
                        this.effectiveDate = lp.attr.effectiveDate
                        this.overdueDate = lp.attr.overdueDate
                    }
                }
                ids.add(journal.id!!)
                requestList.add(req)
            }
            if (list.isEmpty()) {
                break
            }
        }
        if (requestList.isNotEmpty()) {
            val chunked = requestList.chunked(100)
            for (list in chunked) {
                pointMigrationService.batchInsertRecord(list, migrationId, memberPoint.planId!!, memberPoint.pointPlanId!!)
            }
        }
        cleanData(memberPoint, journalIds = ids)
        memberPoint.point = BigDecimal.ZERO
        memberPoint.version = -1
        memberPoint.update()
    }

}