package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.service.datamodel.MemberPointSegment
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Service


/**
 *
 */
@Service
class MemberPointSegmentBaseService : MemberPointDmBaseService<MemberPointSegment>() {
    private val log = LogManager.getLogger(MemberPointSegmentBaseService::class.java)
    override fun log(): Logger = log

    /**
     * 根据会员ID删除积分块
     */
    override fun deleteByMemberId(memberId: String, accountTypeId:Long) {
        val bodyParams = HashMap<String, Any?>()
        bodyParams["memberId"] = memberId
        deleteByFilter(JsonUtils.toJson(bodyParams), accountTypeId.toString())
    }

}