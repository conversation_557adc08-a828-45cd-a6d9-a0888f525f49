package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.datamodel.MergeRecord
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

/**
 *
 */
@Component
class MergeRecordRepository : DataModelRepository<MergeRecord>() {
    private val log = LogManager.getLogger(MergeRecordRepository::class.java)
    override fun log(): Logger = this.log
}