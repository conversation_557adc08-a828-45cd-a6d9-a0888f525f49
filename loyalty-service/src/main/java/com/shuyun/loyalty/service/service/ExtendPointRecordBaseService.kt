package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.service.datamodel.ExtendPointRecord
import com.shuyun.loyalty.service.repository.ExtendPointRecordRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class ExtendPointRecordBaseService {

    @Autowired
    private lateinit var extendPointRecordRepository: ExtendPointRecordRepository

    fun saveOrUpdate(extendPointRecord: ExtendPointRecord, pointAccountId: Long): ExtendPointRecord {
        return extendPointRecordRepository.saveOrUpdate(extendPointRecord, pointAccountId.toString())
    }

}