package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.Table
import javax.validation.constraints.NotEmpty

/**
 * 冻结负积分与发放的有效积分关联表
 */
@DataServiceModel
@FqnVariableModel
@Table(name = "data.loyalty.member.account.FrozenValidStatement{*}")
class MemberPointFrozenValidStatement : BaseMemberPoint()  {

    /** 会员积分账户id */
    @Column
    @NotEmpty
    var memberPointId: String? = null

    /** 非空，积分获取记录ID，所有积分账户内部流转均需要该属性 */
    @Column
    @NotEmpty
    lateinit var gainStatementId: String

    /** 积分生效时间 */
    @Column
    var effectiveDate: ZonedDateTime? = null

    /** 积分过期时间 */
    @Column
    var overdueDate: ZonedDateTime? = null

    /** 数据最后一次修改时间 */
    @Column
    @NotEmpty
    var modified: ZonedDateTime = ZonedDateTime.now()

    /** 记录积分是从哪个状态过来的，当解冻、解锁时，需要还原到原来状态 */
    @Column
    @NotEmpty
    lateinit var fromStatus: PointStateEnum

    /**冻结明细ID**/
    @Column
    var frozenStatementId : String? = null

    @Column
    var openTraceId: String? = null

    @Column
    var validRecordId: String? = null


}