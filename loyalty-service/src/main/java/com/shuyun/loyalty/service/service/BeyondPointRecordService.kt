package com.shuyun.loyalty.service.service

import com.pip.mybatisplus.annotation.NotTransactionSafe
import com.shuyun.loyalty.service.datamodel.BeyondPointRecord
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

@NotTransactionSafe
@Component
class BeyondPointRecordService: MemberPointDmBaseService<BeyondPointRecord>() {

    private val log = LogManager.getLogger(BeyondPointRecordService::class.java)
    override fun log(): Logger = log
}