package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.datamodel.MemberPointSegment
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

/**
 *
 */
@Component
class MemberPointSegmentRepository : DataModelRepository<MemberPointSegment>() {
    private val log = LogManager.getLogger(MemberPointSegmentRepository::class.java)
    override fun log(): Logger = this.log

}