package com.shuyun.loyalty.service.model

import com.shuyun.loyalty.service.meta.ModeTypeEnum
import io.swagger.v3.oas.annotations.media.Schema
import javax.persistence.*

/** 业务端 扣除规则*/
@Entity
@Schema(title = "扣除积分规则")
@Table(name = "point_deduct_rule")
data class PointDeductRule(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(title = "规则ID", type = "Long")
    var id: Long? = null,
    @Schema(title = "规则名称", type = "String")
    var ruleName: String? = null,
    @Schema(title = "规则组ID", type = "Long")
    var ruleGroupId: Long? = null,
    @Schema(title = "SIMPLE:简单模式 SENIOR:高级模式,CONDITION:条件模式", type = "String")
    @Enumerated(EnumType.STRING)
    var conditionMode: ModeTypeEnum? = null,
    @Schema(title = "条件或计算表达式", type = "String")
    var conditionExpression: String? = null,
    @Schema(title = "后端加工后的表达式", type = "String")
    var conditionExpressionTranslated: String? = null,
    @Schema(title = "前端展示表达式时需要的辅助信息，格式前端自定", type = "String")
    var conditionDisplayInfo: String? = null,
    @Schema(title = "前端展示表达式时需要的辅助信息，格式前端自定", type = "String")
    var placeholderInfo: String? = null,
    @Schema(title = "前端展示表达式时需要的辅助信息，格式前端自定", type = "String")
    var expressionDisplayInfo: String? = null,
    @Schema(title = "条件表达式存放变量", type = "String")
    var conditionExpressionVariable: String? = null,
    @Schema(title = "归档状态", type = "Boolean")
    var fileStatus: Boolean? = null,
    @Schema(title = "扣除积分操作列表", type = "object")
    @Transient
    var pointDeductRuleOperateList: List<PointDeductRuleOperate>? = null,
    @Schema(title = "条件表达式id", type = "Int")
    var conditionExpressionFxId: Int? = null,
    @Schema(title = "条件表达式", type = "String")
    var conditionExpressionFx: String? = null,
    @Schema(title = "备注", type = "String")
    var remark: String? = null,
    @Transient
    var quotaType: PointDeductRuleOperate.QuotaTypeEnum? = null,

    @Schema(title = "国际化信息")
    @Transient
    var _i18nPayload: com.shuyun.pip.i18n.I18nPayload? = null,
    @Schema(title = "优先级")
    var sort: Int? = null,
) : BaseModel() {
    companion object {
        const val I18N_GROUP__RULE_NAME = "loyalty:point_deduct_rule:rule_name"
    }
    override fun copyToOldOne(old: BaseModel, backup: Boolean) {}
}