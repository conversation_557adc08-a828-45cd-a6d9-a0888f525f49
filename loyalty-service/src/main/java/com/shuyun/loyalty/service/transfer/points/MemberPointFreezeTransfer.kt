package com.shuyun.loyalty.service.transfer.points

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.entity.enums.FromTypeEnum
import com.shuyun.loyalty.entity.enums.NegativeStrategyEnum
import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.service.datamodel.MemberPointFrozenStatement
import com.shuyun.loyalty.service.datamodel.MemberPointValidStatement
import com.shuyun.loyalty.service.datamodel.PointAction
import com.shuyun.loyalty.service.datamodel.save
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.exception.PointException
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.model.ForbiddenOperation
import org.apache.logging.log4j.LogManager
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime
import kotlin.jvm.optionals.getOrNull

@Service
class MemberPointFreezeTransfer: MemberPointBaseTransfer() {

    private val logger = LogManager.getLogger(MemberPointFreezeTransfer::class.java)


    fun freeze(lp: LoyaltyPoints, p: BigDecimal, forbiddenPort: ForbiddenPort, operation: ForbiddenOperation, journalKey: String) {
        // 作废扣减/使用扣减
        if (lp.hierarchy.negativeStrategy == NegativeStrategyEnum.NOT_ALLOWED) {
            throw PointException(LoyaltyExceptionCode.NOT_FROZEN_ALLOWED)
        }

        // 特殊名单检测
        checkSpecialList(lp, operation, forbiddenPort)

        val leftSegmentDate: LocalDate
        val memberPoint = lp.member.memberPoint!!
        if (lp.type == LoyaltyRequestType.MANUAL) {
            if (lp.attr.businessId != null) {
                // 特殊变更-单笔冻结积分
                val validStatementId = lp.attr.businessId!!
                val vs = validService.findById(validStatementId, lp.hierarchy.id) ?: return
                val status = listOf(PointStateEnum.START, PointStateEnum.VALID, PointStateEnum.FURTHER_VALID)
                if (vs.fromStatus !in status) {
                    // 当前积分状态无法冻结
                    throw PointException(LoyaltyExceptionCode.POINT_STATUS_CHANGED)
                }

                if (lp.afterPoints > vs.point) {
                    lp.pointValue = vs.point
                    lp.afterPoints = vs.point
                }

                // 检查余额是否足够冻结
                if (lp.afterPoints > lp.member.point) {
                    throw PointException(LoyaltyExceptionCode.FROZEN_POINT_LESS_THAN_DEDUCT)
                }

                val gs = gainStatementService.findById(vs.gainStatementId, replacePattern = lp.hierarchy.id.toString()).getOrNull()
                if (gs == null) {
                    logger.warn("原单积分不存在 gsId: {} vsId {}", vs.gainStatementId, vs.id)
                    // 原单积分不存在
                    throw PointException(LoyaltyExceptionCode.GAIN_ID_NOT_FOUND)
                }

                // 单笔全部冻结
                if (vs.point.compareTo(lp.afterPoints) == 0) {
                    vs.delete(lp.hierarchy.id)

                    val id = Uuid.uuid
                    val vs1 = vs.copy(id, status = PointStateEnum.SPECIAL_FROZE, point = lp.afterPoints, openTraceId = vs.id)
                    lp.attr.businessId = id
                    vs1.save(lp.hierarchy.id)

                    // 更新积分获取记录状态
                    gs.status = FSMPointEvent.SPECIAL_FREEZE
                    gs.modified = ZonedDateTime.now()
                    gs.saveOrUpdate(lp.hierarchy.id)
                } else {
                    // 单笔部分冻结
                    vs.point -= lp.afterPoints
                    vs.saveOrUpdate(lp.hierarchy.id)
                    val id = Uuid.uuid
                    val vs1 = vs.copy(id, status = PointStateEnum.SPECIAL_FROZE, point = lp.afterPoints, openTraceId = vs.id)
                    lp.attr.businessId = id
                    vs1.save(lp.hierarchy.id)
                }
                val pair = memberPoint.minus(lp.afterPoints, lp.hierarchy.negativeStrategy!!, vs.overdueDate.shDate())
                leftSegmentDate = pair.first
            } else {
                logger.warn("businessId不能为空")
                return
            }
        } else if (lp.type == LoyaltyRequestType.EVENT) {
            // 事件冻结积分
            // 检查余额是否足够冻结
            // 事件冻结多笔的时候注意更新 lp.member.point 和 memberPoint
            val frozenPoint = if (lp.hierarchy.negativeStrategy == NegativeStrategyEnum.TO_ZERO) {
                if (p > lp.member.point) {
                    if (lp.member.point <= BigDecimal.ZERO) BigDecimal.ZERO else lp.member.point
                } else p
            } else p
            val pair = memberPoint.minus(frozenPoint, lp.hierarchy.negativeStrategy!!)
            leftSegmentDate = pair.first
        } else {
            logger.info("不支持的类型 {}", lp.type)
            return
        }

        // 保存
        lp.member.leftSegmentDate = leftSegmentDate
        lp.afterPoints = (lp.member.point - memberPoint.point).abs()
        lp.afterTotalPoints = memberPoint.point
        lp.buildJournal(PointAction.FREEZE, journalKey).save()
    }



    fun transfer(lp: LoyaltyPoints) {

        when (lp.type) {
            LoyaltyRequestType.API -> {
                // 接口冻结
                val recordId = Uuid.uuid
                val record = initPointRecord(
                    lp, recordId, FSMPointEvent.OPEN_FREEZE,
                    points = lp.afterPoints,
                    changePoints = -lp.afterPoints,
                    totalPoints = lp.afterTotalPoints,
                    pcStatus = PCStatus.FROZEN
                ).apply {
                    recordDetail = "API操作-冻结积分"
                }
                // 查询大于扣减时间的所有积分块
                val segments = segmentService.findDateAfter(lp.hierarchy.id, lp.member.id, lp.date.shDate()).filter {
                    it.expireDate!!.isAfter(lp.member.leftSegmentDate) && it.point >= BigDecimal.ZERO
                }

                val vss = ArrayList<MemberPointValidStatement>()

                // 顺序扣减
                val items = sequentiallyDeduct(
                    lp,
                    lp.pointValue,
                    ::vssFetch,
                    segments,
                    lp.hierarchy.negativeStrategy!!,
                    recordId,
                    FSMPointEvent.OPEN_FREEZE,
                    FSMPointEvent.OPEN_FREEZE,
                    FSMPointEvent.FREEZE) { vs, item ->
                    // 生成冻结状态的有效积分记录
                    val newVS = vs.copy(
                        id = Uuid.uuid,
                        point = vs.subPoints,
                        status = PointStateEnum.OPEN_FROZE,
                        openTraceId = lp.attr.traceId
                    )
                    vss.add(newVS)
                    item.backId = newVS.id
                }
                validService.batchInsert(vss, lp.hierarchy.id.toString())
                batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), items, segments)
            }
            LoyaltyRequestType.MANUAL -> {
                // 特殊变更冻结，只有单笔冻结
                val recordId = Uuid.uuid
                val record = initPointRecord(
                    lp, recordId, FSMPointEvent.SPECIAL_FREEZE,
                    points = lp.afterPoints,
                    changePoints = -lp.afterPoints,
                    totalPoints = lp.member.point - lp.afterPoints,
                    pcStatus = PCStatus.FROZEN
                ).apply {
                    recordDetail = "特殊变更操作-冻结单笔积分"
                }

                val validStatementId = lp.attr.businessId ?: return
                val vs = validService.findById(validStatementId, lp.hierarchy.id) ?: return

                val item = initRecordItem(
                    lp,
                    Uuid.uuid,
                    recordId,
                    -lp.afterPoints,
                    FSMPointEvent.FREEZE,
                    backId = vs.id,
                    parentBackId = vs.openTraceId
                )

                batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), listOf(item), listOf())
            }
            LoyaltyRequestType.EVENT -> {
                // 事件冻结
                // 事件冻结，按原单顺序冻结
                val gainStatementId = lp.attr.businessId ?: return

                // 查找原单
                val gs = gainStatementService.findById(gainStatementId, lp.hierarchy.id.toString()).getOrNull()

                if (gs == null) {
                    logger.warn("原单不存在，冻结积分结束")
                    return
                }

                val frozenPoint = gs.findFrozenPoint(lp.hierarchy.id)

                // 原单关联的冻结积分存在
                if (frozenPoint == null) {
                    logger.debug("冻结积分记录不存在")
                    return
                }

                val isDelayEffective = gs.status == FSMPointEvent.DELAY_SEND

                // 冻结待生效的积分
                if (isDelayEffective) {
                    val recordId = Uuid.uuid
                    val record = initPointRecord(
                        lp, recordId, FSMPointEvent.FREEZE,
                        points = gs.point,
                        changePoints = BigDecimal.ZERO,
                        totalPoints = lp.member.point,
                        pcStatus = PCStatus.DELAY_FROZEN
                    ).apply {
                        recordDetail = "事件冻结待生效的积分"
                    }

                    val item = initRecordItem(
                        lp,
                        Uuid.uuid,
                        recordId,
                        BigDecimal.ZERO,
                        FSMPointEvent.FREEZE,
                        backId = frozenPoint.id,
                        parentBackId = gs.id
                    )

                    gs.status = FSMPointEvent.FREEZE
                    gs.modified = ZonedDateTime.now()
                    gs.saveOrUpdate(lp.hierarchy.id)

                    batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), listOf(item), listOf())
                    return
                }

                // 冻结已生效的积分👇
                val recordId = Uuid.uuid
                val record = initPointRecord(
                    lp, recordId, FSMPointEvent.FREEZE,
                    points = lp.afterPoints,
                    changePoints = -lp.afterPoints,
                    totalPoints = lp.member.point - lp.afterPoints,
                    pcStatus = PCStatus.FROZEN
                ).apply {
                    recordDetail = "事件冻结已生效的积分"
                }

                // 查询大于扣减时间的所有积分块
                val segments = segmentService.findDateAfter(lp.hierarchy.id, lp.member.id, lp.date.shDate()).filter {
                    it.expireDate!!.isAfter(lp.member.leftSegmentDate) && it.point >= BigDecimal.ZERO
                }

                // 查找冻结关联的所有原单获取记录
                val fetch = vssFetchFunc(gainStatementId)

                val frozenStatements = ArrayList<MemberPointFrozenStatement>()

                // 顺序扣减
                // 优先冻结原单关联的有效积分
                val items = sequentiallyDeduct(
                    lp,
                    lp.pointValue,
                    fetch,
                    segments,
                    lp.hierarchy.negativeStrategy!!,
                    recordId,
                    FSMPointEvent.FREEZE,
                    FSMPointEvent.FREEZE,
                    FSMPointEvent.FREEZE,
                    primaryGSId = gainStatementId) { vs, item ->
                    item.backId = Uuid.uuid
                    val frozenStatement = initFrozenStatement(lp, item.backId!!, frozenPoint.id!!, recordId, vs, PointStateEnum.VALID)
                    frozenStatements.add(frozenStatement)
                }

                // 负积分item
                items.filter { it.backId != null && it.parentBackId == null }.forEach {
                    val vs = MemberPointValidStatement().apply {
                        this.memberPointId = gs.memberPointId
                        this.gainStatementId = gs.id!!
                        this.effectiveDate = gs.effectiveDate
                        this.overdueDate = gs.overdueDate
                        this.subPoints = it.point.abs()
                    }
                    val frozenStatement = initFrozenStatement(lp, it.backId!!, frozenPoint.id!!, recordId, vs, PointStateEnum.VALID, FromTypeEnum.NEGATIVE)
                    frozenStatements.add(frozenStatement)
                }

                frozenStatementService.batchInsert(frozenStatements, lp.hierarchy.id.toString())

                batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), items, segments)
            }
            else -> {}
        }
    }
}