package com.shuyun.loyalty.service.util


import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.redis.core.RedisTemplate
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Component
import java.util.concurrent.TimeUnit

/**
 */



@Component
class RedisUtils {

    private val logger = LogManager.getLogger(RedisUtils::class.java)

    @Autowired
    private lateinit var stringRedisTemplate: StringRedisTemplate

    @Autowired
    private lateinit var redisTemplate: RedisTemplate<String,Any?>

    /**
     * 设置string类型值
     */
    fun setString(key: String, value: String) {
        try {
            stringRedisTemplate.opsForValue().set(key, value)
        } catch (e: Exception) {
            logger.error("redis set key error, ",e)
        }
    }


    /**
     * 设置string类型值
     */
    fun getString(key: String): String? {
        try {
            return stringRedisTemplate.opsForValue().get(key)
        } catch (e: Exception) {
            logger.error("redis get key error, ", e)
        }
        return null
    }



    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     * @return Boolean
     */
    fun expire(key: String, time: Long): Boolean {
        return try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS)
            }
            true
        } catch (e: Exception) {
            logger.error(e.message, e)
            false
        }
    }
}