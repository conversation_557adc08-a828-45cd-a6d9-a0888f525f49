package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.extension.getIfPresent
import com.shuyun.loyalty.service.service.MemberFrozenPointService
import com.shuyun.loyalty.service.service.MemberPointGainStatementService
import com.shuyun.loyalty.service.util.ConstantValue.LONG_TERM_OVERDUE_DATE
import com.shuyun.pip.ApplicationContextHolder
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.OneToMany
import javax.persistence.Table
import javax.persistence.Transient
import javax.validation.constraints.NotEmpty

/**
 * 它既是明细，又不是明细，是明细的原因是每一笔积分获取都会在此记录，且后续被使用、冻结、失效、锁定等，也会进行状态变更
 * 原单与几种状态变更的关系整理：
 * 1、过期，需要找到原单修改状态为过期，并扣减有效积分
 *      1）如果原单已过期，则不处理。仅记录操作记录。
 *      2）如果原单已作废，则不处理。仅记录操作记录。
 *      3）如果原单已被使用，则有最早有效积分扣减。
 *      4）原单只有三种状态：过期、作废、已发放。
 * 2、锁定：算作使用
 * 3、使用，原单已发放，
 *
 * 原单积分获取记录
 */
@DataServiceModel
@FqnVariableModel
@Table(name = "data.loyalty.member.account.GainStatement{*}")
class MemberPointGainStatement : SubMemberPoint() {
    companion object;

    /** 积分当前状态, 会员积分获取账户记录，用于退单时追溯，同时其它状态变更，需要同步修改该表状态，比如过期情况，根据该状态判定后决定是否需要再冻结或重算 */
    @Column
    @NotEmpty
    lateinit var status: FSMPointEvent

    /** 积分获取关联的时机，只有当事件计算才有值 */
    @Column
    var eventTypeId: Long? = null

    /** 积分事件触发id，用于账户与事件追踪 */
    @Column
    var traceId: String? = null

    /** 积分生效时间 */
    @Column
    @NotEmpty
    var effectiveDate: ZonedDateTime = ZonedDateTime.now()

    /** 积分过期时间 */
    @Column
    var overdueDate: ZonedDateTime = LONG_TERM_OVERDUE_DATE

    /** 变更时间 */
    @Column
    var modified: ZonedDateTime = ZonedDateTime.now()

    /** 规则组ID，支持重算 */
    @Column
    var ruleGroupId: Long? = null

    /** 发放积分事件时间，支持重算 */
    @Column
    var occurrenceTs: String? = null

    @Column
    var ruleId: Long? = null

    @OneToMany
    @Suppress("unused")
    var memberPointValidStatement: List<MemberPointValidStatement>? = null

    /**
     * 反向操作时需要记录backId
     */
    @Transient
    var backId: String? = null

    @Transient
    var ruleName: String? = null

    @Transient
    var ruleGroupName: String? = null

    @Transient
    var eventTypeName: String? = null

    @Transient
    var enabledGlobalGroupLimit: Boolean = false

    @Transient
    var desc: String? = null

    @Transient
    var ref: MemberPointGainStatement? = null

    @Transient
    var sort: Int? = null

    fun save(accountTypeId: Long) = ApplicationContextHolder.getBean(MemberPointGainStatementService::class.java).save(this, replacePattern = accountTypeId.toString())
    fun saveOrUpdate(accountTypeId: Long) = ApplicationContextHolder.getBean(MemberPointGainStatementService::class.java).saveOrUpdate(this, replacePattern = accountTypeId.toString())
    fun delete(accountTypeId: Long) = ApplicationContextHolder.getBean(MemberPointGainStatementService::class.java).delete(id!!, replacePattern = accountTypeId.toString())

    fun findById(id: String, pointPlanId: Long) = ApplicationContextHolder.getBean(MemberPointGainStatementService::class.java).findById(id, pointPlanId.toString()).getIfPresent()

    /** 由于一笔获取记录与冻结是一一对应的，所以可以根据获取记录反查到积分冻结记录 */
    fun findFrozenPoint(accountTypeId: Long): MemberFrozenPoint? = ApplicationContextHolder.getBean(MemberFrozenPointService::class.java).findFrozenPointByGainStatementId(id!!, accountTypeId)

}