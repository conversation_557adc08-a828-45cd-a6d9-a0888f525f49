package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.service.datamodel.MemberPointSendLimitCalc
import com.shuyun.loyalty.service.meta.PointSendLimitType
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.springframework.stereotype.Service
import java.util.*

@Service
class MemberPointSendLimitCalcService:MemberPointDmBaseService<MemberPointSendLimitCalc>() {

    private val log = LogManager.getLogger(MemberPointSendLimitCalcService::class.java)
    override fun log() = log

    fun findOneRefIdAndType(refId:String, type: PointSendLimitType,memberId: String, pointAccountId: String):Optional<MemberPointSendLimitCalc> {
        val params = HashMap<String, Any?>()
        params["memberId"] = memberId
        params["refId"] = refId
        params["type"] = type.name
        return findOneByFilter(JsonUtils.toJson(params), replacePattern=pointAccountId)
    }


    fun findByMemberId(pointAccountId: Long, memberId: String): List<MemberPointSendLimitCalc> {
        val params = HashMap<String, Any?>()
        params["memberId"] = memberId
        return findListByFilter(JsonUtils.toJson(params), replacePattern = pointAccountId.toString())
    }

    fun findListRefIdType(refIdTypes: List<String>, memberId: String, pointAccountId: String):List<MemberPointSendLimitCalc> {
        val params = HashMap<String, Any?>()
        params["memberId"] = memberId
        params["refIdType"] = mapOf("\$in" to refIdTypes)
        return findListByFilter(JsonUtils.toJson(params), replacePattern=pointAccountId)
    }


}