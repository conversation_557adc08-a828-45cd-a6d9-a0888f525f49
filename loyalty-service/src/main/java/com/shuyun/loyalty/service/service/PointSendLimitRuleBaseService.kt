package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.meta.PointSendLimitKind
import com.shuyun.loyalty.service.meta.PointSendLimitType
import com.shuyun.loyalty.service.model.PointSendLimitRule
import com.shuyun.loyalty.service.repository.PointSendLimitRuleRepository
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import org.apache.commons.lang3.StringUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import java.util.*
import javax.persistence.criteria.Path
import javax.persistence.criteria.Predicate

@Service
class PointSendLimitRuleBaseService {

    private val logger = LogManager.getLogger(PointSendLimitRuleBaseService::class.java)

    @Autowired
    private lateinit var pointSendLimitRuleRepository: PointSendLimitRuleRepository

    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun insertPointLimitRule(rule: PointSendLimitRule) {
        logger.debug("保存积分上限规则{}", { JsonUtils.toJson(rule) })
        pointSendLimitRuleRepository.save(rule)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun deletePointLimitRule(rule: PointSendLimitRule) {
        rule.disabled = true
        logger.debug("删除积分上限规则: ${rule.id}")
        pointSendLimitRuleRepository.save(rule)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun updatePointLimitRule(rule: PointSendLimitRule) {
        logger.debug("更新积分上限规则{}", { JsonUtils.toJson(rule) })
        pointSendLimitRuleRepository.save(rule)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun deletePointLimitRulePhysical(rule: PointSendLimitRule) {
        logger.debug("删除积分上限规则{}", { JsonUtils.toJson(rule) })
        pointSendLimitRuleRepository.delete(rule)
    }

    /**
     * 删除规则组时,计算上限规则引用的规则组ID
     */
    fun updatePointLimitRuleByNlmsIds(nlmsId: String, type: PointSendLimitType) {
        val list = pointSendLimitRuleRepository.findByNlmsIdsAndGroupId(nlmsId,type.toString())

        logger.debug("规则组删除关联引用ID列表数据结果集: {} ", { JsonUtils.toJson(list) })

        val updateList = ArrayList<PointSendLimitRule>()
        val deleteList = ArrayList<PointSendLimitRule>()
        list.forEach { pointSendLimitRule ->
            if (pointSendLimitRule.groupId != null && pointSendLimitRule.groupId.toString() == nlmsId) {
                deleteList.add(pointSendLimitRule)
                return@forEach
            }
            val nlmsIdAry = pointSendLimitRule.nlmsIds!!.split(",")
            val nlsmIdList = nlmsIdAry.dropWhile { it == nlmsId }
            if(nlsmIdList.isEmpty()) {
                deleteList.add(pointSendLimitRule)
                return@forEach
            }
            pointSendLimitRule.nlmsIds = StringUtils.join(nlsmIdList, ",")
            updateList.add(pointSendLimitRule)
        }
        saveOrUpdate(updateList, deleteList)
    }

    /**
     * 删除相引用ID
     */
    fun updatePointLimitRuleByNlmsIds(deleteNlmsId: List<Long>, type: PointSendLimitType, groupId: String) {
        val list = pointSendLimitRuleRepository.findByGroupId(groupId,type.toString())

        logger.debug("规则删除关联引用ID列表数据结果集: {} ", { JsonUtils.toJson(list) })

        val updateList = ArrayList<PointSendLimitRule>()
        val deleteList = ArrayList<PointSendLimitRule>()
        list.forEach { pointSendLimitRule ->
            val nlmsIdAry = pointSendLimitRule.nlmsIds!!.split(",")
            val nlsmIdList = nlmsIdAry.dropWhile { deleteNlmsId.contains(it.toLong())  }
            if(nlsmIdList.isEmpty()) {
                deleteList.add(pointSendLimitRule)
                return@forEach
            }
            pointSendLimitRule.nlmsIds = StringUtils.join(nlsmIdList, ",")
            updateList.add(pointSendLimitRule)
        }
        saveOrUpdate(updateList, deleteList)
    }

    private fun saveOrUpdate(updateList: ArrayList<PointSendLimitRule>,deleteList: ArrayList<PointSendLimitRule>) {
        logger.debug("删除关联引用ID列表数据结果集, 执行update: {} , 执行delete: {}",{ JsonUtils.toJson(updateList) }, { JsonUtils.toJson(deleteList) })
        updateList.forEach {
            ApplicationContextHolder.getBean(PointSendLimitRuleBaseService::class.java).updatePointLimitRule(it)
        }
        deleteList.forEach {
            ApplicationContextHolder.getBean(PointSendLimitRuleBaseService::class.java).deletePointLimitRulePhysical(it)
        }
    }

    fun findLimitRuleList(pointAccountTypeId: Long, type: PointSendLimitType, groupId: Long? = null, kind: PointSendLimitKind?=null,status: EnableStatusEnum?=null):List<PointSendLimitRule> {
        return pointSendLimitRuleRepository.findAll { root, query, cb ->
            val pCreateTime: Path<ZonedDateTime> = root.get("createTime")
            val pAccountId: Path<String> = root.get("pointAccountId")
            val pType: Path<PointSendLimitType> = root.get("type")
            val pDisabled: Path<Boolean> = root.get("disabled")
            val list = ArrayList<Predicate?>()
            list.add(cb.equal(pAccountId, pointAccountTypeId))
            list.add(cb.equal(pDisabled, ConstantValue.LOGIC_DELETE_NOT))
            list.add(cb.equal(pType, type))
            groupId?.run {
                val pGroupId: Path<Long> = root.get("groupId")
                list.add(cb.equal(pGroupId, groupId))
            }
            kind?.run {
                val pKind: Path<PointSendLimitKind> = root.get("kind")
                list.add(cb.equal(pKind, kind))
            }
            status?.run {
                val pStatus: Path<EnableStatusEnum> = root.get("status")
                list.add(cb.equal(pStatus, status))
            }
            query.where(cb.and(*list.toArray(arrayOfNulls<Predicate>(list.size))))
            query.orderBy(cb.asc(pCreateTime))
            query.groupRestriction
        }
    }

    fun findByIdAndDisabled(id:Long,disabled: Boolean): Optional<PointSendLimitRule> {
        return pointSendLimitRuleRepository.findByIdAndDisabled(id, disabled)
    }

    /**
     * 查询规则排除规则
     */
    fun findLimitRuleListExclude(pointAccountTypeId: Long, type: PointSendLimitType): List<PointSendLimitRule> {
        return this.findLimitRuleList(pointAccountTypeId, type, kind = PointSendLimitKind.EXCLUDE,status= EnableStatusEnum.ENABLED)
    }

    /**
     * 查询包含的规则
     */
    fun findLimitRuleListInclude(pointAccountTypeId: Long, type: PointSendLimitType, nlmsId: String): List<PointSendLimitRule> {
        return pointSendLimitRuleRepository.findLimitRuleListInclude(nlmsId,type.toString(), EnableStatusEnum.ENABLED.toString(),false,pointAccountTypeId)
    }

}