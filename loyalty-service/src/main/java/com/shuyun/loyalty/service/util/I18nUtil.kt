package com.shuyun.loyalty.service.util

import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.pip.i18n.LocaleI18nContextHolder

/**
 * 国际化错误码转换工具类
 */
object I18nUtil {

    fun transferMessage(code: LoyaltyExceptionCode) = LocaleI18nContextHolder.getMessage(code.trackErrorCode.toString())

    fun transferMessage(code: LoyaltyExceptionCode, vararg params: String) = LocaleI18nContextHolder.getMessage(code.trackErrorCode.toString(),params)

}