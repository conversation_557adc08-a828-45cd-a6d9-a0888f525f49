package com.shuyun.loyalty.service.model

import com.shuyun.eawf.enums.InstanceStatus
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.datamodel.BaseDataModel
import com.shuyun.loyalty.service.meta.LimitActionEnum
import com.shuyun.loyalty.service.meta.TypeEnum
import com.shuyun.loyalty.service.repository.TransferApproveRepository
import com.shuyun.pip.ApplicationContextHolder
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.LocalDateTime
import javax.persistence.Column
import javax.persistence.EnumType
import javax.persistence.Enumerated
import javax.persistence.Table

@Schema(title = "变更审批")
@Table(name = "data.loyalty.manager.transferApprove")
@DataServiceModel
data class TransferApprove(
    @Schema(title = "数字主键", type = "Long")
    @Column
    var transferApproveInt: Long? = null,

    @Schema(title = "planId", type = "Long")
    @Column
    var planId: Long? = null,

    @Schema(title = "subjectId", type = "Long")
    @Column
    var subjectId: Long? = null,

    @Schema(title = "积分账户或等级体系id", type = "Long")
    @Column
    var accountOrHierarchyId: Long? = null,

    @Schema(title = "积分账户或等级体系名称", type = "String")
    @Column
    var accountOrHierarchyName: String? = null,

    @Schema(title = "操作值", type = "BigDecimal")
    @Column
    var operatorValue: BigDecimal? = null,

    @Schema(title = "操作文件名称", type = "String")
    @Column
    var operatorFileName: String? = null,

    @Schema(title = "操作文件地址", type = "String")
    @Column
    var operatorFileUrl: String? = null,

    @Schema(title = "变更类型", type = "String")
    @Enumerated(EnumType.STRING)
    @Column
    var transferType: TransferTypeEnum? = null,

    @Schema(title = "变更方式", type = "String")
    @Enumerated(EnumType.STRING)
    @Column
    var transferWay: TransferWayEnum? = null,

    @Schema(title = "实际变更时间", type = "DateTime")
    @Column
    var transferTime: LocalDateTime? = null,

    @Schema(title = "审批状态", type = "String")
    @Enumerated(EnumType.STRING)
    @Column
    var approveStatus: ApproveStatusEnum? = null,

    @Schema(title = "变更结果", type = "String")
    @Column
    var transferResult: Boolean? = null,

    @Schema(title = "变更结果信息", type = "String")
    @Column
    var transferMessage: String? = null,

    @Schema(title = "审批流实例id", type = "String")
    @Column
    var instanceId: Long? = null,

    @Schema(title = "审批流id", type = "String")
    @Column
    var processId: Long? = null,

    @Schema(title = "当前审批用户id", type = "String")
    @Column
    var currentApproveUserId: String? = null,

    @Schema(title = "当前审批角色id", type = "String")
    @Column
    var currentApproveRoleId: String? = null,

    @Schema(title = "发起审批用户id", type = "String")
    @Column
    var startApproveUserId: String? = null,

    @Schema(title = "发起审批用户名称", type = "String")
    @Column
    var startApproveUserName: String? = null,

    @Schema(title = "变更请求信息", type = "String")
    @Column
    var transferRequest: String? = null,

    @Schema(title = "创建时间", type = "DateTime")
    @Column
    var createTime: LocalDateTime? = null,

    @Schema(title = "账户类型", type = "String")
    var type: TypeEnum? = null

    ): BaseDataModel() {

    fun save(): TransferApprove {
        return ApplicationContextHolder.getBean(TransferApproveRepository::class.java).save(this)
    }

    fun saveOrUpdate(): TransferApprove {
        return ApplicationContextHolder.getBean(TransferApproveRepository::class.java).saveOrUpdate(this)
    }

    enum class TransferTypeEnum {
        SEND, DEDUCT, ABOLISH, FROZEN, UNFROZEN, IMPORT;

        fun toLimitAction(): LimitActionEnum? {
            return when(this) {
                SEND -> return LimitActionEnum.SEND
                DEDUCT -> return LimitActionEnum.DEDUCT
                ABOLISH -> return LimitActionEnum.ABOLISH
                FROZEN -> return LimitActionEnum.FROZEN
                UNFROZEN -> return LimitActionEnum.UNFROZEN
                else -> null
            }
        }

        companion object {
            fun get(pointTransferType: TransferPointOrGrade.PointTransferTypeEnum?) = when(pointTransferType) {
                TransferPointOrGrade.PointTransferTypeEnum.ADD -> SEND
                TransferPointOrGrade.PointTransferTypeEnum.DEDUCT -> DEDUCT
                TransferPointOrGrade.PointTransferTypeEnum.MANUAL_ABOLISH -> ABOLISH
                else -> null
            }
        }
    }

    enum class TransferWayEnum {
       SINGLE, BATCH, IMPORT;
    }

    enum class ApproveStatusEnum(desc: String) {
        INIT("待提交"),
        PASS("审批通过"),
        PROCESS("处理中"),
        REJECT("拒绝"),
        TERMINATION("中止");

        companion object {
            fun get(instanceStatus: InstanceStatus) = when(instanceStatus) {
                InstanceStatus.INIT -> INIT
                InstanceStatus.PASS -> PASS
                InstanceStatus.PROCESS -> PROCESS
                InstanceStatus.REJECT -> REJECT
                InstanceStatus.TERMINATION -> TERMINATION
                else -> null
            }
        }
    }

    enum class ApproveType(desc: String) {
        WAIT_APPROVE("待我审批"),
        HAVE_APPROVE("我已审批"),
        MY_APPROVE("我的送审")
    }

    enum class ExportTypeEnum(value: String) {
        IMPORT_FILE("importFile"),
        ERROR_FILE("errorFile")
    }


}