package com.shuyun.loyalty.service.infrastructure.es

import com.shuyun.air.es.PublishOptions
import com.shuyun.air.es.event.Event
import com.shuyun.air.es.ser.BodySerializer
import com.shuyun.kylin.es.sdk.EsFactory
import com.shuyun.kylin.es.sdk.meta.EsUser
import com.shuyun.kylin.es.sdk.meta.EventModel
import com.shuyun.kylin.es.sdk.meta.EventProperty
import com.shuyun.kylin.es.sdk.meta.types.*
import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.service.infrastructure.es.EventClient.PropertyValue.*
import com.shuyun.loyalty.service.meta.EventStreamMetasStatusEnum
import com.shuyun.loyalty.service.model.EventStreamMetas
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.frameworkext.filter.VisitTenantInfoHolder
import org.slf4j.LoggerFactory
import java.time.LocalDateTime


object EventClient {
    private val logger = LoggerFactory.getLogger(EventClient::class.java)
    private val factory by lazy { EsFactory.create() }
    private val producer by lazy { factory.createProducer() }
    private val log = LoggerFactory.getLogger(EventClient::class.java)
    private val client by lazy { factory.client }

    fun registerClient() {
        try {
            client.register(EsUser().apply {
                this.userName = GlobalContext.serviceName()
                this.title = GlobalContext.serviceName()
            })
        } catch (e: Exception) {
            log.warn("注册事件服务失败", e)
        }
    }


    fun sendEvent(fqn: String, body: Map<String, Any?>) {
        val e = Event.of(fqn, Uuid.uuid, body)
        log.info("发送MQ消息: {}", Json.toJson(e))
        producer.publishSync(PublishOptions.of(fqn, BodySerializer.map()), e)
    }


    fun upsertModel(eventModel: EventModel) {
        var interval = 1000L
        for (i in 0..4) {
            try {
                client.upsertModel(eventModel)
                return
            } catch (e: Exception) {
                if (i == 4) {
                    logger.error("upsertModel error, fqn: {}", eventModel.fqn, e)
                    throw e
                } else {
                    Thread.sleep(interval)
                    interval *= 2
                }
            }
        }
    }


    fun findESStreamMetasBySubjectFQN(subjectFQN: String): List<EventStreamMetas> {
        val listOfFQN = client.fetchFqnByReference(subjectFQN)
        val permission = client.querySubscribablePermission()
        return listOfFQN.filter { it in permission.topics }.map {
            findESStreamMetas(it)
        }
    }

    // 事件模型转换为事件流元数据
    fun findESStreamMetas(fqn: String): EventStreamMetas {
        val event = client.queryModel(fqn)
        val properties = LinkedHashMap<String, PropertyValue>()
        val references = HashMap<String, String>()
        properties["fqn"] = StringValue("事件类型全限定名")
        properties["key"] = StringValue("事件业务唯一key")
        properties["occurrenceTs"] = IntegerValue("事件发生时间")
        properties["detectionTs"] = IntegerValue("事件检测时间")
        for (it in event.properties) {
            val prop = buildProperty(it) ?: continue
            if (prop.value is ReferenceValue) {
                references[prop.value.reference] = prop.name
            }
            properties[it.name] = prop.value
        }
        return EventStreamMetas().apply {
            this.fqn = event.fqn
            this.name = event.title
            this.schema = JsonUtils.toJson(Schema("Object",event.title, properties))
            this.references = JsonUtils.toJson(references)
            this.source = ConstantValue.ES_SOURCE
            this.tenantId = VisitTenantInfoHolder.getTenantId()
            this.status = EventStreamMetasStatusEnum.INVALID
            this.created = LocalDateTime.now()
        }
    }



    private fun buildProperty(it: EventProperty): Property? {
        if (it.name in listOf("id", "objectId", "detectionDt", "occurrenceDt")) return null
        return when (it.dataType) {
            is DateType -> Property(it.name, DateValue(it.title))
            is IntegerType -> Property(it.name, IntegerValue(it.title))
            is DateTimeType -> Property(it.name, DateTimeValue(it.title))
            is DecimalType -> Property(it.name, NumberValue(it.title))
            is BooleanType -> Property(it.name, BooleanValue(it.title))
            is EnumType -> {
                val enumType = it.dataType as EnumType
                Property(it.name, EnumValue(it.title, enumType.enumValue))
            }
            is ReferenceType -> {
                val referenceType = it.dataType as ReferenceType
                Property(it.name, ReferenceValue(it.title, referenceType.dataModelFqn))
            }
            is ArrayType -> {
                val arrayType = it.dataType as ArrayType
                val elementType = arrayType.elementType
                if (elementType is ObjectType) {
                    val properties = LinkedHashMap<String, PropertyValue>()
                    for (property in elementType.properties) {
                        val prop = buildProperty(property)!!
                        properties[property.name] = prop.value
                    }
                    Property(it.name, ArrayValue(it.title, ObjectValue(it.title, properties)))
                } else {
                    /* FX-BOX 仅支持Object类型的数组
                    val element = it.copy(dataType = elementType)
                    val p = buildProperty(element)!!
                    Property(it.name, ArrayValue(it.title, TypeValue(p.value.type)))
                     */
                    null
                }
            }
            is ObjectType -> {
                val objectType = it.dataType as ObjectType
                val properties = LinkedHashMap<String, PropertyValue>()
                for (property in objectType.properties) {
                    val prop = buildProperty(property)!!
                    properties[property.name] = prop.value
                }
                Property(it.name, ObjectValue(it.title, properties))
            }
            is PhoneNrType -> Property(it.name, PhoneNrValue(it.title))
            is EmailType -> Property(it.name, EmailValue(it.title))
            is UrlType -> Property(it.name, UrlValue(it.title))
            is StringType -> Property(it.name, StringValue(it.title))
            else -> Property(it.name, StringValue(it.title))
        }
    }


    data class Schema(val type: String, val title: String, val properties: Map<String, PropertyValue>)
    data class Property(val name: String, val value: PropertyValue)
    sealed class PropertyValue(open val type: String) {
        data class ReferenceValue(val title: String, val reference: String) : PropertyValue("Reference")
        data class TypeValue(override val type: String) : PropertyValue(type)
        data class StringValue(val title: String) : PropertyValue("String")
        data class PhoneNrValue(val title: String) : PropertyValue("PhoneNr")
        data class EmailValue(val title: String) : PropertyValue("Email")
        data class UrlValue(val title: String) : PropertyValue("Url")
        data class IntegerValue(val title: String) : PropertyValue("Integer")
        data class NumberValue(val title: String) : PropertyValue("Number")
        data class DateTimeValue(val title: String) : PropertyValue("DateTime")
        data class DateValue(val title: String) : PropertyValue("Date")
        data class BooleanValue(val title: String) : PropertyValue("Boolean")
        data class EnumValue(val title: String, val enum: List<String>) : PropertyValue("Enum")
        data class ArrayValue(val title: String, val items: PropertyValue) : PropertyValue("Array")
        data class ObjectValue(val title: String, val properties: Map<String, PropertyValue>) : PropertyValue("Object")
    }

}