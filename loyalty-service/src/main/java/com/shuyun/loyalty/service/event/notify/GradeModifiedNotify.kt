package com.shuyun.loyalty.service.event.notify

import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.service.datamodel.MemberGradeRecord
import com.shuyun.loyalty.service.model.GradeDefinition
import com.shuyun.loyalty.service.model.GradeHierarchy
import com.shuyun.loyalty.service.model.Plan
import com.shuyun.loyalty.service.model.Subject
import com.shuyun.loyalty.service.service.*
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import java.time.ZonedDateTime


class GradeModifiedNotify:LoyaltyNotify() {

    @Transient
    private val planId = "planId"
    @Transient
    private val planName = "planName"
    @Transient
    private val memberId = "memberId"
    @Transient
    private val subjectFqn = "subjectFqn"
    @Transient
    private val gradeHierarchyId = "gradeHierarchyId"
    @Transient
    private val gradeHierarchyName = "gradeHierarchyName"
    @Transient
    private val currentGradeId = "currentGradeId"
    @Transient
    private val currentGradeName = "currentGradeName"
    @Transient
    private val currentEffectDate = "currentEffectDate"
    @Transient
    private val currentOverdueDate = "currentOverdueDate"
    @Transient
    private val originalGradeId = "originalGradeId"
    @Transient
    private val originalGradeName = "originalGradeName"
    @Transient
    private val originalEffectDate = "originalEffectDate"
    @Transient
    private val originalOverdueDate = "originalOverdueDate"
    @Transient
    private val recordType = "recordType"
    @Transient
    private val triggerId = "triggerId"
    @Transient
    private val recordSourceDetail = "recordSourceDetail"
    @Transient
    private val description = "description"
    @Transient
    private val channel = "channel"
    @Transient
    private val id = "id"
    @Transient
    private val changeWay = "changeWay"
    @Transient
    private val traceId = "traceId"

    companion object {
        private const val ESCAPE_KEY = "use.escape.date.c"
    }

    fun setTraceId(traceId: String?) = put(this.traceId, traceId)

    fun setId(id: String) = put(this.id, id)

    fun setPlanId(planId: Long) = put(this.planId, planId)

    fun setPlanName(planName: String) = put(this.planName, planName)

    fun setMemberId(memberId: String) = put(this.memberId, memberId)

    fun setSubjectFqn(subjectFqn: String) = put(this.subjectFqn, subjectFqn)

    fun setGradeHierarchyId(gradeHierarchyId: Long) = put(this.gradeHierarchyId, gradeHierarchyId)

    fun setGradeHierarchyName(gradeHierarchyName: String) = put(this.gradeHierarchyName, gradeHierarchyName)

    fun setCurrentGradeId(currentGradeId: Long) = put(this.currentGradeId, currentGradeId)

    fun setCurrentGradeName(currentGradeName: String) = put(this.currentGradeName, currentGradeName)

    fun setCurrentEffectDate(currentEffectDate: ZonedDateTime) = put(this.currentEffectDate, if (Property.getSysOrEnv(ESCAPE_KEY, true)) JsonUtils.toJson(currentEffectDate) else currentEffectDate)

    fun setCurrentOverdueDate(currentOverdueDate: ZonedDateTime?) = put(this.currentOverdueDate, if (Property.getSysOrEnv(ESCAPE_KEY, true)) JsonUtils.toJson(currentOverdueDate) else currentOverdueDate)

    fun setOriginalGradeId(originalGradeId: Long) = put(this.originalGradeId, originalGradeId)

    fun setOriginalGradeName(originalGradeName: String) = put(this.originalGradeName, originalGradeName)

    fun setOriginalEffectDate(originalEffectDate: ZonedDateTime?) = put(this.originalEffectDate, if (Property.getSysOrEnv(ESCAPE_KEY, true)) JsonUtils.toJson(originalEffectDate) else originalEffectDate)

    fun setOriginalOverdueDate(originalOverdueDate: ZonedDateTime?) = put(this.originalOverdueDate, if (Property.getSysOrEnv(ESCAPE_KEY, true)) JsonUtils.toJson(originalOverdueDate) else originalOverdueDate)

    fun setRecordType(recordType: String) = put(this.recordType, recordType)

    fun setTriggerId(triggerId: String?) = put(this.triggerId, triggerId)

    fun setRecordSourceDetail(recordSourceDetail: String) = put(this.recordSourceDetail, recordSourceDetail)

    fun setDescription(description: String) = put(this.description, description)

    fun setChannel(channel: String?) = put(this.channel, channel)

    fun setChangeWay(changeWay: String) = put(this.changeWay, changeWay)

    fun initByRecord(record : MemberGradeRecord):GradeModifiedNotify{
        val gradeHierarchyId = record.gradeHierarchyId!!
        var plan: Plan
        var subject: Subject
        var hierarchy: GradeHierarchy
        var grades: List<GradeDefinition>
        try {
            plan = LoyaltyPrograms.findPlanByGradeHierarchyId(gradeHierarchyId) ?: throw Exception()
            subject = plan.subjectList!!.first()
            hierarchy = subject.gradeHierarchyList!!.first()
            grades = hierarchy.gradeDefinitions!!
        } catch (_: Exception) {
            hierarchy = ApplicationContextHolder.getBean(GradeHierarchyBaseService::class.java).getEffectiveOne(gradeHierarchyId, ZonedDateTime.now())
            grades = ApplicationContextHolder.getBean(GradeDefinitionBaseService::class.java).findByEnabledHierarchyByVersionId(hierarchy.versionId!!)
            subject = ApplicationContextHolder.getBean(SubjectBaseService::class.java).findByVersionId(hierarchy.subjectVersionId!!)
            plan = ApplicationContextHolder.getBean(PlanBaseService::class.java).findByVersionId(subject.planVersionId!!)
        }

        setTraceId(record.traceId)
        setChangeWay(record.changeWay!!.name)
        setId(record.id!!)
        setPlanId(record.planId!!)
        setPlanName(plan.name!!)
        setMemberId(record.memberId!!)
        setSubjectFqn(record.subjectFqn!!)
        setGradeHierarchyId(gradeHierarchyId)
        setGradeHierarchyName(hierarchy.name!!)

        setCurrentGradeId(record.currentGradeId!!)
        setCurrentGradeName(if(record.currentGradeId==-1L) "无等级" else grades.find { it.id == record.currentGradeId }!!.name!!)
        setCurrentEffectDate(record.currentEffectDate!!)
        setCurrentOverdueDate(record.currentOverdueDate)

        setOriginalGradeId(if(record.originalGradeId == null) -1 else record.originalGradeId!!)
        setOriginalGradeName(
                if(record.originalGradeId == null || record.originalGradeId == -1L)
                    "无等级"
                else
                    grades.find { it.id == record.originalGradeId }!!.name!!
        )
        setOriginalEffectDate(record.originalEffectDate)
        setOriginalOverdueDate(record.originalOverdueDate)

        setRecordType(record.recordType!!.name)
        setTriggerId(record.triggerId)
        record.recordSourceDetail?.let { setRecordSourceDetail(record.recordSourceDetail!!) }
        record.description?.let{setDescription(record.description!!)}
        setChannel(record.channel)
        return this
    }

}