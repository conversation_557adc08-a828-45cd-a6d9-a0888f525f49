package com.shuyun.loyalty.service.service

import com.pip.mybatisplus.pools.DmPoolFactory
import com.pip.mybatisplus.toolkit.DataapiHttpFactory
import com.pip.shuyun.pool.transaction.DmTransaction
import com.shuyun.dm.api.vo.FetchStartRequest
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.service.infrastructure.es.EventClient
import com.shuyun.loyalty.service.meta.EventOccasionEnum
import com.shuyun.loyalty.service.meta.PlanStatusEnum
import com.shuyun.loyalty.service.model.*
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.DateUtils
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.DisposableBean
import org.springframework.beans.factory.InitializingBean
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.ScheduledThreadPoolExecutor
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference


object LoyaltyPrograms {

    private val logger = LogManager.getLogger(LoyaltyPrograms::class.java)
    private val schedule = ScheduledThreadPoolExecutor(1)
    private var refreshed = AtomicBoolean(false)
    private var paused = AtomicBoolean(false)
    private var retryCount = 50

    private var planWrappersReference = AtomicReference<List<PlanWrapper>>()

    private val svc get(): LoyaltyProgramService? {
        return runCatching { ApplicationContextHolder.getBean(LoyaltyProgramService::class.java) }.getOrNull()
    }

    private fun getPlanWrappers(): List<PlanWrapper>? {
        if (planWrappersReference.get() == null) {
            if (svc == null) return emptyList()
            val plans = svc!!.findPlanWrappers()
            planWrappersReference.set(plans)
        }
        return planWrappersReference.get()
    }


    @Synchronized
    fun start() {
        if (refreshed.get()) return
        schedule.scheduleWithFixedDelay({
            try {
                if (paused.get() || svc == null) return@scheduleWithFixedDelay
                val start = System.currentTimeMillis()
                val plans = svc!!.findPlanWrappers()
                planWrappersReference.set(plans)
                if (!refreshed.get()) {
                    logger.info("====== 刷新忠诚度方案结束 耗时: ${(System.currentTimeMillis() - start) / 1000.0}s =========")
                } else {
                    logger.trace("====== 刷新忠诚度方案结束 耗时: ${(System.currentTimeMillis() - start) / 1000.0}s =========")
                }
                refreshed.compareAndSet(false, true)
            } catch (e: Exception) {
                logger.error("刷新忠诚度方案失败", e)
            }
        }, 10, 30, TimeUnit.SECONDS)
    }

    @Synchronized
    fun pause() = paused.set(true)

    @Synchronized
    fun stop() {
        pause()
        schedule.shutdownNow()
    }


    fun findEsMetas(fqn: String, useCacheFirst: Boolean = true): EventStreamMetas? {
        if (svc == null) return null
        return svc!!.getEsMetas(fqn, useCacheFirst)
    }

    fun reloadEsMetas(fqns: List<String>) {
        if (svc == null || fqns.isEmpty()) return
        return svc!!.reloadEsMetas(fqns)
    }


    fun getEsFqnList(): List<String> {
        val set = HashSet<String>()
        getPlanWrappers()?.forEach { planWrapper ->
            planWrapper.subjects.forEach { subjectWrapper ->
                subjectWrapper.eventTypes.forEach { eventType ->
                    if (eventType.eventStreamSource == ConstantValue.ES_SOURCE) {
                        set.add(eventType.eventStream!!)
                    }
                }
            }
        }
        return set.toList()
    }


    fun findPlansByFqn(fqn: String, zonedDateTime: ZonedDateTime, count: Int = 0): List<Plan> {
        if (!refreshed.get()) {
            start()
            if (count > retryCount) {
                throw IllegalStateException("刷新方案任务尚未启动")
            }
            Thread.sleep(200)
            return findPlansByFqn(fqn, zonedDateTime, count + 1)
        }
        val date = DateUtils.formatMinutesCacheKey(zonedDateTime)
        val results = ArrayList<Plan>()
        val planWrappersMap = filterByDate(date)
        planWrappersMap.forEach ec@{ (_, planWrapper) ->
            val subjectWrappers = planWrapper.subjects
            if (subjectWrappers.isEmpty()) {
                return@ec
            }
            val newSubjects = ArrayList<Subject>()
            subjectWrappers.forEach sub@ { subjectWrapper ->
                if (subjectWrapper.eventTypes.isEmpty()) {
                    return@sub
                }
                val fqnEventTypes = subjectWrapper.eventTypes.filter { it.eventStream == fqn }
                if (fqnEventTypes.isEmpty()) {
                    return@sub
                }
                val subject = subjectWrapper.subject.duplicate()
                subject.eventTypeList = fqnEventTypes.duplicate()
                subject.eventTypeList!!.forEach { setRelatedEventTypes(subjectWrapper, it) }
                val isPointEvent = subject.eventTypeList!!.any { it.occasion?.contains(EventOccasionEnum.CALC_POINT) == true }
                val isGradeEvent = subject.eventTypeList!!.any { it.occasion?.contains(EventOccasionEnum.CALC_GRADE) == true || it.operationGrade != null }
                val isMedalEvent = subject.eventTypeList!!.any { it.occasion?.contains(EventOccasionEnum.CALC_MEDAL) == true || it.operationMedal != null }

                if (isPointEvent && subjectWrapper.pointAccountTypes.isNotEmpty()) {
                    subject.pointAccountTypeList = subjectWrapper.pointAccountTypes.duplicate()
                }
                if (isGradeEvent && subjectWrapper.gradeHierarchies.isNotEmpty()) {
                    subject.gradeHierarchyList = subjectWrapper.gradeHierarchies.duplicate()
                }
                if (isMedalEvent && subjectWrapper.medalHierarchies.isNotEmpty()) {
                    subject.medalHierarchyList = subjectWrapper.medalHierarchies.duplicate()
                }

                if (!subject.pointAccountTypeList.isNullOrEmpty() ||
                    !subject.gradeHierarchyList.isNullOrEmpty() ||
                    !subject.medalHierarchyList.isNullOrEmpty()) {
                    newSubjects.add(subject)
                }
            }
            if (newSubjects.isNotEmpty()) {
                val plan = planWrapper.plan.duplicate()
                plan.subjectList = newSubjects
                results.add(plan)
            }
        }
        return results
    }


    fun findPlanById(planId: Long, zonedDateTime: ZonedDateTime = ZonedDateTime.now()): Plan? {
        val planWrapper = getPlanWrappers()
            ?.asSequence()
            ?.filter { it.plan.id == planId }
            ?.filter { it.plan.publishedTime != null }
            ?.filter { it.plan.status == PlanStatusEnum.PUBLISHED }
            ?.filter { ZonedDateTime.parse(it.plan.publishedTime!!).isBefore(zonedDateTime) }
            ?.maxByOrNull { ZonedDateTime.parse(it.plan.publishedTime!!).toInstant().toEpochMilli() } ?: return null

        val plan = planWrapper.plan.duplicate()
        val subjectWrappers = planWrapper.subjects.sortedByDescending { it.subject.versionId ?: it.subject.id }

        val subjectList = ArrayList<Subject>()
        for (subjectWrapper in subjectWrappers) {
            val subject = subjectWrapper.subject.duplicate()
            val pointAccountTypes = subjectWrapper.pointAccountTypes
                .sortedByDescending { it.versionId ?: it.id }
                .duplicate()
            val gradeHierarchies = subjectWrapper.gradeHierarchies
                .sortedByDescending { it.versionId ?: it.id }
                .duplicate()
            subject.gradeHierarchyList = gradeHierarchies
            subject.pointAccountTypeList = pointAccountTypes
            subjectList.add(subject)
        }

        plan.subjectList = subjectList
        return plan
    }

    fun findGradeHierarchy(id: Long): GradeHierarchy? {
        val hs = HashMap<Long, GradeHierarchy>()
        filterByDate().forEach { (_, planWrapper) ->
            planWrapper.subjects.forEach { subjectWrapper ->
                subjectWrapper.gradeHierarchies.firstOrNull { it.id == id }?.let {
                    hs[id] = it
                }
            }
        }
        return hs[id]
    }

    fun findMedalHierarchy(id: Long): MedalHierarchy? {
        val hs = HashMap<Long, MedalHierarchy>()
        filterByDate().forEach { (_, planWrapper) ->
            planWrapper.subjects.forEach { subjectWrapper ->
                subjectWrapper.medalHierarchies.firstOrNull { it.id == id }?.let {
                    hs[id] = it
                }
            }
        }
        return hs[id]
    }


    enum class HierarchyType { POINT, GRADE, MEDAL }

    fun findAllHierarchyIds(type: HierarchyType): Set<Long> {
        val ids = HashSet<Long>()
        filterByDate().forEach { (_, planWrapper) ->
            planWrapper.subjects.forEach { subjectWrapper ->
                when (type) {
                    HierarchyType.POINT -> subjectWrapper.pointAccountTypes.forEach { ids.add(it.id!!) }
                    HierarchyType.GRADE -> subjectWrapper.gradeHierarchies.forEach { ids.add(it.id!!) }
                    HierarchyType.MEDAL -> subjectWrapper.medalHierarchies.forEach { ids.add(it.id!!) }
                }
            }
        }
        return ids
    }

    fun findPlanByAccountTypeId(
        pointAccountTypeId: Long,
        zonedDateTime: ZonedDateTime = ZonedDateTime.now(),
    ): Plan? {
        val planWrapper = getPlanWrappers()
            ?.filter { it.plan.publishedTime != null && ZonedDateTime.parse(it.plan.publishedTime!!).isBefore(zonedDateTime) }
            ?.filter {
                it.subjects.any { subjectWrapper ->
                    subjectWrapper.pointAccountTypes.any { x -> x.id == pointAccountTypeId }
                }
            }?.maxByOrNull { ZonedDateTime.parse(it.plan.publishedTime!!).toInstant().toEpochMilli() } ?: return null

        val plan = planWrapper.plan.duplicate()

        val subjectWrapper = planWrapper.subjects
            .filter { it.pointAccountTypes.any { x -> x.id == pointAccountTypeId } }
            .sortedByDescending { it.subject.versionId ?: it.subject.id }
            .first()

        val subject = subjectWrapper.subject.duplicate()

        val pointAccountType = subjectWrapper.pointAccountTypes
            .filter { it.id == pointAccountTypeId }
            .sortedByDescending { it.versionId ?: it.id }
            .first()
            .duplicate()

        subject.pointAccountTypeList = listOf(pointAccountType)
        plan.subjectList = listOf(subject)

        return plan
    }


    private fun filterByDate(date: ZonedDateTime = ZonedDateTime.now()): Map<Long, PlanWrapper> {
        val map = HashMap<Long, PlanWrapper>()
        val planWrappers = getPlanWrappers() ?: emptyList()
        planWrappers.groupBy { it.plan.id }.forEach { (id, plans) ->
            plans.filter { it.plan.publishedTime != null }
                .filter { !ZonedDateTime.parse(it.plan.publishedTime!!).isAfter(date) }
                .maxByOrNull { ZonedDateTime.parse(it.plan.publishedTime!!) }
                ?.let { map[id!!] = it }
        }
        return map
    }


    private fun setRelatedEventTypes(subjectWrapper: SubjectWrapper, eventType: EventType) {
        eventType.occasion = eventType.occasionString?.let { Json.parse<List<EventOccasionEnum>>(it) }
        if (!eventType.relatedEventTypeIds.isNullOrBlank()) {
            val relatedEventTypeIds = eventType.relatedEventTypeIds!!.split(",").map { it.trim().toLong() }
            val relatedEventTypeList = ArrayList<EventType>()
            for (relatedEventTypeId in relatedEventTypeIds) {
                val relatedEventTypes = subjectWrapper.eventTypes.filter { it.id == relatedEventTypeId }
                for (relatedEventType in relatedEventTypes) {
                    setRelatedEventTypes(subjectWrapper, relatedEventType)
                }
                relatedEventTypeList.addAll(relatedEventTypes.duplicate())
            }
            eventType.relatedEventTypes = relatedEventTypeList
        }
    }

    fun findPlanByGradeHierarchyId(
        gradeHierarchyId: Long,
        zonedDateTime: ZonedDateTime = ZonedDateTime.now(),
    ): Plan? {
        val planWrapper = getPlanWrappers()
            ?.asSequence()
            ?.filter { it.plan.publishedTime != null }
            ?.filter { ZonedDateTime.parse(it.plan.publishedTime!!).isBefore(zonedDateTime) }
            ?.filter {
                it.subjects.any { subjectWrapper ->
                    subjectWrapper.gradeHierarchies.any { x -> x.id == gradeHierarchyId }
                }
            }?.maxByOrNull { ZonedDateTime.parse(it.plan.publishedTime!!).toInstant().toEpochMilli() } ?: return null

        val plan = planWrapper.plan.duplicate()

        val subjectWrapper = planWrapper.subjects
            .asSequence()
            .filter { it.gradeHierarchies.any { x -> x.id == gradeHierarchyId } }
            .sortedByDescending { it.subject.versionId ?: it.subject.id }
            .first()

        val subject = subjectWrapper.subject.duplicate()

        val gradeHierarchy = subjectWrapper.gradeHierarchies
            .asSequence()
            .filter { it.id == gradeHierarchyId }
            .sortedByDescending { it.versionId ?: it.id }
            .first()
            .duplicate()

        subject.gradeHierarchyList = listOf(gradeHierarchy)
        subject.eventTypeList = subjectWrapper.eventTypes.filter { it.operationGrade != null }.duplicate()
        plan.subjectList = listOf(subject)

        return plan
    }


    fun findPlanByMedalHierarchyId(
        medalHierarchyId: Long,
        zonedDateTime: ZonedDateTime = ZonedDateTime.now(),
    ): Plan? {
        val planWrapper = getPlanWrappers()
            ?.filter { it.plan.publishedTime != null && ZonedDateTime.parse(it.plan.publishedTime!!).isBefore(zonedDateTime) }
            ?.filter {
                it.subjects.any { subjectWrapper ->
                    subjectWrapper.medalHierarchies.any { x -> x.id == medalHierarchyId }
                }
            }?.maxByOrNull { ZonedDateTime.parse(it.plan.publishedTime!!).toInstant().toEpochMilli() } ?: return null

        val plan = planWrapper.plan.duplicate()

        val subjectWrapper = planWrapper.subjects
            .filter { it.medalHierarchies.any { x -> x.id == medalHierarchyId } }
            .sortedByDescending { it.subject.versionId ?: it.subject.id }
            .first()

        val subject = subjectWrapper.subject.duplicate()

        val medalHierarchy = subjectWrapper.medalHierarchies
            .filter { it.id == medalHierarchyId }
            .sortedByDescending { it.versionId ?: it.id }
            .first()
            .duplicate()

        subject.medalHierarchyList = listOf(medalHierarchy)
        subject.eventTypeList = subjectWrapper.eventTypes.filter { it.operationMedal != null }.duplicate()
        plan.subjectList = listOf(subject)

        return plan
    }


    private inline fun <reified T> T.duplicate(): T = Json.copy(this)

    private inline fun <reified T> List<T>.duplicate(): List<T> = Json.copy(this)
}

class SubjectWrapper(
    val subject: Subject,
    val eventTypes: List<EventType>,
    val pointAccountTypes: List<PointAccountType>,
    val gradeHierarchies: List<GradeHierarchy>,
    val medalHierarchies: List<MedalHierarchy>
)

class PlanWrapper(
    val plan: Plan,
    val subjects: List<SubjectWrapper>
)


@Service
class LoyaltyProgramService : InitializingBean, DisposableBean {

    companion object {
        const val CHUNKED_SIZE = 300
        const val DM_LOG_WARN_TIME_MS = 5000L
    }

    private val esMetas = ConcurrentHashMap<String, EventStreamMetas>()


    fun getEsMetas(fqn: String, useCacheFirst: Boolean = true): EventStreamMetas? {
        if (useCacheFirst) {
            val m = esMetas[fqn]
            if (m == null) {
                reloadEsMetas(listOf(fqn))
                return esMetas[fqn]
            } else {
                return m
            }
        } else {
            reloadEsMetas(listOf(fqn))
            return esMetas[fqn]
        }
    }


    fun reloadEsMetas(fqns: List<String>) {
        for (fqn in fqns) {
            val ms = runCatching {
                EventClient.findESStreamMetas(fqn)
            }.getOrNull()
            if (ms == null) {
                esMetas.remove(fqn)
            } else {
                esMetas[fqn] = ms
            }
        }
    }


    @DmTransaction
    fun findPlanWrappers(): List<PlanWrapper> {
        val allPlans = findAllPlans().groupBy { it.id }.filter { (_, v) ->
            v.any { it.status == PlanStatusEnum.PUBLISHED }
        }.values.flatten()
        val allSubjects = findSubjects(allPlans.map { it.versionId!! })
        val allSubjectsVersionIds = allSubjects.map { it.versionId!! }
        val svs = HashMap<Long, String>()
        allSubjects.forEach { svs[it.versionId!!] = it.dataType!! }
        val allEventTypes = findEventTypes(svs)
        val allPointAccountTypes = findPointAccountTypes(allSubjectsVersionIds)
        val allGradeHierarchies = findGradeHierarchies(allSubjectsVersionIds)
        val allMedalHierarchies =  findMedalHierarchies(allSubjectsVersionIds)

        val planWrappers = ArrayList<PlanWrapper>()
        for (plan in allPlans) {
            val planVersionId = plan.versionId ?: plan.id
            val subjects = allSubjects.filter { it.planVersionId == planVersionId }
            val subjectWrappers = ArrayList<SubjectWrapper>()
            for (subject in subjects) {
                val eventTypes = allEventTypes.filter { it.subjectVersionId == subject.versionId }
                val accountTypes = allPointAccountTypes.filter { it.subjectVersionId == subject.versionId }
                val gradeHierarchies = allGradeHierarchies.filter { it.subjectVersionId == subject.versionId }
                val medalHierarchies = allMedalHierarchies.filter { it.subjectVersionId == subject.versionId }
                if (accountTypes.isEmpty() && gradeHierarchies.isEmpty() && medalHierarchies.isEmpty()) {
                    continue
                }
                val subjectWrapper = SubjectWrapper(subject, eventTypes, accountTypes, gradeHierarchies, medalHierarchies)
                subjectWrappers.add(subjectWrapper)
            }
            if (subjectWrappers.isEmpty()) {
                continue
            }
            planWrappers.add(PlanWrapper(plan, subjectWrappers))
        }
        return planWrappers
    }


    // 查询主体对应的勋章体系
    private fun findMedalHierarchies(subjectVersionIds: List<Long>): List<MedalHierarchy> {
        if (subjectVersionIds.isEmpty()) return emptyList()
        val medalHierarchies = ArrayList<MedalHierarchy>()
        val list = subjectVersionIds.chunked(CHUNKED_SIZE)
        list.forEach {
            DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext(), dmLogWarnTime = DM_LOG_WARN_TIME_MS) { sdk ->
                val sql = """
                    SELECT 
                        `versionId`,`id`,`name`,`subjectVersionId`,`sort`,`executeOrder`,`status`,`disabled`,`creatorId`,`creatorName`,`createTime`,`updaterId`,`updaterName`,`updateTime`,`referencePath` 
                    FROM data.loyalty.manager.medalHierarchy  
                    WHERE subjectVersionId in (:subjectVersionIds)
                    AND disabled = 0
                    AND status = 'PUBLISHED' 
                    ORDER BY versionId
                    LIMIT 10000
                """.trimIndent()
                val params = mapOf("subjectVersionIds" to it)
                val response = sdk.execute(sql, params)
                val versionIds = ArrayList<Long>()
                val chunkedMedalHierarchies = ArrayList<MedalHierarchy>()
                for (data in response.data) {
                    val medalHierarchy = Json.convert<MedalHierarchy>(data)
                    versionIds.add(medalHierarchy.versionId!!)
                    chunkedMedalHierarchies.add(medalHierarchy)
                }
                // 勋章体系下的勋章定义
                val medalDefinitions = findMedalDefinitions(versionIds).groupBy { it.medalHierarchyVersionId }
                chunkedMedalHierarchies.forEach {
                    it.medalDefinitions = medalDefinitions[it.versionId]
                }
                medalHierarchies.addAll(chunkedMedalHierarchies)
            }
        }

        return medalHierarchies
    }


    // 查询勋章定义
    private fun findMedalDefinitions(medalHierarchyVersionIds: List<Long>): List<MedalDefinition> {
        if (medalHierarchyVersionIds.isEmpty()) return emptyList()
        val medalDefinitions = ArrayList<MedalDefinition>()
        val chunk = medalHierarchyVersionIds.chunked(CHUNKED_SIZE)
        for (ids in chunk) {
            DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext(), dmLogWarnTime = DM_LOG_WARN_TIME_MS) { medalDefinitionSDK ->
                val medalDefinitionSQL = """
                    SELECT 
                        `versionId`,`id`,`name`,`medalHierarchyVersionId`,`sort`,`disabled`,`creatorId`,`creatorName`,`createTime`,`updaterId`,`updaterName`,`updateTime`,`icon` 
                    FROM data.loyalty.manager.medalDefinition
                    WHERE disabled = 0 
                    AND medalHierarchyVersionId in (:medalHierarchyVersionIds)
                    ORDER BY sort ASC 
                    LIMIT 10000
                """.trimIndent()
                val medalDefinitionParams = mapOf("medalHierarchyVersionIds" to ids)
                val medalDefinitionResponse = medalDefinitionSDK.execute(medalDefinitionSQL, medalDefinitionParams)
                for (medalDefinitionData in medalDefinitionResponse.data) {
                    val medalDefinition = Json.convert<MedalDefinition>(medalDefinitionData)
                    medalDefinitions.add(medalDefinition)
                }
            }
        }
        return medalDefinitions
    }


    // 查询主体对应的等级体系
    private fun findGradeHierarchies(subjectVersionIds: List<Long>): List<GradeHierarchy> {
        if (subjectVersionIds.isEmpty()) return emptyList()
        val gradeHierarchies = ArrayList<GradeHierarchy>()
        val list = subjectVersionIds.chunked(CHUNKED_SIZE)
        list.forEach {
            DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext(), dmLogWarnTime = DM_LOG_WARN_TIME_MS) { sdk ->
                val sql = """
                    SELECT 
                        `versionId`,`id`,`subjectVersionId`,`status`,`name`,`disabled`,`creatorId`,`creatorName`,`createTime`,`updaterId`,`updaterName`,`updateTime`,`referencePath`,`sort`,`executeOrder`,`defaultDegradeNum`,`defaultDegradeOverdue`,`lowestDegradeTarget`,`lowestDegradeType` 
                    FROM data.loyalty.manager.gradeHierarchy
                    WHERE subjectVersionId in (:subjectVersionIds) 
                    AND disabled = 0
                    AND status = 'PUBLISHED' 
                    ORDER BY versionId
                    LIMIT 10000
                """.trimIndent()
                val params = mapOf("subjectVersionIds" to it)
                val response = sdk.execute(sql, params)
                val chunkedGradeHierarchies = ArrayList<GradeHierarchy>()
                val gradeHierarchiesVersionIds = ArrayList<Long>()
                for (data in response.data) {
                    val gradeHierarchy = Json.convert<GradeHierarchy>(data)
                    gradeHierarchiesVersionIds.add(gradeHierarchy.versionId!!)
                    chunkedGradeHierarchies.add(gradeHierarchy)
                }
                // 等级体系下的等级定义
                val gradeDefinitions = findGradeDefinitions(gradeHierarchiesVersionIds).groupBy { it.gradeHierarchyVersionId!! }
                chunkedGradeHierarchies.forEach {
                    it.gradeDefinitions = gradeDefinitions[it.versionId]
                }
                gradeHierarchies.addAll(chunkedGradeHierarchies)
            }
        }

        return gradeHierarchies
    }


    // 查询等级定义
    private fun findGradeDefinitions(gradeHierarchyVersionIds: List<Long>): List<GradeDefinition> {
        if (gradeHierarchyVersionIds.isEmpty()) return emptyList()
        val gradeDefinitions = ArrayList<GradeDefinition>()
        val chunk = gradeHierarchyVersionIds.chunked(CHUNKED_SIZE)
        for (ids in chunk) {
            DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext(), dmLogWarnTime = DM_LOG_WARN_TIME_MS) { gradeDefinitionSDK ->
                val gradeDefinitionSQL = """
                    SELECT 
                        `versionId`,`id`,`gradeHierarchyVersionId`,`moveId`,`disabled`,`creatorId`,`name`,`creatorName`,`createTime`,`updaterId`,`updaterName`,`updateTime`,`sort` 
                    FROM data.loyalty.manager.gradeDefinition 
                    WHERE disabled = 0 
                    AND gradeHierarchyVersionId in (:gradeHierarchyVersionIds)
                    ORDER BY sort ASC
                    LIMIT 10000
                """.trimIndent()
                val gradeDefinitionParams = mapOf("gradeHierarchyVersionIds" to ids)
                val gradeDefinitionResponse =
                    gradeDefinitionSDK.execute(gradeDefinitionSQL, gradeDefinitionParams)
                for (gradeDefinitionData in gradeDefinitionResponse.data) {
                    val gradeDefinition = Json.convert<GradeDefinition>(gradeDefinitionData)
                    gradeDefinitions.add(gradeDefinition)
                }
            }
        }

        return gradeDefinitions
    }


    fun fetch(sql: String, callback: (List<Map<String, Any>>) -> Unit) {
        val request = FetchStartRequest(sql, mapOf(),1000)
        DataapiHttpFactory.httpSdk().fetch(request).use { fetchData ->
            var data: List<Map<String, Any>>
            do {
                data = fetchData.next().data
                if (data.isEmpty()) {
                    break
                }
                callback(data)
            } while (data.isNotEmpty())
        }
    }


    // 查询所有计划
    private fun findAllPlans(): List<Plan> {
        val plans = ArrayList<Plan>()
        val sql = """
            SELECT 
                `versionId`,`id`,`status`,`name`,`description`,`sortType`,`disabled`,`creatorId`,`creatorName`,`createTime`,`updaterId`,`updaterName`,`updateTime`,`backup`,`publishedTime`,`filedTime`,`specialConditionName`,`specialConditionTagName`,`specialConditionCohortName`,`specialConditionStatus`,`specialConditionOccasionString`,`gradeReferenceTime`,`pointReferenceTime`,`sendLimitRuleSwitch`,`medalReferenceTime` 
            FROM data.loyalty.manager.plan 
            WHERE disabled = 0 AND (status='PUBLISHED' OR status = 'FILED')
        """.trimIndent()
        fetch(sql) { data ->
            data.forEach {
                val plan = Json.convert<Plan>(it)
                plans.add(plan)
            }
        }
        plans.sortBy { it.id }
        return plans
    }


    // 查询计划下的主体
    private fun findSubjects(planVersionIds: List<Long>): List<Subject> {
        if (planVersionIds.isEmpty()) return emptyList()
        val subjects = ArrayList<Subject>()
        val list = planVersionIds.chunked(CHUNKED_SIZE)
        list.forEach {
            DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext(), dmLogWarnTime = DM_LOG_WARN_TIME_MS) { sdk ->
                val sql = """
                    SELECT 
                        `versionId`,`id`,`planVersionId`,`status`,`name`,`dataType`,`disabled`,`creatorId`,`creatorName`,`createTime`,`updaterId`,`updaterName`,`updateTime`,`sort`,`mergeFqn`,`hasMerge` 
                    FROM data.loyalty.manager.subject 
                    WHERE planVersionId in (:planVersionIds) 
                    AND disabled = 0 
                    AND status = 'ENABLED'
                    ORDER BY versionId
                    LIMIT 10000
                """.trimIndent()
                val params = mapOf("planVersionIds" to it)
                val response = sdk.execute(sql, params)
                for (data in response.data) {
                    val subject = Json.convert<Subject>(data)
                    subjects.add(subject)
                }
            }
        }
        return subjects
    }


    // 查询主体对应的时机
    private fun findEventTypes(svs: Map<Long, String>): List<EventType> {
        if (svs.isEmpty()) return emptyList()
        val eventTypes = ArrayList<EventType>()
        val subjectVersionIds = svs.keys.toList()
        val list = subjectVersionIds.chunked(CHUNKED_SIZE)
        for (ids in list) {
            DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext(), dmLogWarnTime = DM_LOG_WARN_TIME_MS) { sdk ->
                val sql = """
                    SELECT 
                        `versionId`,`id`,`subjectVersionId`,`status`,`name`,`eventStream`,`disabled`,
                        `creatorId`,`creatorName`,`createTime`,`updaterId`,`updaterName`,`updateTime`,
                        `referencePath`,`sort`,`operation`,`originalOrderPath`,`relatedEventTypeIds`,`occasionString`,
                        `operationGrade`,`channelTypePath`,`shopIdPath`,`KZZD1Path`,`KZZD2Path`,`KZZD3Path`,
                        `pointSendCycleTimePath`,`limitType`,`limitTime`,`operationMedal` ,eventStreamSource,
                        `matchingTimePathForPointsRule`,`matchingTimePathForMedalRule`,`matchingTimePathForGradeRule`,
                        `delayedEventStrategy`, `delayedEventPath`,`discardDelayedEventAfter`
                    FROM data.loyalty.manager.eventType 
                    WHERE subjectVersionId in (:subjectVersionIds) 
                    AND disabled = 0 
                    AND status = 'ENABLED'
                    ORDER BY versionId
                    LIMIT 10000
                """.trimIndent()
                val params = mapOf("subjectVersionIds" to ids)
                val response = sdk.execute(sql, params)
                for (data in response.data) {
                    val eventType = Json.convert<EventType>(data)
                    if (eventType.eventStreamSource == ConstantValue.ES_SOURCE) {
                        val ms = esMetas[eventType.eventStream] ?: runCatching {
                            EventClient.findESStreamMetas(eventType.eventStream!!)
                        }.getOrNull()
                        if (ms != null) {
                            esMetas[eventType.eventStream!!] = Json.copy(ms)
                            val references = Json.parse<Map<String, String>>(ms.references!!)
                            val subjectFqn = svs[eventType.subjectVersionId]!!
                            if (references.contains(subjectFqn)) {
                                ms.references = Json.toJson(references[subjectFqn])
                                eventType.eventStreamMetas = ms
                            }
                        }
                    }
                    eventTypes.add(eventType)
                }
            }
        }
        return eventTypes
    }


    // 查询主体对应的积分系统
    private fun findPointAccountTypes(subjectVersionIds: List<Long>): List<PointAccountType> {
        if (subjectVersionIds.isEmpty()) return emptyList()
        val pointAccountTypes = ArrayList<PointAccountType>()
        val list = subjectVersionIds.chunked(CHUNKED_SIZE)
        list.forEach {
            DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext(), dmLogWarnTime = DM_LOG_WARN_TIME_MS) { sdk ->
                val sql = """
                    SELECT 
                        `versionId`,`id`,`subjectVersionId`,`status`,`name`,`unit`,`precision`,`rounding`,`negativeStrategy`,`topLimit`,`description`,`sort`,`disabled`,`creatorId`,`creatorName`,`createTime`,`updaterId`,`updaterName`,`updateTime`,`executeOrder`,`singleTopLimit`,`priorityDeduction`,`processSwitch`,`processId`,`sendLimitRule` 
                    FROM data.loyalty.manager.pointAccountType 
                    WHERE subjectVersionId in (:subjectVersionIds) 
                    AND disabled = 0
                    AND status = 'PUBLISHED' 
                    ORDER BY versionId
                    LIMIT 10000
                """.trimIndent()
                val params = mapOf("subjectVersionIds" to it)
                val response = sdk.execute(sql, params)
                for (data in response.data) {
                    val pointAccountType = Json.convert<PointAccountType>(data)
                    pointAccountTypes.add(pointAccountType)
                }
            }
        }

        return pointAccountTypes
    }


    override fun destroy() {
        LoyaltyPrograms.stop()
    }

    override fun afterPropertiesSet() {
        LoyaltyPrograms.start()
    }
}

