package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.util.converter.ZonedDateTime2StringConverter
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component
import java.time.ZonedDateTime
import java.util.*

@Component
class EventTypeRepository: DataModelCarryRepository<EventType>() {

    private val log = LogManager.getLogger(EventTypeRepository::class.java)
    override fun log(): Logger = log

    fun findEventTypeByFqn(subjectVersionId: Long): List<EventType> {
        val sql = "select " +
                " channelTypePath,subjectVersionId,eventStream,originalOrderPath,operationGrade,operationMedal," +
                " matchingTimePathForPointsRule,matchingTimePathForMedalRule,matchingTimePathForGradeRule, delayedEventStrategy, delayedEventPath,discardDelayedEventAfter," +
                " operation,status " +
                "from data.loyalty.manager.eventType where disabled=0 and subjectVersionId = :subjectVersionId"
        val params = HashMap<String, Any>()
        params["subjectVersionId"] = subjectVersionId
        return executeList(sql,params)
    }


    fun findBySubjectVersionIdAndDisabled(subjectVersionId: Long, disabled: Boolean): List<EventType> {
        val params = HashMap<String, Any?>()
        params["subjectVersionId"] = subjectVersionId
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params))
    }


    fun findBySubjectVersionIdAndDisabled(subjectVersionId: Long, status: EnableStatusEnum, disabled: Boolean): List<EventType> {
        val params = HashMap<String, Any?>()
        params["subjectVersionId"] = subjectVersionId
        params["disabled"] = disabled
        params["status"] = status
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findBySubjectVersionId(subjectVersionId: Long): List<EventType> {
        val params = HashMap<String, Any?>()
        params["subjectVersionId"] = subjectVersionId
        return findListByFilter(JsonUtils.toJson(params))
    }


    fun findByIdAndDate(id:Long, date: ZonedDateTime): Optional<EventType> {
        val sql = "select e.limitType, e.limitTime, e.pointSendCycleTimePath, e.shopIdPath, e.KZZD1Path, e.KZZD2Path, e.KZZD3Path, " +
                "e.channelTypePath,e.name,e.eventStream,e.occasionString,e.operation,e.operationGrade,e.operationMedal,e.originalOrderPath,e.relatedEventTypeIds," +
                "e.matchingTimePathForPointsRule,e.matchingTimePathForMedalRule,e.matchingTimePathForGradeRule, e.delayedEventStrategy, e.delayedEventPath,e.discardDelayedEventAfter," +
                "e.subjectVersionId,e.id,referencePath,e.sort,e.status,e.versionId " +
                "from data.loyalty.manager.eventType e " +
                "inner join data.loyalty.manager.subject s on e.subjectVersionId = s.versionId " +
                "inner join data.loyalty.manager.plan p on s.planVersionId = p.versionId " +
                "where e.disabled=0 and s.disabled=0 and p.disabled=0 and e.id = :id and (p.status='PUBLISHED' OR p.status = 'FILED') " +
                "and e.status = 'ENABLED' and p.publishedTime <=:date and (p.filedTime>=:date or p.filedTime is null) order by e.subjectVersionId desc"
        val params = HashMap<String, Any>()
        params["id"] = id
        params["date"] = ZonedDateTime2StringConverter().convert(date)!!
        return executeOne(sql, params)
    }
}