package com.shuyun.loyalty.service.model

import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.message.EventStreamEventMessage
import com.shuyun.loyalty.service.message.point.PointAccountEventMessage
import java.util.*


class PointAccountExecuteOrder(
    plan: Plan,
    val subject: Subject,
    val pointAccountType: PointAccountType,
    val eventType: EventType,
    val matchingTime: Date,
    order: Int
): ExecuteOrder(plan, order) {

    /** 构建执行事件 */
    override fun buildMessage(event: Event): EventStreamEventMessage {
        val pointAccountEventMessage = PointAccountEventMessage(this, event, matchingTime)
        pointAccountEventMessage.setEvent(event)
        return pointAccountEventMessage
    }
}


abstract class ExecuteOrder(
    val plan: Plan,
    val order: Int
) {

    /** 构建执行事件 */
    abstract fun buildMessage(event: Event): EventStreamEventMessage

}