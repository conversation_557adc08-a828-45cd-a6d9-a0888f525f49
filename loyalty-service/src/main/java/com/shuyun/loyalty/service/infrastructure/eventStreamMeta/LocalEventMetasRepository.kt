package com.shuyun.loyalty.service.infrastructure.eventStreamMeta

import com.github.benmanes.caffeine.cache.Caffeine
import com.shuyun.lite.util.Common
import com.shuyun.loyalty.service.model.EventStreamMetas
import com.shuyun.loyalty.service.repository.EventStreamMetasRepository
import com.shuyun.loyalty.service.service.LoyaltyPrograms
import com.shuyun.pip.frameworkext.filter.VisitTenantInfoHolder
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.jvm.optionals.getOrNull

@Component
class LocalEventMetasRepository {

    private val log = LogManager.getLogger(LocalEventMetasRepository::class.java)

    @Autowired
    private lateinit var eventStreamMetasRepository: EventStreamMetasRepository


    private val modelCache = Caffeine.newBuilder()
        .refreshAfterWrite(Common.getInteger("local.cache.refreshAfterWrite", 300).toLong(), TimeUnit.SECONDS)
        .expireAfterAccess(7, TimeUnit.DAYS)
        .maximumSize(10000)
        .softValues()
        .build<Pair<String,String>, Optional<EventStreamMetas>> { (fqn, tenantId) ->
            log.debug("fqn:{} , tenantId:{} ", fqn, tenantId)
             eventStreamMetasRepository.findOne { root, _, cb ->
                cb.and(
                    cb.equal(root.get<String>("fqn"), fqn),
                    root.get<String>("tenantId").`in`("default", tenantId)
                )
            }
        }

    fun findById(fqn: String): EventStreamMetas? {
        return modelCache[Pair(fqn, VisitTenantInfoHolder.getTenantId())]?.getOrNull() ?: LoyaltyPrograms.findEsMetas(fqn)
    }
}