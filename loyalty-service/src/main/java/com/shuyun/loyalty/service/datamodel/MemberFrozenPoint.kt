package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.service.MemberFrozenPointService
import com.shuyun.loyalty.service.service.MemberPointFrozenStatementService
import com.shuyun.pip.ApplicationContextHolder
import javax.persistence.Column
import javax.persistence.OneToMany
import javax.persistence.Table

/** 虽然目前冻结与锁定看起来数据模型一样，但还是不考虑设计成一个。 */
/**
 * 会员冻结积分记录
 * 根据场景分析，会员积分冻结一定时根据有效获取记录进行冻结，所以，积分获取记录可以记录在memberFrozenPoint中，作为根据积分获取记录，查询冻结记录
 */
@DataServiceModel
@FqnVariableModel
@Table(name = "data.loyalty.member.account.FrozenPoint{*}")
class MemberFrozenPoint() : SubMemberPoint() {

    @Column
    lateinit var fromStatus: FSMPointEvent

    @Column
    lateinit var gainStatementId: String

    @OneToMany
    @Suppress("unused")
    var memberPointFrozenStatement: List<MemberPointFrozenStatement>? = null

    fun saveOrUpdate(accountTypeId: Long) = ApplicationContextHolder.getBean(MemberFrozenPointService::class.java).saveOrUpdate(this, replacePattern = accountTypeId.toString())

    fun save(accountTypeId: Long) = ApplicationContextHolder.getBean(MemberFrozenPointService::class.java).save(this, replacePattern = accountTypeId.toString())

    fun delete(accountTypeId: Long) = ApplicationContextHolder.getBean(MemberFrozenPointService::class.java).delete(this.id!!, replacePattern = accountTypeId.toString())

    fun deleteStatement(accountTypeId: Long) = ApplicationContextHolder.getBean(MemberPointFrozenStatementService::class.java).deleteByFrozenId(this.id!!, accountTypeId)

}