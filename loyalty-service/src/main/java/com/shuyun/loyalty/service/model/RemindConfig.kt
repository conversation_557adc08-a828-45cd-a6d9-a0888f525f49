package com.shuyun.loyalty.service.model

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.entity.api.constants.IntervalUnit
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.meta.RemindType
import com.shuyun.loyalty.service.meta.TimeTypeEnum
import com.shuyun.loyalty.service.util.ModelInitUtil
import io.swagger.v3.oas.annotations.media.Schema
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.EnumType
import javax.persistence.Enumerated
import javax.persistence.Table

/**
 * 到期配置
 */
@Schema(title = "到期配置")
@Table(name = "data.loyalty.manager.remindConfig")
@DataServiceModel
data class RemindConfig(

    @Schema(title = "名称", type = "String")
    @Column
    var name: String? = null,

    @Schema(title = "计划ID", type = "String")
    @Column
    var planVersionId: Long? = null,

    @Schema(title = "主体ID", type = "String")
    @Column
    var subjectVersionId: Long? = null,

    @Schema(title = "过期ID", type = "String")
    @Column
    var id: Long? = null,

    @Schema(title = "积分账户id或者等级体系id", type = "Int")
    @Column
    var parentId: Long? = null,

    @Schema(title = "积分账户id或者等级体系id", type = "Int")
    @Column
    var parentVersionId: Long? = null,

    @Schema(title = "到期类型", type = "String")
    @Enumerated(EnumType.STRING)
    @Column
    var remindType: RemindType? = null,

    @Schema(title = "时间类型，相对/绝对", type = "String")
    @Enumerated(EnumType.STRING)
    @Column
    var timeType: TimeTypeEnum? = null,

    @Schema(title = "天数", type = "Long")
    @Column
    var timeDay: Int? = null,

    @Schema(title = "月", type = "Long")
    @Column
    var timeMonth: Int? = null,

    @Schema(title = "年", type = "Long")
    @Column
    var timeYear: Int? = null,

    @Schema(title = "符号", type = "String")
    @Column
    var timeSign: String? = null,

    @Schema(title = "绝对时间", type = "String")
    @Column
    var timeDate: ZonedDateTime? = null,

    @Schema(title = "发送时间", type = "Int")
    @Column
    var cycleDay: Int? = null,

    @Schema(title = "提醒间隔单位", type = "date")
    @Enumerated(EnumType.STRING)
    @Column
    var intervalUnit: IntervalUnit? = null,

    @Schema(title = "提醒间隔值", type = "date")
    @Column
    var intervalValue: Int? = null,

    @Schema(title = "前端排序", type = "Int")
    @Column
    var sort: Int? = null,

    @Schema(title = "状态", type = "String")
    @Enumerated(EnumType.STRING)
    @Column
    var status: EnableStatusEnum? = null,

    @Schema(title = "主体表", type = "String")
    @Column
    var subjectFqn: String? = null,

    @Schema(title = "记录表名", type = "String")
    @Column
    var remindTableName: String? = null,

    @Schema(title = "完成时间", type = "date")
    @Column
    var completeTime: String? = null

) : BaseDataServiceModel() {
    override fun copyToOldOne(old: BaseDataServiceModel, backup: Boolean) {
        ModelInitUtil.copyPropertiesIgnoreNull(this, old)
    }

}