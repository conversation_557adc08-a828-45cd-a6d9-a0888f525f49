package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.EventOccasionEnum
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.meta.PropertyBelongerTypeEnum
import com.shuyun.loyalty.service.model.EventStreamMetas
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.loyalty.service.repository.EventTypeRepository
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.ZonedDateTime

@Service
class EventTypeBaseService {

    private val logger = LogManager.getLogger(EventTypeBaseService::class.java)

    @Autowired
    private lateinit var eventTypeRepository: EventTypeRepository

    @Autowired
    private lateinit var customizedPropertyBaseService: CustomizedPropertyBaseService

    @Autowired
    private lateinit var eventStreamMetasBaseService: EventStreamMetasBaseService

    @Autowired
    private lateinit var propertyTemplateBaseService: PropertyTemplateBaseService

    /**新增时机本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun insert(eventType: EventType) {
        eventType.name = eventType.name!!.trim()
        eventType.versionId = null
        val eventStreamMetas = if (eventType.eventStreamSource == ConstantValue.ES_SOURCE) {
            eventStreamMetasBaseService.findESStreamMetas(eventType.eventStream!!, eventType.subjectFqn!!)
        } else {
            eventStreamMetasBaseService.findByFqn(eventType.eventStream!!)
        } ?: throw LoyaltyException(LoyaltyExceptionCode.EVENT_NOT_MATCH)
        eventType.referencePath = eventStreamMetas.getReferencePathJson()
        eventType.status = EnableStatusEnum.DRAFT
        eventTypeRepository.save(eventType)
        eventType.id = eventType.versionId
        eventTypeRepository.save(eventType)
    }

    /**更新时机本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun update(eventType: EventType,  eventStreamMetas: EventStreamMetas) {
        eventType.name = eventType.name!!.trim()
        eventType.referencePath = eventStreamMetas.getReferencePathJson()
        eventTypeRepository.save(eventType)
    }

    /**删除时机本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun delete(eventType: EventType) {
        eventType.disabled = true
        eventTypeRepository.save(eventType)
    }


    fun findCacheEventTypeBySubjectVersionIdAndFqn(subjectVersionId: Long, fqn: String): EventType?{
        return eventTypeRepository.findEventTypeByFqn(subjectVersionId).filter { it.status == EnableStatusEnum.ENABLED }.find { it.eventStream == fqn }
    }

    /**根据主体ID获取时机详情*/
    fun findDetailBySubjectVersionId(subjectVersionId: Long): List<EventType> {
        val eventTypes = eventTypeRepository.findBySubjectVersionIdAndDisabled(subjectVersionId, false)
        eventTypes.forEach {
            it.customizedPropertyList = customizedPropertyBaseService.findDetailByBelongerVersionIdAndBelongerType(it.versionId!!, PropertyBelongerTypeEnum.EVENT)
            it.propertyTemplateList = propertyTemplateBaseService.findDetailByBelongerVersionIdAndBelongerType(it.versionId!!, PropertyBelongerTypeEnum.EVENT)
            if (it.occasionString.isNullOrEmpty()) {
                it.occasion = emptyList()
            } else {
                it.occasion = JsonUtils.parse2List(it.occasionString, EventOccasionEnum::class.java)
            }
        }
        return eventTypes
    }

    /**根据时间及生效ID获取生效对象*/
    fun getEffectiveOne(id: Long,date: ZonedDateTime) = findByIdAndDate(id,date)

    fun getEffectiveEventTypeName(id: Long, date: ZonedDateTime):String? = findByIdAndDate(id,date).name

    fun findByIdAndDate(id: Long,date: ZonedDateTime):EventType{
        logger.debug("通过时机ID({})和时间({})查询时机项", id, date)
        val eventType = eventTypeRepository.findByIdAndDate(id, date)
        return if(eventType.isPresent) eventType.get() else throw LoyaltyException(LoyaltyExceptionCode.EVENT_TYPE_NOT_FOUND)
    }

}

