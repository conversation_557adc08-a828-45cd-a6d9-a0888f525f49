package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.sdk.Limits
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.meta.PointSendLimitType
import com.shuyun.loyalty.service.util.ConstantValue
import java.math.BigDecimal
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.EnumType
import javax.persistence.Enumerated
import javax.persistence.Table

/**
 * 积分发放上限累计表
 */
@Table(name = "data.loyalty.member.point.SendLimitCalc{*}")
@DataServiceModel
@FqnVariableModel
class MemberPointSendLimitCalc : BaseDataModel() {
    @Column
    var pointAccountId: Long? = null
    @Column
    var planId: Long? = null
    @Column
    var memberId: String? = null
    @Column
    var created: ZonedDateTime? = null
    @Column
    var modified: ZonedDateTime = ZonedDateTime.now()
    @Column
    @Enumerated(EnumType.STRING)
    var type: PointSendLimitType? = null
    @Column
    var value: String? = null
    @Column
    var refId: String? = null
    //refId与refId合并值, 用于时机、规则组、规则、会员全部查询出来, in查询
    @Column
    var refIdType: String? = null
    @Transient
    var valueMap: MutableMap<String, Number>? = null
}

object MemberPointSendLimitCalcMapper {

    fun build(type: PointSendLimitType, pointAccountId: Long, planId: Long, memberId: String, refId: String, now: ZonedDateTime = ZonedDateTime.now()): MemberPointSendLimitCalc {
        return MemberPointSendLimitCalc().apply {
            this.type = type
            this.pointAccountId = pointAccountId
            this.planId = planId
            this.memberId = memberId
            this.refId = refId
            this.value = Limits.buildLimitValue(now).toString()
            this.created = now
            this.refIdType = refId + ConstantValue.HORIZON + type
        }
    }


    fun updateValueOfCycle(memberPointSendLimitCalc: MemberPointSendLimitCalc,now: ZonedDateTime) {
        val m = Limits.incrementAndGet(memberPointSendLimitCalc.value!!, BigDecimal.ZERO, 0, now)
        memberPointSendLimitCalc.value = m.toString()
        memberPointSendLimitCalc.valueMap = m.toMutableMap()
    }
}


