package com.shuyun.loyalty.service.util

import com.shuyun.loyalty.entity.enums.ProcessRecordTypeEnum
import com.shuyun.loyalty.service.datamodel.ProcessRecord
import com.shuyun.loyalty.service.repository.DataModelRepository
import com.shuyun.loyalty.service.repository.ProcessRecordRepository
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager

object ProcessRecordUtil {

    private val log = LogManager.getLogger(ProcessRecordUtil::class.java)
    private val removeRepeatRepository: DataModelRepository<ProcessRecord> = ApplicationContextHolder.getBean(ProcessRecordRepository::class.java)

    /**判断数据服务是否有事件，有返回true，没有返回false*/
    fun isProcessRecord(effectId: String, key: String, type: ProcessRecordTypeEnum): Boolean {
        if (!MessageUtils.isIgnoreIdempotent() && type == ProcessRecordTypeEnum.POINT) {
            return false
        }
        var flag = true
        try {
            val params = HashMap<String, Any?>()
            params["effectId"] = effectId
            params["key"] = key
            params["type"] = type
            if (!removeRepeatRepository.findOneByFilter(JsonUtils.toJson(params)).isPresent) {
                val repeatObj = ProcessRecord()
                repeatObj.effectId = effectId
                repeatObj.key = key
                repeatObj.type = type
                removeRepeatRepository.saveOrUpdate(repeatObj, replacePattern = effectId)
                log.debug("添加事件记录 :{} ", { JsonUtils.toJson(repeatObj) })
                flag = false
            }
        } catch (e: Exception) {
            log.error("任务执行过程中失败, effectId:{}, key: {}, type: {}", effectId, key, type, e)
            throw e
        }
        return flag
    }

    /**删除数据服务事件记录*/
    fun delProcessRecord(effectId:String, key:String, type: ProcessRecordTypeEnum){
        if(!MessageUtils.isIgnoreIdempotent()) {
            return
        }

        val locker = ApplicationContextHolder.getBean(Locker::class.java)
        val lock = locker.getLock("processRecord_"+effectId+"_"+key+"_"+type)
        try {
            lock.lock()
            val params = HashMap<String, Any?>()
            params["effectId"] = effectId
            params["key"] = key
            params["type"] = type
            removeRepeatRepository.deleteByFilter(JsonUtils.toJson(params), effectId)
            log.debug("删除事件记录, effectId: {}, key: {}, type: {}", effectId, key, type)
        } catch (e: Exception) {
            log.error("任务执行过程中失败, effectId: {}, key: {}, type: {}", effectId, key, type, e)
        } finally {
            lock.unlock()
        }
    }
}