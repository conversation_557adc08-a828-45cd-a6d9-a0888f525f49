package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.service.datamodel.MemberMedal
import com.shuyun.loyalty.service.repository.MemberMedalRepository
import com.shuyun.pip.component.json.JsonUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.lang.Boolean.FALSE
import java.util.*

@Service
class MemberMedalBaseService {

    @Autowired
    private lateinit var memberMedalRepository: MemberMedalRepository

    fun findOneByFilter(filter: String, medalHierarchyId: Long, sort: String = "{}"): Optional<MemberMedal> {
        return memberMedalRepository.findOneByFilter(filter, medalHierarchyId.toString(), sort)
    }

    fun saveOrUpdate(memberMedal: MemberMedal, medalHierarchyId: Long): MemberMedal {
        return memberMedalRepository.saveOrUpdate(memberMedal, medalHierarchyId.toString())
    }

    fun currentMedalDefinitionIds(memberId: String, medalHierarchyId: Long): List<Long?> {
        return currentMedalDefinition(memberId, medalHierarchyId, FALSE).map { it.medalDefinitionId }
    }

    fun currentMedalDefinition(memberId: String, medalHierarchyId: Long, disabled: Boolean?): List<MemberMedal> {
        val params = mutableMapOf<String, Any?>()
        params["memberId"] = memberId
        params["disabled"] = disabled
        val filter = JsonUtils.toJson(params)
        return memberMedalRepository.findListByFilter(filter, "{}", 0, 3000, medalHierarchyId.toString())
    }

    fun findMemberMedal(memberId: String, medalHierarchyId: Long, medalDefinitionId: Long,
                        disabled: Boolean?): Optional<MemberMedal> {
        val params = mutableMapOf<String, Any?>()
        params["memberId"] = memberId
        params["medalDefinitionId"] = medalDefinitionId
        params["disabled"] = disabled
        return findOneByFilter(JsonUtils.toJson(params), medalHierarchyId)
    }

    fun save(memberMedal: MemberMedal, hierarchyId: Long): MemberMedal {
        return memberMedalRepository.save(memberMedal, hierarchyId.toString())
    }



}