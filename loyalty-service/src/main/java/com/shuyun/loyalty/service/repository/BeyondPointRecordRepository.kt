package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.datamodel.BeyondPointRecord
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

@Component
class BeyondPointRecordRepository: DataModelRepository<BeyondPointRecord>() {
    private val log = LogManager.getLogger(BeyondPointRecordRepository::class.java)
    override fun log(): Logger = this.log
}