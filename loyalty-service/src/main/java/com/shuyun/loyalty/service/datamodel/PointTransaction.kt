package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.service.PointTransactionBaseService
import com.shuyun.pip.ApplicationContextHolder
import java.math.BigDecimal
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.Table

abstract class BasePointTransaction : BaseDataModel() {
    @Column
    var businessId: String? = null

    @Column
    var point: BigDecimal = BigDecimal.ZERO

    @Column
    var recordType: String? = null

    @Column
    var memberId: String? = null

    @Column
    var created: ZonedDateTime? = null

    @Column
    var modified: ZonedDateTime? = null
}

/**
 * 积分事物正向操作表
 */
@DataServiceModel
@FqnVariableModel
@Table(name = "data.loyalty.member.account.PointTransaction{*}")
open class PointTransaction : BasePointTransaction() {

    fun saveOrUpdate(pointPlanId: String): PointTransaction {
        return ApplicationContextHolder.getBean(PointTransactionBaseService::class.java).saveOrUpdate(this, pointPlanId)
    }

    fun insert(pointPlanId: String): PointTransaction {
        return ApplicationContextHolder.getBean(PointTransactionBaseService::class.java).save(this, pointPlanId)
    }

}