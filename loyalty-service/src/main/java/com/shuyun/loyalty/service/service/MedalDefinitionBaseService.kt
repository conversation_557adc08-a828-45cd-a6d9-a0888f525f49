package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.model.MedalDefinition
import com.shuyun.loyalty.service.repository.MedalDefinitionRepository
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.lang.Boolean.FALSE
import java.time.ZonedDateTime
import java.util.*

@Service
class MedalDefinitionBaseService {

    private val log = LogManager.getLogger(MedalDefinitionBaseService::class.java)

    @Autowired
    private lateinit var medalDefinitionRepository: MedalDefinitionRepository

    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun insert(medalDefinition: MedalDefinition) {
        val medalDef = if (medalDefinition.versionId != null) {
            medalDefinition.deepCopy()
        } else {
            medalDefinition.versionId = null
            medalDefinition
        }
        medalDefinitionRepository.save(medalDef)
        medalDef.id = medalDef.versionId
        medalDefinitionRepository.save(medalDef)
    }

    fun findEnabledMedalHierarchyByVersionId(medalHierarchyVersionId: Long): List<MedalDefinition> {
        log.info("根据等勋章体系versionId查询勋章:{}", medalHierarchyVersionId)
        return medalDefinitionRepository.findByMedalHierarchyVersionIdAndDisabled(medalHierarchyVersionId, FALSE)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun delete(medalDefinition: MedalDefinition) {
        medalDefinition.disabled = true
        medalDefinitionRepository.save(medalDefinition)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun save(medalDefinition: MedalDefinition) {
        medalDefinitionRepository.save(medalDefinition)
    }

    fun getEffectiveOne(id: Long, date: ZonedDateTime): MedalDefinition {
        return medalDefinitionRepository.findByIdAndDate(id, date).orElseThrow { IllegalArgumentException("勋章不存在 $id") }
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun update(medalDefinition: MedalDefinition) {
        medalDefinitionRepository.save(medalDefinition)
    }

    fun findById(id: Long): Optional<MedalDefinition> {
        return medalDefinitionRepository.findById(id.toString())
    }

    fun findByVersionId(versionId: Long): MedalDefinition {
        return medalDefinitionRepository.findByVersionId(versionId).orElseGet {
            throw LoyaltyException(LoyaltyExceptionCode.MEDAL_DEFINITION_NOT_FOUND)
        }
    }

}