package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.meta.PropertyBelongerTypeEnum
import com.shuyun.loyalty.service.meta.TypeEnum
import com.shuyun.loyalty.service.model.Subject
import com.shuyun.loyalty.service.repository.SubjectRepository
import com.shuyun.loyalty.service.util.DateUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import java.util.stream.Collectors


@Service
class SubjectBaseService{

    private val log = LogManager.getLogger(SubjectBaseService::class.java)

    @Autowired
    private lateinit var subjectRepository: SubjectRepository

    @Autowired
    private lateinit var eventTypeBaseService: EventTypeBaseService

    @Autowired
    private lateinit var customizedPropertyBaseService: CustomizedPropertyBaseService

    @Autowired
    private lateinit var pointAccountTypeBaseService: PointAccountTypeBaseService

    @Autowired
    private lateinit var gradeHierarchyBaseService: GradeHierarchyBaseService

    @Autowired
    private lateinit var propertyTemplateBaseService: PropertyTemplateBaseService

    @Autowired
    private lateinit var medalHierarchyBaseService: MedalHierarchyBaseService

    fun findAll(): List<Subject> {
        return subjectRepository.findAll().filter { it.disabled == false }
    }

    fun findById(id: String) = subjectRepository.findById(id)

    /**分页查询专用*/
    fun findPageByPlanVersionId(planVersionId: List<Long>):List<Subject> {

        if(planVersionId.isEmpty())  return arrayListOf()

        val subjectList = subjectRepository.findPageByPlanVersionIdAndDisabled(planVersionId,false)

        val subjectVersionIdList= subjectList.stream().map { it.versionId!! }.collect(Collectors.toList())

        if(subjectVersionIdList.isEmpty())  return arrayListOf()

        val pointAccountTypeList = pointAccountTypeBaseService.findPageBySubjectVersionIdList(subjectVersionIdList)
        val gradeHierarchyList = gradeHierarchyBaseService.findPageBySubjectVersionIdList(subjectVersionIdList)
        val medalHierarchyList = medalHierarchyBaseService.findPageBySubjectVersionIdList(subjectVersionIdList)

        subjectList.forEach { subject ->
            subject.pointAccountTypeList = pointAccountTypeList.filter { it.subjectVersionId == subject.versionId }
            subject.gradeHierarchyList = gradeHierarchyList.filter {  it.subjectVersionId == subject.versionId  }
            subject.medalHierarchyList = medalHierarchyList.filter { it.subjectVersionId == subject.versionId }
        }
        return subjectList
    }

    /**根据计划ID获取主体详情，交互使用*/
    fun findDetailByPlanVersionId(planVersionId: Long):List<Subject> {
        try{
            val subjectList = subjectRepository.findByPlanVersionIdAndDisabled(planVersionId,false)
            subjectList.forEach {
                it.eventTypeList = eventTypeBaseService.findDetailBySubjectVersionId(it.versionId!!)
                it.pointAccountTypeList = pointAccountTypeBaseService.findDetailBySubjectVersionId(it.versionId!!)
                it.gradeHierarchyList = gradeHierarchyBaseService.findDetailBySubjectVersionId(it.versionId!!)
                it.medalHierarchyList = medalHierarchyBaseService.findDetailBySubjectVersionId(it.versionId!!)
                it.customizedPropertyList = customizedPropertyBaseService.findDetailByBelongerVersionIdAndBelongerType(it.versionId!!,PropertyBelongerTypeEnum.SUBJECT)
                it.propertyTemplateList = propertyTemplateBaseService.findDetailByBelongerVersionIdAndBelongerType(it.versionId!!, PropertyBelongerTypeEnum.SUBJECT)
            }
            return subjectList
        }catch (e:Exception){
            log.error("获取计划主体失败！",e)
        }

        return arrayListOf()
    }



    /**新增主体本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun insert(subject: Subject){
        subject.name = subject.name!!.trim()
        subject.versionId = null
        subject.status = EnableStatusEnum.DRAFT
        subjectRepository.save(subject)
        subject.id = subject.versionId
        subjectRepository.save(subject)

    }

    /**更新主体本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun update(subject: Subject){
        subject.name = subject.name!!.trim()
        subjectRepository.save(subject)
    }

    /**删除主体本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun delete(subject: Subject){
        subject.disabled = true
        subjectRepository.save(subject)
    }

    /**根据时间及生效ID获取生效对象*/
    fun getEffectiveOne(id: Long, date: ZonedDateTime):Subject{
        val subject = subjectRepository.findByIdAndDate(id, date)
        return if(subject.isPresent) subject.get() else throw LoyaltyException(LoyaltyExceptionCode.NOT_FOUND)
    }

    /**根据ID获取主体*/
    fun findByVersionId(versionId: Long) : Subject{
        return subjectRepository.findByVersionId(versionId).orElseThrow { NoSuchElementException("主体不存在 versionId: $versionId") }
    }


    fun findSubject(accountTypeId: Long): Subject{
        val pointAccountType = pointAccountTypeBaseService.getEffectiveOne(accountTypeId, DateUtils.formatMinutesCacheKey(ZonedDateTime.now()))
        val subject  =  findByVersionId(pointAccountType.subjectVersionId!!)
        if(subject.hasMerge == null || !subject.hasMerge!!)  subject.mergeFqn = null
        return subject
    }

    fun findSubjectByGradeHierarchyId(gradeHierarchyId: Long): Subject {
        val gradeHierarchy = gradeHierarchyBaseService.getEffectiveOne(gradeHierarchyId,DateUtils.formatMinutesCacheKey(ZonedDateTime.now()))
        val subject  =  findByVersionId(gradeHierarchy.subjectVersionId!!)
        if(subject.hasMerge == null || !subject.hasMerge!!)  subject.mergeFqn = null
        return subject
    }

    fun findSubjectByMedalHierarchyId(medalHierarchyId: Long): Subject {
        val medalHierarchy = medalHierarchyBaseService.getEffectiveOneCache(medalHierarchyId)
        val subject = findByVersionId(medalHierarchy.subjectVersionId!!)
        if (subject.hasMerge == null || !subject.hasMerge!!) subject.mergeFqn = null
        return subject
    }

    fun findSubjectByAccountOrHierarchyId(accountOrHierarchyId:Long,type: TypeEnum):Subject?{
         val plan =when(type){
              TypeEnum.POINT-> LoyaltyPrograms.findPlanByAccountTypeId(accountOrHierarchyId)
              TypeEnum.GRADE-> LoyaltyPrograms.findPlanByGradeHierarchyId(accountOrHierarchyId)
              TypeEnum.MEDAL-> LoyaltyPrograms.findPlanByMedalHierarchyId(accountOrHierarchyId)
          }
        return plan?.subjectList?.first()
    }
}

