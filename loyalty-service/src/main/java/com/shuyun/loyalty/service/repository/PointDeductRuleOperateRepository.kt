package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.model.PointDeductRuleOperate
import com.shuyun.loyalty.service.model.PointSendRule
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface PointDeductRuleOperateRepository : JpaRepository<PointDeductRuleOperate, Long> {

    fun save(pointRule: PointSendRule)
    fun deleteByRuleGroupId(ruleGroupId: Long)
    fun findByRuleGroupId(ruleId: Long): List<PointDeductRuleOperate>
    fun findByRuleIdAndDisabled(ruleId: Long, disabled: Boolean): List<PointDeductRuleOperate>
}