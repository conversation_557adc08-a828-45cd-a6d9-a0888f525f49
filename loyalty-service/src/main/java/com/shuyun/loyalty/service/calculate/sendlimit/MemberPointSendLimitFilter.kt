package com.shuyun.loyalty.service.calculate.sendlimit

import com.shuyun.loyalty.service.datamodel.MemberPointSendLimitCalcMapper
import com.shuyun.loyalty.service.extension.getIfPresent
import com.shuyun.loyalty.service.meta.PointSendLimitLogic
import com.shuyun.loyalty.service.meta.PointSendLimitType
import com.shuyun.loyalty.service.model.PointSendLimitRule
import com.shuyun.loyalty.service.service.MemberPointSendLimitCalcService
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class MemberPointSendLimitFilter : AbstractMemberPointSendLimitFilter() {

    private val logger = LogManager.getLogger(MemberPointSendLimitFilter::class.java)

    @Autowired
    private lateinit var memberPointSendLimitCalcService: MemberPointSendLimitCalcService

    override fun before(dto: IMemberPointSendLimitFilter.SendLimitFilterDTO) {
        if(dto.memberPointId == null || dto.pointValue!! <= ConstantValue.defaultZeroLine || dto.sendLimitRule.isNullOrBlank()) {
            dto.isContinue = false
            return
        }
        dto.refId = dto.memberPointId!!

        val pointSendLimitRuleList = ArrayList<PointSendLimitRule>()
        pointSendLimitRuleList.add(PointSendLimitRule().apply {
            this.logic = PointSendLimitLogic.and
            this.nlms = dto.sendLimitRule
        })
        dto.sendLimitRuleList = pointSendLimitRuleList
        val sendLimitParams = dto.sendLimitParams!!

        // 查询对应积分账号ID 已经累计的上限值
        var memberPointSendLimitCalc = memberPointSendLimitCalcService.findOneRefIdAndType(dto.refId!!,
            PointSendLimitType.MEMBER, sendLimitParams.memberId, sendLimitParams.pointAccountTypeId.toString()).getIfPresent()

        // 如果为空则创建个用于计算
        if(memberPointSendLimitCalc == null) {
            memberPointSendLimitCalc = MemberPointSendLimitCalcMapper.build(PointSendLimitType.MEMBER,
                sendLimitParams.pointAccountTypeId,
                sendLimitParams.planId, sendLimitParams.memberId, dto.refId!!,dto.sendLimitParams!!.time)
        }

        logger.debug("会员积分上限拦截累计值 :{} ", { JsonUtils.toJson(memberPointSendLimitCalc) })
        @Suppress("UNCHECKED_CAST")
        val valueMap = JsonUtils.parse(memberPointSendLimitCalc.value,MutableMap::class.java) as MutableMap<String, Number>
        memberPointSendLimitCalc.valueMap = valueMap
        dto.memberPointSendLimitCalc = memberPointSendLimitCalc
    }

    override fun pointSendLimitType(): PointSendLimitType {
        return PointSendLimitType.MEMBER
    }

    override fun order(): Int {
        return 3
    }
}