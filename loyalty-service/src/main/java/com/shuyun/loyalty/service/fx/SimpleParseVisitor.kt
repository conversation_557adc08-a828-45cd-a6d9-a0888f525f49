package com.shuyun.loyalty.service.fx

import com.shuyun.fx.factory.DslEpp
import com.shuyun.fx.factory.FxUtils
import com.shuyun.fx.model.Literal
import com.shuyun.fx.parser.base.FxBaseVisitor
import com.shuyun.fx.parser.base.FxParser
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import java.util.*

/**
 * 简单表达式解析
 * 1.解析第一层or 和 第二层 and
 * 2.解析 and 第一层
 */
class SimpleParseVisitor : FxBaseVisitor<Literal>(){

    private val logger = LogManager.getLogger(SimpleParseVisitor::class.java)

    var result: ArrayList<String> = ArrayList()

    var command: String? = null

    var comparison: Triple<String/*属性函数id*/, String/*比较符*/, Any/*目标值*/>? = null

    override fun visitLogicalExpr(ctx: FxParser.LogicalExprContext): Literal {
        try {
            when {
                (Objects.nonNull(ctx.OR()) && command == "OR") || (Objects.nonNull(ctx.AND()) && command == "AND") -> {
                    val sequence = ctx.logicalExpr().asSequence()
                    sequence.iterator().forEach { logicalExpr ->
                        result.add(logicalExpr.text)
                    }
                }
                (Objects.nonNull(ctx.logicalExpr()) && ctx.logicalExpr().size > 0 ) -> {
                     return visit(ctx.logicalExpr(0))
                }
                Objects.nonNull(ctx.comparisonExpr()) -> {
                    return visit(ctx.comparisonExpr())
                }
            }
        }catch (e: Exception) {
            logger.warn("解析表达式异常",e)
        }
        return Literal()
    }

    override fun visitComparisonExpr(ctx: FxParser.ComparisonExprContext): Literal {
        logger.debug("解析表达式: {}", { ctx.arithmeticExpr().size })
        if(ctx.comparisonExpr().size == 2) {
            comparison =  Triple(ctx.comparisonExpr(0).text,FxUtils.comparisonSymbol(ctx).toString(),ctx.comparisonExpr(1).text)
            logger.debug("解析属性为:{} ",{JsonUtils.toJson(comparison)})
        }else if(ctx.arithmeticExpr().size == 3) {
            val list = ArrayList<String>()
            list.add(ctx.arithmeticExpr(1).text)
            list.add(ctx.arithmeticExpr(2).text)
            comparison =  Triple(ctx.arithmeticExpr(0).text,FxUtils.comparisonSymbol(ctx).toString(),list)
        }
        return Literal()
    }
}

fun main() {
//    val parseVisitor = SimpleParseVisitor()
//    val result = parseVisitor.visit(DslEpp.dslParse("( {1304} CONTAIN \"-1\" ) and ( {1306} CONTAIN \"122\" ) or ( {1305} >= 1 ) and ( {1307} CONTAIN \"afds\" )"))
//    parseVisitor.visit(DslEpp.dslParse("( {894} CONTAIN \"法国丁根奋斗\" )"))
//    var dsl = "( {1304} CONTAIN \"-1\" ) and ( {1306} CONTAIN \"122\" ) or ( {1305} >= 1 ) and ( {1307} CONTAIN \"afds\" )"

    //var dsl = " ( {1305} >= 1 ) and ( {1307} CONTAIN \"afds\" )"


    val visitor = SimpleParseVisitor()
//    visitor.command = "OR"
//    visitor.visit(DslEpp.dslParse(dsl))
//    var or = visitor.result
//    if (or.isEmpty()) {
//        or.add(dsl)
//    }
//    var filterGroupList = ArrayList<BudgetGradeResponse.BudgetGradeRuleFilterGroupResponse>()
//    visitor = SimpleParseVisitor()
//    visitor.command = "AND"
//    or.forEach {
//        var filterGroup = BudgetGradeResponse.BudgetGradeRuleFilterGroupResponse()
//        filterGroupList.add(filterGroup)
//        visitor.visit(DslEpp.dslParse(it))
//        var and = visitor.result
//        if(and.isEmpty()) {
//            and.add(it)
//        }
//        and.forEach { exp ->
//            var filter = BudgetGradeResponse.BudgetGradeRuleFilterResponse()
//            filter.expressionObj = exp
//            filterGroup.filterList.add(filter)
//        }
//        visitor.result.clear()
//    }
//    filterGroupList.forEach {
//
//    }
//    println(JsonUtils.toJson(filterGroupList))
//
//    visitor.visit(DslEpp.dslParse("( {1305} >= 1 )"))
//    println(JsonUtils.toJson(visitor.comparison))
//
//
//    visitor.visit(DslEpp.dslParse("( {1289} IN SelectorValue(50074, [Id(\"9818c75713ef4a888a231aed8d501c25\")], \"Id\", \"共选择1条明细数据\") )"))
//    println(JsonUtils.toJson(visitor.comparison))
//
//    "SelectorValue(50074, [Id(\"9818c75713ef4a888a231aed8d501c25\")], \"Id\", \"共选择1条明细数据\")"
//        .replace("SelectorValue(","").split(",").forEach{
//            println(it)
//        }

    visitor.visit(DslEpp.dslParse(" ( SUM(123) > 10)"))
    println(JsonUtils.toJson(visitor.comparison))
}