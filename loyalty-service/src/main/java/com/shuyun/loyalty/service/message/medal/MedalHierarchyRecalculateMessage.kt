package com.shuyun.loyalty.service.message.medal

import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.exception.MemberException
import com.shuyun.loyalty.service.message.EventMessage
import com.shuyun.loyalty.service.message.MessageHandlerType
import com.shuyun.loyalty.service.model.MedalHierarchy
import com.shuyun.loyalty.service.model.Plan
import java.util.*

class MedalHierarchyRecalculateMessage(
    plan: Plan, medalHierarchy: MedalHierarchy, event: Event, val matchingTime: Date,forbiddenPort: ForbiddenPort
) : AbstractMedalEventMessage(plan, event, medalHierarchy,forbiddenPort), EventMessage {
    override fun memberId(): String {
        try {
            return getDsIdBySubjectId(subjectVersionId)
        } catch (_: Exception) {
            throw MemberException(LoyaltyExceptionCode.MEMBER_NOT_FOUND)
        }
    }

    override fun eventHandlerType(): MessageHandlerType {
        return MessageHandlerType.MEDAL_OVER_HANDLER
    }

    fun eventTraceId(): String {
        return getTraceId(subjectVersionId)
    }
}