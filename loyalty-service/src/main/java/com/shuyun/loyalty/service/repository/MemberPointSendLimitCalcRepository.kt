package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.datamodel.MemberPointSendLimitCalc
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

@Component
class MemberPointSendLimitCalcRepository: DataModelRepository<MemberPointSendLimitCalc>() {
    private val log = LogManager.getLogger(MemberPointSendLimitCalcRepository::class.java)
    override fun log(): Logger = this.log
}