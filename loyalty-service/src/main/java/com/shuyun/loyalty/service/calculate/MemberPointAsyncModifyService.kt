package com.shuyun.loyalty.service.calculate

import com.pip.shuyun.pool.transaction.DmTransaction
import com.pip.shuyun.pool.transaction.TransactionInfoHolder
import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.entity.api.constants.ChangeMode
import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.dto.MemberPointMessage
import com.shuyun.loyalty.entity.dto.MemberRequest.Companion.MEMBER_OPERATOR_LOCK_KEY_FORMAT
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.service.datamodel.MemberPointRecord
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.exception.PointException
import com.shuyun.loyalty.service.extension.buildResponseEntity
import com.shuyun.loyalty.service.extension.toJson
import com.shuyun.loyalty.service.kafka.IPointSyncProducer
import com.shuyun.loyalty.service.kafka.PointModifyMessage
import com.shuyun.loyalty.service.meta.ImportStatusEnum
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.model.OpenBatchLog
import com.shuyun.loyalty.service.service.ChangeModeBaseService
import com.shuyun.loyalty.service.service.ChannelTypeBaseService
import com.shuyun.loyalty.service.service.LoyaltyPrograms
import com.shuyun.loyalty.service.service.MemberPointService
import com.shuyun.loyalty.service.transfer.points.LoyaltyPoints
import com.shuyun.loyalty.service.transfer.points.LoyaltyRequestType
import com.shuyun.loyalty.service.transfer.points.MemberPointDeductTransfer
import com.shuyun.loyalty.service.transfer.points.MemberPointSendTransfer
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.MergeUtil
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.exception.AbstractPipException
import com.shuyun.pip.exception.ExceptionUtils
import com.shuyun.pip.exception.ResponseErrorData
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.util.*

@Component
class MemberPointAsyncModifyService {
    private val log = LogManager.getLogger(MemberPointAsyncModifyService::class.java)

    @Autowired
    private lateinit var locker: Locker

    @Autowired
    private lateinit var memberPointService: MemberPointService

    @Autowired
    private lateinit var channelTypeBaseService: ChannelTypeBaseService

    @Autowired
    private lateinit var changeModeBaseService: ChangeModeBaseService


    fun pointModify(message: PointModifyMessage) {
        var memberId = message.memberPointRecord!!.memberId
        val pointPlanId = message.memberPointRecord!!.pointPlanId!!
        val pointRecord = message.memberPointRecord!!
        val plan = LoyaltyPrograms.findPlanByAccountTypeId(message.memberPointRecord!!.pointPlanId!!)
            ?: throw PointException(LoyaltyExceptionCode.PLAN_NOT_FOUND)
        val subject = plan.subjectList!!.first()
        memberId = MergeUtil.findMergePointMemberId(subject, memberId) ?: memberId
        val lock = locker.getLock(String.format(MEMBER_OPERATOR_LOCK_KEY_FORMAT, pointPlanId, memberId))
        try {
            lock.lock()
            if (pointRecord.point < BigDecimal.ZERO) {
                throw LoyaltyException(LoyaltyExceptionCode.POINT_LESS_THAN_ZERO)
            }
            pointRecord.channel?.let { channelTypeBaseService.check(it) }
            pointRecord.changeMode?.let { changeModeBaseService.check(it) }

            val result = ApplicationContextHolder.getBean(MemberPointAsyncModifyService::class.java).modify(pointPlanId, memberId, pointRecord)

            if (!result.isNullOrEmpty()) {
                log.info("积分被部分拦截: memberId: {}, traceId: {} msg: {}", pointRecord.memberId, pointRecord.traceId, result)
            }
            if (message.batchId != null) OpenBatchLog().incrSuccessNumber(message.batchId)

        } catch (ex: Throwable) {
            if (message.batchId != null) {
                OpenBatchLog().incrFailNumber(message.batchId, ImportStatusEnum.RUNNING.name)
                val errorData = when (ex) {
                    is SdkException -> ResponseErrorData(ex.error_code, ex.msg, ex.module, ex.service).toJson()
                    is AbstractPipException -> ex.buildResponseEntity().toJson()
                    else -> ResponseErrorData(LoyaltyExceptionCode.UNKNOWN_EXCEPTION.code, ex.message,
                        ExceptionUtils.ERROR_RESPONSE_MODULE, GlobalContext.serviceName(),
                        ExceptionUtils.ERROR_RESPONSE_HELP_URL).toJson()
                }
                OpenBatchLog().apply {
                    this.parentId = message.batchId
                    this.responseErrorData = errorData
                    this.memberId = message.memberPointRecord!!.memberId
                    this.createTime = Date()
                }.save()
            }
            log.error("处理POINT_MODIFY_INPUT事件失败 message: ${JsonUtils.toJson(message)}", ex)
        } finally {
            lock.unlock()
        }
    }


    @DmTransaction
    fun modify(pointPlanId: Long, memberId: String, pointRecord: MemberPointRecord): String? {
        val memberPoint = when (pointRecord.recordType) {
            FSMPointEvent.SEND -> memberPointService.getOrCreate(pointPlanId, memberId)
            FSMPointEvent.DEDUCT -> memberPointService.getByMemberId(pointPlanId, memberId)
            else -> throw LoyaltyException(LoyaltyExceptionCode.REQUEST_EXCEPTION)
        } ?: throw LoyaltyException(LoyaltyExceptionCode.POINT_NOT_FOUND)
        val lp = LoyaltyPoints.of(
            memberPoint = memberPoint,
            type = LoyaltyRequestType.MANUAL,
            point = pointRecord.point.abs(),
            changeMode = pointRecord.changeMode ?: ChangeMode.INTERFACE_BATCH.name,
            channel = pointRecord.channel ?: ConstantValue.DEFAULT_CHANNEL_TYPE,
            traceId = pointRecord.traceId!!,
            uniqueId = pointRecord.key!!,
            shopId = pointRecord.shopId,
            kzzd1 = pointRecord.KZZD1,
            kzzd2 = pointRecord.KZZD2,
            kzzd3 = pointRecord.KZZD3,
            desc = pointRecord.desc,
        ).apply {
            attr.actionId = pointRecord.actionId
            attr.actionName = pointRecord.actionName
            attr.actionNodeId = pointRecord.actionNodeId
            attr.actionNodeName = pointRecord.actionNodeName
        }

        var result: String? = null
        when (pointRecord.recordType) {
            FSMPointEvent.SEND -> {
                lp.attr.effectiveDate = pointRecord.effectiveDate ?: lp.date.minusSeconds(1)
                lp.attr.overdueDate = pointRecord.overdueDate ?: ConstantValue.LONG_TERM_OVERDUE_DATE
                val sendTransfer = ApplicationContextHolder.getBean(MemberPointSendTransfer::class.java)
                result = sendTransfer.send(lp, lp.afterPoints, ForbiddenPort.INTERFACE, lp.attr.uniqueId)
            }
            FSMPointEvent.DEDUCT -> {
                val deductTransfer = ApplicationContextHolder.getBean(MemberPointDeductTransfer::class.java)
                deductTransfer.deduct(lp, ForbiddenPort.INTERFACE, ForbiddenOperation.POINT_DEDUCT_BY_USE, lp.attr.uniqueId)
            }
            else -> throw LoyaltyException(LoyaltyExceptionCode.REQUEST_EXCEPTION)
        }

        TransactionInfoHolder.afterCommit {
            ApplicationContextHolder.getBean(IPointSyncProducer::class.java)
                .send(
                    MemberPointMessage(
                        lp.hierarchy.id,
                        lp.member.memberId,
                        lp.member.id
                    )
                )
        }

        return result
    }

}