package com.shuyun.loyalty.service.service

import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.service.datamodel.MemberPoint
import com.shuyun.loyalty.service.datamodel.MemberPointSegment
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.extension.utcStr
import com.shuyun.loyalty.service.util.ConstantValue.LONG_TERM_OVERDUE_DATE
import com.shuyun.loyalty.service.util.SQL
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit

@Service
class PointSegmentService : MemberPointDmBaseService<MemberPointSegment>() {

    private val logger = LogManager.getLogger(PointSegmentService::class.java)
    override fun log(): Logger = logger

    fun save(segment: MemberPointSegment): MemberPointSegment {
        return super.save(segment, segment.pointPlanId.toString())
    }

    fun batchInsert(hierarchyId: Long, segments: List<MemberPointSegment>) {
        if (segments.isEmpty()) return
        val sql = SQL.build {
            var sql = """
                INSERT INTO data.loyalty.member.account.PointSegment$hierarchyId (
                    id, planId, pointPlanId, memberPointId, 
                    memberId, subjectFqn, point, expireDate, 
                    modified, created, memberPoint
                ) VALUES 
            """.trimIndent()
            var i = 0L
            for (item in segments) {
                val now = item.created.plus(i++, ChronoUnit.MILLIS)
                sql += """
                    (
                    ${item.id.escaped()}, 
                    ${item.planId.escaped()}, 
                    ${item.pointPlanId.escaped()}, 
                    ${item.memberPointId.escaped()}, 
                    ${item.memberId.escaped()}, 
                    ${item.subjectFqn.escaped()}, 
                    ${item.point.escaped()}, 
                    ${item.expireDate.escaped()}, 
                    ${now.escaped()}, 
                    ${now.escaped()}, 
                    ${item.memberPointId.escaped()}
                    ),
                """.trimIndent()
            }
            sql.trim().dropLast(1)
        }

        DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
            sdk.execute(sql, mapOf())
        }
    }


    fun findDateBefore(hierarchyId: Long, memberPointId: String, date: LocalDate): List<MemberPointSegment> {
        val sql = """
            SELECT 
                `id`,`planId`,`pointPlanId`,`memberPointId`,`memberId`,
                `subjectFqn`,`point`,`expireDate`,`modified`,`created` 
            FROM data.loyalty.member.account.PointSegment${hierarchyId}
            WHERE memberPointId = :memberPointId AND expireDate < :date 
        """.trimIndent()
        val params = mapOf("memberPointId" to memberPointId, "date" to date)
        val result =  DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
            sdk.execute(sql, params)
        }!!
        if (result.isSuccess && result.data.isNotEmpty()) {
            return JsonUtils.parse2List(JsonUtils.toJson(result.data), MemberPointSegment::class.java)
        }
        return emptyList()
    }

    fun findIdsDateBeforeByMemberId(hierarchyId: Long, memberId: String, date: LocalDate): List<String> {
        val sql = """
            SELECT 
                `id`
            FROM data.loyalty.member.account.PointSegment${hierarchyId}
            WHERE memberId = :memberId AND expireDate < :date 
        """.trimIndent()
        val params = mapOf("memberId" to memberId, "date" to date)
        val result =  DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
            sdk.execute(sql, params)
        }!!
        val ids = mutableListOf<String>()
        if (result.isSuccess && result.data.isNotEmpty()) {
            for (map in result.data) {
                @Suppress("UNCHECKED_CAST")
                val row = map as Map<String, Any>
                ids.add(row["id"].toString())
            }
        }
        return ids
    }


    fun findDateAfter(hierarchyId: Long, memberPointId: String, date: LocalDate, limit: Int = 1000): List<MemberPointSegment> {
        // 查询大于等于date的最左边的n条
        val sql = """
            SELECT 
                `id`,`planId`,`pointPlanId`,`memberPointId`,`memberId`,
                `subjectFqn`,`point`,`expireDate`,`modified`,`created` 
            FROM data.loyalty.member.account.PointSegment${hierarchyId}
            WHERE memberPointId = :memberPointId 
                AND ((expireDate >= :date AND point >= 0) OR (expireDate = :longTermOverdueDate AND point <= 0))
            ORDER BY expireDate
            LIMIT $limit
        """.trimIndent()
        val params = mapOf(
            "memberPointId" to memberPointId,
            "date" to date,
            "longTermOverdueDate" to LONG_TERM_OVERDUE_DATE.shDate()
        )
        val result =  DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
            sdk.execute(sql, params)
        }!!
        if (!result.isSuccess || result.data.isEmpty()) {
            return emptyList()
        }
        return JsonUtils.parse2List(JsonUtils.toJson(result.data), MemberPointSegment::class.java)
    }


    fun findFirstDateAfter(hierarchyId: Long, memberPointId: String, date: LocalDate): MemberPointSegment? {
        return findDateAfter(hierarchyId, memberPointId, date, limit = 1).firstOrNull()
    }


    // 根据积分过期时间扣减 rangeFrom < expireDate <= rangeTo
    fun minusByExpireDateRange(hierarchyId: Long, memberPointId: String, points: BigDecimal, rangeFrom: LocalDate, rangeTo: LocalDate) {
        for (i in 1..5) {
            try {
                DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
                    val update = """
                        UPDATE data.loyalty.member.account.PointSegment$hierarchyId
                        SET point = point - :points, modified = :modified 
                        WHERE memberPointId = :memberPointId AND expireDate > :rangeFrom AND expireDate <= :rangeTo
                    """.trimIndent()
                    val p = mapOf(
                        "points" to points,
                        "memberPointId" to memberPointId,
                        "rangeFrom" to rangeFrom,
                        "rangeTo" to rangeTo,
                        "modified" to ZonedDateTime.now().utcStr(),
                    )
                    sdk.execute(update, p)
                }!!
                break
            } catch (e: Throwable) {
                if (i == 5) {
                    throw e
                } else {
                    Thread.sleep(10)
                }
            }
        }
    }



    fun init(memberPoint: MemberPoint, expireDate: LocalDate, point: BigDecimal = BigDecimal.ZERO): MemberPointSegment {
        val segment = MemberPointSegment()
        segment.id = Uuid.uuid
        segment.planId = memberPoint.planId
        segment.pointPlanId = memberPoint.pointPlanId
        segment.memberPointId = memberPoint.id
        segment.memberId = memberPoint.memberId
        segment.subjectFqn = memberPoint.subjectFqn
        segment.point = point
        segment.expireDate = expireDate
        segment.modified = ZonedDateTime.now()
        segment.created = ZonedDateTime.now()
        return segment
    }

    fun deleteByIds(accountTypeId: Long, ids: List<String>) {
        if (ids.isEmpty()) return
        val sql = """
            DELETE FROM data.loyalty.member.account.PointSegment$accountTypeId 
            WHERE id IN (:ids)
        """.trimIndent()
        val params = mapOf("ids" to ids)
        DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
            sdk.execute(sql, params)
        }
    }

}