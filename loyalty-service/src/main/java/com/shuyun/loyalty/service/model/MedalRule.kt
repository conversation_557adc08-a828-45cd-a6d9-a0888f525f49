package com.shuyun.loyalty.service.model

import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.shuyun.loyalty.service.datamodel.Alert
import com.shuyun.loyalty.service.datamodel.MemberMedal
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.exception.GradeException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.extension.set
import com.shuyun.loyalty.service.extension.setDayEnd
import com.shuyun.loyalty.service.meta.*
import com.shuyun.loyalty.service.meta.ValidTimeTypeEnum.CHANGE
import com.shuyun.loyalty.service.meta.ValidTimeTypeEnum.RESET
import com.shuyun.loyalty.service.service.CustomizedPropertyBaseService
import com.shuyun.loyalty.service.util.DateUtils
import com.shuyun.loyalty.service.util.ExpressionIdentUtil
import com.shuyun.loyalty.service.util.ModelInitUtil
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import io.swagger.v3.oas.annotations.media.Schema
import org.apache.logging.log4j.LogManager
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.*
import javax.persistence.*

@Entity
@Schema(title = "勋章规则")
@Table(name = "medal_rule")
data class MedalRule(

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Schema(title = "规则ID", type = "Long")
    var id: Long? = null,

    @Schema(title = "规则名称", type = "String")
    var name: String? = null,

    @Schema(title = "规则组ID", type = "Long")
    var groupId: Long? = null,

    @Schema(title = "勋章是否永久有效", type = "Boolean")
    var medalEffectForever: Boolean? = null,

    @Schema(title = "结束年类型 RELATIVE_TIME:相对时间，ABSOLUTE_TIME:绝对时间", type = "String")
    @Enumerated(EnumType.STRING)
    var endYearType: TimeTypeEnum? = null,

    @Schema(title = "结束年时间", type = "Int")
    var endYearTime: Int? = null,

    @Schema(title = "结束月类型 RELATIVE_TIME:相对时间，ABSOLUTE_TIME:绝对时间", type = "String")
    @Enumerated(EnumType.STRING)
    var endMonthType: TimeTypeEnum? = null,

    @Schema(title = "结束月时间", type = "Int")
    var endMonthTime: Int? = null,

    @Schema(title = "结束日类型 RELATIVE_TIME:相对时间，ABSOLUTE_TIME:绝对时间", type = "String")
    @Enumerated(EnumType.STRING)
    var endDayType: TimeTypeEnum? = null,

    @Schema(title = "结束日时间", type = "Int")
    var endDayTime: Int? = null,

    @Schema(title = "结束时间属性id", type = "Long")
    var endTimePropertyId: Long? = null,

    @Schema(title = "SIMPLE:简单模式 SENIOR:高级模式", type = "String")
    var mode: ModeTypeEnum? = null,

    @Schema(title = "前端展示表达式时需要的辅助信息，格式前端自定", type = "String")
    var displayInfo: String? = null,

    @Schema(title = "前端展示表达式时需要的辅助信息，格式前端自定", type = "String")
    var placeholderInfo: String? = null,

    @Schema(title = "前端展示表达式时需要的辅助信息，格式前端自定", type = "String")
    var expressionDisplayInfo: String? = null,

    @Schema(title = "前端保存的转义后的计算表达式", type = "String")
    var expression: String? = null,

    @Schema(title = "后端加工后的表达式", type = "String")
    var expressionTranslated: String? = null,

    @Schema(title = "条件表达式存放变量", type = "String")
    var expressionVariable: String? = null,

    @Schema(title = "勋章有效期类型", type = "String")
    @Enumerated(EnumType.STRING)
    var validTimeType: ValidTimeTypeEnum? = null,
    
    @Schema(title = "勋章改变时间值", type = "String")
    var changeTimeValue: Int? = null,
    
    @Schema(title = "勋章改变时间类型（缩短，延长）", type = "String")
    @Enumerated(EnumType.STRING)
    var changeTimeType: ChangeTimeType? = null,
    
    @Schema(title = "勋章改变时间单位", type = "String")
    @Enumerated(EnumType.STRING)
    var changeUnitType: ChangeUnitType? = null,

    @Schema(title = "条件表达式id", type = "Int")
    var expressionFxId: Int? = null,

    @Schema(title = "条件表达式", type = "String")
    var expressionFx: String? = null,

    @Schema(title = "参考属性ID", type = "String")
    var refCustomizedPropertyId: String? = null,

    @Schema(title = "参考属性当时的fxId", type = "String")
    var refCustomizedPropertyFxId: String? = null,

    @Schema(title = "重算后，勋章生效时间如何变动", type = "String", defaultValue = "RESET")
    @Enumerated(EnumType.STRING)
    var effectValidTimeType: ValidTimeTypeEnum = RESET

    ): BaseModel() {
    companion object {
        @Transient
        private val log = LogManager.getLogger(MedalRule::class.java)
    }

    override fun copyToOldOne(old: BaseModel, backup: Boolean) {
        old as MedalRule
        ModelInitUtil.copyPropertiesIgnoreNull(this, old)
        old.endDayTime = this.endDayTime
        old.endMonthTime = this.endMonthTime
        old.endYearTime = this.endYearTime
    }

    fun calculateOverdueTime(event: Event, dataType: String?,memberMedal: MemberMedal?): LocalDateTime? {
        var calendar = if (memberMedal?.overdueDate != null && validTimeType != RESET) {
            GregorianCalendar.from(memberMedal.overdueDate)
        } else Calendar.getInstance()
        if (this.medalEffectForever!!) return null

        if (this.endTimePropertyId != null && this.endTimePropertyId != -1L) {
            val fxId = ApplicationContextHolder.getBean(CustomizedPropertyBaseService::class.java)
                .getEffectiveOne(this.endTimePropertyId!!, ZonedDateTime.now()).fxId!!
            val endDate = ExpressionIdentUtil.eppById(fxId, event, JsonNodeFactory.instance.objectNode().put("name", dataType))
            val endCalendar = DateUtils.dateToCalendar(endDate.toString())
            if (endCalendar == null) {
                log.error("勋章过期时间函数返回日期错误，勋章规则：{}", JsonUtils.toJson(this))
                throw IllegalArgumentException("勋章过期时间函数返回日期错误")
            }
            this.endYearType = TimeTypeEnum.ABSOLUTE_TIME
            this.endYearTime = endCalendar.get(Calendar.YEAR)
            this.endMonthType = TimeTypeEnum.ABSOLUTE_TIME
            this.endMonthTime = endCalendar.get(Calendar.MONTH)+1
            this.endDayType = TimeTypeEnum.ABSOLUTE_TIME
            this.endDayTime = endCalendar.get(Calendar.DAY_OF_MONTH)
        }
        if (this.refCustomizedPropertyId !=null && this.refCustomizedPropertyFxId !=null
            && this.refCustomizedPropertyId != "-1" && this.refCustomizedPropertyFxId != "-1") {
            log.info("计算勋章过期日期 规则ID:{}, 函数ID:{}", this.id, this.refCustomizedPropertyFxId)
            val date = ExpressionIdentUtil.eppById(this.refCustomizedPropertyFxId!!.toInt(), event,
                JsonNodeFactory.instance.objectNode().put("subject", dataType)).toString()
            calendar = DateUtils.getCalendar(date)
            if (null == calendar) {
                log.error("勋章过期时间函数返回日期错误,勋章计算规则:{}", JsonUtils.toJson(this))
                alert(event, JsonUtils.toJson(this), "计算勋章过期日期错误")
                throw IllegalArgumentException("勋章过期时间函数返回日期错误")
            }
        }

        return when (validTimeType) {
            CHANGE -> {
                if (memberMedal != null && memberMedal.overdueDate == null) return null
                val timeSign = if(changeTimeType == ChangeTimeType.LENGTHEN) 1 else -1
                val unitType = when(changeUnitType) {
                    ChangeUnitType.DAY -> Calendar.DAY_OF_MONTH
                    ChangeUnitType.MONTH -> Calendar.MONTH
                    ChangeUnitType.YEAR -> Calendar.YEAR
                    else -> throw GradeException(LoyaltyExceptionCode.GRADE_TIME_TYPE_ERROR)
                }
                calendar.set(unitType, calendar.get(unitType).plus(changeTimeValue!! * timeSign))
                if(calendar.time < Date()) {
                    val calendarLt = Calendar.getInstance()
                    calendarLt.setDayEnd()
                }
                LocalDateTime.ofInstant(calendar.toInstant(), ZoneOffset.systemDefault())
            }
            RESET -> {
                calendar.set(endYearType!!, Calendar.YEAR, endYearTime!!)
                calendar.set(endMonthType!!, Calendar.MONTH, endMonthTime!!)
                if (endDayTime == -1)
                    calendar.set(Calendar.DATE, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
                else
                    calendar.set(endDayType!!, Calendar.DATE, endDayTime!!)
                calendar.setDayEnd()

                val overdue = calendar.time
                if (overdue.before(Calendar.getInstance().time)) {
                    log.error("勋章有效期已过期,勋章规则:{},过期时间:{}", JsonUtils.toJson(this), JsonUtils.toJson(overdue))
                    alert(event, JsonUtils.toJson(this), "勋章过期时间在当前时间之前,计算结果:${JsonUtils.toJson(overdue)}")
                    throw IllegalArgumentException("勋章过期时间在当前时间之前")
                }
                LocalDateTime.ofInstant(calendar.toInstant(), ZoneOffset.systemDefault())
            }
            else -> LocalDateTime.now()
        }
    }

    private fun alert(event: Event, content: String, error: String) {
        Alert().build(JsonUtils.toJson(event), content, AlertBizType.MEDAL.type, error, null).save()
    }

}