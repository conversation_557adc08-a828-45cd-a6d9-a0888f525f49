package com.shuyun.loyalty.service.transfer.points

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.service.datamodel.*
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.service.EventTypeBaseService
import com.shuyun.loyalty.service.service.PointRuleGroupBaseService
import com.shuyun.loyalty.service.service.PointSendRuleBaseService
import com.shuyun.loyalty.service.util.sendNotify
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.ZonedDateTime
import kotlin.jvm.optionals.getOrNull

@Service
class MemberPointEffectTransfer: MemberPointBaseTransfer() {

    private val logger = LogManager.getLogger(MemberPointEffectTransfer::class.java)

    @Autowired
    private lateinit var eventTypeBaseService: EventTypeBaseService

    @Autowired
    private lateinit var pointRuleGroupBaseService: PointRuleGroupBaseService

    @Autowired
    private lateinit var pointSendRuleBaseService: PointSendRuleBaseService


    fun effect(lp: LoyaltyPoints) {
        val memberPoint = lp.member.memberPoint!!
        val now = ZonedDateTime.now()
        val gss = gainStatementService.findToBeEffectivePoints(lp.hierarchy.id, lp.member.id,now)
        var addPoints = false
        if (gss.isNotEmpty()) {
            for (gs in gss) {
                lp.member.point = memberPoint.point
                lp.pointValue = gs.point
                lp.afterPoints = gs.point

                val foundAttr = applyEventAndAttr(lp, gs)
                if (!foundAttr && gs.point > BigDecimal.ZERO) {
                    setKZZD(lp, gs)
                }
                lp.attr.businessId = gs.id
                lp.attr.traceId = gs.traceId ?: "-"
                lp.attr.uniqueId = gs.id!!
                lp.attr.effectiveDate = gs.effectiveDate
                lp.attr.overdueDate = gs.overdueDate

                lp.attr.desc = "待生效积分转为生效积分"
                lp.attr.recordDetail = "积分生效-${gs.id}"

                // 积分上限过滤
                val beforePoints = gs.point
                filterLimit(lp, gs.point, FSMPointEvent.TIMER, gs.eventTypeId, gs.ruleGroupId)//次数
                if (beforePoints != lp.afterPoints) {
                    logger.info("积分被发放上限过滤 gsId: {} 之前分值：{} 过滤后分值：{}", gs.id, beforePoints, lp.afterPoints)
                }
                gs.status = FSMPointEvent.SEND
                gs.modified = ZonedDateTime.now()
                gs.point = lp.afterPoints
                gs.saveOrUpdate(lp.hierarchy.id)
                if (gs.point.compareTo(BigDecimal.ZERO) == 0) {
                    continue
                }
                //发放总积分
                memberPoint.plus(lp.afterPoints, gs.effectiveDate,  gs.overdueDate, lp.date, updateNow = false)
                lp.afterTotalPoints = memberPoint.point

                addPoints = true
            }
        }
        if (addPoints) {
            memberPoint.modified = ZonedDateTime.now()
            memberPoint.saveLog()
            memberPoint.update()
            transfer(lp)
        }
        logger.info("检查待生效积分完成 hierarchyId: {} memberId: {} gssSize: {}", lp.hierarchy.id, lp.member.memberId, gss.size)
    }


    private fun applyEventAndAttr(lp: LoyaltyPoints, gs: MemberPointGainStatement): Boolean {
        var foundAttr = false
        if (gs.point > BigDecimal.ZERO && gs.ruleGroupId != null && gs.eventTypeId != null && gs.ruleId !=null && gs.occurrenceTs != null) {
            lp.type = LoyaltyRequestType.EVENT
            val j = MemberPointCalculateTask.findByTraceId(lp.hierarchy.id, gs.traceId!!).find {
                it.lp?.pointRuleGroup?.id == gs.ruleGroupId && it.lp?.eventType?.id == gs.eventTypeId
            }
            if (j != null) {
                lp.eventType = j.lp?.eventType
                lp.pointRuleGroup = j.lp?.pointRuleGroup
                lp.sendRule = j.lp?.sendRule
                lp.attr = j.lp?.attr!!
                foundAttr = true
            } else {
                val eventType = eventTypeBaseService.findByIdAndDate(gs.eventTypeId!!, gs.created)
                lp.eventType = PointEventType(
                    eventType.id!!,
                    eventType.name!!,
                    gs.occurrenceTs!!.toLong(),
                    EventOperationEnum.SEND,
                )
                pointRuleGroupBaseService.findById(gs.ruleGroupId!!)?.let {
                    lp.pointRuleGroup = PointGroup(it.id!!, it.groupName!!, it.limitPoint != null)
                }
                pointSendRuleBaseService.findPointSendRule(gs.ruleGroupId!!, gs.ruleId!!)?.let {
                    lp.sendRule = PointIssuanceRule(it.id!!, it.ruleName!!)
                }
            }
        }
        return foundAttr
    }


    private fun setKZZD(lp: LoyaltyPoints, gs: MemberPointGainStatement) {
        try {
            var oldRecord = recordService.findById(gs.id!!, lp.hierarchy.id.toString()).getOrNull()
            if (oldRecord == null && gs.traceId != null) {
                oldRecord = recordService.findByMemberIdAndTraceIdList(lp.hierarchy.id, gs.memberId, gs.traceId!!)
                    .asSequence()
                    .filter { it.traceId == gs.traceId }
                    .filter { it.recordType == FSMPointEvent.DELAY_SEND || it.recordType == FSMPointEvent.RECALCULATE || it.recordType == FSMPointEvent.UNFREEZE }
                    .filter { it.effectiveDate == null || it.effectiveDate!!.isEqual(gs.effectiveDate) }
                    .sortedByDescending { it.created }
                    .firstOrNull()
            }
            gs.ruleGroupId?.let { lp.pointRuleGroup = PointGroup(it, oldRecord?.ruleGroup ?: "") }
            gs.ruleId?.let { lp.sendRule = PointIssuanceRule(it, oldRecord?.ruleName ?: "") }
            lp.attr.apply {
                this.shopId = oldRecord?.shopId
                this.kzzd1 = oldRecord?.KZZD1
                this.kzzd2 = oldRecord?.KZZD2
                this.kzzd3 = oldRecord?.KZZD3
            }
        } catch (e: Exception) {
            logger.warn("查询历史变更记录失败", e)
        }
    }


    fun transfer(lp: LoyaltyPoints) {

        // 保存拦截记录
        saveLimitRecord(lp, FSMPointEvent.TIMER)

        if(lp.afterPoints <= BigDecimal.ZERO && !lp.hierarchy.sendLimitResults.isNullOrEmpty()) {
            logger.info("积分全部被上限规则拦截")
            return
        }

        val gsId = lp.attr.businessId ?: return
        val gs = gainStatementService.findById(gsId, replacePattern = lp.hierarchy.id.toString()).getOrNull() ?: return
        val recordId = Uuid.uuid
        val record = initPointRecord(
            lp, recordId, FSMPointEvent.TIMER,
            points = lp.afterPoints,
            changePoints = lp.afterPoints,
            totalPoints = lp.afterTotalPoints,
            pcStatus = PCStatus.VALID,
            effectiveDate = lp.attr.effectiveDate,
            overdueDate = lp.attr.overdueDate
        )

        val records = ArrayList<MemberPointRecord>()
        val items = ArrayList<PointRecordItem>()
        val vss = ArrayList<MemberPointValidStatement>()

        // 有效积分记录
        val vs = initValidPoints(
            lp,
            Uuid.uuid,
            gs.id!!,
            lp.afterPoints,
            PointStateEnum.VALID,
            lp.attr.effectiveDate!!,
            lp.attr.overdueDate!!
        )
        // 处理立即生效积分发放记录
        val (records1, items1, vss1) = transferSend(lp, record.id!!, gs, vs)

        records.add(record)
        records.addAll(records1)
        items.addAll(items1)
        vss.addAll(vss1)

        recordService.batchInsert(records, lp.hierarchy.id.toString())
        itemService.batchInsert(items, lp.hierarchy.id.toString())
        validService.batchInsert(vss, lp.hierarchy.id.toString())

        records.sendNotify()
    }
}