package com.shuyun.loyalty.service.metrics

import com.shuyun.lite.util.Common
import io.micrometer.core.instrument.Tags

object MetricsExtend {


    fun tagsBuilder() = Tags.of("ENVIRONMENT", Common.getSysOrEnv("ENVIRONMENT"),
                "SERVICE_NAME", Common.getSysOrEnv("SERVICE_NAME"),
                "APP_VERSION", Common.getSysOrEnv("APP_VERSION"),
                "API_VERSION", Common.getSysOrEnv("API_VERSION"))
}