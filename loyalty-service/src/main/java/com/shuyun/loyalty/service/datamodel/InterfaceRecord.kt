package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.datamodel.InterfaceRecord.Companion.dataModelFqn
import com.shuyun.loyalty.service.repository.InterfaceRecordRepository
import com.shuyun.pip.ApplicationContextHolder
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.Table


@Table(name = dataModelFqn)
@DataServiceModel
class InterfaceRecord: BaseDataModel() {
    @Column
    var sequence : String?=null
    @Column
    var content:String?=null
    @Column
    var created : ZonedDateTime?=null

    companion object {
        const val dataModelFqn = "data.loyalty.interface.Record"
    }


    fun save() = ApplicationContextHolder.getBean(InterfaceRecordRepository::class.java).save(this)

    fun delete() = ApplicationContextHolder.getBean(InterfaceRecordRepository::class.java).delete(this.id!!)

}

