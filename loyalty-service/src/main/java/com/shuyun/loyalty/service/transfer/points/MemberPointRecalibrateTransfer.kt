package com.shuyun.loyalty.service.transfer.points

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.service.datamodel.*
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.model.PointDeductRuleOperate
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.sendNotify
import org.apache.logging.log4j.LogManager
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.ZonedDateTime
import kotlin.jvm.optionals.getOrNull

// 积分重算
@Service
class MemberPointRecalibrateTransfer: MemberPointBaseTransfer() {

    private val logger = LogManager.getLogger(MemberPointRecalibrateTransfer::class.java)


    fun recalibrate(lp: LoyaltyPoints) {
        // 重算的规则组和规则
        val recalculateRuleGroup = lp.pointRuleGroup
        val recalculateRule = lp.deductRule
        val recalculateDesc = lp.attr.desc

        // 重算
        val (oldGSS, newNotInOldGSS, newInOldGSS, oldNotInNewGSS) = recalculate(lp)

        if (oldGSS.isEmpty() && newNotInOldGSS.isEmpty() && newInOldGSS.isEmpty() && oldNotInNewGSS.isEmpty()) {
            logger.info("积分重算，没有需要重算的积分 {} {} {}", lp.hierarchy.id, lp.attr.traceId, lp.eventType?.name)
            return
        }

        val journalist = ArrayList<MemberPointCalculateTask>()

        val memberPoint = lp.member.memberPoint!!
        var flag = false
        // 发
        if (newNotInOldGSS.isNotEmpty() && !checkSpecial(lp, ForbiddenOperation.POINT_SEND, ForbiddenPort.CALC_EVENT)) {
            val list = ArrayList<MemberPointGainStatement>()
            for (gs in newNotInOldGSS) {
                lp.member.point = memberPoint.point
                lp.pointValue = gs.point
                lp.afterPoints = gs.point
                lp.pointRuleGroup = PointGroup(gs.ruleGroupId!!, gs.ruleGroupName!!, gs.enabledGlobalGroupLimit)
                lp.sendRule = PointIssuanceRule(gs.ruleId!!, gs.ruleName!!)
                lp.deductRule = null
                lp.attr.desc = gs.desc
                lp.attr.effectiveDate = gs.effectiveDate
                lp.attr.overdueDate = gs.overdueDate
                lp.attr.sign = "+"
                lp.attr.businessId = gs.id
                // 积分上限过滤
                filterLimit(lp, gs.point, FSMPointEvent.RECALCULATE, gs.eventTypeId, gs.ruleGroupId, isReissue = true)
                if (lp.afterPoints <= ConstantValue.defaultZeroLine) {
                    val m = lp.hierarchy.sendLimitResults?.firstOrNull()?.desc ?: ""
                    logger.warn("积分重算补发被上限过滤 之前分值：{} 过滤后分值：{} {}", gs.point, lp.afterPoints, m)
                    continue
                }
                gs.point = lp.afterPoints
                lp.pointValue = lp.afterPoints
                // 待生效的积分总账不发生变化
                val point = if (gs.status == FSMPointEvent.DELAY_SEND) BigDecimal.ZERO else lp.afterPoints
                // 发放总积分
                memberPoint.plus(point, gs.effectiveDate, gs.overdueDate, lp.date)

                lp.afterPoints = memberPoint.point - lp.member.point
                lp.afterTotalPoints = memberPoint.point
                val journalKey = lp.attr.uniqueId + "-" + gs.id
                val journal = lp.buildJournal(PointAction.RECALCULATE, journalKey)
                journalist.add(journal)
                list.add(gs)
            }
            gainStatementService.batchInsert(list, lp.hierarchy.id.toString())
        }
        // 多退少补
        for (gs in newInOldGSS) {
            if (gs.point == gs.ref!!.point || gs.ref!!.status == FSMPointEvent.SPECIAL_ABOLISH) {
                // 新旧相等或者被待生效特殊作废的不用处理
                continue
            }

            if (gs.ref!!.status == FSMPointEvent.FREEZE) {
                // 被冻结的积分只能解冻重算
                continue
            } else {
                // 再次检查是否有冻结的积分
                val frozenPoint = frozenPointService.findFrozenPointByGainStatementId(gs.ref!!.id!!, gs.pointPlanId!!)
                if (frozenPoint != null) {
                    continue
                }
            }

            if (gs.overdueDate.shDate().isBefore(ZonedDateTime.now().shDate())) {
                // 过期时间大于等于今天的才能重算
                continue
            }

            if (recalculateRule!!.quotaType == PointDeductRuleOperate.QuotaTypeEnum.ZERO) {
                logger.info("积分冻结规则扣减额度为0")
                val zeroRecord = initPointRecord(
                    lp, Uuid.uuid, FSMPointEvent.RECALCULATE,
                    points = BigDecimal.ZERO,
                    changePoints = BigDecimal.ZERO,
                    totalPoints = lp.member.point,
                    pcStatus = if (gs.status == FSMPointEvent.DELAY_SEND) PCStatus.DELAY else PCStatus.VALID).apply {
                    this.recordDetail = "积分重算规则配额为0"
                }
                zeroRecord.insert(lp.hierarchy.id)
                listOf(zeroRecord).sendNotify()
                continue
            }

            lp.pointRuleGroup = PointGroup(gs.ruleGroupId!!, gs.ruleGroupName!!, gs.enabledGlobalGroupLimit)
            lp.attr.businessId = gs.ref!!.id
            lp.member.point = memberPoint.point
            lp.attr.desc = gs.desc

            // 旧大于新，需要把旧多出来的部分扣除
            if (gs.point < gs.ref!!.point) {
                if (checkSpecial(lp, ForbiddenOperation.POINT_DEDUCT, ForbiddenPort.CALC_EVENT))  continue  //扣减黑名单里，跳过
                lp.pointRuleGroup = recalculateRuleGroup
                lp.deductRule = recalculateRule
                lp.sendRule = null
                lp.attr.recordDetail = "<- ${gs.ruleGroupId}-${gs.ruleId}"
                lp.attr.desc = recalculateDesc
                val deductPoint = gs.ref!!.point - gs.point
                val journal = recalculateMinus(lp, deductPoint, gs)
                journalist.add(journal)
                flag = true
            }

            // 新大于旧，需要把新多出来的部分补发
            if (gs.point > gs.ref!!.point) {
                if (checkSpecial(lp, ForbiddenOperation.POINT_SEND, ForbiddenPort.CALC_EVENT)) continue //在发放黑名单里，跳过
                lp.pointRuleGroup = PointGroup(gs.ruleGroupId!!, gs.ruleGroupName!!, gs.enabledGlobalGroupLimit)
                lp.sendRule = PointIssuanceRule(gs.ruleId!!, gs.ruleName!!)
                lp.deductRule = null
                lp.attr.desc = gs.desc
                val sendPoint = gs.point - gs.ref!!.point
                val journal = recalculatePlus(lp, sendPoint, gs)
                journalist.add(journal)
                if (gs.status == FSMPointEvent.SEND) {
                    gs.point = gs.ref!!.point + lp.afterPoints
                }
            }

            // 更新积分获取积分为新积分值
            gs.ref!!.point = gs.point
            gs.ref!!.modified = ZonedDateTime.now()
            gs.ref!!.saveOrUpdate(lp.hierarchy.id)
        }

        // 作废重算
        if (oldNotInNewGSS.isNotEmpty() && !checkSpecial(lp, ForbiddenOperation.POINT_DEDUCT_BY_ABOLISH, ForbiddenPort.CALC_EVENT)) {
            for (gs in oldNotInNewGSS) {
                val journal = invalidNotMatched(
                    lp,
                    gs,
                    recalculateRuleGroup,
                    recalculateRule,
                    recalculateDesc,
                    PointAction.RECALCULATE
                )
                journalist.add(journal)
                // 作废重算后原单积分就不存在了，这里修改为0
                gs.point = BigDecimal.ZERO
                gs.modified = ZonedDateTime.now()
                gs.saveOrUpdate(lp.hierarchy.id)
            }
            flag = true
        }

        // 释放积分上限
        if (flag) {//有过变更才进行调整
            val sendEventTypeId = lp.eventType!!.relativePointEventType!!.id
            val newGSS = gainStatementService.findByTraceIdList(
                lp.hierarchy.id,
                sendEventTypeId,
                lp.attr.traceId,
                lp.member.id
            )
            releaseLimit(lp, oldGSS, newGSS, sendEventTypeId)
        }

        // 保存总积分变更日志
        memberPoint.update()
        memberPoint.saveLog()

        MemberPointCalculateTask.batchSave(lp.hierarchy.id, journalist)
    }


    fun transfer(lp: LoyaltyPoints) {
        // 保存拦截记录
        saveLimitRecord(lp, FSMPointEvent.RECALCULATE)
        if(lp.afterPoints <= BigDecimal.ZERO && !lp.hierarchy.sendLimitResults.isNullOrEmpty()) {
            val m = lp.hierarchy.sendLimitResults?.firstOrNull()?.desc ?: ""
            logger.info("积分全部被上限规则拦截 {}", m)
            return
        }
        val gainStatementId = lp.attr.businessId!!
        val gs = gainStatementService.findById(gainStatementId, lp.hierarchy.id.toString()).getOrNull()
        if (gs == null) {
            logger.error("积分重算，无原单积分获取积分：$gainStatementId")
            return
        }
        if (gs.status == FSMPointEvent.EXPIRE) {
            logger.info("积分重算，原单积分获取积分已过期：$gainStatementId")
            return
        }
        // 重算待生效的积分
        if (gs.status == FSMPointEvent.DELAY_SEND) {
            val recordId = Uuid.uuid
            val record = initPointRecord(
                lp, recordId, FSMPointEvent.RECALCULATE,
                points = lp.pointValue,
                changePoints = BigDecimal.ZERO,
                totalPoints = lp.afterTotalPoints,
                pcStatus = if(lp.attr.abolish == true) PCStatus.DELAY_ABOLISH else PCStatus.DELAY
            )

            val item = initRecordItem(
                lp,
                id = Uuid.uuid,
                recordId = recordId,
                points = BigDecimal.ZERO,
                status = FSMPointEvent.RECALCULATE,
                backId = Uuid.uuid,
                parentBackId = gs.id
            )
            when(lp.attr.sign!!) {
                "+" -> {
                    record.recordDetail = "重算补待生效积分"
                    record.effectiveDate = gs.effectiveDate
                    record.overdueDate = gs.overdueDate
                    item.effectiveDate = gs.effectiveDate
                    item.overdueDate = gs.overdueDate
                }
                "-" -> {
                    val s = if(lp.attr.abolish == true) "作废" else ""
                    val suffix = lp.attr.recordDetail ?: ""
                    record.recordDetail = "${s}重算退待生效积分 $suffix"
                    record.point = -record.point
                    record.changePoint = -record.changePoint
                }
                else -> return
            }
            gs.delete(lp.hierarchy.id)
            gs.id = item.backId
            gs.status = FSMPointEvent.DELAY_SEND
            gs.modified = ZonedDateTime.now()
            gs.save(lp.hierarchy.id)
            batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), listOf(item), listOf())
        } else {
            // 重算已生效的积分
            if (lp.afterPoints.compareTo(BigDecimal.ZERO) == 0) {
                return
            }
            val recordId = Uuid.uuid
            val record = initPointRecord(
                lp, recordId, FSMPointEvent.RECALCULATE,
                points = lp.afterPoints,
                changePoints = lp.afterPoints,
                totalPoints = lp.afterTotalPoints,
                pcStatus = if(lp.attr.abolish == true) PCStatus.ABOLISH else PCStatus.VALID
            )
            when(lp.attr.sign!!) {
                "+" -> {
                    val items = ArrayList<PointRecordItem>()
                    offset(lp, lp.member.point, lp.afterPoints, recordId, FSMPointEvent.RECALCULATE) { offsetItems, offsetPoint ->
                        items.addAll(offsetItems)
                        // 抵消负积分之后剩余的积分如果还大于0
                        if (offsetPoint > BigDecimal.ZERO) {
                            val item = initRecordItem(
                                lp,
                                Uuid.uuid,
                                recordId,
                                offsetPoint,
                                FSMPointEvent.RECALCULATE,
                                backId = Uuid.uuid,
                                effectiveDate = gs.effectiveDate,
                                overdueDate = gs.overdueDate
                            )
                            items.add(item)
                            val vs = initValidPoints(lp,
                                item.backId!!,
                                gs.id!!, offsetPoint,
                                PointStateEnum.VALID,
                                gs.effectiveDate,
                                gs.overdueDate
                            )
                            vs.save(lp.hierarchy.id)
                        }
                    }
                    record.effectiveDate = gs.effectiveDate
                    record.overdueDate = gs.overdueDate
                    record.recordDetail = "重算补已生效积分"
                    batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), items, listOf())
                }
                "-" -> {
                    val items = ArrayList<PointRecordItem>()
                    val s = if(lp.attr.abolish == true) "作废" else ""
                    val suffix = lp.attr.recordDetail ?: ""
                    record.recordDetail = "${s}重算退已生效积分 $suffix"
                    record.point = -record.point
                    record.changePoint = -record.changePoint

                    // 查询大于扣减时间的所有积分块
                    val segments = segmentService.findDateAfter(lp.hierarchy.id, lp.member.id, lp.date.shDate()).filter {
                        it.expireDate!!.isAfter(lp.member.leftSegmentDate) && it.point >= BigDecimal.ZERO
                    }

                    val fetch = vssFetchFunc(gainStatementId)
                    // 顺序扣减
                    // 优先冻结原单关联的有效积分
                    val items2 = sequentiallyDeduct(
                        lp,
                        lp.pointValue,
                        fetch,
                        segments,
                        lp.hierarchy.negativeStrategy!!,
                        recordId,
                        FSMPointEvent.RECALCULATE,
                        FSMPointEvent.DEDUCT,
                        FSMPointEvent.RECALCULATE,
                        primaryGSId = gainStatementId
                    )
                    items.addAll(items2)
                    batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), items, segments)
                }
                else -> {}
            }
        }

        // 更新积分获取记录状态
        val vss = validService.findListByGainStatementId(lp.hierarchy.id, gs.id!!)
        if (vss.isEmpty()) {
            if (gs.status == FSMPointEvent.SEND
                || gs.status == FSMPointEvent.REVERSE_SEND
                || gs.point == BigDecimal.ZERO
                ) {
                gs.status = FSMPointEvent.DEDUCT
            }
        }
        gs.modified = ZonedDateTime.now()
        gs.saveOrUpdate(lp.hierarchy.id)
    }
}