package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.fx.function.CurrentMemberGradeFunction
import com.shuyun.loyalty.service.fx.function.CurrentMemberMedalFunction
import com.shuyun.loyalty.service.fx.function.CurrentMemberPointFunction
import com.shuyun.loyalty.service.meta.*
import com.shuyun.loyalty.service.model.Plan
import com.shuyun.loyalty.service.model.Plan.Companion.convertStatusToStatusList
import com.shuyun.loyalty.service.repository.PlanRepository
import com.shuyun.loyalty.service.repository.PointAccountTypeRepository
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.util.converter.ZonedDateTime2StringConverter
import org.apache.commons.lang3.StringUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import java.util.*
import java.util.stream.Collectors

@Service
class PlanBaseService {

    private val log = LogManager.getLogger(PlanBaseService::class.java)

    @Autowired
    private lateinit var planRepository: PlanRepository

    @Autowired
    private lateinit var subjectBaseService: SubjectBaseService

    @Autowired
    private lateinit var pointAccountTypeRepository: PointAccountTypeRepository

    //对String 类型字符串进行转义
    private fun transferredCommonValue(value: String): String {
        return value.replace("\\", "\\\\")
            .replace("'", "\\\'")
            .replace("\"", "\\\"")

    }

    //对String 类型字符串进行转义
    private fun transferredLikeValue(value: String): String {
        return value.replace("%", "\\%")
            .replace("_", "\\_")
    }

    fun findAll(): List<Plan> {
        val planList = planRepository.findAll()
        return planList.filter { it.disabled == false }
    }

    /**根据计划状态获取发布或草稿状态下的所有计划详情分页*/
    fun getPlanDetailPage(name: String?, status: PlanStatusEnum, pageable: Pageable): Page<Plan> {
        val planPage: Page<Plan> =
            if (StringUtils.isEmpty(name))
                planRepository.findByStatusInAndDisabledOrderByCreateTimeDesc(convertStatusToStatusList(status), false, pageable)
            else {
                val transferredName = transferredLikeValue(transferredCommonValue(name!!))
                planRepository.findByNameContainingAndStatusInAndDisabledOrderByCreateTimeDesc(transferredName, convertStatusToStatusList(status), false, pageable)
            }

        if (planPage.totalPages == 0) return planPage
        val planVersionIdList = planPage.content.stream().map { it.versionId!! }.collect(Collectors.toList())
        val subjectVersionIdList = subjectBaseService.findPageByPlanVersionId(planVersionIdList)

        planPage.content.forEach { plan ->
            plan.subjectList = subjectVersionIdList.filter { it.planVersionId == plan.versionId }
        }
        return planPage
    }

    /**查询所有发布的计划*/
    fun findAllPublishByPlan(status: PlanStatusEnum): List<Plan> {
        val planList = planRepository.findAllPublishByPlan(convertStatusToStatusList(status), false)
        if (planList.size == 0) return arrayListOf()

        val planVersionIdList = planList.stream().map { it.versionId!! }.collect(Collectors.toList())
        val subjectVersionIdList = subjectBaseService.findPageByPlanVersionId(planVersionIdList)

        planList.forEach { plan ->
            plan.subjectList = subjectVersionIdList.filter { it.planVersionId == plan.versionId }
        }
        return planList
    }

    /**根据计划状态获取发布或草稿状态下的所有计划(不包括归属于它的实体)*/
    fun getPlanList(status: PlanStatusEnum): List<Plan> {
        return planRepository.findByStatusInAndDisabled(convertStatusToStatusList(status), false)
    }


    /**根据计划planId获取所有状态的计划副本(不包括归属于它的实体)*/
    fun getPlanList(id: Long, statuses: List<PlanStatusEnum> = emptyList()): List<Plan> {
        return planRepository.findByIdAndDisabled(id, false, statuses)
    }

    fun findPlanByName(id: Long, status: PlanStatusEnum): String {
        val planOptional: Optional<Plan> =
            planRepository.findByIdAndStatusInAndDisabled(id, convertStatusToStatusList(status), false)
        if (!planOptional.isPresent) {
            throw LoyaltyException(LoyaltyExceptionCode.PLAN_NOT_FOUND, id.toString())
        }
        return planOptional.get().name!!
    }

    fun findPlanByNameNo(id: Long, status: PlanStatusEnum): String {
        val planOptional: Optional<Plan> =
            planRepository.findByIdAndStatusInAndDisabled(id, convertStatusToStatusList(status), false)
        if (!planOptional.isPresent) {
            throw LoyaltyException(LoyaltyExceptionCode.PLAN_NOT_FOUND, id.toString())
        }
        return planOptional.get().name!!
    }


    fun findPlanDetailByPlanIdAndStatus(id: Long, status: PlanStatusEnum, showViewCondition: Boolean = false): Plan {
        val planOptional: Optional<Plan> =
            planRepository.findByIdAndStatusInAndDisabled(id, convertStatusToStatusList(status), false)
        if (!planOptional.isPresent) {
            throw LoyaltyException(LoyaltyExceptionCode.PLAN_NOT_FOUND, id.toString())
        }
        val plan = planOptional.get()
        plan.specialConditionOccasion = plan.specialConditionOccasionString?.let {
            JsonUtils.parse2List(plan.specialConditionOccasionString, EventOccasionEnum::class.java)
        }

        plan.subjectList = subjectBaseService.findDetailByPlanVersionId(plan.versionId!!)
        if(showViewCondition) {
            val pointAccountTypeIdList = ArrayList<Long>()
            plan.subjectList?.forEach { subject ->
                subject.pointAccountTypeList?.forEach { pointAccountType ->
                    pointAccountTypeIdList.add(pointAccountType.id!!)
                }
            }
            if(pointAccountTypeIdList.size != 0 ) {
                // 查询是否存在过审批流
                val list = pointAccountTypeRepository.findProcessSwitchByIds(pointAccountTypeIdList)
                if(list.isNotEmpty()) {
                    plan.subjectList?.forEach { subject->
                        subject.pointAccountTypeList?.forEach { pointAccountType ->
                            pointAccountType.processOpen = list.contains(pointAccountType.id!!)
                        }
                    }
                }
            }
        }
        plan.sendLimitRuleSwitchOne = planRepository.findSendLimitRuleSwitch(id)
        return plan
    }

    fun findPlanByPlanIdAndStatus(id: Long, status: PlanStatusEnum): Optional<Plan> {
        return planRepository.findByIdAndStatusInAndDisabled(id, convertStatusToStatusList(status), false)
    }

    /**根据计划id获取计划详情*/
    fun findPlanDetailByVersionId(versionId: Long): Plan {
        val plan = findById(versionId)
        plan.subjectList = subjectBaseService.findDetailByPlanVersionId(plan.versionId!!)
        return plan
    }


    fun findById(versionId: Long): Plan {
        val planOptional = planRepository.findById(versionId.toString())
        if (!planOptional.isPresent) {
            throw LoyaltyException(LoyaltyExceptionCode.PLAN_NOT_FOUND, versionId.toString())
        }
        val plan = planOptional.get()
        if (null != plan.disabled && plan.disabled!!) {
            throw LoyaltyException(LoyaltyExceptionCode.PLAN_NOT_FOUND, versionId.toString())
        }
        return plan
    }


    /**根据计划id获取计划*/
    fun findByVersionId(versionId: Long): Plan {
        val planOptional: Optional<Plan> = planRepository.findByVersionId(versionId)
        if (!planOptional.isPresent) {
            throw LoyaltyException(LoyaltyExceptionCode.PLAN_NOT_FOUND, versionId.toString())
        }
        val plan = planOptional.get()
        if (null != plan.disabled && plan.disabled!!) {
            throw LoyaltyException(LoyaltyExceptionCode.PLAN_NOT_FOUND, plan.versionId.toString())
        }
        return plan
    }

    /**新增计划本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun insert(plan: Plan) {
        checkPlanNameConflict(plan)
        plan.name = plan.name!!.trim()
        plan.versionId = null
        plan.status = PlanStatusEnum.DRAFT
        plan.backup = false
        plan.specialConditionCohortName = plan.specialConditionCohortName?.let { plan.specialConditionCohortName!!.trim() }
        plan.specialConditionTagName = plan.specialConditionTagName?.let { plan.specialConditionTagName!!.trim() }
        plan.specialConditionName = plan.specialConditionName?.let { plan.specialConditionName!!.trim() }
        plan.specialConditionStatus = plan.specialConditionStatus ?: EnableStatusEnum.DRAFT
        planRepository.save(plan)
        plan.id = plan.versionId
        planRepository.save(plan)
    }

    /**更新计划本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun update(plan: Plan) {
        checkPlanNameConflict(plan)
        plan.name = plan.name!!.trim()
        plan.specialConditionCohortName = plan.specialConditionCohortName?.let { plan.specialConditionCohortName!!.trim() }
        plan.specialConditionTagName = plan.specialConditionTagName?.let { plan.specialConditionTagName!!.trim() }
        plan.specialConditionName = plan.specialConditionName?.let { plan.specialConditionName!!.trim() }
        plan.specialConditionStatus = plan.specialConditionStatus ?: EnableStatusEnum.DRAFT
        planRepository.save(plan)
    }

    /**归档计划本身*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun file(plan: Plan) {
        val zone = ZonedDateTime2StringConverter()
        plan.status = PlanStatusEnum.FILED
        plan.filedTime = zone.convert(ZonedDateTime.now())
        planRepository.save(plan)
    }

    /**删除计划本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun delete(plan: Plan) {
        plan.disabled = true
        planRepository.save(plan)
    }

    /**校验计划名称是否存在冲突*/
    fun checkPlanNameConflict(plan: Plan) {
        val planList = planRepository.findByNameAndDisabled(plan.name!!, false)
        planList.forEach {
            if (null == plan.id || plan.id != it.id) {
                throw LoyaltyException(LoyaltyExceptionCode.PLAN_REPEATED)
            }
        }
    }



    /**
     * 获取计划中当前等级或当前积分配置参考类型
     */
    fun getFxReferenceTime(id: Long,type:String):Boolean {
        val fqn = when(type) {
            CurrentMemberPointFunction.FUN_NAME -> "data.loyalty.manager.pointAccountType"
            CurrentMemberGradeFunction.FUN_NAME -> "data.loyalty.manager.gradeHierarchy"
            CurrentMemberMedalFunction.FUN_NAME -> "data.loyalty.manager.medalHierarchy"
            else -> return false
        }

        val plan = planRepository.findFxReferenceTimeByIdAndDate(id, ZonedDateTime.now(), fqn)
        if(!plan.isPresent) {
            log.warn("查询积分或等级或勋章参考时间计划不存在 参数 id:$id, type:$type ")
            return false
        }
        val value = when(type) {
            CurrentMemberPointFunction.FUN_NAME -> plan.get().pointReferenceTime
            CurrentMemberGradeFunction.FUN_NAME -> plan.get().gradeReferenceTime
            CurrentMemberMedalFunction.FUN_NAME -> plan.get().medalReferenceTime
            else -> return false
        }

        return FxReferenceTime.CURRENT == value
    }


    fun findPlanByAccountOrHierarchyId(accountOrHierarchyId:Long,type: TypeEnum):Plan?{
        return when(type){
            TypeEnum.POINT-> LoyaltyPrograms.findPlanByAccountTypeId(accountOrHierarchyId)
            TypeEnum.GRADE-> LoyaltyPrograms.findPlanByGradeHierarchyId(accountOrHierarchyId)
            TypeEnum.MEDAL-> LoyaltyPrograms.findPlanByMedalHierarchyId(accountOrHierarchyId)
        }
    }
}
