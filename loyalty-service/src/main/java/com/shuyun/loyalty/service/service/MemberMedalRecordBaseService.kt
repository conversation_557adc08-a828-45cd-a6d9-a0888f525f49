package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.service.datamodel.MemberMedalRecord
import com.shuyun.loyalty.service.repository.MemberMedalRecordRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class MemberMedalRecordBaseService {

    @Autowired
    private lateinit var memberMedalRecordRepository: MemberMedalRecordRepository

    fun saveOrUpdate(memberMedalRecord: MemberMedalRecord, medalHierarchyId: Long): MemberMedalRecord {
        return memberMedalRecordRepository.saveOrUpdate(memberMedalRecord, medalHierarchyId.toString())
    }

}