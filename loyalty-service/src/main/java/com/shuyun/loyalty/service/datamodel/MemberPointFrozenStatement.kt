package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.entity.enums.FromTypeEnum
import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.service.MemberPointFrozenStatementService
import com.shuyun.pip.ApplicationContextHolder
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne
import javax.persistence.Table
import javax.validation.constraints.NotEmpty

/**
 * 冻结积分明细记录。
 * 将有效、待发放积分记录，转存到该表中。
 * 用于当退单时，用来追溯原单积分，进行冻结。历史订单当前状态。
 * fixme: 冻结时，如果原单已经过期，则不扣积分。如果原单已经冻结、则不再重复冻结，解冻时需要看所有子订单均为解冻。（每次计算均按整个订单计算）
 * 当发放积分时：无  -> 生效、待生效
 * 当生效时：待生效  -> 生效
 * 当使用时：生效  -> 已使用
 * 当过期时：生效 -> 已过期
 * 当冻结时：生效、待生效、已使用   ->   冻结s
 * 当预扣时：生效  ->   预扣
 * 当解冻时：冻结   ->  生效、待生效、过期
 */
@DataServiceModel
@FqnVariableModel
@Table(name = "data.loyalty.member.account.FrozenStatement{*}")
class MemberPointFrozenStatement : MemberPointStatement() {

    @ManyToOne
    @JoinColumn(name = "gainStatementId")
    @Suppress("unused")
    private var gainStatement: MemberPointGainStatement? = null

    /** 冻结记录id */
    @Column
    lateinit var frozenId: String

    /** 冻结的原有效或待发放积分明细 */
    @ManyToOne
    @JoinColumn(name = "frozenId")
    @Suppress("unused")
    var memberFrozenPoint: MemberFrozenPoint? = null

    /**冻结时明细来源有效积分表id**/
    @Column
    var fromStatementId : String? = null

    /**冻结时明细来源类型 neg **/
    @Column
    var fromType : FromTypeEnum? = null


    // 记录原单记录一笔附加信息，解冻的时候好还回去 比如店铺ID,扩展字段,变更记录ID等
    @Column
    @NotEmpty
    var data: String = "{}"

    fun save(accountTypeId: Long): MemberPointFrozenStatement {
        val frozenStatement = MemberPointFrozenStatement()
        frozenStatement.frozenId = frozenId
        frozenStatement.gainStatementId = gainStatementId
        frozenStatement.point = point
        frozenStatement.created = ZonedDateTime.now()
        frozenStatement.fromStatus = PointStateEnum.VALID
        frozenStatement.memberId = memberId
        frozenStatement.subjectFqn = subjectFqn
        frozenStatement.modified = ZonedDateTime.now()
        frozenStatement.effectiveDate = effectiveDate
        frozenStatement.memberPointId = memberPointId
        frozenStatement.overdueDate = overdueDate
        frozenStatement.planId = planId
        frozenStatement.pointPlanId = pointPlanId
        frozenStatement.gainStatementId = gainStatementId
        frozenStatement.fromType = fromType
        frozenStatement.fromStatementId = fromStatementId
        return ApplicationContextHolder.getBean(MemberPointFrozenStatementService::class.java).saveOrUpdate(frozenStatement, replacePattern = accountTypeId.toString())
    }

    fun update(accountTypeId: Long): MemberPointFrozenStatement {
        return ApplicationContextHolder.getBean(MemberPointFrozenStatementService::class.java).saveOrUpdate(this, replacePattern = accountTypeId.toString())
    }

    fun insert(accountTypeId: Long): MemberPointFrozenStatement {
        return ApplicationContextHolder.getBean(MemberPointFrozenStatementService::class.java).save(this, replacePattern = accountTypeId.toString())
    }

    fun delete(accountTypeId: Long) = ApplicationContextHolder.getBean(MemberPointFrozenStatementService::class.java).delete(id!!, replacePattern = accountTypeId.toString())

}