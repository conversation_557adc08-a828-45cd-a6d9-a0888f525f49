package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.model.ImportFieldConfig
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

@Component
class ImportFieldConfigRepository : DataModelRepository<ImportFieldConfig>() {

    private val log = LogManager.getLogger(ImportFieldConfigRepository::class.java)
    override fun log(): Logger = log

}