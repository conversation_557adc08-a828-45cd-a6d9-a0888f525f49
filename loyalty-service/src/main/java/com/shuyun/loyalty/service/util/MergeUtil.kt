package com.shuyun.loyalty.service.util

import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.dm.api.dataapi.request.QueryDataRequest
import com.shuyun.loyalty.service.model.Subject
import org.apache.logging.log4j.LogManager

/**
 * 1. 触发积分变更
 * 2. 查询副卡，副卡是否存在数据
 *      2.1 副卡不存在模型中 -> 不做处理
 *      3.1 副卡存在模型中 -> 1.说明可能忠诚度处理了合卡
 *                         2. 说明外部处理了合卡
 *          ->  1.如果是事件则重新推送一条
 *              2.如果是接口则重新变更一次
 *
 */
object MergeUtil {

    private val logger = LogManager.getLogger(ModelInitUtil::class.java)

    fun findMergePointMemberId(subject: Subject, mergeMemberId: String): String? {
        if (subject.mergeFqn == null) return null
        return findMergeMemberId(subject.mergeFqn!!, mergeMemberId)
    }

    private fun findMergeMemberId(mergeFqn: String, mergeMemberId: String): String? {
        val queryDataRequest = QueryDataRequest().apply {
            this.fqn = mergeFqn
            this.fields = "mainMemberId"
            this.filter = "{\"mergeMemberId\": {\"\$eq\": \"${mergeMemberId}\"} }"
            this.sort = "{}"
            this.withTotals = true
            this.offset = 0
            this.limit = 10
        }

        val resultResponse = DmPoolFactory.executeQuery({ sdk -> sdk.queryObjects(queryDataRequest) })

        if (resultResponse.totals <= 0) return null
        logger.debug("查询出副卡会员:$mergeMemberId 信息,说明发生过合卡")
        return resultResponse.data[0]["mainMemberId"].toString()
    }
}