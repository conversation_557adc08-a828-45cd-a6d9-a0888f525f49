package com.shuyun.loyalty.service.service

import com.shuyun.pip.retrofit.RetrofitService
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path


@RetrofitService("risk-control", "v1")
interface CheckListApi {

    @POST("api/checklist/checkExist4Cache")
    fun checkMember(@Body request: CheckExistRequest): Call<List<String>>
}


@RetrofitService("system-configuration", "v1")
interface ChecklistSubjectApi {
    @GET("api/checklist/fqn/{fqn}")
    fun getChecklistSubjectByFqn(@Path("fqn")fqn: String): Call<ChecklistSubject>
}

data class CheckExistRequest(val checklistType: ChecklistType, val groupIds: String?, val customers: List<String>)

data class ChecklistSubject(
    var id: String?,
    var subjectName: String?,
    var fqn: String?,
    var updateTime: String?,
    var createTime: String?,
    var updator: String?,
    var creator: String?,
    var remark: String?,
)

enum class ChecklistType {
    BLACK,  // 客户黑名单
    WHITE,  // 客户白名单
    MOBILE, // 手机号
    EMAIL;  // 邮件地址
}