package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.service.model.DerivationEventMetas
import com.shuyun.loyalty.service.repository.DerivationEventMetasRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class DerivationEventMetasBaseService {

    @Autowired
    private lateinit var derivationEventMetasRepository: DerivationEventMetasRepository


    fun findAll():List<DerivationEventMetas>{
        return derivationEventMetasRepository.findAll()
    }


    fun findByCodeIn(codes: Collection<String>): List<DerivationEventMetas>{
        if (codes.isEmpty()) return emptyList()
        return derivationEventMetasRepository.findByCodeIn(codes)
    }


    fun save(eventStreamMetas: DerivationEventMetas) {
        derivationEventMetasRepository.save(eventStreamMetas)
    }
}