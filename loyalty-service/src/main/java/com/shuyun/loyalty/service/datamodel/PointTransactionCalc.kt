package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.service.PointTransactionCalcBaseService
import com.shuyun.pip.ApplicationContextHolder
import java.math.BigDecimal
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.Table
import javax.persistence.Transient

/**
 * 积分反向操作计算表
 */
@DataServiceModel
@FqnVariableModel
@Table(name = "data.loyalty.member.account.PointTransactionCalc{*}")
open class PointTransactionCalc :BaseDataModel() {
    @Column
    var businessId: String? = null
    @Column
    var gainStatementId: String? = null
    @Column
    var point: BigDecimal = BigDecimal.ZERO
    @Column
    var effectiveDate: ZonedDateTime? = null
    @Column
    var overdueDate: ZonedDateTime? = null
    @Column
    var sourceId: String? = null
    @Column
    var sort: Int? = null
    @Column
    var created: ZonedDateTime? = null
    @Column
    var modified: ZonedDateTime? = null
    @Column
    var recordId: String? = null
    @Column
    var shopId: String? = null
    @Column
    var backId: String? = null
    @Column
    var ext1: String? = null
    @Column
    var ext2: String? = null
    @Column
    var ext3: String? = null
    /**
     * 内部逆向操作积分使用 0 全部使用 1 部分使用
     */
    @Transient
    var used: Int? = null

    fun saveOrUpdate(pointPlanId: String):PointTransactionCalc {
        return ApplicationContextHolder.getBean(PointTransactionCalcBaseService::class.java).saveOrUpdate(this,pointPlanId)
    }

    fun save(pointPlanId: String):PointTransactionCalc {
        return ApplicationContextHolder.getBean(PointTransactionCalcBaseService::class.java).save(this,pointPlanId)
    }

    fun delete(pointPlanId: String) {
        ApplicationContextHolder.getBean(PointTransactionCalcBaseService::class.java).delete(this.id!!,pointPlanId)
    }
}