package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.meta.PropertyBelongerTypeEnum
import com.shuyun.loyalty.service.model.CustomizedProperty
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.util.converter.ZonedDateTime2StringConverter
import org.apache.commons.lang3.StringUtils
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Repository
import java.time.ZonedDateTime
import java.util.*

@Repository
class CustomizedPropertyRepository : DataModelCarryRepository<CustomizedProperty>() {

    private val log = LogManager.getLogger(CustomizedPropertyRepository::class.java)
    override fun log(): Logger = log

    fun findByBelongerVersionIdAndBelongerTypeAndDisabled(belongerVersionId: Long, belongerType: PropertyBelongerTypeEnum, disabled: Boolean): List<CustomizedProperty> {
        val params = HashMap<String, Any?>()
        params["belongerVersionId"] = belongerVersionId
        params["belongerType"] = belongerType.name
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findByBelongerVersionIdAndBelongerType(belongerVersionId: Long, belongerType: PropertyBelongerTypeEnum): List<CustomizedProperty> {
        val params = HashMap<String, Any?>()
        params["belongerVersionId"] = belongerVersionId
        params["belongerType"] = belongerType.name
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findByIdAndBelongerTypeAndBelongerVersionId(id: Long, belongerType: PropertyBelongerTypeEnum, belongerVersionId: Long): CustomizedProperty? {
        val params = HashMap<String, Any?>()
        params["id"] = id
        params["belongerVersionId"] = belongerVersionId
        params["belongerType"] = belongerType.name
        return findListByFilter(JsonUtils.toJson(params)).firstOrNull()
    }

    fun findEventCustomizedPropertyByIdAndDate(id:Long, date: ZonedDateTime): Optional<CustomizedProperty> {
        val sql = "select pro.fxId,pro.name,pro.expression,pro.dsl,pro.id,pro.comparatorString,pro.dataType,pro.belongerVersionId,pro.belongerType,pro.widgetType,pro.ruleConfigString,pro.defaultValue,pro.sort,pro.status,pro.versionId " +
                "from data.loyalty.manager.customizedProperty pro inner join data.loyalty.manager.eventType e on pro.belongerVersionId = e.versionId " +
                "inner join data.loyalty.manager.subject s on e.subjectVersionId = s.versionId " +
                "inner join data.loyalty.manager.plan p on s.planVersionId = p.versionId " +
                "where pro.disabled=0 and pro.belongerType = 'EVENT' and e.disabled=0 and s.disabled=0 and p.disabled=0 " +
                "and pro.id = :id and (p.status='PUBLISHED' OR p.status = 'FILED') and pro.status = 'ENABLED' and p.publishedTime<=:date and (p.filedTime>=:date or p.filedTime is null) order by e.subjectVersionId desc"
        val params = HashMap<String, Any>()
        params["id"] = id
        params["date"] = ZonedDateTime2StringConverter().convert(date)!!
        return executeOne(sql, params)
    }

    fun findSubjectCustomizedPropertyByIdAndDate(id:Long, date: ZonedDateTime): Optional<CustomizedProperty> {
        val sql = "select pro.fxId,pro.name,pro.expression,pro.dsl,pro.id,pro.comparatorString,pro.dataType,pro.belongerVersionId,pro.belongerType,pro.widgetType,pro.ruleConfigString,pro.defaultValue,pro.sort,pro.status,pro.versionId " +
                "from data.loyalty.manager.customizedProperty pro " +
                "inner join data.loyalty.manager.subject s on pro.belongerVersionId = s.versionId " +
                "inner join data.loyalty.manager.plan p on s.planVersionId = p.versionId " +
                "where pro.disabled=0 and pro.belongerType = 'SUBJECT' and s.disabled=0 and p.disabled=0 and pro.id = :id " +
                "and (p.status='PUBLISHED' OR p.status = 'FILED') and pro.status = 'ENABLED' and p.publishedTime<=:date and (p.filedTime>=:date or p.filedTime is null) order by s.versionId desc"
        val params = HashMap<String, Any>()
        params["id"] = id
        params["date"] = ZonedDateTime2StringConverter().convert(date)!!
        return executeOne(sql, params)
    }


    fun findEventCustomizedPropertyByAllDate(date: ZonedDateTime): List<CustomizedProperty> {
        val sql = "select pro.id, pro.name " +
                "from data.loyalty.manager.customizedProperty pro inner join data.loyalty.manager.eventType e on pro.belongerVersionId = e.versionId " +
                "inner join data.loyalty.manager.subject s on e.subjectVersionId = s.versionId " +
                "inner join data.loyalty.manager.plan p on s.planVersionId = p.versionId " +
                "where pro.disabled=0 and pro.belongerType = 'EVENT' and e.disabled=0 and s.disabled=0 and p.disabled=0 " +
                "and (p.status='PUBLISHED' OR p.status = 'FILED') and pro.status = 'ENABLED' and p.publishedTime<=:date and (p.filedTime>=:date or p.filedTime is null)"
        val params = HashMap<String, Any>()
        params["date"] = ZonedDateTime2StringConverter().convert(date)!!
        return executeList(sql, params)
    }

    fun findSubjectCustomizedPropertyByAllDate(date: ZonedDateTime): List<CustomizedProperty> {
        val sql = "select pro.id, pro.name " +
                "from data.loyalty.manager.customizedProperty pro " +
                "inner join data.loyalty.manager.subject s on pro.belongerVersionId = s.versionId " +
                "inner join data.loyalty.manager.plan p on s.planVersionId = p.versionId " +
                "where pro.disabled=0 and pro.belongerType = 'SUBJECT' and s.disabled=0 and p.disabled=0 " +
                "and (p.status='PUBLISHED' OR p.status = 'FILED') and pro.status = 'ENABLED' and p.publishedTime<=:date and (p.filedTime>=:date or p.filedTime is null)"
        val params = HashMap<String, Any>()
        params["date"] = ZonedDateTime2StringConverter().convert(date)!!
        return executeList(sql, params)
    }

    fun findCustomizedPropertyBySubjectId(subjectId:Long,propertyIdList:List<Long>): Long{

        val pIdList = ArrayList<Long>()
        pIdList.addAll(propertyIdList)
        pIdList.add(0)

        val sql = "select count(b.id) id " +
                "from data.loyalty.manager.customizedProperty a " +
                "inner join data.loyalty.manager.subject b on a.belongerVersionId=b.versionId " +
                "where a.belongerType='SUBJECT' and b.id=:id and a.id in (${StringUtils.join(pIdList,",")})"

        val params = HashMap<String, Any>()
        params["id"] = subjectId
        log().debug("execute one sql: {}",sql)
        return executeOne(sql, params).get().id!!
    }

}