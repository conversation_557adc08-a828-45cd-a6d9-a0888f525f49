package com.shuyun.loyalty.service.calculate

import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.shuyun.fx.data.FxVariableProvider
import com.shuyun.loyalty.service.message.point.PointAccountEventMessage
import com.shuyun.loyalty.service.meta.RuleSortTypeEnum
import com.shuyun.loyalty.service.meta.TimeTypeEnum
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.loyalty.service.model.PointSendRule
import com.shuyun.loyalty.service.model.Subject
import com.shuyun.loyalty.service.model.calcPointDateTime
import com.shuyun.loyalty.service.service.CustomizedPropertyBaseService
import com.shuyun.loyalty.service.util.DateUtils
import com.shuyun.loyalty.service.util.ExpressionIdentUtil
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.exception.AbstractPipException
import com.shuyun.pip.i18n.LocaleI18nContextHolder
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.time.ZonedDateTime
import java.util.*

@Component
class BudgetMemberPointCalculate {

    private val logger = LogManager.getLogger(BudgetMemberPointCalculate::class.java)

    @Autowired
    private lateinit var fxVariableProvider: FxVariableProvider

    /**
     * 计算发放的规则
     */
    fun memberPointSend(message: PointAccountEventMessage, now: ZonedDateTime = ZonedDateTime.now()): ArrayList<Map<String, String?>> {
        val memberPointList = ArrayList<Map<String, String?>>()

        for (ruleGroup in message.ruleGroups) {

            logger.debug("积分预算-开始积分的发送. 计划ID:{} , 积分类型ID:{} , 规则组:{}, 时机eventTypeId:{} ", message.plan.id, message.pointAccountType.id, ruleGroup.id, ruleGroup.eventTypeId)

            //获取一条规则组下所有规则
            var sendRuleList = ruleGroup.sendRuleList ?: emptyList()

            //重算，不进行有效期过滤
            if (!message.isEffectiveRecalculate) {
                sendRuleList = sendRuleList.filter {
                    //过滤有效期错误的规则
                    val (effectiveDate, overdueDate) = it.calcPointDateTime(message.executeOrder.subject.dataType!!, message.event, now, message.eventType.pointSendCycleTimePath)
                    overdueDate == null || (overdueDate.isAfter(ZonedDateTime.now()) && overdueDate.isAfter(effectiveDate))
                }
            }

            if (sendRuleList.isEmpty()) continue

            // 通过表达式规则过滤规则
            val trueRuleList = sendRuleList.filter {
                try {
                    ExpressionIdentUtil.eppById(it.conditionExpressionFxId!!, message.event, JsonNodeFactory.instance.objectNode().put("subject", message.subject.dataType)).toString().toBoolean()
                } catch (e: Exception) {
                    var msg = e.message
                    if (e is AbstractPipException) {
                        msg = LocaleI18nContextHolder.getMessage(e.trackErrorCode, e.args)
                    }
                    val fx = fxVariableProvider.findById(it.conditionExpressionFxId!!.toLong())
                    logger.error("积分预算-表达式计算错误：规则组ID：{}, 规则名称：{}, 错误描述：[{}] 表达式：{}", it.ruleGroupId, it.ruleName, msg, JsonUtils.toJson(fx), e)
                    false
                }
            }

            logger.debug("积分预算-满足条件的规则是 {}", { JsonUtils.toJson(trueRuleList) })

            //没有满足的规则，则跳过本次循环
            if (trueRuleList.isEmpty()) continue

            ruleGroup.sendRuleList = trueRuleList

            // 计算满足条件的积分有效值和有效时间
            val satisfyList = this.trueRuleList(ruleGroup, message.subject, message)

            //一个规则组下没有满足条件的规则
            if (satisfyList.isEmpty()) continue

            //1.一个规则组下满足条件的只有一条规则
            if (satisfyList.size == 1) {
                logger.debug("积分预算-规则组下只有一条规则满足条件 满足的规则：{} ", {JsonUtils.toJson(satisfyList[0])})

                memberPointList.addAll(listOf(satisfyList[0]))
                continue
            }
            //按照规则组中的规则顺序，计算发放积分
            val memberPoint = when (ruleGroup.ruleSortType) {
                //1.按照规则顺序，取优先级高的
                RuleSortTypeEnum.MAX_LEVEL -> this.maxLevelToCalculate(satisfyList)
                //2.累加积分
                RuleSortTypeEnum.TOTAL_VALUE -> this.totalLevelToCalculate(satisfyList)
                //3.取最大积分
                RuleSortTypeEnum.MAX_VALUE -> this.maxValueToCalculate(satisfyList)
                //4.取最小积分
                RuleSortTypeEnum.MIN_VALUE -> this.minValueToCalculate(satisfyList)
                else -> listOf()
            }
            memberPointList.addAll(memberPoint)
        }

        return memberPointList
    }

    //计算满足条件的积分有效值和有效时间
    fun trueRuleList(ruleGroup: PointRuleGroup, subject: Subject, message: PointAccountEventMessage):ArrayList<Map<String, String>>{
        //创建满足条件的list
        val satisfyList = ArrayList<Map<String, String>>()
        //计算满足条件的积分有效值和有效时间
        ruleGroup.sendRuleList!!.forEach {
            //积分值
            val pointValue = ExpressionIdentUtil.eppById(it.actionExpressionFxId!!, message.event, JsonNodeFactory.instance.objectNode().put("subject",subject.dataType)).toString().toBigDecimal()
            //<0 报错
            if (pointValue < BigDecimal.ZERO) {
                throw IllegalArgumentException("积分值错误")
            }
            //按照规则组中的规则顺序，计算发放积分
            val effectiveSort = when (ruleGroup.ruleSortType) {
                //3.取最大积分
                RuleSortTypeEnum.MAX_VALUE -> pointEffectiveTime(it,message, subject.dataType)["effectiveSort"]!!
                //4.取最小积分
                RuleSortTypeEnum.MIN_VALUE -> pointEffectiveTime(it,message, subject.dataType)["effectiveSort"]!!
                else -> ""
            }
            satisfyList.add(mapOf(
                    Pair("pointValue", pointValue.toString()), //积分值
                    Pair("ruleId", it.id.toString()), //规则ID
                    Pair("ruleName", it.ruleName.toString()), //规则名称
                    Pair("ruleGroupId", ruleGroup.id.toString()), //规则组ID
                    Pair("ruleGroupName", ruleGroup.groupName.toString()), //规则组名称
                    Pair("effectiveSort", effectiveSort), //积分有效期排序
                    Pair("sort", it.sort.toString()), //规则优先级
            ))
        }
        return  satisfyList
    }

    //1.按照规则顺序，取优先级高的
    fun maxLevelToCalculate(satisfyList: List<Map<String, String>>):List<Map<String, String?>>{
        //发放积分
        val satisfy = satisfyList.sortedBy { it["sort"]!!.toBigDecimal() }[0]
        logger.debug("send point, max level to calculate , {}",{JsonUtils.toJson(satisfy)})
        return listOf(satisfy)
    }

    //2.累加积分
    fun totalLevelToCalculate(satisfyList: List<Map<String, String>>):List<Map<String, String?>>{
        //直接返回所有规则
        val groupIdList=ArrayList<Map<String, String?>>()
        satisfyList.forEach {
            groupIdList.addAll(listOf(it))
        }
        logger.debug("send point, total level to calculate , {}",{JsonUtils.toJson(satisfyList)})
        return groupIdList
    }

    //3.取最大积分
    fun maxValueToCalculate(satisfyList: List<Map<String, String>>):List<Map<String, String?>>{
        //1.积分值从大到小排序
        val sortPointList = satisfyList.sortedByDescending { it["pointValue"]!!.toBigDecimal()}
        logger.debug("send point value from big to small , {}",{JsonUtils.toJson(sortPointList)})

        //2.判断集合中最大积分值是否唯一
        val maxPointList = satisfyList.filter { sortPointList[0]["pointValue"]!!.toBigDecimal() == it["pointValue"]!!.toBigDecimal() }
        logger.debug("send point value, big is only one? , size = {}",maxPointList.size)

        //2.1最大积分值唯一
        if (maxPointList.size == 1) {
            logger.debug("send point value, big and only one , {}",{JsonUtils.toJson(maxPointList[0])})
            return listOf(maxPointList[0])
        }
        //2.2不唯一
        //多个相等的积分值，且积分值都是最大，判断有效期是否有永久
        val foreverPointList = maxPointList.filter { it["effectiveSort"] == "" }

        //2.2.1有永久
        if (foreverPointList.isNotEmpty()) {
            logger.debug("send point have forever end time , {}",{JsonUtils.toJson(foreverPointList[0])})
            return listOf(foreverPointList[0])
        }
        //2.2.2没有永久,按照积分有效期排序
        val noForeverPointList = maxPointList.sortedByDescending { it["effectiveSort"]!!.toBigDecimal() }
        //返回积分有效期最大值
        logger.debug("not forever end time , send point {}",{JsonUtils.toJson(noForeverPointList[0])})
        return listOf(noForeverPointList[0])
    }

    //3.取最小积分
    fun minValueToCalculate(satisfyList: List<Map<String, String>>):List<Map<String, String?>>{
        //1.积分值从小到大排序
        val sortPointList = satisfyList.sortedBy { it["pointValue"]!!.toBigDecimal() }
        logger.debug("send point value from small to big , {}",{JsonUtils.toJson(sortPointList)})

        //2.判断集合中最小积分值是否唯一
        val minPointList = satisfyList.filter { sortPointList[0]["pointValue"]!!.toBigDecimal() == it["pointValue"]!!.toBigDecimal() }

        //2.1最小积分值唯一
        if (minPointList.size == 1) {
            //发送积分
            logger.debug("send point value, small and only one , {}",{JsonUtils.toJson(minPointList)})
            return listOf(minPointList[0])
        }
        //2.2不唯一
        //多个相等的积分值，且积分值都是最小，判断有效期是否有永久
        var noForeverPointList = minPointList.filter { it["effectiveSort"] != "" }

        //最小积分值有效期全是永久
        if (noForeverPointList.isEmpty()) {
            //发送积分
            return listOf(minPointList[0])
        }
        //最小积分值没有永久的时间
        //去除永久后，最小积分集合是否只有一条数据
        if (noForeverPointList.size == 1) {
            //发送积分
            return listOf(noForeverPointList[0])
        }
        //去除有效期为永久，按照有效期从小到大排序
        noForeverPointList = noForeverPointList.sortedBy { it["effectiveSort"]!!.toBigDecimal() }
        //发送积分
        logger.debug("delete forever, from small to big , send point {}",{JsonUtils.toJson(noForeverPointList[0])})
        return listOf(noForeverPointList[0])
    }

    //计算积分有效期
    fun pointEffectiveTime(pointRule: PointSendRule, message: PointAccountEventMessage, dataType: String? = null): Map<String, String> {
        if (pointRule.effectTimePropertyId != null && pointRule.effectTimePropertyId != -1L) {
            setPropertyTime(pointRule, message, dataType, true)
        }
        if (pointRule.expireTimePropertyId != null && pointRule.expireTimePropertyId != -1L) {
            setPropertyTime(pointRule, message, dataType, false)
        }
        //获取系统年月日
        val calendarInstance = Calendar.getInstance()
        val calendarMap = mapOf(
                Pair("year", calendarInstance.get(Calendar.YEAR)),
                Pair("month", calendarInstance.get(Calendar.MONTH)),
                Pair("day", calendarInstance.get(Calendar.DAY_OF_MONTH))
        )
        logger.trace("获取系统年月日 :{}",{JsonUtils.toJson(calendarMap)})
        //获取有效期开始时间
        val effectPair = this.getCalendar(pointRule, calendarMap, "start", message)
        val effectStartCalendar = effectPair.first

        //生成有效期开始时间
        val takeEffectStartTimeLong = if(effectPair.second) {
            effectStartCalendar.timeInMillis
        }else {
            val takeEffectStartTime = if (effectStartCalendar.time < Date()) Date() else effectStartCalendar.time
            this.date2Long(takeEffectStartTime)
        }

        //永久结束
        if (pointRule.endYearTime?.isEmpty() == true) {
            //返回有效期开始和结束时间
            val map=mapOf(
                    Pair("startTime", takeEffectStartTimeLong.toString()),
                    Pair("endTime", ""),
                    Pair("effectiveSort", "")//永久结束,有效时长为空
            )
            logger.debug("有效期永久,有效期开始时间:{}",{JsonUtils.toJson(map)})
            return map
        }
        //获取有效期结束时间
        val calendarMapStart = mapOf(
                Pair("year", effectStartCalendar.get(Calendar.YEAR)),
                Pair("month", effectStartCalendar.get(Calendar.MONTH)),
                Pair("day", effectStartCalendar.get(Calendar.DAY_OF_MONTH))
        )

        val effectEndPair = this.getCalendar(pointRule, calendarMapStart, "end", message)
        val effectEndCalendar = effectEndPair.first
        //积分生成有效期时间
        val effectEndTimeLong= effectEndCalendar.timeInMillis
        //返回有效期开始和结束时间
        val map= mapOf(
                Pair("startTime", takeEffectStartTimeLong.toString()),
                Pair("endTime", effectEndTimeLong.toString()),
                Pair("effectiveSort", (effectEndTimeLong-takeEffectStartTimeLong).toString()) //获取有效时长，精确到秒
        )
        logger.debug("有效期时间:{}",{JsonUtils.toJson(map)})
        return map
    }

    //相对时间，绝对时间
    private fun getCalendar(pointRule: PointSendRule, calendarMapParam: Map<String, Int>, type: String, message: PointAccountEventMessage): Pair<Calendar,Boolean> {
        var calendarMap = calendarMapParam

        var calendar = Calendar.getInstance()
        //设置有效期开始年月日
        val yearType: TimeTypeEnum = if (type == "start") pointRule.startYearType!! else pointRule.endYearType!!
        val monthType: TimeTypeEnum = if (type == "start") pointRule.startMonthType!! else pointRule.endMonthType!!
        val dayType: TimeTypeEnum = if (type == "start") pointRule.startDayType!! else pointRule.endDayType!!
        val yearTime: String = if (type == "start") pointRule.startYearTime!! else pointRule.endYearTime!!
        val monthTime: String = if (type == "start") pointRule.startMonthTime!! else pointRule.endMonthTime!!
        val dayTime: String = if (type == "start") pointRule.startDayTime!! else pointRule.endDayTime!!

        var flag = false

        // 相对时间,需要获取时间中时间周期 积分生效开始时间需要替换, 而结束时间是按开始时间计算,不能替换 yearTime ='' 是立即生效
        if (yearType != TimeTypeEnum.ABSOLUTE_TIME && type == "start" && yearTime.isNotEmpty()) {
            val calendarRule = message.findPointSendCycleTimePath()
            logger.info("替换积分发放时间计算基准值结果 $calendarRule")
            if(calendarRule != null) {
                try{
                    val calendarMapRule = mapOf(Pair("year", calendarRule.get(Calendar.YEAR)),Pair("month", calendarRule.get(Calendar.MONTH)),Pair("day", calendarRule.get(Calendar.DAY_OF_MONTH)))
                    logger.info("获取系统年月日 old :{} new :{} ",{JsonUtils.toJson(calendarMap)}, {JsonUtils.toJson(calendarMapRule)})
                    calendarMap = calendarMapRule
                    calendar = calendarRule
                    flag = true
                }catch (e :Exception) {
                    logger.warn("替换配置的时间异常, 直接使用系统时间 ",e)
                }
            }
        }


        if(yearTime.isNotEmpty()){
            //获取规则年份
            calendar.set(Calendar.YEAR, if (yearType == TimeTypeEnum.ABSOLUTE_TIME) yearTime.toInt() else calendarMap["year"]!! + yearTime.toInt())
            //获取规则月份
            calendar.set(Calendar.MONTH, if (monthType == TimeTypeEnum.ABSOLUTE_TIME) monthTime.toInt() - 1 else calendarMap["month"]!! + monthTime.toInt())
            //获取规则日
            if(pointRule.endDayTime != "-1") calendar.set(Calendar.DAY_OF_MONTH, if (dayType == TimeTypeEnum.ABSOLUTE_TIME) dayTime.toInt() else calendarMap["day"]!! + dayTime.toInt())
            if(type == "start") calendar.set(Calendar.DAY_OF_MONTH, if (dayType == TimeTypeEnum.ABSOLUTE_TIME) dayTime.toInt() else calendarMap["day"]!! + dayTime.toInt())
        }

        if(type != "start"){
            calendar.set(Calendar.HOUR_OF_DAY,23)
            calendar.set(Calendar.MINUTE,59)
            calendar.set(Calendar.SECOND,59)
        }

        if(type != "start" && pointRule.endDayTime == "-1") {
            val last = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
            calendar.set(Calendar.DAY_OF_MONTH, last)
        }

        logger.debug("规则ID：${pointRule.id}, 获取有效期 {}: {}",{JsonUtils.toJson(type)},{SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(calendar.time)})
        return Pair(calendar,flag)
    }

    private fun setPropertyTime(pointRule: PointSendRule, message: PointAccountEventMessage,
                                dataType: String? = null, isEffect: Boolean) {
        val propertyId = if (isEffect) pointRule.effectTimePropertyId else pointRule.expireTimePropertyId
        val customizedProperty = ApplicationContextHolder.getBean(CustomizedPropertyBaseService::class.java)
            .getEffectiveOne(propertyId!!, ZonedDateTime.now())
        val date = ExpressionIdentUtil.eppById(customizedProperty.fxId!!, message.event, JsonNodeFactory.instance.objectNode().put("subject",dataType))
        val calendar = DateUtils.dateToCalendar(date.toString())
        if (calendar == null) {
            logger.error("时间函数返回日期错误，：{}", { date })
            throw IllegalArgumentException("等级过期时间函数返回日期错误")
        }
        if (isEffect) {
            pointRule.startYearType = TimeTypeEnum.ABSOLUTE_TIME
            pointRule.startMonthType = TimeTypeEnum.ABSOLUTE_TIME
            pointRule.startDayType = TimeTypeEnum.ABSOLUTE_TIME
            pointRule.startYearTime = "${calendar.get(Calendar.YEAR)}"
            pointRule.startMonthTime = "${calendar.get(Calendar.MONTH)+1}"
            pointRule.startDayTime = "${calendar.get(Calendar.DAY_OF_MONTH)}"
        } else {
            pointRule.endYearType = TimeTypeEnum.ABSOLUTE_TIME
            pointRule.endMonthType = TimeTypeEnum.ABSOLUTE_TIME
            pointRule.endDayType = TimeTypeEnum.ABSOLUTE_TIME
            pointRule.endYearTime = "${calendar.get(Calendar.YEAR)}"
            pointRule.endMonthTime = "${calendar.get(Calendar.MONTH)+1}"
            pointRule.endDayTime = "${calendar.get(Calendar.DAY_OF_MONTH)}"
        }
    }

    private fun date2Long(date: Date): Long {
        val time = Calendar.getInstance()
        time.time = date
        return time.timeInMillis
    }
}
