package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.entity.api.constants.PublishStatusEnum
import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.exception.PointException
import com.shuyun.loyalty.service.meta.EventStreamMetasStatusEnum
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.model.DerivationEventMetas
import com.shuyun.loyalty.service.model.DerivationEventType
import com.shuyun.loyalty.service.model.PointAccountType
import com.shuyun.loyalty.service.repository.PointAccountTypeRepository
import com.shuyun.loyalty.service.repository.SubjectRepository
import com.shuyun.loyalty.service.util.DateUtils
import org.apache.commons.io.IOUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.support.PathMatchingResourcePatternResolver
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import java.util.*

@Service
class PointAccountTypeBaseService {

    @Autowired
    private lateinit var subjectRepository: SubjectRepository

    @Autowired
    private lateinit var pointAccountTypeRepository: PointAccountTypeRepository

    @Autowired
    private lateinit var derivationEventMetasBaseService: DerivationEventMetasBaseService

    @Autowired
    private lateinit var remindConfigBaseService: RemindConfigBaseService

    private val log = LogManager.getLogger(PointAccountTypeBaseService::class.java)

    fun findAll(): List<PointAccountType> {
        val pointAccountTypeList = pointAccountTypeRepository.findAll()
        return pointAccountTypeList.filter { it.disabled == false }
    }

    /**根据主体ID获取账户类型详情*/
    fun findPageBySubjectVersionIdList(subjectVersionIdList: List<Long>): List<PointAccountType> {
        return pointAccountTypeRepository.findPageBySubjectVersionIdAndDisabled(subjectVersionIdList, false)
    }

    /**根据主体ID获取账户类型详情*/
    fun findDetailBySubjectVersionId(subjectVersionId: Long, calc:Boolean=false): List<PointAccountType> {
        val pointAccountTypeList = pointAccountTypeRepository.findBySubjectVersionIdAndDisabled(subjectVersionId, false)
        if (calc) return pointAccountTypeList
        pointAccountTypeList.forEach {
            it.remindConfigList = remindConfigBaseService.findParentVersionId(it.versionId!!)
        }
        return pointAccountTypeList
    }


    /**新增账户类型本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun insert(pointAccountType: PointAccountType) {
        pointAccountType.versionId = null
        pointAccountType.status = PublishStatusEnum.DRAFT
        pointAccountTypeRepository.save(pointAccountType)
        pointAccountType.id = pointAccountType.versionId
        pointAccountTypeRepository.save(pointAccountType)
        derivationEventMetasBaseService.save(initPointAccountTypeDerivation(pointAccountType, DerivationEventType.POINT_RECORD))
        derivationEventMetasBaseService.save(initPointAccountTypeDerivation(pointAccountType, DerivationEventType.POINT_EXPIRE_REMIND))
        derivationEventMetasBaseService.save(initPointAccountTypeDerivation(pointAccountType, DerivationEventType.POINT_DELAY_REMIND))
        derivationEventMetasBaseService.save(initPointAccountTypeDerivation(pointAccountType, DerivationEventType.POINT_RECORD_ITEM))
        derivationEventMetasBaseService.save(initPointAccountTypeDerivation(pointAccountType, DerivationEventType.BEYOND_POINT_RECORD))
        derivationEventMetasBaseService.save(initPointAccountTypeDerivation(pointAccountType, DerivationEventType.LIMIT_POINT_RECORD))
    }

    fun initPointAccountTypeDerivation(pointAccountType: PointAccountType, derivationEventType: DerivationEventType): DerivationEventMetas{
        val subject = subjectRepository.findByVersionId(pointAccountType.subjectVersionId!!)
        val resource = PathMatchingResourcePatternResolver().getResource("classpath:planPublishInit/derivation.event.${derivationEventType.fileName}.json")
        var jsonEvent = IOUtils.toString(resource.inputStream,"UTF-8")!!
        jsonEvent = jsonEvent.replace("{id}",pointAccountType.id.toString()).replace("{dataModelFqn}",subject.get().dataType!!)
        val derivationEventMetas = DerivationEventMetas().apply {
            this.code = derivationEventType.code.plus("_").plus(pointAccountType.id)
            this.schema = jsonEvent
            this.fqn = "data.loyalty.member.account.${derivationEventType.fileName}".plus(pointAccountType.id)
            this.name = "积分账户衍生事件,${derivationEventType.fileName}: ".plus(pointAccountType.id)
            this.status = EventStreamMetasStatusEnum.VALID
            this.created = Date()
            this.modified = Date()
        }
        return derivationEventMetas
    }

    /**更新账户类型本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun update(pointAccountType: PointAccountType, derivationEventTypes: List<DerivationEventType>? = null) {
        pointAccountTypeRepository.save(pointAccountType)
        val types = ArrayList<DerivationEventType>()
        if (derivationEventTypes == null) {
            val codes = ArrayList<String>()
            for (entry in DerivationEventType.entries) {
                if (entry != DerivationEventType.GRADE_EXPIRE_REMIND && entry != DerivationEventType.GRADE_RECORD) {
                    codes.add(entry.code.plus("_").plus(pointAccountType.id))
                }
            }
            val derivationEventMetaCodes = setOf(*derivationEventMetasBaseService.findByCodeIn(codes).map { it.code!! }.toTypedArray())
            codes.forEach { c ->
                if (c !in derivationEventMetaCodes) {
                    DerivationEventType.fromCode(c)?.let { types.add(it) }
                }
            }
        } else {
            types.addAll(derivationEventTypes)
        }

        for (derivationEventType in types) {
            derivationEventMetasBaseService.save(initPointAccountTypeDerivation(pointAccountType, derivationEventType))
        }
    }

    /**删除账户类型本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun delete(pointAccountType: PointAccountType) {
        pointAccountType.disabled = true
        pointAccountTypeRepository.save(pointAccountType)
    }

    /**根据时间及生效ID获取生效对象*/
    fun getEffectiveOne(id: Long, date: ZonedDateTime):PointAccountType{
        val pointAccountType = pointAccountTypeRepository.findByIdAndDate(id, date)
        return if(pointAccountType.isPresent) pointAccountType.get() else throw PointException(LoyaltyExceptionCode.POINT_NOT_FOUND)
    }


    fun findPointAccountType(id: Long):PointAccountType{
        log.debug("查询真实积分账户信息 $id")
        return getEffectiveOne(id, DateUtils.formatMinutesCacheKey(ZonedDateTime.now()))
    }



}

