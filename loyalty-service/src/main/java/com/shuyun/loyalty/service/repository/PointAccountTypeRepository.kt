package com.shuyun.loyalty.service.repository

import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.loyalty.service.model.PointAccountType
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import com.shuyun.pip.util.converter.ZonedDateTime2StringConverter
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component
import java.time.ZonedDateTime
import java.util.*

@Component
class PointAccountTypeRepository : DataModelCarryRepository<PointAccountType>() {

    private val log = LogManager.getLogger(GradeDefinitionRepository::class.java)
    override fun log(): Logger = log

    fun findPageBySubjectVersionIdAndDisabled(subjectVersionIdList: List<Long>, disabled: Boolean): List<PointAccountType> {
        val params = HashMap<String, Any?>()
        params["disabled"] = disabled
        val list = ArrayList<HashMap<String,Any?>>()
        if(subjectVersionIdList.size <= 0) return arrayListOf()
        if(subjectVersionIdList.size ==1) {
            params["subjectVersionId"] = subjectVersionIdList[0]
        } else {
            subjectVersionIdList.forEach {
                val paramsItem = HashMap<String, Any?>()
                paramsItem["subjectVersionId"] = it
                list.add(paramsItem)
            }
            params["\$or"] = list
        }
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findBySubjectVersionIdAndDisabled(subjectVersionId: Long, disabled: Boolean): List<PointAccountType> {
        val params = HashMap<String, Any?>()
        params["subjectVersionId"] = subjectVersionId
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findBySubjectVersionId(subjectVersionId: Long): List<PointAccountType> {
        val params = HashMap<String, Any?>()
        params["subjectVersionId"] = subjectVersionId
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findByIdAndDate(id:Long, date: ZonedDateTime): Optional<PointAccountType> {
        val sql = "select a.sendLimitRule, a.name,a.unit,a.subjectVersionId,a.id,a.precision,a.rounding,a.negativeStrategy,a.topLimit,a.description,a.sort,a.executeOrder,a.status,a.singleTopLimit,a.priorityDeduction,a.versionId,a.processSwitch,a.processId " +
                "from data.loyalty.manager.pointAccountType a " +
                "inner join data.loyalty.manager.subject s on a.subjectVersionId = s.versionId " +
                "inner join data.loyalty.manager.plan p on s.planVersionId = p.versionId " +
                "where a.disabled=0 and s.disabled=0 and p.disabled=0 and a.id = :id and (p.status='PUBLISHED' OR p.status = 'FILED') " +
                "and a.status = 'PUBLISHED' and p.publishedTime<=:date and (p.filedTime>=:date or p.filedTime is null) order by a.subjectVersionId desc limit 1"
        val params = HashMap<String, Any>()
        params["id"] = id
        params["date"] = ZonedDateTime2StringConverter().convert(date)!!
        return executeOne(sql, params)
    }

    /**
     * 查询已经发布的积分账号是否开启过审批流
     */
    @Suppress("UNCHECKED_CAST")
    fun findProcessSwitchByIds(ids: List<Long>): List<Long> {
        val sql ="select id from data.loyalty.manager.pointAccountType where id in (:ids) and `status`='PUBLISHED' and processSwitch=true group by id"
        val resultResponse = DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.execute(sql, mapOf("ids" to ids)) }
        val resultObject = resultResponse!!.data
        if(resultObject.isNullOrEmpty()) {
            return listOf()
        }
        resultObject as List<Map<String, Any?>>
        return resultObject.map { it["id"].toString().toLong() }
    }

    fun findPointAccountTypeIdByPlanId(planId: Long): List<PointAccountType> {

        val sql = "select c.id from data.loyalty.manager.pointAccountType c " +
            "inner join data.loyalty.manager.subject e on e.versionId=c.subjectVersionId " +
            "inner join data.loyalty.manager.plan f on f.versionId=e.planVersionId where f.id = :planId"

        val params = HashMap<String, Any>()
        params["planId"] = planId

        return executeList(sql, params)
    }
}