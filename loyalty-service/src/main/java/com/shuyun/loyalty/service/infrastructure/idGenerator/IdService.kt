package com.shuyun.loyalty.service.infrastructure.idGenerator

import com.shuyun.loyalty.sdk.Property
import com.shuyun.pip.retrofit.RetrofitProxy
import com.shuyun.pip.retrofit.RetrofitService
import org.slf4j.LoggerFactory
import retrofit2.Call
import retrofit2.http.*
import java.util.concurrent.ConcurrentHashMap

@RetrofitService("id-service", "")
interface IdServiceApi {
    @POST("core/generators")
    fun create(@Body request: CreateIdRequest): Call<Map<String, Any?>>

    @POST("core/generators/{id}/generate")
    fun generate(@Body request: GenerateIdRequest, @Path("id") id: String): Call<Map<String, Any?>>

    data class CreateIdRequest(
        val title: String,
        val fqn: String,
        val group: String,
        val module: String,
        val type: String,
        val init: Int,
        val step: Int,
        val description: String
    )

    data class GenerateIdRequest(
        val count: Int
    )

    /**
     * 查询ID生成器
     * ```json
     * [
     *     {
     *         "id": "vcayqu",
     *         "title": "loyalty-seq-id",
     *         "fqn": "loyalty4.vcayqu",
     *         "builtIn": false,
     *         "module": "loyalty4",
     *         "group": "loyalty4",
     *         "type": "SEQUENCE",
     *         "limitType": "NONE",
     *         "init": 6000,
     *         "step": 1,
     *         "available": true,
     *         "status": "RELEASED",
     *         "createAt": "2025-04-10T03:34:05.110Z",
     *         "updateAt": "2025-04-10T03:34:05.110Z"
     *     }
     * ]
     * ```
     */
    @GET("core/generators")
    fun query(@Query("module") module: String, @Query("group") group: String, @Query("type") type: String): Call<List<Map<String, Any?>>>
}

object IdService {

    private val logger = LoggerFactory.getLogger(IdService::class.java)
    private val api by lazy { RetrofitProxy.apiProxy(IdServiceApi::class.java) }

    private const val GROUP = "loyalty"
    private const val MODULE = "loyalty"
    private const val TYPE = "SEQUENCE"
    private const val STEP = 1
    private const val DESCRIPTION = "loyalty4"

    const val F1 = "loyalty.pg.meta.seq"
    const val F2 = "loyalty.eawf.approve.seq"

    private val SEQ_IDS = ConcurrentHashMap<String, String>()

    fun init() {
        SEQ_IDS[F1] = init(F1, 600_000,"忠诚度方案配置ID", "忠诚度方案配置ID")
        SEQ_IDS[F2] = init(F2, 100_000,"忠诚度审批列表ID", "忠诚度审批列表ID")
    }


    private fun createSeq(init: Int, fqn: String, title: String, description: String = DESCRIPTION): String {
        val requestBody = IdServiceApi.CreateIdRequest(
            title = title,
            fqn = fqn,
            group = GROUP,
            module = MODULE,
            type = TYPE,
            init = init,
            step = STEP,
            description = description
        )

        val request = api.create(requestBody)
        val response = request.execute()
        return if (response.isSuccessful) {
            response.body()?.get("id")?.toString() ?: throw RuntimeException(
                "创建ID生成器失败: 返回结果格式错误 title: $title, fqn: $fqn, init: $init"
            )
        } else {
           throw RuntimeException("创建ID生成器失败: ${response.errorBody()?.string()}")
        }
    }


    fun nextId(fqn: String): Long {
        val id = SEQ_IDS[fqn] ?: throw RuntimeException("ID生成器未初始化: $fqn")
        val requestBody = IdServiceApi.GenerateIdRequest(count = 1)
        val request = api.generate(requestBody, id)
        val response = request.execute()
        val value = if (response.isSuccessful) {
            response.body()?.get("result")?.let { (it as? List<*>)?.firstOrNull() }
        } else {
            throw RuntimeException("获取下一个ID失败: ${response.errorBody()?.string()}")
        }
        return value?.toString()?.toLong() ?: throw RuntimeException("获取下一个ID失败: 返回结果格式错误")
    }


    private fun queryId(fqn: String): String? {
        val request = api.query(MODULE, GROUP, TYPE)
        val response = request.execute()
        return if (response.isSuccessful) {
            val body = response.body()
            body?.find { it["fqn"] == fqn }?.get("id")?.toString()
        } else {
            throw RuntimeException("查询ID生成器失败: ${response.errorBody()?.string()}")
        }
    }


    private fun init(fqn: String, defaultInitialValue: Int, title: String, description: String): String {
        for (i in 0 .. 5) {
            try {
                val id = queryId(fqn)
                if (id != null) {
                    logger.info("ID生成器已存在: fqn={}, id={}", fqn, id)
                    return id
                }
                val init = Property.getSysOrEnv("${fqn}.init", defaultInitialValue)
                val newId = createSeq(init, fqn, title, description)
                logger.info("创建ID生成器成功: $fqn, $newId")
                return newId
            } catch (e: Exception) {
                Thread.sleep(500)
                if (i == 5) {
                    throw RuntimeException("创建ID生成器失败: $fqn, ${e.message}", e)
                }
            }
        }
        throw RuntimeException("创建ID生成器失败: $fqn")
    }
}

