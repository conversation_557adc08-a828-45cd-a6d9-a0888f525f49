package com.shuyun.loyalty.service.util

import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.service.exception.GradeException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.exception.MedalException
import com.shuyun.loyalty.service.meta.TypeEnum
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.service.GradeHierarchyBaseService
import com.shuyun.loyalty.service.service.MedalHierarchyBaseService
import com.shuyun.loyalty.service.service.SpecialListConfigBaseService
import com.shuyun.loyalty.service.service.SubjectBaseService
import com.shuyun.pip.ApplicationContextHolder
import java.time.ZonedDateTime

/**
 * 黑名单工具类
 */
object SpecialCheckerUtils {


    fun checkMemberInSpecialGrade(
        memberId: String,
        hierarchyId: Long,
        forbiddenOperation: ForbiddenOperation,
        forbiddenPort: ForbiddenPort,
        hashFindMember: Boolean = true
    ): Boolean {
        val hierarchy = try {
            ApplicationContextHolder.getBean(GradeHierarchyBaseService::class.java).getEffectiveOneCache(hierarchyId, DateUtils.formatMinutesCacheKey(ZonedDateTime.now()))
        } catch (e: Exception) {
            throw GradeException(LoyaltyExceptionCode.GRADE_HIERARCHY_NOT_FOUND, e)
        }
        val subject = ApplicationContextHolder.getBean(SubjectBaseService::class.java).findByVersionId(hierarchy.subjectVersionId!!)
        return ApplicationContextHolder.getBean(SpecialListConfigBaseService::class.java).checkMemberInSpecialList(
            memberId,
            subject.id!!,
            hierarchyId,
            forbiddenOperation,
            forbiddenPort,
            hierarchy.subjectVersionId!!,
            hashFindMember,
            TypeEnum.GRADE
        )
    }

    fun checkMemberInSpecialMedal(
        memberId: String, medalHierarchyId: Long, forbiddenOperation: ForbiddenOperation, forbiddenPort: ForbiddenPort,
        hashFindMember: Boolean = true
    ): Boolean {
        val medalHierarchy = try {
            ApplicationContextHolder.getBean(MedalHierarchyBaseService::class.java)
                .getEffectiveOneCache(medalHierarchyId, DateUtils.formatMinutesCacheKey(ZonedDateTime.now()))
        } catch (_: Exception) {
            throw MedalException(LoyaltyExceptionCode.MEDAL_HIERARCHY_NOT_FOUND)
        }

        val subject = ApplicationContextHolder.getBean(SubjectBaseService::class.java)
            .findByVersionId(medalHierarchy.subjectVersionId!!)

        return ApplicationContextHolder.getBean(SpecialListConfigBaseService::class.java).checkMemberInSpecialList(
            memberId,
            subject.id!!,
            medalHierarchyId,
            forbiddenOperation,
            forbiddenPort,
            medalHierarchy.subjectVersionId!!,
            hashFindMember,
            TypeEnum.MEDAL
        )
    }
}