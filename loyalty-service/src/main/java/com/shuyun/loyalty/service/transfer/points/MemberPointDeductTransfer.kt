package com.shuyun.loyalty.service.transfer.points

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.entity.enums.NegativeStrategyEnum
import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.service.datamodel.PointAction
import com.shuyun.loyalty.service.datamodel.PointTransactionCalc
import com.shuyun.loyalty.service.datamodel.PointTransactionStatement
import com.shuyun.loyalty.service.datamodel.save
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.exception.PointException
import com.shuyun.loyalty.service.exception.UsedPointException
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.service.LoyaltyPrograms
import com.shuyun.loyalty.service.service.PointTransactionBaseService
import com.shuyun.loyalty.service.service.PointTransactionCalcBaseService
import com.shuyun.loyalty.service.service.PointTransactionStatementBaseService
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime
import kotlin.jvm.optionals.getOrNull

@Service
class MemberPointDeductTransfer: MemberPointBaseTransfer() {

    private val logger = LogManager.getLogger(MemberPointDeductTransfer::class.java)

    @Autowired
    private lateinit var ptService: PointTransactionBaseService

    @Autowired
    private lateinit var ptcsService: PointTransactionCalcBaseService

    @Autowired
    private lateinit var ptssService: PointTransactionStatementBaseService

    fun deduct(lp: LoyaltyPoints, forbiddenPort: ForbiddenPort, operation: ForbiddenOperation?, journalKey: String) {
        // 作废扣减/使用扣减
        val abolish = lp.attr.abolish == true
        if (lp.hierarchy.negativeStrategy == NegativeStrategyEnum.NOT_ALLOWED) {
            if (abolish) {
                // 积分作废因扣减策略被禁止
                throw PointException(LoyaltyExceptionCode.NOT_ABOLISH_ALLOWED)
            } else {
                // 积分扣减因扣减策略被禁止
                throw PointException(LoyaltyExceptionCode.NOT_DEDUCT_ALLOWED)
            }
        }
        // 特殊名单检测
        if (operation != null) {
            checkSpecialList(lp, operation, forbiddenPort)
        }
        val memberPoint = lp.member.memberPoint!!
        val leftSegmentDate: LocalDate
        var actualPoint = lp.afterPoints
        if (lp.attr.businessId != null) {
            // 单笔操作不用检查余额，变更的积分值的积分一定小于等于单笔的积分值
            // 单笔操作-作废扣生效的积分
            if (lp.attr.pendingPoints == true) {
                // 单笔待生效积分作废，此时singleId = gainStatementId
                val gainStatementId = lp.attr.businessId!!
                val gs = gainStatementService.findByIds(listOf(gainStatementId), lp.hierarchy.id).firstOrNull()
                    ?: throw PointException(LoyaltyExceptionCode.GAIN_ID_NOT_FOUND)
                if (gs.status != FSMPointEvent.DELAY_SEND) {
                    // 原单积分状态不是待生效
                    throw PointException(LoyaltyExceptionCode.POINT_STATUS_CHANGED)
                }
                lp.afterPoints = gs.point
                gs.status = FSMPointEvent.SPECIAL_ABOLISH
                gs.modified = ZonedDateTime.now()
                gs.saveOrUpdate(lp.hierarchy.id)
                // 还需要更新一些值，所有0分也调用一次
                val pair = memberPoint.minus(BigDecimal.ZERO, lp.hierarchy.negativeStrategy!!)
                leftSegmentDate = pair.first
            } else {
                // 单笔扣减已生效的积分，此时singleId = validStatementId
                val validStatementId = lp.attr.businessId!!
                val vs = validService.findById(validStatementId, lp.hierarchy.id)
                    ?: throw UsedPointException(LoyaltyExceptionCode.VALID_POINT_NOT_FOUND)

                val gs = vs.findGainStatementPoint(lp.hierarchy.id) ?: throw UsedPointException(LoyaltyExceptionCode.VALID_POINT_NOT_FOUND)

                val statusList = mutableListOf(PointStateEnum.START, PointStateEnum.VALID, PointStateEnum.FURTHER_VALID)
                if (vs.fromStatus == PointStateEnum.SPECIAL_FROZE) {
                    // 单笔作废已冻结积分，特殊冻结的积分只有作废操作
                    if (!abolish) {
                        throw PointException(LoyaltyExceptionCode.POINT_STATUS_CHANGED)
                    }
                    lp.pointValue = vs.point
                    lp.afterPoints = vs.point
                    vs.openTraceId = vs.id
                    // 临时用冻结类型占用，保证页面上查不出来
                    vs.fromStatus = PointStateEnum.FROZEN
                    vs.saveOrUpdate(lp.hierarchy.id)

                    val vss = validService.findListByGainStatementId(lp.hierarchy.id, vs.gainStatementId)
                    val found = vss.find { it.fromStatus in statusList }
                    if (found == null) {
                        // 更新积分获取记录状态
                        gs.status = FSMPointEvent.DEDUCT
                        gs.modified = ZonedDateTime.now()
                        gs.saveOrUpdate(lp.hierarchy.id)
                    }
                    // 被冻结的时候已经扣减了，所以作废被冻结的积分不用再扣减
                    val pair = memberPoint.minus(BigDecimal.ZERO, lp.hierarchy.negativeStrategy!!, vs.overdueDate.shDate())
                    leftSegmentDate = pair.first
                } else {
                    // 单笔积分扣减 只有可用状态的积分才能扣减
                    if (vs.fromStatus !in statusList) {
                        throw PointException(LoyaltyExceptionCode.POINT_STATUS_CHANGED)
                    }
                    // 如果传入最大值超过单笔剩余最大值，取单笔剩余最大值
                    if (lp.afterPoints > vs.point) {
                        lp.pointValue = vs.point
                        lp.afterPoints = vs.point
                    }

                    if (lp.afterPoints > lp.member.point) {
                        throw PointException(LoyaltyExceptionCode.POINT_LESS_THAN_DEDUCT)
                    }

                    // 单笔全部扣减
                    if (vs.point.compareTo(lp.afterPoints) == 0) {
                        vs.openTraceId = vs.id
                        vs.fromStatus = PointStateEnum.USED
                        vs.saveOrUpdate(lp.hierarchy.id)
                        // 更新积分获取记录状态
                        gs.status = FSMPointEvent.DEDUCT
                        gs.modified = ZonedDateTime.now()
                        gs.saveOrUpdate(lp.hierarchy.id)
                    } else {
                        // 单笔部分扣减
                        vs.point -= lp.afterPoints
                        vs.saveOrUpdate(lp.hierarchy.id)
                        val id = Uuid.uuid
                        val vs1 = vs.copy(id, status = PointStateEnum.USED, point = lp.afterPoints, openTraceId = vs.id)
                        lp.attr.businessId = id
                        validService.batchInsert(listOf(vs1), lp.hierarchy.id.toString())
                    }
                    val pair = memberPoint.minus(lp.afterPoints, lp.hierarchy.negativeStrategy!!, vs.overdueDate.shDate())
                    leftSegmentDate = pair.first
                }
            }
        } else {
            // 检查余额是否足够扣减
            if (lp.afterPoints > lp.member.point) {
                // OPENAPI接口不允许扣除到负
                if (forbiddenPort == ForbiddenPort.INTERFACE) {
                    // 剩余积分值不够扣减
                    throw PointException(LoyaltyExceptionCode.POINT_LESS_THAN_DEDUCT)
                }
                if (lp.hierarchy.negativeStrategy == NegativeStrategyEnum.TO_ZERO && lp.member.point <= BigDecimal.ZERO) {
                    throw PointException(LoyaltyExceptionCode.POINT_LESS_THAN_DEDUCT)
                }
                actualPoint = if (lp.hierarchy.negativeStrategy == NegativeStrategyEnum.TO_ZERO) lp.member.point else lp.afterPoints
            }

            // 扣减总积分
            val pair = memberPoint.minus(actualPoint, lp.hierarchy.negativeStrategy!!)
            leftSegmentDate = pair.first
        }

        // 保存
        lp.member.leftSegmentDate = leftSegmentDate
        lp.afterPoints = (lp.member.point - memberPoint.point).abs()
        lp.afterTotalPoints = memberPoint.point
        lp.buildJournal(PointAction.DEDUCT, journalKey).save()
    }



    fun transfer(lp: LoyaltyPoints) {
        when (lp.type) {
            LoyaltyRequestType.API -> {
                // 常规接口扣减
                val recordId = Uuid.uuid
                val record = initPointRecord(
                    lp, recordId, FSMPointEvent.DEDUCT,
                    points = lp.afterPoints,
                    changePoints = -lp.afterPoints,
                    totalPoints = lp.afterTotalPoints,
                    pcStatus = PCStatus.USED
                ).apply {
                    recordDetail = "API扣减"
                }
                // 查询大于扣减时间的所有积分块
                val segments = segmentService.findDateAfter(lp.hierarchy.id, lp.member.id, lp.date.shDate()).filter {
                    it.expireDate!!.isAfter(lp.member.leftSegmentDate) && it.point >= BigDecimal.ZERO
                }
                var negativeStrategy = lp.hierarchy.negativeStrategy
                if (negativeStrategy == null) {
                    LoyaltyPrograms.findPlanByAccountTypeId(lp.hierarchy.id)?.let {
                        negativeStrategy = it.subjectList!!.first().pointAccountTypeList!!.first().negativeStrategy
                    }
                }
                // 顺序扣减
                val items = sequentiallyDeduct(
                    lp,
                    lp.pointValue,
                    ::vssFetch,
                    segments,
                    negativeStrategy!!,
                    recordId,
                    FSMPointEvent.DEDUCT,
                    FSMPointEvent.DEDUCT,
                    FSMPointEvent.DEDUCT
                )
                batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), items, segments)
            }
            LoyaltyRequestType.MANUAL -> {
                // 积分特殊变更操作的扣减 使用扣减/作废扣减
                // 单笔操作不需要更新积分块，前面第一阶段已经更新过了
                if (lp.attr.businessId != null) {
                    // 单笔作废待生效的积分, 待生效的积分作废后不允许重算，待生效的积分只有作废一个操作
                    if (lp.attr.pendingPoints == true) {
                        val gainStatementId = lp.attr.businessId!!
                        val gs = gainStatementService.findById(gainStatementId, replacePattern = lp.hierarchy.id.toString()).getOrNull()
                        if (gs == null || gs.status != FSMPointEvent.SPECIAL_ABOLISH) {
                            return
                        }
                        val recordId = Uuid.uuid
                        val record = initPointRecord(
                            lp, recordId, FSMPointEvent.SPECIAL_ABOLISH,
                            points = gs.point,
                            changePoints = BigDecimal.ZERO,
                            totalPoints = lp.afterTotalPoints,
                            pcStatus = PCStatus.DELAY_ABOLISH
                        ).apply {
                            this.recordDetail = "特殊变更操作-作废待生效的积分"
                        }

                        val item = initRecordItem(lp, Uuid.uuid, recordId, BigDecimal.ZERO, FSMPointEvent.ABOLISH, parentBackId = gs.id)
                        batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), listOf(item), listOf())
                        return
                    }

                    // 对应已经生效的积分，lp.attr.singleId == validStatementId
                    val validStatementId = lp.attr.businessId!!
                    val vs = validService.findById(validStatementId, lp.hierarchy.id) ?: return
                    // 删除已被扣减的记录
                    vs.delete(lp.hierarchy.id)

                    // 单笔作废已经生效或者已经冻结的积分
                    if (lp.attr.abolish == true) {
                        if (lp.attr.pendingPoints == true) {
                            return
                        }
                        if (vs.fromStatus != PointStateEnum.USED && vs.fromStatus != PointStateEnum.FROZEN) {
                            return
                        }
                        // 单笔作废已冻结的积分
                        if (vs.fromStatus == PointStateEnum.FROZEN) {
                            val recordId = Uuid.uuid
                            val record = initPointRecord(
                                lp, recordId, FSMPointEvent.SPECIAL_ABOLISH,
                                points = vs.point,
                                changePoints = BigDecimal.ZERO,
                                totalPoints = lp.afterTotalPoints,
                                pcStatus = PCStatus.FROZEN_ABOLISH
                            ).apply {
                                this.recordDetail = "特殊变更操作-作废已冻结的积分"
                            }
                            val item = initRecordItem(
                                lp,
                                Uuid.uuid,
                                recordId,
                                BigDecimal.ZERO,
                                FSMPointEvent.ABOLISH,
                                parentBackId = vs.openTraceId
                            )
                            batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), listOf(item), listOf())
                            return
                        }
                        // 单笔作废正常状态的积分
                        if (vs.fromStatus == PointStateEnum.USED) {
                            val recordId = Uuid.uuid
                            val record = initPointRecord(
                                lp, recordId, FSMPointEvent.SPECIAL_ABOLISH,
                                points = lp.afterPoints,
                                changePoints = -lp.afterPoints,
                                totalPoints = lp.afterTotalPoints,
                                pcStatus = PCStatus.ABOLISH
                            ).apply {
                                this.recordDetail = "特殊变更操作-作废单笔已生效的积分"
                            }
                            val item = initRecordItem(
                                lp,
                                Uuid.uuid,
                                recordId,
                                -lp.afterPoints,
                                FSMPointEvent.ABOLISH,
                                parentBackId = vs.openTraceId
                            )
                            batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), listOf(item), listOf())
                            return
                        }
                        return
                    }

                    // 单笔使用扣减已经生效的积分
                    val recordId = Uuid.uuid
                    val record = initPointRecord(
                        lp, recordId, FSMPointEvent.DEDUCT,
                        points = lp.afterPoints,
                        changePoints = -lp.afterPoints,
                        totalPoints = lp.afterTotalPoints,
                        pcStatus = PCStatus.USED
                    ).apply {
                        this.recordDetail = "特殊变更操作-使用单笔已生效的积分"
                    }

                    val item = initRecordItem(
                        lp,
                        Uuid.uuid,
                        recordId,
                        -vs.point,
                        FSMPointEvent.DEDUCT,
                        parentBackId = vs.openTraceId
                    )

                    batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), listOf(item), listOf())
                    return
                }

                // 批量使用扣减或者批量作废扣减👇
                val use = lp.attr.abolish != true   // 使用扣减
                if (use) {
                    // 批量使用扣减
                    val recordId = Uuid.uuid
                    val record = initPointRecord(
                        lp, recordId, FSMPointEvent.DEDUCT,
                        points = lp.afterPoints,
                        changePoints = -lp.afterPoints,
                        totalPoints = lp.afterTotalPoints,
                        pcStatus = PCStatus.USED
                    ).apply {
                        this.recordDetail = lp.attr.recordDetail ?: "特殊变更操作-批量使用扣减积分"
                    }
                    // 查询大于扣减时间的所有积分块
                    val segments = segmentService.findDateAfter(lp.hierarchy.id, lp.member.id, lp.date.shDate()).filter {
                        it.expireDate!!.isAfter(lp.member.leftSegmentDate) && it.point >= BigDecimal.ZERO
                    }
                    // 顺序扣减
                    val items = sequentiallyDeduct(
                        lp,
                        lp.pointValue,
                        ::vssFetch,
                        segments,
                        lp.hierarchy.negativeStrategy!!,
                        recordId,
                        FSMPointEvent.DEDUCT,
                        FSMPointEvent.DEDUCT,
                        FSMPointEvent.DEDUCT
                    )
                    batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), items, segments)
                } else {
                    // 批量作废扣减
                    val recordId = Uuid.uuid
                    val record = initPointRecord(
                        lp, recordId, FSMPointEvent.SPECIAL_ABOLISH,
                        points = lp.afterPoints,
                        changePoints = -lp.afterPoints,
                        totalPoints = lp.member.point - lp.afterPoints,
                        pcStatus = PCStatus.ABOLISH
                    ).apply {
                        this.recordDetail = lp.attr.recordDetail ?: "特殊变更操作-批量作废扣减积分"
                    }
                    // 查询大于扣减时间的所有积分块
                    val segments = segmentService.findDateAfter(lp.hierarchy.id, lp.member.id, lp.date.shDate()).filter {
                        it.expireDate!!.isAfter(lp.member.leftSegmentDate) && it.point >= BigDecimal.ZERO
                    }
                    // 顺序扣减
                    val items = sequentiallyDeduct(
                        lp,
                        lp.pointValue,
                        ::vssFetch,
                        segments,
                        lp.hierarchy.negativeStrategy!!,
                        recordId,
                        FSMPointEvent.SPECIAL_ABOLISH,
                        FSMPointEvent.DEDUCT,
                        FSMPointEvent.ABOLISH
                    )
                    batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), items, segments)
                }
            }
            LoyaltyRequestType.TX_API -> {
                // 支持撤销的扣减
                // 常规接口扣减
                val recordId = Uuid.uuid
                val record = initPointRecord(
                    lp, recordId, FSMPointEvent.DEDUCT,
                    points = lp.afterPoints,
                    changePoints = -lp.afterPoints,
                    totalPoints = lp.afterTotalPoints,
                    pcStatus = PCStatus.USED
                ).apply {
                    recordDetail = "API操作-支持撤销的积分扣减"
                }

                val pointTransaction = ptService.getByBusinessId(lp.member.memberId, lp.attr.traceId, FSMPointEvent.DEDUCT.name, lp.hierarchy.id)
                if (pointTransaction == null) {
                    logger.info("反向扣减记录不存在，traceId:{}", lp.attr.traceId)
                    return
                }

                // 查询大于扣减时间的所有积分块
                val segments = segmentService.findDateAfter(lp.hierarchy.id, lp.member.id, lp.date.shDate()).filter {
                    it.expireDate!!.isAfter(lp.member.leftSegmentDate) && it.point >= BigDecimal.ZERO
                }

                var sort = 0
                val ptcs = ArrayList<PointTransactionCalc>()
                val ptss = ArrayList<PointTransactionStatement>()

                // 顺序扣减
                val items = sequentiallyDeduct(
                    lp,
                    lp.pointValue,
                    ::vssFetch,
                    segments,
                    lp.hierarchy.negativeStrategy!!,
                    recordId,
                    FSMPointEvent.DEDUCT,
                    FSMPointEvent.DEDUCT,
                    FSMPointEvent.DEDUCT) { vs, item ->

                    item.backId = Uuid.uuid
                    // 保存用于撤销的积分记录
                    val transactionCalc = initTransactionCalc(lp, recordId, pointTransaction.id!!, item.backId!!, vs, sort)
                    val transactionStatement = initPointTransactionStatement(pointTransaction.id!!, item.backId!!, vs, sort)
                    ptcs.add(transactionCalc)
                    ptss.add(transactionStatement)
                    sort++
                }
                ptcsService.batchInsert(ptcs, lp.hierarchy.id.toString())
                ptssService.batchInsert(ptss, lp.hierarchy.id.toString())
                batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), items, segments)
            }
            else -> {}
        }
    }
}