package com.shuyun.loyalty.service.model

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.exception.LPException
import com.shuyun.loyalty.service.meta.PlanStatusEnum
import com.shuyun.loyalty.service.meta.TypeEnum
import com.shuyun.loyalty.service.service.ChecklistSubjectApi
import com.shuyun.loyalty.service.service.PlanBaseService
import com.shuyun.loyalty.service.service.SubjectBaseService
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.retrofit.RetrofitProxy
import io.swagger.v3.oas.annotations.media.Schema
import org.apache.logging.log4j.LogManager
import java.time.ZonedDateTime
import javax.persistence.*


@Schema(title = "特殊名单配置")
@Table(name = "data.loyalty.manager.specialListConfig")
@DataServiceModel
data class SpecialListConfig(

    @Schema(title = "id", type = "number")
    @Column
    var id: Long? = null,

    @Schema(title = "方案名称", type = "String")
    @Column
    var name: String? = null,

    @Schema(title = "所属计划名称", type = "number")
    @Column
    var planName: String? = null,

    @Schema(title = "所属主体名称", type = "number")
    @Column
    var subjectName: String? = null,

    @Transient
    var forbiddenConfig: Map<String,List<ForbiddenConfig>>? = null,

    @Schema(title = "黑名单配置", type = "object")
    @Column
    var forbiddenConfigString: String? = null,

    @Schema(title = "黑名单主题ID", type = "string")
    @Column
    var specialListThemeId: String? = null,

    @Schema(title = "黑名单主题名称", type = "string")
    @Column
    var specialListThemeName: String? = null,

    @Schema(title = "匹配字段", type = "string")
    @Column
    var columnPath: String? = null,

    @Schema(title = "已选黑名单分组ID(英文半角逗号隔开)", type = "string")
    @Column
    var specialListGroupIds: String? = null,

    @Schema(title = "已选黑名单分组名称(英文半角逗号隔开)", type = "string")
    @Column
    var specialListGroupNames: String? = null,

    @Schema(title = "已选黑名单分组名称i8n(英文半角逗号隔开)", type = "string")
    @Column
    var specialListGroupNamesI18n: String? = null,

    @Schema(title = "配置状态", type = "string")
    @Enumerated(EnumType.STRING)
    @Column
    var status: SpecialListConfigStatus? = null,

    @Schema(title = "更新时间", type = "date")
    @Column
    var publishedTime: String? = null,

    @Schema(title = "是否展示给前端", type = "String")
    @Column
    var display: Boolean? = null,

    @Schema(title = "所属计划", type = "number")
    @Column
    var planId: Long? = null,

    @Schema(title = "所属主体", type = "number")
    @Column
    var subjectId: Long? = null

) : BaseDataServiceModel() {
    override fun copyToOldOne(old: BaseDataServiceModel, backup: Boolean) {
        throw  UnsupportedOperationException("<UNK>")
    }

    //判断归档的主体是否存在
    fun subjectIsDisabled(): Boolean {
        val plan = try {
            ApplicationContextHolder.getBean(PlanBaseService::class.java)
                .findPlanDetailByPlanIdAndStatus(this.planId!!, PlanStatusEnum.PUBLISHED)
        } catch (e: Exception) {
            throw IllegalArgumentException("无发布的计划", e)
        }
        return plan.subjectList!!.find {
            it.id == this.subjectId && it.status == EnableStatusEnum.DISABLED
        } is Subject
    }

    fun fullFillConfigStringByConfig() {
        this.forbiddenConfigString = JsonUtils.toJson(this.forbiddenConfig)
    }

    fun fullFillConfigByConfigString() {
        val fqn = this.columnPath!!.substringBeforeLast(".")
        val checklistSubjectApi =
            RetrofitProxy.apiProxy(ChecklistSubjectApi::class.java).getChecklistSubjectByFqn(fqn)
        val response = checklistSubjectApi.execute()
        if (response.isSuccessful) {
            val checklistSubject = response.body()
            this.specialListThemeName = checklistSubject!!.subjectName
            this.forbiddenConfig = Json.parse<Map<String, List<ForbiddenConfig>>>(this.forbiddenConfigString!!)
            try {
                this.planName = ApplicationContextHolder.getBean(PlanBaseService::class.java)
                    .findPlanByName(this.planId!!, PlanStatusEnum.PUBLISHED)
                this.subjectName = ApplicationContextHolder.getBean(SubjectBaseService::class.java)
                    .getEffectiveOne(this.subjectId!!, ZonedDateTime.now()).name
            } catch (e: Throwable) {
                LogManager.getLogger(SpecialListConfig::class.java).error("获取计划,主体名称报错 {}", e.message)
            }
        } else {
            LogManager.getLogger(SpecialListConfig::class.java).error("查询特殊名单接口异常 fqn: {} {}", fqn, response.errorBody()?.string())
            throw LPException("查询特殊名单接口异常")
        }
    }


    fun checkAccountOrHierarchyAndOperationInConfig(
        id: Long,
        forbiddenOperationType: ForbiddenOperation,
        forbiddenPort: ForbiddenPort,
        type: TypeEnum
    ): Boolean {
        fullFillConfigByConfigString()
        val forbiddenConfigs = forbiddenConfig?.get(type.type)
        return if (forbiddenConfigs != null) {
            val forbiddenConfig = forbiddenConfigs.firstOrNull { it.id == id }
            if (forbiddenConfig != null) {
                forbiddenConfig.forbiddens[forbiddenPort]?.any { it == forbiddenOperationType } == true
            } else false
        } else false
    }
}

@Schema(title = "ForbiddenConfig")
data class ForbiddenConfig(

    @Schema(title = "所属账户或者等级体系", type = "number")
    var id: Long? = null,

    @Schema(title = "所属账户或等级体系名称", type = "string")
    var name: String? = null,

    @Schema(title = "排序", type = "Int")
    var sort: Int? = null,

    @Schema(title = "所禁止的场景操作", type = "object")
    var forbiddens: Map<ForbiddenPort,List<ForbiddenOperation>>
)

@Suppress("unused")
enum class ForbiddenOperation {
    GRADE_UPGRADE, GRADE_HOLD, GRADE_DEGRADE,
    POINT_SEND, POINT_FREEZE, POINT_UNFREEZE, POINT_DEDUCT_BY_USE, POINT_DEDUCT_BY_ABOLISH,POINT_EXPIRE,POINT_EFFECT,POINT_SEND_RECYCLE,POINT_DEDUCT_RECYCLE,POINT_DEDUCT,
    MEDAL_OBTAIN, MEDAL_KEEP, MEDAL_RECYCLE
}

enum class SpecialListConfigStatus {

    DRAFT, ENABLED, ENABLED_DRAFT, DISABLED, DISABLED_DRAFT, FILED;

    fun enable(): SpecialListConfigStatus {
        return if (this == DISABLED) {
            ENABLED
        } else if (this == DISABLED_DRAFT) {
            ENABLED_DRAFT
        } else throw IllegalArgumentException("status $this enabled not allowed")
    }

    fun disable(): SpecialListConfigStatus {
        return if (this == ENABLED) {
            DISABLED
        } else if (this == ENABLED_DRAFT) {
            DISABLED_DRAFT
        } else throw IllegalArgumentException("status $this disabled not allowed")
    }

    fun publish(): SpecialListConfigStatus {
        return if (this == DRAFT || this == ENABLED_DRAFT) {
            ENABLED
        } else if (this == DISABLED_DRAFT) {
            DISABLED
        } else {
            throw IllegalArgumentException("status $this published not allowed")
        }
    }

    fun backup(): SpecialListConfigStatus {
        return if (this == ENABLED) {
            ENABLED_DRAFT
        } else if (this == DISABLED) {
            DISABLED_DRAFT
        } else {
            throw IllegalArgumentException("status $this backup not allowed")
        }
    }

}