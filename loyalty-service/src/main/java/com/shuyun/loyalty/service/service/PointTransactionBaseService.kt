package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.service.datamodel.PointTransaction
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Service

@Service
class PointTransactionBaseService : MemberPointDmBaseService<PointTransaction>() {
    private val log = LogManager.getLogger(PointTransactionBaseService::class.java)
    override fun log(): Logger = log

    fun getByBusinessId(
        memberId: String,
        businessId: String,
        recordType: String,
        pointPlanId: Long
    ): PointTransaction? {
        val recordTypes = listOf(recordType)
        return getByBusinessId(
            memberId = memberId,
            businessId = businessId,
            recordTypes = recordTypes,
            pointPlanId = pointPlanId
        )
    }


    fun getByBusinessId(
        memberId: String,
        businessId: String,
        recordTypes: List<String>,
        pointPlanId: Long
    ): PointTransaction? {
        val sql = """
            SELECT `id`,`businessId`,`memberId`,`recordType`,`point`,`modified`,`created` 
            FROM data.loyalty.member.account.PointTransaction$pointPlanId 
            WHERE businessId = :businessId AND memberId = :memberId AND recordType in (:recordTypes)
            LIMIT 1
        """.trimIndent()
        val params = mapOf(
            "businessId" to businessId,
            "memberId" to memberId,
            "recordTypes" to recordTypes
        )
        val data = execute(sql, params).data.firstOrNull()
        return data?.let { Json.convert(it) }
    }

}