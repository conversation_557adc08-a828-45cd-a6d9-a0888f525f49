package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.repository.AlertRepository
import com.shuyun.pip.ApplicationContextHolder
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.Table


@Table(name = "data.loyalty.manager.alert")
@DataServiceModel
class Alert : BaseDataModel() {

    @Column
    var data: String? = null

    @Column
    var content: String? = null

    @Column
    var bizType: String? = null

    @Column
    var errorMsg: String? = null

    @Column
    var errorCode: String? = null

    @Column
    var created: ZonedDateTime? = null

    @Column
    var modified: ZonedDateTime? = null

    fun save() {
        ApplicationContextHolder.getBean(AlertRepository::class.java).save(this)
    }

    fun build(data: String, content: String, bizType: String, errorMsg: String,errorCode:String?): Alert {
        this.created = ZonedDateTime.now()
        this.modified = this.created
        this.content = content
        this.data = data
        this.bizType = bizType
        this.errorMsg = errorMsg
        this.errorCode = errorCode
        return this
    }


}
