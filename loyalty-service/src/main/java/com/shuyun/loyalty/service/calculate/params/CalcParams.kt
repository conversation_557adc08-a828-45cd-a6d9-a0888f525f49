package com.shuyun.loyalty.service.calculate.params

import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.meta.RuleGroupStatusEnum
import kotlin.properties.Delegates

open class CalcParams() : Params {
    var pointPlanId: Long by Delegates.notNull()
    // 2022年8月12日该参数在计算时已经赋值
    var operationEnum: EventOperationEnum? = null

    open var eventTypeId: Long? = null

    var groupName: String? = null
    var ruleGroupId: Long? = null
    var groupStatus: List<RuleGroupStatusEnum>? = null

    constructor(pointAccountTypeId: Long): this() {
        this.pointPlanId = pointAccountTypeId
    }

    constructor(pointAccountTypeId: Long, scoreType: EventOperationEnum?, groupName: String?, ruleGroupId: Long?, groupStatus: List<RuleGroupStatusEnum>?): this(pointAccountTypeId) {
        this.operationEnum = scoreType
        this.groupName = groupName
        this.ruleGroupId = ruleGroupId
        this.groupStatus = groupStatus
    }
    override fun isDeduct() = false
}

