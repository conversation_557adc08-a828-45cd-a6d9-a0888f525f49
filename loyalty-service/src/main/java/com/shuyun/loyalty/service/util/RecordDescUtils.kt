package com.shuyun.loyalty.service.util

import com.shuyun.loyalty.service.model.PointDeductRule
import org.apache.commons.lang3.StringUtils

object RecordDescUtils {

    fun get(ruleGroupName: String?, deductRule: PointDeductRule?): String? {
        if (deductRule != null && !StringUtils.isBlank(deductRule.remark)) {
            return deductRule.remark
        }
        return ruleGroupName
    }

    fun get(ruleGroupName: String?,remark: String?): String? {
        if(!StringUtils.isBlank(remark)) {
            return remark
        }
        return ruleGroupName
    }
}