package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.enums.NegativeStrategyEnum
import com.shuyun.loyalty.entity.enums.NegativeStrategyEnum.TO_ZERO
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.extension.adjustToSHTimeEnd
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.service.MemberPointLogService
import com.shuyun.loyalty.service.service.MemberPointService
import com.shuyun.loyalty.service.service.MemberPointValidStatementService
import com.shuyun.loyalty.service.service.PointSegmentService
import com.shuyun.loyalty.service.transfer.points.LoyaltyPoints
import com.shuyun.loyalty.service.util.ConstantValue.LONG_TERM_OVERDUE_DATE
import com.shuyun.pip.ApplicationContextHolder
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime
import javax.persistence.*

/**
 * fixme 优化 saveOrUpdate看是否存在主键，存在则更新变更的value，将整个对象保存，需要改成监听变更的属性，在update对象时，只更新修改过的属性
 * fixme 二期 积分账户间的扣减，需要进行权限定义，按调用角色区分，系统层面默认权限，用户层面按自定义权限
 * TODO 所有积分入库前，及计算后，在必要的点需要未回舍入计算
 * 为了不把逻辑设计得过于集中复杂，导致后续扩展和状态判定上增加难度。
 * 将积分冻结、积分锁定，通过子账户形式进行记录。带来的问题是数据转移带来的事务及数据库压力，但考虑该场景应该还可以接受。
 * 结构：
 *      会员积分
 *          会员冻结账户
 *              冻结账户原获取记录明细：用于解冻后，能准确还原积分状态及有效期
 *          会员锁定账户
 *              锁定账户原获取记录明细：用于解锁后，能准确还原积分状态及有效期
 * 会员积分账户信息，该数据对象存放于数据服务
 * 主要包括几个重要信息，关联的会员id、该会员在该积分账户下的会员积分账户id、版本号、子账户信息
 */
@DataServiceModel
@FqnVariableModel
@Table(name = "data.loyalty.member.account.Point{*}")
class MemberPoint : BaseMemberPoint() {

    /** 版本，主要考虑使用乐观锁解决性能问题，最终实现取决于数据服务支持 */
    @Column
    var version: Int = 0

    /** 最后一次修改时间 */
    @Column
    var modified: ZonedDateTime = ZonedDateTime.now()

    /** 会员有效积分记录明细 */
    @Suppress("unused")
    @OneToMany
    var pointValidStatement: List<MemberPointValidStatement>? = null

    /** 会员积分获取账户记录，用于退单时追溯，同时其它状态变更，需要同步修改该表状态，比如过期情况，根据该状态判定后决定是否需要再冻结或重算，该记录数据不删除，只更改状态 */
    @OneToMany
    @Suppress("unused")
    var pointGainStatement: List<MemberPointGainStatement>? = null

    /** 积分冻结记录 */
    @OneToMany
    @Suppress("unused")
    var memberFrozenPoint: List<MemberFrozenPoint>? = null

    @ManyToOne
    @JoinColumn(name = "memberId")
    var member: Any? = null

    // 是否开启积分段计算,默认未开启
    @Column
    var openSegmentFlag: Boolean = false


    @Transient
    val lp: LoyaltyPoints? = null

    fun deleteByFilter(ids: Array<String>, accountTypeId: Long) =
        ApplicationContextHolder.getBean(MemberPointValidStatementService::class.java).deleteBatchValidPoint(ids, accountTypeId)

    fun saveLog(newPoint: BigDecimal = point, createdTime: ZonedDateTime = ZonedDateTime.now()) = ApplicationContextHolder.getBean(MemberPointLogService::class.java).save(this, this.pointPlanId!!, newPoint, createdTime)

    /** 保存或者更新 */
    // 该方法作废，使用基于版本号更新的版本
    @Deprecated("请使用独立的更新和保存方法代替")
    fun saveOrUpdate(accountTypeId: Long): MemberPoint {
        modified = ZonedDateTime.now()
        ApplicationContextHolder.getBean(MemberPointService::class.java).saveOrUpdate(this, replacePattern = accountTypeId.toString())
        return this
    }

    // 保存
    fun save(): MemberPoint {
        ApplicationContextHolder.getBean(MemberPointService::class.java).save(this, replacePattern = pointPlanId.toString())
        return this
    }


    fun update() {
        modified = ZonedDateTime.now()
        val sql = """
            UPDATE data.loyalty.member.account.Point$pointPlanId SET 
                point = :point,
                memberId = :memberId,
                modified = :modified,
                openSegmentFlag = :openSegmentFlag,
                version = :nextVersion
            WHERE id = :id
        """.trimIndent()
        val nextVersion = version + 1
        val params = mapOf(
            "point" to point,
            "memberId" to memberId,
            "modified" to modified,
            "openSegmentFlag" to openSegmentFlag,
            "id" to id,
            "nextVersion" to nextVersion,
            "version" to version
        )
        val svc = ApplicationContextHolder.getBean(MemberPointService::class.java)
        svc.execute(sql, params)
        version = nextVersion
    }


    fun buildSegment(point: BigDecimal, expireDate: LocalDate): MemberPointSegment {
        val segment = MemberPointSegment()
        segment.id = Uuid.uuid
        segment.planId = planId
        segment.pointPlanId = pointPlanId
        segment.memberPointId = id
        segment.memberId = memberId
        segment.subjectFqn = subjectFqn
        segment.point = point
        segment.expireDate = expireDate
        segment.modified = ZonedDateTime.now()
        segment.created = ZonedDateTime.now()
        return segment
    }


    // 加一笔积分
    fun plus(p: BigDecimal, effectiveDate: ZonedDateTime, overdueDateTime: ZonedDateTime, now: ZonedDateTime, updateNow: Boolean = true) {
        if (id == null) throw IllegalArgumentException("积分账户id不能为空")
        val segmentService = ApplicationContextHolder.getBean(PointSegmentService::class.java)

        val overdueDate = overdueDateTime.adjustToSHTimeEnd()

        if (overdueDate.shDate() < now.shDate()) return
        val isPending = effectiveDate.isAfter(now)
        modified = ZonedDateTime.now()

        if (isPending) {
            // 只更新最近生效时间和最近过期时间
            if (updateNow) {
                update()
            }
            return
        }

        if (p <= BigDecimal.ZERO) return

        // 是否已经存在本过期时间对应的积分块
        val segment = segmentService.findFirstDateAfter(pointPlanId!!, id!!, overdueDate.shDate())
        if (segment == null) {
            // 不存在，先初始化一个块
            val st = buildSegment(p, overdueDate.shDate())
            segmentService.save(st)
        } else {
            if (segment.point < BigDecimal.ZERO) {
                // 如果当前块（大于新增的这笔的最近的一笔，不是当前剩余可用积分块）的积分小于0，说明是负积分块，需要先把负积分块的积分加到当前块
                if (segment.point + p > BigDecimal.ZERO) {
                    // 如果加上本次积分后，积分块的积分大于0，则把负积分块删除，仅保留当前块
                    segmentService.deleteByMemberPointId(id!!, pointPlanId!!)
                    segmentService.save(buildSegment(segment.point + p, overdueDate.shDate()))
                } else if ((segment.point + p).compareTo(BigDecimal.ZERO) == 0) {
                    // 如果加上本次积分后，积分块的积分等于0，0分块不需要保留，直接清除全部数据
                    segmentService.deleteByMemberPointId(id!!, pointPlanId!!)
                } else {
                    // 如果加上本次积分后，积分块的积分小于0，更新负积分块，这个采用全部清空再重建的方式
                    segmentService.deleteByMemberPointId(id!!, pointPlanId!!)
                    segmentService.save(buildSegment(segment.point + p, LONG_TERM_OVERDUE_DATE.shDate()))
                }
            } else {
                // 如果当前块的积分大于0，说明是正积分块，直接更新当前块
                if (segment.expireDate!! == overdueDate.shDate()) {
                    // 如果当前块的过期时间等于新增的这笔的过期时间，则直接更新当前块
                    segment.point += p
                    segment.modified = ZonedDateTime.now()
                    segmentService.saveOrUpdate(segment, segment.pointPlanId.toString())
                } else {
                    // 不存在和新增的这笔积分块过期时间相同的积分块，直接新建
                    segmentService.save(buildSegment(segment.point + p, overdueDate.shDate()))
                }
            }
        }
        // 更新小于新增这笔过期时间之前的块 ----- 当前时间 <------| 新增这笔过期时间
        // 如果新增的这笔过期时间等于当前时间，则不需要更新
        if (overdueDate.shDate() != now.shDate()) {
            val from = now.shDate().minusDays(1)
            // 这里过期时间-1是因为上面已经新增了一笔过期时间等于overdueDate的块
            val to = overdueDate.shDate().minusDays(1)
            // 更新，-p：负负得正 (减去一个负数等于加上一个正数)
            segmentService.minusByExpireDateRange(pointPlanId!!, id!!, -p, from, to)
        }

        // 更新会员积分账户
        point += p

        if (updateNow) {
            // 保存总积分变更日志
            update()
            saveLog()
        }
    }


    // 减一笔积分
    // 返回扣减的过期时间是哪一天的实际积分值
    fun minus(p: BigDecimal, negativeStrategy: NegativeStrategyEnum, expireDate: LocalDate? = null, updateNow: Boolean = true): Pair<LocalDate, BigDecimal> {
        val now = ZonedDateTime.now().shDate()
        val forever = LONG_TERM_OVERDUE_DATE.shDate()
        if (negativeStrategy == NegativeStrategyEnum.NOT_ALLOWED || p.compareTo(BigDecimal.ZERO) == 0) return Pair(now, BigDecimal.ZERO)
        val segmentService = ApplicationContextHolder.getBean(PointSegmentService::class.java)

        val segments = segmentService.findDateAfter(pointPlanId!!, id!!, now)
        point = segments.firstOrNull()?.point ?: BigDecimal.ZERO

        var deductExpireDate = segments.firstOrNull()?.expireDate ?: now

        val afterPoint = point - p

        // 扣减之后剩余积分小于等于0
        if (afterPoint < BigDecimal.ZERO) {
            if (negativeStrategy == TO_ZERO) {
                return if (point <= BigDecimal.ZERO) Pair(now, BigDecimal.ZERO) else minus(point, TO_ZERO)
            }
            // 采用先删除再创建一个新的
            segmentService.deleteByMemberPointId(id!!, pointPlanId!!)
            val segment = buildSegment(point - p, forever)
            segmentService.save(segment)
            deductExpireDate = segment.expireDate!!
        } else if (afterPoint.compareTo(BigDecimal.ZERO) == 0) {
            // 扣减之后剩余积分等于0
            // 直接删除全部积分块
            segmentService.deleteByMemberPointId(id!!, pointPlanId!!)
            deductExpireDate = forever
        } else {
            if (expireDate == null) {
                val segment = segments.first()
                segment.point = afterPoint
                segment.modified = ZonedDateTime.now()
                segmentService.saveOrUpdate(segment, replacePattern = this.pointPlanId.toString())
            } else {
                for (segment in segments) {
                    if (!segment.expireDate!!.isAfter(expireDate)) {
                        segment.point = afterPoint
                        segment.modified = ZonedDateTime.now()
                        segmentService.saveOrUpdate(segment, replacePattern = this.pointPlanId.toString())
                    }
                }
                deductExpireDate = expireDate
            }
        }
        // 更新会员积分账户
        point = afterPoint
        modified = ZonedDateTime.now()

        if (updateNow) {
            // 保存总积分变更日志
            update()
            saveLog()
        }
        return Pair(deductExpireDate, p)
    }

}