package com.shuyun.loyalty.service.calculate.sendlimit

import com.shuyun.loyalty.service.datamodel.MemberPointSendLimitCalcMapper
import com.shuyun.loyalty.service.extension.getIfPresent
import com.shuyun.loyalty.service.model.PointSendLimitRule
import com.shuyun.loyalty.service.service.MemberPointSendLimitCalcService
import com.shuyun.loyalty.service.service.PointSendLimitRuleBaseService
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired

abstract class AbstractMemberPointSendLimitEventFilter: AbstractMemberPointSendLimitFilter(){

    private val logger = LogManager.getLogger(AbstractMemberPointSendLimitEventFilter::class.java)

    @Autowired
    private lateinit var memberPointSendLimitCalcService: MemberPointSendLimitCalcService

    @Autowired
    private lateinit var pointSendLimitRuleBaseService: PointSendLimitRuleBaseService

    /**
     * 事件公用准备数据
     */
    override fun before(dto: IMemberPointSendLimitFilter.SendLimitFilterDTO) {
        val pointSendLimitType = pointSendLimitType()
        val sendLimitParams = dto.sendLimitParams!!

        val sendLimitRuleList = ArrayList<PointSendLimitRule>()
        // 查询排除事件
        val excludeLimitRuleList = pointSendLimitRuleBaseService.findLimitRuleListExclude(sendLimitParams.pointAccountTypeId, pointSendLimitType)
        if (excludeLimitRuleList.isNotEmpty()) {
            // 排除中不存在的模型,然后进行过滤计算
            val ruleList = excludeLimitRuleList.filter { !it.nlmsIds!!.split(",").contains(dto.refId.toString()) }
            logger.debug("排除规则计算: {} ", { JsonUtils.toJson(ruleList) })
            sendLimitRuleList.addAll(ruleList)
        }
        // 查询包含事件
        val includeLimitRuleList = pointSendLimitRuleBaseService.findLimitRuleListInclude(sendLimitParams.pointAccountTypeId, pointSendLimitType,dto.refId!!)
        sendLimitRuleList.addAll(includeLimitRuleList)
        if (sendLimitRuleList.isEmpty()) {
            dto.isContinue = false
            return
        }
        var memberPointSendLimitCalc = memberPointSendLimitCalcService.findOneRefIdAndType(dto.refId!!,
            pointSendLimitType,sendLimitParams.memberId ,sendLimitParams.pointAccountTypeId.toString()).getIfPresent()

        if (memberPointSendLimitCalc == null) {
            memberPointSendLimitCalc = MemberPointSendLimitCalcMapper.build(pointSendLimitType,
                sendLimitParams.pointAccountTypeId,
                sendLimitParams.planId,
                sendLimitParams.memberId,
                dto.refId!!,
                dto.sendLimitParams!!.time
            )
        }
        //logger.debug("$pointSendLimitType 积分上限拦截before,已经累计值 : {} ", {JsonUtils.toJson(memberPointSendLimitCalc)})
        @Suppress("UNCHECKED_CAST")
        val valueMap = JsonUtils.parse(memberPointSendLimitCalc.value,MutableMap::class.java) as MutableMap<String, Number>
        memberPointSendLimitCalc.valueMap = valueMap
        dto.sendLimitRuleList = sendLimitRuleList
        dto.memberPointSendLimitCalc = memberPointSendLimitCalc
    }
}