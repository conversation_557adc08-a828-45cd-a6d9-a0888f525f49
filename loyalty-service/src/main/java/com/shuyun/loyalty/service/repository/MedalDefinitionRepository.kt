package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.model.MedalDefinition
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.util.converter.ZonedDateTime2StringConverter
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component
import java.time.ZonedDateTime
import java.util.*

@Component
class MedalDefinitionRepository: DataModelCarryRepository<MedalDefinition>() {

    private val log = LogManager.getLogger(MedalDefinitionRepository::class.java)
    override fun log(): Logger { return log }

    fun findByMedalHierarchyVersionId(medalHierarchyVersionId: Long): List<MedalDefinition> {
        val params = HashMap<String, Any?>()
        params["medalHierarchyVersionId"] = medalHierarchyVersionId
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findByMedalHierarchyVersionIdAndDisabled(medalHierarchyVersionId: Long, disabled: Boolean):
        List<MedalDefinition> {
        val params = HashMap<String, Any?>()
        params["medalHierarchyVersionId"] = medalHierarchyVersionId
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findByIdAndDate(id: Long, date: ZonedDateTime): Optional<MedalDefinition> {
        val sql = "select m.name, m.medalHierarchyVersionId, m.sort, m.id, m.versionId " +
            "from data.loyalty.manager.medalDefinition m " +
            "inner join data.loyalty.manager.medalHierarchy h on m.medalHierarchyVersionId = h.versionId " +
            "inner join data.loyalty.manager.subject s on h.subjectVersionId = s.versionId " +
            "inner join data.loyalty.manager.plan p on s.planVersionId = p.versionId " +
            "where m.disabled=0 and h.disabled=0 and s.disabled=0 and p.disabled=0 and m.id = :id and (p.status='PUBLISHED' OR p.status = 'FILED') " +
            "and h.status = 'PUBLISHED' and p.publishedTime<=:date and (p.filedTime>=:date or p.filedTime is null) order by h.subjectVersionId desc"
        val params = HashMap<String, Any>()
        params["id"] = id
        params["date"] = ZonedDateTime2StringConverter().convert(date)!!
        return executeOne(sql, params)
    }

    fun findByVersionId(versionId: Long): Optional<MedalDefinition> {
        return findOneByFilter(JsonUtils.toJson(mapOf(Pair("versionId", versionId))))
    }

}