package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.model.MedalRuleGroup
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface MedalRuleGroupRepository: JpaRepository<MedalRuleGroup, Long> {

    fun findByMedalHierarchyIdAndDisabledOrderByCreateTimeDesc(medalHierarchyId: Long, disabled: Boolean): List<MedalRuleGroup>

    fun findByMedalHierarchyIdAndNameAndDisabledOrderByCreateTimeDesc(medalHierarchyId: Long, name: String, disabled: Boolean): List<MedalRuleGroup>

    fun findByIdAndDisabled(id: Long, disabled: Boolean): Optional<MedalRuleGroup>

    fun findByMedalDefinitionIdAndDisabledOrderBySort(medalDefinitionId: Long, disabled: Boolean): List<MedalRuleGroup>

    fun findByMedalHierarchyId(medalHierarchyId: Long): List<MedalRuleGroup>

    fun findByMedalHierarchyIdAndDisabled(medalHierarchyId: Long, disabled: Boolean): List<MedalRuleGroup>

}