package com.shuyun.loyalty.service.calculate

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.bo.SendLimitRuleBO
import com.shuyun.loyalty.entity.bo.SendLimitRuleCalcResult
import com.shuyun.loyalty.service.calculate.params.SendLimitParams
import com.shuyun.loyalty.service.calculate.sendlimit.IMemberPointSendLimitFilter
import com.shuyun.loyalty.service.calculate.sendlimit.MemberPointSendLimitEventFilter
import com.shuyun.loyalty.service.calculate.sendlimit.MemberPointSendLimitGroupFilter
import com.shuyun.loyalty.service.datamodel.MemberPointRecord
import com.shuyun.loyalty.service.datamodel.MemberPointSendLimitCalcMapper
import com.shuyun.loyalty.service.meta.PointSendLimitType
import com.shuyun.loyalty.service.model.PointAccountType
import com.shuyun.loyalty.service.service.MemberPointSendLimitCalcService
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.SendLimitUtils
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import org.apache.commons.lang.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.time.ZonedDateTime

/**
 * 积分发放上限计算
 */
@Component
class MemberPointSendLimitService {


    @Autowired
    private lateinit var memberPointSendLimitCalcService: MemberPointSendLimitCalcService


    /**
     * 时机、规则组、会员 filter chain
     */
    fun filter(
        memberPointRecord: MemberPointRecord, pointAccountType: PointAccountType,
        eventTypeId: Long? = null, groupId: Long? = null,
        isRecalculate: Boolean = false,
        isReissue: Boolean = false,
        enableSendLimit: Boolean = false,
        onlyFilterCount: Boolean = false
    ): SendLimitRuleCalcResult? {
        val sendLimitFilterDTO = IMemberPointSendLimitFilter.SendLimitFilterDTO()
        sendLimitFilterDTO.sendLimitParams = SendLimitParams(
            memberPointRecord.planId!!,
            memberPointRecord.pointPlanId!!,
            memberPointRecord.memberId
        ).apply {
            this.open = enableSendLimit
        }
        if (!sendLimitFilterDTO.sendLimitParams!!.open) {
            return null
        }
        sendLimitFilterDTO.eventTypeId = eventTypeId
        sendLimitFilterDTO.groupId = groupId
        sendLimitFilterDTO.pointValue = memberPointRecord.point
        sendLimitFilterDTO.sendLimitRule = pointAccountType.sendLimitRule
        sendLimitFilterDTO.memberPointId = memberPointRecord.memberPointId!!
        sendLimitFilterDTO.isRecalculate = isRecalculate
        sendLimitFilterDTO.isReissue = isReissue
        sendLimitFilterDTO.onlyFilterCount = onlyFilterCount
        sendLimitFilterDTO.sendLimitRuleCalcResult = SendLimitRuleCalcResult().apply {
            this.now = sendLimitFilterDTO.sendLimitParams!!.time
            this.point = memberPointRecord.point
            this.open = true
        }

        // 时机、规则组
        ApplicationContextHolder.getBean(MemberPointSendLimitEventFilter::class.java).filter(sendLimitFilterDTO)
        ApplicationContextHolder.getBean(MemberPointSendLimitGroupFilter::class.java).filter(sendLimitFilterDTO)

        if (sendLimitFilterDTO.sendLimitRuleCalcResult.result) {
            sendLimitFilterDTO.sendLimitRuleCalcResult.realPoint = sendLimitFilterDTO.pointValue!!
            memberPointRecord.point = sendLimitFilterDTO.pointValue!!
        }
        memberPointRecord.sendLimitRuleCalcResult = sendLimitFilterDTO.sendLimitRuleCalcResult
        return memberPointRecord.sendLimitRuleCalcResult!!
    }

    /**
     * 积分上限计算完毕需要保存预算数据和已经累计数据
     */
    fun cumulative(memberPointRecord: MemberPointRecord, eventTypeId: Long?, groupId: Long?,isRecalculate: Boolean = false) {
        // 计算累计上限值,并保存
        if (memberPointRecord.recordType == FSMPointEvent.REVERSE_SEND) {
            return
        }
        val list = ArrayList<String>()
        eventTypeId?.let { list.add(eventTypeId.toString()+ ConstantValue.HORIZON +PointSendLimitType.EVENT) }
        groupId?.let { list.add(groupId.toString()+ ConstantValue.HORIZON + PointSendLimitType.RULE_GROUP) }
        this.cumulative(memberPointRecord, list, isRecalculate)
    }

    /**
     * 查询并更新已经累计的值
     */
    private fun cumulative(memberPointRecord: MemberPointRecord, refIds: List<String>,isRecalculate: Boolean = false) {
        val now = ZonedDateTime.now()
        val pointAccountTypeId =  memberPointRecord.pointPlanId!!
        val memberPointSendLimitCalcList = memberPointSendLimitCalcService.findListRefIdType(refIds, memberPointRecord.memberId, pointAccountTypeId.toString()).toMutableList()
        val refIdTypeDB = memberPointSendLimitCalcList.map { it.refIdType }
        val notExistRefIdTypes = refIds - refIdTypeDB.toSet()
        // 循环处理不存在一级维度累计记录
        notExistRefIdTypes.forEach { refIdType ->
            if (StringUtils.isBlank(refIdType)) {
                return@forEach
            }
            val ary = refIdType!!.split(ConstantValue.HORIZON)
            val pointSendLimitType = PointSendLimitType.entries.first { it.name == ary[1] }
            val memberPointSendLimitCalc = MemberPointSendLimitCalcMapper.build(
                pointSendLimitType, pointAccountTypeId,
                memberPointRecord.planId!!,
                memberPointRecord.memberId,
                ary[0],
                now
            )
            memberPointSendLimitCalcList.add(memberPointSendLimitCalc)
        }

        memberPointSendLimitCalcList.forEach { memberPointSendLimitCalc ->
            // 一次订单,相同时机、相同规则组 只累计一次
            val flag = SendLimitUtils.isHaveAndAdd(memberPointSendLimitCalc.type!!, memberPointSendLimitCalc.refId!!)
            //清除并计算所有维度
            @Suppress("UNCHECKED_CAST")
            memberPointSendLimitCalc.valueMap = JsonUtils.parse(memberPointSendLimitCalc.value, MutableMap::class.java) as MutableMap<String, Number>
            MemberPointSendLimitCalcMapper.updateValueOfCycle(memberPointSendLimitCalc, now!!)

            memberPointSendLimitCalc.valueMap!!.forEach mpslc@ { entry ->
                if (entry.key.endsWith(SendLimitRuleBO.SendLimitRuleType.POINT.name)) {
                    if (memberPointRecord.recordType == FSMPointEvent.DELAY_SEND) {
                        return@mpslc
                    }
                    memberPointSendLimitCalc.valueMap!![entry.key] = entry.value.toString().toBigDecimal().plus(memberPointRecord.point)
                } else {
                    if (isRecalculate) return@mpslc
                    if (flag) {
                        // 相同时机或相同规则组 只累计1次
                        memberPointSendLimitCalc.valueMap!![entry.key] = entry.value.toLong().plus(1)
                    }
                }
            }
            memberPointSendLimitCalc.value = JsonUtils.toJson(memberPointSendLimitCalc.valueMap)
            memberPointSendLimitCalc.modified = now
            memberPointSendLimitCalcService.saveOrUpdate(memberPointSendLimitCalc, memberPointSendLimitCalc.pointAccountId!!.toString())
        }
    }
}