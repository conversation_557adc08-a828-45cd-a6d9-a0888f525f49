package com.shuyun.loyalty.service.fx


import com.github.benmanes.caffeine.cache.Caffeine
import com.shuyun.fx.factory.DslEpp
import com.shuyun.fx.meta.DataTypeValue
import com.shuyun.fx.property.FxVariable
import com.shuyun.loyalty.service.model.Expression
import com.shuyun.loyalty.service.repository.ExpressionRepository
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.Duration
import java.util.concurrent.TimeUnit
import kotlin.jvm.optionals.getOrNull

/**
 * 扩展fx增改查
 */
@Component
class ExtendFxVariableProvider(private val expressionRepository: ExpressionRepository) : IExtendFxVariableProvider {

    private val cache = Caffeine.newBuilder()
        .refreshAfterWrite(Duration.ofMinutes(5))
        .maximumSize(50000)
        .expireAfterWrite(30, TimeUnit.DAYS)
        .build<Long, FxVariable> { findByFxId(it) }

    @Transactional
    override fun create(expr: String): Long {
        val expression = Expression()
        expression.dsl = expr
        validationAndFillValue(expression)
        expressionRepository.save(expression)
        return expression.id!!
    }


    @Transactional
    override fun update(id: Long, expr: String) {
        val expression = getOne(id)
        expression.dsl = expr
        validationAndFillValue(expression)
    }


    private fun findByFxId(id: Long): FxVariable? {
        val expression = expressionRepository.findById(id).getOrNull()
        return if (expression == null) {
            null
        } else {
            val fxVariable = FxVariable().apply {
                this.attrType = expression.attrType
                this.dataType = expression.dataType
                this.dsl = expression.dsl
                this.isArray = expression.isArray
                this.defaultValue = expression.defaultValue
            }
            fxVariable
        }
    }


    override fun findById(id: Long): FxVariable {
        val fxVariable = cache.get(id)
        return fxVariable ?: throw IllegalArgumentException("表达式不存在,id:${id}")
    }

    private fun getOne(id: Long): Expression {
        return expressionRepository.findById(id)
            .orElseGet { throw IllegalArgumentException("表达式不存在,id:${id}") }
    }

    /**
     * 验证表达式
     */
    private fun validationAndFillValue(expression: Expression) {
        val attrType = DslEpp.analyseAttrType(expression.dsl)
        expression.attrType = attrType

        val result = DslEpp.validation(expression.dsl)
        expression.dataType = result
        if (result == DataTypeValue.Array) expression.isArray = true
    }
}