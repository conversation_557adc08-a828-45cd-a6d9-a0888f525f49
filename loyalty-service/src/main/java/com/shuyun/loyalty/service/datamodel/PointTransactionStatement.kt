package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.service.PointTransactionStatementBaseService
import com.shuyun.pip.ApplicationContextHolder
import java.math.BigDecimal
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.Table

/**
 * 积分事物正向明细表
 */
@DataServiceModel
@FqnVariableModel
@Table(name = "data.loyalty.member.account.PointTransactionStatement{*}")
open class PointTransactionStatement :BaseDataModel() {
    @Column
    var gainStatementId: String? = null
    @Column
    var point: BigDecimal = BigDecimal.ZERO
    @Column
    var effectiveDate: ZonedDateTime? = null
    @Column
    var overdueDate: ZonedDateTime? = null
    @Column
    var pointTransactionId: String? = null
    @Column
    var sort: Int? = null
    @Column
    var created: ZonedDateTime? = null
    @Column
    var modified: ZonedDateTime? = null
    /**店铺ID*/
    @Column
    var shopId: String? = null
    /**反向操作记账使用*/
    @Column
    var backId: String? = null
    /**
     * 预留字段1
     */
    var ext1: String? = null
    /**
     * 预留字段2
     */
    var ext2: String? = null
    /**
     * 预留字段3
     */
    var ext3: String? = null

    fun saveOrUpdate(pointPlanId: String):PointTransactionStatement {
        return ApplicationContextHolder.getBean(PointTransactionStatementBaseService::class.java).saveOrUpdate(this,pointPlanId)
    }

    fun save(pointPlanId: String):PointTransactionStatement {
        return ApplicationContextHolder.getBean(PointTransactionStatementBaseService::class.java).save(this,pointPlanId)
    }
}