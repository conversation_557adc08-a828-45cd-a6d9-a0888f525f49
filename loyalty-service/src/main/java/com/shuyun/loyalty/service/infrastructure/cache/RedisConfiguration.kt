package com.shuyun.loyalty.service.infrastructure.cache

import com.shuyun.pip.component.json.JsonUtils
import org.springframework.boot.autoconfigure.AutoConfigureBefore
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
import org.springframework.cache.annotation.EnableCaching
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.data.redis.connection.RedisConnectionFactory
import org.springframework.data.redis.core.*
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer
import org.springframework.data.redis.serializer.StringRedisSerializer

/**
 * Redis配置
 *
 */
@EnableCaching
@Configuration
@AutoConfigureBefore(RedisAutoConfiguration::class)
class RedisConfiguration {

    @Bean
    fun redisTemplate(redisConnectionFactory: RedisConnectionFactory): RedisTemplate<String, Any?> {
        val jackson2JsonRedisSerializer = Jackson2JsonRedisSerializer(Any::class.java)
        jackson2JsonRedisSerializer.setObjectMapper(JsonUtils.objectMapper())

        val redisTemplate = RedisTemplate<String, Any?>()
        redisTemplate.keySerializer = StringRedisSerializer()
        redisTemplate.hashKeySerializer = StringRedisSerializer()
        redisTemplate.valueSerializer = jackson2JsonRedisSerializer
        redisTemplate.hashValueSerializer = jackson2JsonRedisSerializer
        // 默认使用jackson
        redisTemplate.setDefaultSerializer(jackson2JsonRedisSerializer)
        redisTemplate.setConnectionFactory(redisConnectionFactory)
        redisTemplate.afterPropertiesSet()
        return redisTemplate
    }

    @Bean
    fun hashOperations(redisTemplate: RedisTemplate<String, Any?>): HashOperations<String, String, Any?> {
        return redisTemplate.opsForHash()
    }

    @Bean
    fun valueOperations(redisTemplate: RedisTemplate<String, String?>): ValueOperations<String, String?> {
        return redisTemplate.opsForValue()
    }

    @Bean
    fun listOperations(redisTemplate: RedisTemplate<String, Any?>): ListOperations<String, Any?> {
        return redisTemplate.opsForList()
    }

    @Bean
    fun setOperations(redisTemplate: RedisTemplate<String, Any?>): SetOperations<String, Any?> {
        return redisTemplate.opsForSet()
    }

    @Bean
    fun zSetOperations(redisTemplate: RedisTemplate<String, Any?>): ZSetOperations<String, Any?> {
        return redisTemplate.opsForZSet()
    }
}