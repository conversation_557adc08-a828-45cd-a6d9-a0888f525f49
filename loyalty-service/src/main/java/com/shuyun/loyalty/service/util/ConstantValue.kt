package com.shuyun.loyalty.service.util

import java.math.BigDecimal
import java.time.ZonedDateTime


/**
 * 部分通用常量定义
 */
object ConstantValue {
    const val DEFAULT_CHANNEL_TYPE = "loyalty"
    /** 默认系统操作人 */
    const val DEFAULT_OPERATOR = "System"
    val defaultZeroLine = BigDecimal("0.000001")
    const val RULE_EVENT_FQN_MATCH = "event.*"
    // 未删除
    const val LOGIC_DELETE_NOT = false

    const val HORIZON = "|"

    val LONG_TERM_OVERDUE_DATE: ZonedDateTime = ZonedDateTime.parse("3000-12-12T00:00:00.000Z")

    const val ES_SOURCE = "event-service"

    // 用于积分迁移
    const val MIGRATION_MAGIC_MEMBER_ID_PREFIX = "MMMID-"
    const val TENANT_ID = "tenantId"
}

