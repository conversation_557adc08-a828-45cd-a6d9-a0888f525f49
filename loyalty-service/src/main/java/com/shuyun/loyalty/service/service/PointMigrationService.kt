package com.shuyun.loyalty.service.service

import com.github.f4b6a3.tsid.Tsid
import com.pip.mybatisplus.pools.DmPoolFactory
import com.pip.mybatisplus.toolkit.DataapiHttpFactory
import com.shuyun.dm.api.vo.FetchStartRequest
import com.shuyun.loyalty.entity.api.request.MemberPointImportRequest
import com.shuyun.loyalty.entity.dto.MemberPointMessage
import com.shuyun.loyalty.service.extension.utcStr
import com.shuyun.loyalty.service.kafka.IPointSyncProducer
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.ConstantValue.MIGRATION_MAGIC_MEMBER_ID_PREFIX
import com.shuyun.loyalty.service.util.MDCUtils
import com.shuyun.loyalty.service.util.SQL
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import org.apache.commons.lang3.RandomStringUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.ZonedDateTime
import java.util.*
import kotlin.concurrent.thread

@Service
class PointMigrationService {

    private val log = LogManager.getLogger(PointMigrationService::class.java)

    companion object {
        private const val DATE_TIME_PADDING = 10_000_000_000_000 //补齐位数
        private const val LOCK_PREFIX = "LOYALTY_MIGRATION"
        private const val MIGRATION_PARTITION_NUM = 30
    }

    @Autowired
    private lateinit var locker: Locker


    fun batchInsertRecord(
        requests: List<MemberPointImportRequest>,
        migrationId: String,
        planId: Long,
        hierarchyId: Long,
    ) {
        val recordSql = SQL.build {
            var sql = """
                INSERT INTO data.loyalty.migration.transfer.log 
                (`id`,`migrationId`,`planId`,`pointId`,`memberId`,`action`,`point`,`effectiveDate`,`overdueDate`,`description`,`channelType`,`shopId`,`kzzd1`,`kzzd2`,`kzzd3`,`key`,`overrideHistory`,`createdDate`)
                VALUES 
            """.trimIndent()
            for (r in requests) {
                val effectiveDate = if (r.action == MemberPointImportRequest.Type.SEND ) (r.effectiveDate ?: r.createdDate) else null
                val overdueDate = if (r.action == MemberPointImportRequest.Type.SEND ) (r.overdueDate ?: ConstantValue.LONG_TERM_OVERDUE_DATE) else null
                val id = r.myId ?: (DATE_TIME_PADDING + r.createdDate!!.toInstant().toEpochMilli()).toString()
                    .plus(Tsid.fast().toLowerCase())
                sql += """
                    (
                    ${id.escaped()},
                    ${migrationId.escaped()},
                    ${planId},
                    ${hierarchyId},
                    ${r.memberId.escaped()},
                    ${r.action!!.name.escaped()},
                    ${r.point},
                    ${effectiveDate.escaped()},
                    ${overdueDate.escaped()},
                    ${r.description.escaped()},
                    ${r.channelType.escaped()},
                    ${r.shopId.escaped()},
                    ${r.KZZD1.escaped()},
                    ${r.KZZD2.escaped()},
                    ${r.KZZD3.escaped()},
                    ${Tsid.fast().toLowerCase().escaped()},
                    ${r.overrideHistory ?: false},
                    ${r.createdDate.escaped()}
                    ),
                """.trimIndent()
            }
            sql.dropLast(1)
        }
        DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { it.execute(recordSql, mapOf()) }
    }


    fun updateMigMemberStatusByIds(ids: List<String>, status: String, error: String? = null) {
        if (ids.isEmpty()) return
        val sql = """
            UPDATE data.loyalty.migration.transfer.member 
            SET status = :status, error = :error, updateDate = :updateDate
            WHERE id in (:ids)
        """.trimIndent()
        val params =
            mapOf("ids" to ids, "status" to status, "error" to error, "updateDate" to ZonedDateTime.now().utcStr())
        DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { it.execute(sql, params) }
    }


    data class MigrationMember(
        val id: String,
        val migrationId: String,
        val planId: Long,
        val pointId: Long,
        val memberId: String,
        val partitionNum: Int,
        val status: String,
        val createdDate: ZonedDateTime,
        val updateDate: ZonedDateTime
    )


    fun findMigMemberById(id: String): MigrationMember? {
        val sql = """
            SELECT `id`,`migrationId`,`planId`,`pointId`,`memberId`,`partitionNum`,`status`,`createdDate`,`updateDate` 
            FROM data.loyalty.migration.transfer.member 
            WHERE id = :id
        """.trimIndent()
        val params = mapOf("id" to id)
        val migrationMembers = DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
            val response = sdk.execute(sql, params)
            response.data.map {
                val raw = it as Map<*, *>
                MigrationMember(
                    raw["id"] as String,
                    raw["migrationId"] as String,
                    (raw["planId"] as Number).toLong(),
                    (raw["pointId"] as Number).toLong(),
                    raw["memberId"] as String,
                    (raw["partitionNum"] as Number).toInt(),
                    raw["status"] as String,
                    ZonedDateTime.parse(raw["createdDate"] as String),
                    ZonedDateTime.parse(raw["updateDate"] as String)
                )
            }
        } ?: emptyList()
        return migrationMembers.firstOrNull()
    }


    fun delete(mm: MigrationMember) {
        val sql1 = "delete from data.loyalty.migration.transfer.member where id = '${mm.id}'"
        val sql2 = "delete from data.loyalty.migration.transfer.log where migrationId = '${mm.migrationId}' AND pointId = ${mm.pointId} AND memberId = '${mm.memberId}'"
        DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { it.execute(sql1, mapOf()) }
        DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { it.execute(sql2, mapOf()) }
    }



    data class MigrationRecord(
        val id: String,
        val migrationId: String,
        val planId: Long,
        val pointId: Long,
        val memberId: String,
        val action: String,
        val point: BigDecimal,
        val effectiveDate: ZonedDateTime?,
        val overdueDate: ZonedDateTime?,
        val description: String?,
        val channelType: String?,
        val shopId: String?,
        val kzzd1: String?,
        val kzzd2: String?,
        val kzzd3: String?,
        val key: String,
        val overrideHistory: Boolean,
        val createdDate: ZonedDateTime,
    )


    fun findMigrationRecordList(
        migrationId: String,
        hierarchyId: Long,
        memberId: String
    ): LinkedList<MigrationRecord> {
        val sql = """
            SELECT 
                `id`,`migrationId`,`planId`,`pointId`,`memberId`, `action`,`point`,`effectiveDate`,`overdueDate`,
                `description`,`channelType`,`shopId`, `kzzd1`,`kzzd2`,`kzzd3`,`key`,`overrideHistory`,`createdDate`
            FROM data.loyalty.migration.transfer.log 
            WHERE migrationId = :migrationId AND pointId = :pointId AND memberId = :memberId
            ORDER BY id ASC
        """.trimIndent()
        val params = mapOf(
            "migrationId" to migrationId,
            "pointId" to hierarchyId,
            "memberId" to memberId
        )
        val records = LinkedList<MigrationRecord>()
        fetch(sql, params) {
            for (raw in it) {
                records.add(
                    MigrationRecord(
                        raw["id"] as String,
                        raw["migrationId"] as String,
                        (raw["planId"] as Number).toLong(),
                        (raw["pointId"] as Number).toLong(),
                        raw["memberId"] as String,
                        raw["action"] as String,
                        raw["point"].toString().toBigDecimal(),
                        raw["effectiveDate"]?.let { ZonedDateTime.parse(it.toString()) },
                        raw["overdueDate"]?.let { ZonedDateTime.parse(it.toString()) },
                        raw["description"] as String?,
                        raw["channelType"] as String?,
                        raw["shopId"] as String?,
                        raw["kzzd1"] as String?,
                        raw["kzzd2"] as String?,
                        raw["kzzd3"] as String?,
                        raw["key"] as String,
                        raw["overrideHistory"] as Boolean,
                        ZonedDateTime.parse(raw["createdDate"] as String)
                    )
                )
            }
        }
        return records
    }


    fun migrate(hierarchyId: Long, migrationId: String) {
        for (i in 0..<MIGRATION_PARTITION_NUM) {
            thread {
                val lockKey = "$LOCK_PREFIX:${hierarchyId}_${migrationId}_${i}"
                val lock = locker.getLock(lockKey)
                if (!lock.tryLock()) {
                    return@thread
                }
                try {
                    val producer = ApplicationContextHolder.getBean(IPointSyncProducer::class.java)
                    val sql = "SELECT `id`, `pointId`, `memberId` FROM data.loyalty.migration.transfer.member WHERE migrationId = $migrationId and partitionNum = $i and status = 'NEW'"
                    fetch(sql, mapOf()) { maps ->
                        val ids = HashSet<String>()
                        for (map in maps) {
                            MDCUtils.getAndSetTraceId(RandomStringUtils.randomAlphanumeric(8))
                            try {
                                val id = map["id"] as String
                                val memberId = map["memberId"] as String
                                val pointId = map["pointId"].toString().toLong()
                                if (pointId != hierarchyId) {
                                    continue
                                }
                                ids.add(id)
                                val mmmid = MIGRATION_MAGIC_MEMBER_ID_PREFIX + id
                                val event = MemberPointMessage(pointId, memberId, mmmid)
                                producer.send(event)
                                log.info(
                                    "发送迁移会员消息 migrationId: {} memberId: {}",
                                    migrationId,
                                    memberId
                                )
                            } finally {
                                MDCUtils.clear()
                            }
                        }
                        updateMigMemberStatusByIds(ids.toList(), "IN_PROGRESS")
                    }
                } finally {
                    lock.unlock()
                }
            }
        }
    }


//    fun getResults(pointId: Long, migrationId: String): HashMap<String, Any> {
//        val result = LinkedHashMap<String, Any>()
//        val sql = """
//            SELECT count(id) as cnt
//            FROM data.loyalty.migration.transfer.member
//            WHERE migrationId = $migrationId
//        """.trimIndent()
//        val response = DmPoolFactory.execute { sdk ->
//            val params = HashMap<String, Any>()
//            sdk.execute(sql, params)
//        }!!
//
//        if (response.isSuccess && response.data.isNotEmpty()) {
//            @Suppress("UNCHECKED_CAST")
//            val row = response.data.first() as Map<String, Any?>
//            val cnt = (row["cnt"] as? Number)?.toLong() ?: 0L
//            if (cnt == 0L) {
//                result["progress"] = "已完成"
//            } else {
//                result["progress"] = "剩余：${cnt}个会员未完成"
//            }
//        }
//        return result
//    }


    private fun fetch(sql: String, param: Map<String, Any?>, callback: (List<Map<String, Any?>>) -> Unit) {
        val request = FetchStartRequest(sql,param, 1000)
        DataapiHttpFactory.httpSdk().fetch(request).use { fetchData ->
            var data: List<Map<String, Any>>
            do {
                data = fetchData.next().data
                if (data.isEmpty()) {
                    break
                }
                try {
                    callback(data)
                } catch (e: Exception) {
                    log.error("拉取数据异常: {}", JsonUtils.toJson(data), e)
                }
            } while (data.isNotEmpty())
        }
    }
}


