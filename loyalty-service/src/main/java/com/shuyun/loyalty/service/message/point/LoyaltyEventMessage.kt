package com.shuyun.loyalty.service.message.point

import com.shuyun.fx.schema.JsonFinder
import com.shuyun.lite.util.Common
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.event.findEventValueByPath
import com.shuyun.loyalty.service.exception.LPException
import com.shuyun.loyalty.service.meta.DelayedEventStrategy
import com.shuyun.loyalty.service.meta.EventOccasionEnum
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.loyalty.service.model.Plan
import java.time.Duration
import java.time.ZonedDateTime
import java.util.*

class LoyaltyEventMessage(val plan: Plan, val event: Event, val date: ZonedDateTime?) {

    companion object {
        const val PARTITION_KEY = "partitionKey"
    }

    private data class BasicMessage(val planId: Long, val subjectId: Long, val eventTypeId: Long, val memberId: String, val expired: Boolean)

    private val basicMessages: List<BasicMessage> by lazy {
        val subjects = plan.subjectList
        if (subjects.isNullOrEmpty()) throw LPException("未找到事件(${event.getFqn()})关联的主体")
        val basicMessages = ArrayList<BasicMessage>()
        for (subject in subjects) {
            val eventType = subject.eventTypeList?.find { it.eventStream == event.getFqn() } ?: throw RuntimeException("事件消息无对应的时机(${event.getFqn()})")
            var expired = false
            if (eventType.delayedEventStrategy == DelayedEventStrategy.DISCARD_AFTER) {
                val delayedEventPath = eventType.delayedEventPath ?: Event.OCCURRENCE_TS
                val delayedEventValue = event.findEventValueByPath(delayedEventPath)
                if (delayedEventValue.isNullOrEmpty()) {
                    throw LPException("事件消息中缺少判定延迟消息的属性值(${delayedEventPath})")
                }
                val deadline = messageDeadline(delayedEventPath, delayedEventValue, event)
                    ?: throw LPException("事件消息中存在格式不正确的属性(path: $delayedEventPath value: ${delayedEventValue})")
                val diff = Date.from((date ?: ZonedDateTime.now()).toInstant()).time - deadline.time
                expired = diff > Duration.parse(eventType.discardDelayedEventAfter!!).toMillis()
            }
            val memberId = JsonFinder.find(
                event.asObjectNode(),
                eventType.eventStreamMetas!!.fetchPathByDsFqn(subject.dataType!!, event)
            ).asText()
            if (memberId.isNullOrBlank()) {
                throw LPException("事件消息中缺少主体会员ID属性")
            }
            basicMessages.add(BasicMessage(plan.id!!, subject.id!!, eventType.id!!, memberId, expired))
        }
        basicMessages
    }


    fun messagePartitionKey(): String {
        return basicMessages.joinToString(separator = "|") { it.memberId }
    }


    fun isExpiredMessage(planId: Long, subjectId: Long, eventTypeId: Long): Boolean {
        return basicMessages.any {
            it.planId == planId && it.subjectId == subjectId && it.eventTypeId == eventTypeId && it.expired
        }
    }

    private fun isOnlyPointMessage(): Boolean { return event.get(Event.RETURN_ORDER_EVENT_TYPE) != null }

    fun isPointMessage(eventType: EventType): Boolean {
        return eventType.occasion?.contains(EventOccasionEnum.CALC_POINT) == true
                && !Common.getBoolean("loyalty.point.calculate.disabled")
    }

    fun isGradeMessage(eventType: EventType): Boolean {
        return (eventType.occasion?.contains(EventOccasionEnum.CALC_GRADE) == true || eventType.operationGrade != null)
                && !Common.getBoolean("loyalty.grade.calculate.disabled") && !isOnlyPointMessage()
    }

    fun isMedalMessage(eventType: EventType): Boolean {
        return (eventType.occasion?.contains(EventOccasionEnum.CALC_MEDAL) == true || eventType.operationMedal != null)
                && !Common.getBoolean("loyalty.medal.calculate.disabled") && !isOnlyPointMessage()
    }


    private fun messageDeadline(matchingTimePath: String, matchingTimeValue: String, event: Event): Date? {
        return when (matchingTimePath) {
            Event.OCCURRENCE_TS -> Date(event.getOccurrenceTs())
            Event.DETECTION_TS -> Date(event.getDetectionTs())
            else -> {
                matchingTimeValue.runCatching {
                    val zonedDateTime = ZonedDateTime.parse(this)
                    Date.from(zonedDateTime.toInstant())
                }.getOrNull()
            }
        }
    }
}