package com.shuyun.loyalty.service.fx.function

import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.dm.api.dataapi.request.QueryDataRequest
import com.shuyun.fx.exception.FxExceptionConstants
import com.shuyun.fx.exception.IllegalArgumentGrammarFxException
import com.shuyun.fx.function.AbstractFxFunction
import com.shuyun.fx.meta.DataTypeValue
import com.shuyun.fx.model.Literal
import com.shuyun.loyalty.entity.dto.MemberRequest
import com.shuyun.loyalty.service.service.MemberPointService
import com.shuyun.loyalty.service.service.PlanBaseService
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.pip.component.json.JsonUtils
import org.apache.commons.lang3.StringUtils
import org.apache.logging.log4j.LogManager
import org.stringtemplate.v4.ST
import org.stringtemplate.v4.STGroupFile
import java.math.BigDecimal
import java.time.ZonedDateTime

class CurrentMemberPointFunction : AbstractFxFunction() {
    companion object {
        const val FUN_NAME = "CURRENT_MEMBER_POINT"
        private val logger = LogManager.getLogger(CurrentMemberPointFunction::class.java)
    }

    override fun name() = FUN_NAME

    override fun transSt(stg: STGroupFile, params: List<ST>, vararg other: Any): ST {
        return params.first()
    }

    override fun validationType(params: List<DataTypeValue>): DataTypeValue {
        if (params.size != 1) {
            throw IllegalArgumentGrammarFxException(FxExceptionConstants.FX_VALIDATION_REQUIRED_ONE_PARAMS)
        }
        return DataTypeValue.Number
    }

    override fun validation(params: List<Literal>) {
    }

    override fun eval(params: List<Literal>): Literal {
        val memberId = params.first().toStrNotNull()
        val accountTypeId = params[1].toInteger()
        val zonedDateTime = params[2].toZonedDateTime()
        if (CurrentFuncTLK.getCurrentPoints(accountTypeId) != null) {
            logger.debug("从线程变量中获取当前积分 accountTypeId: {} memberId: {} 结果: {}", accountTypeId, memberId, CurrentFuncTLK.getCurrentPoints(accountTypeId))
            return Literal(CurrentFuncTLK.getCurrentPoints(accountTypeId), DataTypeValue.Number)
        }
        val flag = ApplicationContextHolder.getBean(PlanBaseService::class.java).getFxReferenceTime(accountTypeId, FUN_NAME)
        logger.debug("查询当前积分参考时间 accountTypeId:${accountTypeId} 结果: ${if (flag) "当前时间" else "快照时间"}")
        return if (flag) {
            val p = lastedPoint(memberId, accountTypeId)
            logger.debug("LastedPoint 结果: {}", p.data?.toString())
            p
        } else {
            val p = versionedPoint(memberId, accountTypeId, zonedDateTime)
            logger.debug("VersionedPoint 结果: {}", p.data?.toString())
            p
        }
    }

    private fun versionedPoint(memberId: String, accountTypeId: Long, zonedDateTime: ZonedDateTime): Literal {
        val filterList = arrayListOf<String>()
        val accountTypeFilter = "{\"pointPlanId\": $accountTypeId }"
        filterList.add(accountTypeFilter)
        filterList.add("{\"memberId\": \"$memberId\" }")
        filterList.add("{\"created\": {\"\$le\": " + JsonUtils.toJson(zonedDateTime) + "} }")//所有生效时间小于等于指定时间的记录，生效时间最大的

        val filter = "{\"\$and\":[" + StringUtils.join(filterList.toArray(), ",") + "]}"
        val sort = "{\"created\": \"desc\"}"

        val queryDataRequest = QueryDataRequest()
        queryDataRequest.fqn = "data.loyalty.member.account.point.Log$accountTypeId"
        queryDataRequest.filter = filter
        queryDataRequest.fields = "point"
        queryDataRequest.offset = 0
        queryDataRequest.limit = 1
        queryDataRequest.sort = sort
        logger.debug("query current point:{}", { JsonUtils.toJson(queryDataRequest) })

        val response = DmPoolFactory.execute { sdk -> sdk.queryObjects(queryDataRequest) }!!
        val literal = if (response.data.isEmpty()) 0.0 else response.data[0]["point"].toString().toBigDecimal()
        return Literal(literal, DataTypeValue.Number)
    }


    private fun lastedPoint(memberId: String, accountTypeId: Long): Literal {
        val locker = ApplicationContextHolder.getBean(Locker::class.java)
        val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT, accountTypeId, memberId))
        try {
            lock.lock()
            val mp = ApplicationContextHolder.getBean(MemberPointService::class.java).getByMemberId(accountTypeId, memberId)
            return Literal((mp?.point ?: BigDecimal.ZERO), DataTypeValue.Number)
        } finally {
            lock.unlock()
        }
    }
}
