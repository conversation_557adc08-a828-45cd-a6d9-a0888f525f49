package com.shuyun.loyalty.service.transfer.points

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.service.datamodel.MemberPointRecord
import com.shuyun.loyalty.service.datamodel.PointRecordItem
import com.shuyun.loyalty.service.datamodel.PointTransactionCalc
import com.shuyun.loyalty.service.datamodel.PointTransactionStatement
import com.shuyun.loyalty.service.service.PointTransactionCalcBaseService
import com.shuyun.loyalty.service.service.PointTransactionStatementBaseService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import kotlin.jvm.optionals.getOrNull

// 消耗冻结的积分
@Service
class MemberPointUseFrozenTransfer: MemberPointBaseTransfer() {

    @Autowired
    private lateinit var ptcsService: PointTransactionCalcBaseService

    @Autowired
    private lateinit var ptssService: PointTransactionStatementBaseService


    fun transfer(lp: LoyaltyPoints) {

        // 只能使用被接口冻结的积分
        val vss = validService.findByOpenTraceId(
            lp.hierarchy.id,
            lp.member.id,
            lp.attr.traceId,
            listOf(PointStateEnum.OPEN_FROZE)
        )

        if (vss.isEmpty()) {
            return
        }

        val records = ArrayList<MemberPointRecord>()

        // 接口冻结
        val unfrozenRecord = initPointRecord(
            lp, Uuid.uuid, FSMPointEvent.OPEN_UNFREEZE,
            points = lp.pointValue,
            changePoints = lp.pointValue,
            totalPoints = lp.member.point + lp.pointValue,
            pcStatus = PCStatus.VALID
        ).apply {
            recordDetail = "API操作-接口解冻"
        }

        val useFrozenRecord = initPointRecord(
            lp, Uuid.uuid, FSMPointEvent.DEDUCT,
            points = lp.pointValue,
            changePoints = -lp.pointValue,
            totalPoints = lp.afterTotalPoints,
            pcStatus = PCStatus.USED
        ).apply {
            recordDetail = "API操作-消耗冻结"
        }

        if (lp.attr.autoFillShopId == true) {
            // 查询冻结时的ShopId
            val oldRecord = recordService.findByTraceIdList(lp.member.id, lp.attr.traceId, lp.hierarchy.id, limit = 100)
                .filter { it.recordType == FSMPointEvent.OPEN_FREEZE }.maxByOrNull { it.created }
            unfrozenRecord.shopId = oldRecord?.shopId ?: lp.attr.shopId
            useFrozenRecord.shopId = oldRecord?.shopId ?: lp.attr.shopId
        }

        records.add(unfrozenRecord)
        records.add(useFrozenRecord)

        val items = ArrayList<PointRecordItem>()
        val ptcs = ArrayList<PointTransactionCalc>()
        val ptss = ArrayList<PointTransactionStatement>()

        val ptxId = if (lp.type == LoyaltyRequestType.TX_API) lp.attr.backId else null
        var sort = 0
        val gsIds = HashSet<String>()
        for (vs in vss) {
            val txBackId = if (ptxId != null) Uuid.uuid else null
            val unfreezeItem = initRecordItem(
                lp,
                Uuid.uuid,
                unfrozenRecord.id!!,
                vs.point,
                FSMPointEvent.UNFREEZE,
                backId = Uuid.uuid,
                parentBackId = vs.id,
                effectiveDate = vs.effectiveDate,
                overdueDate = vs.overdueDate
            )
            val useFrozenItem = initRecordItem(
                lp,
                Uuid.uuid,
                useFrozenRecord.id!!,
                -vs.point,
                FSMPointEvent.DEDUCT,
                backId = txBackId,
                parentBackId = unfreezeItem.backId
            )

            vs.subPoints = vs.point
            // 如果是支持撤销的操作
            if (ptxId != null) {
                useFrozenItem.backId = Uuid.uuid
                val tc = initTransactionCalc(lp, useFrozenRecord.id!!, ptxId, useFrozenItem.backId!!, vs, sort)
                val ts = initPointTransactionStatement(ptxId, useFrozenItem.backId!!, vs, sort)
                ptcs.add(tc)
                ptss.add(ts)
                sort++
            }

            items.add(unfreezeItem)
            items.add(useFrozenItem)
            gsIds.add(vs.gainStatementId)
        }

        validService.deleteBatchValidPoint(vss.map { it.id!! }.toTypedArray(), lp.hierarchy.id)

        // 更新积分获取记录状态
        for (gsId in gsIds) {
            val lst = validService.findListByGainStatementId(lp.hierarchy.id, gsId)
            if (lst.isEmpty()) {
                val gs = gainStatementService.findById(gsId, lp.hierarchy.id.toString()).getOrNull()
                if (gs?.status == FSMPointEvent.SEND || gs?.status == FSMPointEvent.SPECIAL_FREEZE || gs?.status == FSMPointEvent.OPEN_FREEZE) {
                    gs.status = FSMPointEvent.DEDUCT
                    gs.modified = ZonedDateTime.now()
                    gs.saveOrUpdate(lp.hierarchy.id)
                }
            }
        }
        ptcsService.batchInsert(ptcs, lp.hierarchy.id.toString())
        ptssService.batchInsert(ptss, lp.hierarchy.id.toString())

        batchUpdateRecordAndSegment(lp.hierarchy.id, records, items, listOf())
    }
}