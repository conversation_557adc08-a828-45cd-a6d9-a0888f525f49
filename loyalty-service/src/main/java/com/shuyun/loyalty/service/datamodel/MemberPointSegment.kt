package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.datamodel.MemberPointSegment.Companion.FQN
import java.time.LocalDate
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.Table
import javax.persistence.Transient
import javax.validation.constraints.NotEmpty

@DataServiceModel
@FqnVariableModel
@Table(name = "$FQN{*}")
open class MemberPointSegment : SubMemberPoint() {

    companion object {
        const val FQN = "data.loyalty.member.account.PointSegment"
    }

    /** 数据最后一次修改时间 */
    @Column
    @NotEmpty
    var modified: ZonedDateTime? = null

    /** 过期时间 年月日 */
    @Column
    @NotEmpty
    var expireDate: LocalDate? = null

    @Transient
    var updated: Boolean = false

    @Transient
    var initial: Boolean = false
}
