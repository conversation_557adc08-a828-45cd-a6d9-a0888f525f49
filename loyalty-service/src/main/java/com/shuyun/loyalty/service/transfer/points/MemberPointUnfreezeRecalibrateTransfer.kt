package com.shuyun.loyalty.service.transfer.points

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.service.datamodel.*
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.model.PointDeductRuleOperate
import com.shuyun.loyalty.service.util.sendNotify
import org.apache.logging.log4j.LogManager
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.ZonedDateTime

// 积分解冻重算
@Service
class MemberPointUnfreezeRecalibrateTransfer: MemberPointBaseTransfer() {

    private val logger = LogManager.getLogger(MemberPointUnfreezeRecalibrateTransfer::class.java)


    fun recalibrate(lp: LoyaltyPoints) {

        // 重算的规则组和规则
        val recalculateRuleGroup = lp.pointRuleGroup
        val recalculateRule = lp.deductRule
        val recalculateDesc = lp.attr.desc
        var flag = false
        // 解冻重算不匹配新规则，只匹配被冻结的积分的原规则
        val (oldGSS, _, newInOldGSS, oldNotInNewGSS) = recalculate(lp)

        if (oldGSS.isEmpty() && newInOldGSS.isEmpty() && oldNotInNewGSS.isEmpty()) {
            logger.info("积分重算，没有需要解冻重算的积分 {} {} {}", lp.hierarchy.id, lp.attr.traceId, lp.eventType?.name)
            return
        }

        val journalist = ArrayList<MemberPointCalculateTask>()

        val memberPoint = lp.member.memberPoint!!

        fun unfreeze(fp: MemberFrozenPoint, gs: MemberPointGainStatement, desc: String?) {
            if (fp.point <= BigDecimal.ZERO) {
                return
            }
            if (recalculateRule!!.quotaType == PointDeductRuleOperate.QuotaTypeEnum.ZERO) {
                logger.info("积分解冻重算规则扣减额度为0")
                val zeroRecord = initPointRecord(
                    lp, Uuid.uuid, FSMPointEvent.RECALCULATE,
                    points = BigDecimal.ZERO,
                    changePoints = BigDecimal.ZERO,
                    totalPoints = lp.member.point,
                    pcStatus = if (fp.fromStatus == FSMPointEvent.DELAY_SEND) PCStatus.DELAY else PCStatus.VALID).apply {
                    this.recordDetail = "积分解冻重算规则配额为0"
                }
                zeroRecord.insert(lp.hierarchy.id)
                listOf(zeroRecord).sendNotify()
                return
            }

            // 关联发放的规则组和规则
            lp.pointRuleGroup = PointGroup(gs.ruleGroupId!!, gs.ruleGroupName!!, gs.enabledGlobalGroupLimit)
            lp.sendRule = PointIssuanceRule(gs.ruleId!!, gs.ruleName!!)

            lp.attr.businessId = gs.ref!!.id
            lp.attr.backId = Uuid.uuid
            lp.member.point = memberPoint.point
            lp.attr.desc = desc

            // 先解冻积分
            unFrozenEventPoint(lp, gs, fp)

            // 保存
            val journalKey = lp.attr.uniqueId + "-unfreeze-"  + gs.ref!!.id
            val unfreezeJournal = lp.buildJournal(PointAction.UNFREEZE, journalKey)
            journalist.add(unfreezeJournal)

            if (fp.fromStatus == FSMPointEvent.DELAY_SEND) {
                lp.attr.businessId = lp.attr.backId
                lp.attr.backId = null
            }
        }

        // 多退少补
        for (gs in newInOldGSS) {
            val fp = frozenPointService.findFrozenPointByGainStatementId(gs.ref!!.id!!, gs.pointPlanId!!) ?: continue
            unfreeze(fp, gs, recalculateDesc)

            // 新旧相等，仅解冻
            if (gs.point.compareTo(gs.ref!!.point) == 0) {
                continue
            }
            // 过期了，仅解冻
            if (gs.overdueDate.shDate().isBefore(lp.date.shDate())) {
                continue
            }

            // 解冻后的剩余积分值已经发生了变更这里更新一下
            lp.member.point = lp.member.memberPoint!!.point

            val frozenPoints = fp.point.abs()

            // 旧大于新，需要把旧多出来的部分扣除
            if (gs.point < frozenPoints && !checkSpecial(lp, ForbiddenOperation.POINT_DEDUCT, ForbiddenPort.CALC_EVENT)) {
                lp.pointRuleGroup = recalculateRuleGroup
                lp.deductRule = recalculateRule
                lp.attr.recordDetail = "<- ${gs.ruleGroupId}-${gs.ruleId}"
                lp.sendRule = null
                lp.attr.desc = recalculateDesc
                val deductPoint = frozenPoints - gs.point
                val journal = recalculateMinus(lp, deductPoint, gs)
                journalist.add(journal)
                flag = true
            }

            // 新大于旧，需要把新多出来的部分补发
            if (gs.point > frozenPoints && !checkSpecial(lp, ForbiddenOperation.POINT_SEND, ForbiddenPort.CALC_EVENT)) {
                lp.pointRuleGroup = PointGroup(gs.ruleGroupId!!, gs.ruleGroupName!!, gs.enabledGlobalGroupLimit)
                lp.sendRule = PointIssuanceRule(gs.ruleId!!, gs.ruleName!!)
                lp.deductRule = null
                lp.attr.desc = gs.desc
                val sendPoint = gs.point - frozenPoints
                val journal = recalculatePlus(lp, sendPoint, gs)
                journalist.add(journal)
                if (gs.status == FSMPointEvent.SEND) {
                    gs.point = frozenPoints + lp.afterPoints
                }
            }

            // 更新积分获取积分为新积分值
            gs.ref!!.point = gs.point
            gs.ref!!.modified = ZonedDateTime.now()
            gs.ref!!.saveOrUpdate(lp.hierarchy.id)
        }

        // 重算作废
        if (oldNotInNewGSS.isNotEmpty() && !checkSpecial(lp, ForbiddenOperation.POINT_DEDUCT_BY_ABOLISH, ForbiddenPort.CALC_EVENT)) {
            for (gs in oldNotInNewGSS) {
                val fp = frozenPointService.findFrozenPointByGainStatementId(gs.ref!!.id!!, gs.pointPlanId!!) ?: continue
                unfreeze(fp, gs, recalculateDesc)

                val journal = invalidNotMatched(
                    lp,
                    gs,
                    recalculateRuleGroup,
                    recalculateRule,
                    recalculateDesc,
                    PointAction.RECALCULATE
                )

                // 重算后原单积分就不存在了，这里修改为0
                gs.point = BigDecimal.ZERO
                gs.modified = ZonedDateTime.now()
                gs.saveOrUpdate(lp.hierarchy.id)
                flag = true
                journalist.add(journal)
            }
        }

        // 释放积分上限
        if (flag) {//有过变动才进行积分积分上限调整
            val sendEventTypeId = lp.eventType!!.relativePointEventType!!.id
            val newGSS = gainStatementService.findByTraceIdList(
                lp.hierarchy.id,
                sendEventTypeId,
                lp.attr.traceId,
                lp.member.id
            )
            releaseLimit(lp, oldGSS, newGSS, sendEventTypeId)
        }

        logger.info("解冻重算后，剩余总分：{}", memberPoint.point)
        // 保存总积分变更日志
        memberPoint.update()
        memberPoint.saveLog()

        MemberPointCalculateTask.batchSave(lp.hierarchy.id, journalist)
    }
}