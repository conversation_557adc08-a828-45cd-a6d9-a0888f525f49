package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.model.SpecialListConfig
import com.shuyun.loyalty.service.model.SpecialListConfigStatus
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Component
import java.util.*

@Component
class SpecialListConfigRepository : DataModelCarryRepository<SpecialListConfig>() {

    private val log = LogManager.getLogger(PropertyTemplateRepository::class.java)
    override fun log(): Logger = log

    fun findByDisplayAndDisabledOrderByCreateTimeDesc(display: Boolean,disabled: Boolean, pageable: Pageable): Page<SpecialListConfig> {
        val params = HashMap<String, Any?>()
        params["display"] = display
        params["disabled"] = disabled
        return findPageByFilter(JsonUtils.toJson(params),"{\"createTime\": \"desc\"}",pageable)
    }

    fun findByIdAndDisplayAndDisabled(id:Long,display: Boolean,disabled: Boolean): Optional<SpecialListConfig> {
        val params = HashMap<String, Any?>()
        params["id"] = id
        params["display"] = display
        params["disabled"] = disabled
        return findOneByFilter(JsonUtils.toJson(params))
    }

    fun findByIdAndDisabledOrderByVersionId(id:Long,disabled: Boolean):List<SpecialListConfig> {
        val params = HashMap<String, Any?>()
        params["id"] = id
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params),"{\"createTime\": \"desc\"}")
    }

    fun findBySubjectIdAndDisabled(subjectId: Long, disabled: Boolean): List<SpecialListConfig>{
        val params = HashMap<String, Any?>()
        params["subjectId"] = subjectId
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findByPlanIdAndDisabled(planId: Long, disabled: Boolean): List<SpecialListConfig> {
        val params = HashMap<String, Any?>()
        params["planId"] = planId
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findBySubjectIdAndDisabledAndStatus(subjectId: Long, disabled: Boolean, status: SpecialListConfigStatus): List<SpecialListConfig>{
        log.debug("查询黑名单配置,主体id:$subjectId")
        val params = HashMap<String, Any?>()
        params["subjectId"] = subjectId
        params["disabled"] = disabled
        params["status"] = status
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun deleteByPlanId(planId: Long) {
        val params = HashMap<String, Any?>()
        params["planId"] = planId
        deleteByFilter(JsonUtils.toJson(params))
    }
}