package com.shuyun.loyalty.service.meta

enum class ModeTypeEnum(val type: String) {
    CONDITION("CONDITION"),//条件模式
    SIMPLE("SIMPLE"),//简单模式
    SENIOR("SENIOR"),//高级模式
}

enum class TimeTypeEnum(val type: String) {
    RELATIVE_TIME("RELATIVE_TIME"),//相对时间
    ABSOLUTE_TIME("ABSOLUTE_TIME"),//绝对时间

    // 用于提醒
    RELATIVE_TIME_DAY("RELATIVE_TIME_DAY"),//相对时间天
    RELATIVE_TIME_YEAR("RELATIVE_TIME_YEAR"),//相对时间年
    RELATIVE_TIME_YEAR_MONTH("RELATIVE_TIME_YEAR_MONTH"),//相对时间年月
}

enum class SendTypeEnum(val type: String) {
    IMMEDIATE_TIME("IMMEDIATE_TIME"),//立即发放
    DELAY_SEND("DELAY_SEND"),//延迟发放
}

enum class ValidTimeTypeEnum(val type: String) {
    UNCHANGE("UNCHANGE"), //不变
    RESET("RESET"), //重置
    CHANGE("RESET"), //在原始有效期上改变
}

enum class ChangeTimeType(val type: String) {
    LENGTHEN("LENGTHEN"), //拉长
    SHORTEN("SHORTEN") //缩短
}

enum class ChangeUnitType(val type: String) {
    YEAR("YEAR"),
    MONTH("MONTH"),
    DAY("DAY")
}

enum class AlertBizType(val type: String) {
    GRADE("GRADE"),
    MEDAL("MEDAL")
}