package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.datamodel.MemberPointRecord
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

@Component
class MemberPointRecordRepository: DataModelRepository<MemberPointRecord>() {
    private val log = LogManager.getLogger(MemberPointRecordRepository::class.java)
    override fun log(): Logger = this.log
}