package com.shuyun.loyalty.service.calculate.sendlimit

import com.shuyun.loyalty.entity.bo.SendLimitRuleBO
import com.shuyun.loyalty.entity.bo.SendLimitRuleCalcBO
import com.shuyun.loyalty.entity.bo.SendLimitRuleResultDetail
import com.shuyun.loyalty.service.meta.PointSendLimitLogic
import com.shuyun.loyalty.service.transfer.points.PointLimitRuleType
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.DateUtils
import com.shuyun.loyalty.service.util.SendLimitUtils
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import java.math.BigDecimal

abstract class AbstractMemberPointSendLimitFilter: IMemberPointSendLimitFilter {

    private val logger = LogManager.getLogger(AbstractMemberPointSendLimitFilter::class.java)

    override fun filter(dto: IMemberPointSendLimitFilter.SendLimitFilterDTO) {
        dto.isContinue = true // 每个filter执行完毕重置单一执行标识
        before(dto)
        if (dto.isContinue) doFilter(dto)
        if (dto.isContinue) after(dto)
    }

    override fun before(dto: IMemberPointSendLimitFilter.SendLimitFilterDTO) {}

    override fun after(dto: IMemberPointSendLimitFilter.SendLimitFilterDTO) {}

    /**
     * 计算拦截，并修改剩余积分
     */
    protected open fun doFilter(dto: IMemberPointSendLimitFilter.SendLimitFilterDTO) {
        val result = calculate(dto)
        if (result.isEmpty()) return
        // 更改为拦截后发的积分,且补充拦截详情
        dto.pointValue = dto.remainingPointValue!!
        dto.sendLimitRuleCalcResult.sendLimitRuleResultDetail.addAll(result)
        dto.sendLimitRuleCalcResult.result = true
    }

    /**
     * 计算
     */
    fun calculate(dto: IMemberPointSendLimitFilter.SendLimitFilterDTO): ArrayList<SendLimitRuleResultDetail> {
        val list  = ArrayList<SendLimitRuleResultDetail>()
        if (dto.sendLimitRuleList.isNullOrEmpty() || dto.memberPointSendLimitCalc == null) {
            logger.debug("缺少计算参数,不计算 {}", { JsonUtils.toJson(dto) })
            return list
        }
        if(dto.pointValue!! <= ConstantValue.defaultZeroLine) {
            logger.debug("待发放积分为零,不计算 {}", { JsonUtils.toJson(dto) })
            return list
        }
        val sendLimitRuleType = pointSendLimitType()
        logger.debug("{} 积分上限拦截计算 : {} ", { sendLimitRuleType }, { JsonUtils.toJson(dto) })

        val sendLimitRuleList = dto.sendLimitRuleList
        val before = dto.pointValue!!
        // 剩余积分
        var remaining = dto.pointValue!!
        val time = dto.sendLimitParams!!.time


        for (sendLimitRule in sendLimitRuleList!!) {
            if (remaining.compareTo(BigDecimal.ZERO) == 0) {
                break
            }
            // 排序以最小的在前边,计算时仅计算最小的一个就可以
            val sendLimitRuleBOListSource = JsonUtils.parse2List(sendLimitRule.nlms, SendLimitRuleCalcBO::class.java).toCollection(ArrayList())
            val sendLimitRuleBOListCount = sendLimitRuleBOListSource.filter { SendLimitRuleBO.SendLimitRuleType.COUNT == it.type }
            val sendLimitRuleBOListPoint = sendLimitRuleBOListSource.filter { SendLimitRuleBO.SendLimitRuleType.POINT == it.type }

            val sortedCountRuleList = if(PointSendLimitLogic.and == sendLimitRule.logic) sendLimitRuleBOListCount.sortedBy { it.value } else sendLimitRuleBOListCount.sortedByDescending { it.value }
            for (sendLimitRuleCalcBO in sortedCountRuleList) {
                if (dto.isRecalculate && !dto.isReissue) {
                    break
                }
                // 本线程已经累计过次数
                val accumulatedCount = if (SendLimitUtils.isNotHave(sendLimitRuleType, dto.refId!!)) 0 else 1
                // 计算次数
                val dateStr = DateUtils.getStartDateByType(time.toLocalDate(), sendLimitRuleCalcBO.cycle!!)
                val key = dateStr.plus(ConstantValue.HORIZON).plus(sendLimitRuleCalcBO.cycle).plus(ConstantValue.HORIZON).plus(sendLimitRuleCalcBO.type)
                val currentValue = (dto.memberPointSendLimitCalc!!.valueMap!![key] ?: 0).toLong() - accumulatedCount
                if (sendLimitRuleCalcBO.value!! < currentValue.plus(1)) {
                    // 到达次数上限, 不发积分
                    sendLimitRuleCalcBO.beforeCount = currentValue
                    sendLimitRuleCalcBO.afterCount = currentValue.plus(1)
                    sendLimitRuleCalcBO.afterPoint = BigDecimal.ZERO
                    sendLimitRuleCalcBO.beforePoint = remaining
                    sendLimitRuleCalcBO.result = true
                    remaining = sendLimitRuleCalcBO.afterPoint!!
                    val sendLimitRuleResultDetail = SendLimitRuleResultDetail()
                    sendLimitRuleResultDetail.logic = sendLimitRule.logic.toString()
                    sendLimitRuleResultDetail.limitId = sendLimitRule.id
                    sendLimitRuleResultDetail.type = sendLimitRuleType.toString()
                    sendLimitRuleResultDetail.limitRuleType = PointLimitRuleType.EVENT_TYPE_COUNT_LIMIT.name
                    sendLimitRuleResultDetail.sendLimitCalc = mapOf(key to sendLimitRuleCalcBO.value)
                    sendLimitRuleResultDetail.limitRuleCalcList = sendLimitRuleBOListSource
                    sendLimitRuleResultDetail.refId = dto.refId
                    list.add(sendLimitRuleResultDetail)
                    break
                }
                if(PointSendLimitLogic.or == sendLimitRule.logic) {
                    break
                }
            }

            val sortedPointRuleList = if(PointSendLimitLogic.and == sendLimitRule.logic) sendLimitRuleBOListPoint.sortedBy { it.value } else sendLimitRuleBOListPoint.sortedByDescending { it.value }
            for (sendLimitRuleCalcBO in sortedPointRuleList) {
                if ((dto.isRecalculate && !dto.isReissue) || dto.onlyFilterCount) {
                    break
                }
                val dateStr = DateUtils.getStartDateByType(time.toLocalDate(), sendLimitRuleCalcBO.cycle!!)
                val key = dateStr.plus(ConstantValue.HORIZON).plus(sendLimitRuleCalcBO.cycle).plus(ConstantValue.HORIZON).plus(sendLimitRuleCalcBO.type)
                val currentValue = (dto.memberPointSendLimitCalc!!.valueMap!![key] ?: 0.0).toString().toBigDecimal()
                if (sendLimitRuleCalcBO.value!!.toBigDecimal() < currentValue.toString().toBigDecimal().plus(remaining)) {
                    sendLimitRuleCalcBO.afterPoint = sendLimitRuleCalcBO.value!!.toBigDecimal().minus(currentValue)
                    if (sendLimitRuleCalcBO.afterPoint!! < BigDecimal.ZERO) sendLimitRuleCalcBO.afterPoint = BigDecimal.ZERO
                    sendLimitRuleCalcBO.beforePoint = remaining
                    sendLimitRuleCalcBO.result = true
                    remaining = sendLimitRuleCalcBO.afterPoint!!
                    val sendLimitRuleResultDetail = SendLimitRuleResultDetail()
                    sendLimitRuleResultDetail.logic = sendLimitRule.logic.toString()
                    sendLimitRuleResultDetail.limitId = sendLimitRule.id
                    sendLimitRuleResultDetail.type = sendLimitRuleType.toString()
                    sendLimitRuleResultDetail.limitRuleType = PointLimitRuleType.EVENT_TYPE_POINT_LIMIT.name
                    sendLimitRuleResultDetail.sendLimitCalc = mapOf(key to sendLimitRuleCalcBO.value)
                    sendLimitRuleResultDetail.limitRuleCalcList = sendLimitRuleBOListSource
                    sendLimitRuleResultDetail.refId = dto.refId
                    list.add(sendLimitRuleResultDetail)
                    if (remaining.compareTo(BigDecimal.ZERO) == 0) {
                        break
                    }
                }
                if(PointSendLimitLogic.or == sendLimitRule.logic) {
                    break
                }
            }


        }
        dto.remainingPointValue = remaining
        // 剩余积分给参数,用于 时机、规则组、规则依次计算
        if (before.compareTo(remaining) != 0) {
            logger.info("{} 积分上限计算前结果 : {} 积分上限计算后结果 : {} ", { sendLimitRuleType }, { before }, { if(list.isEmpty()) "通过" else JsonUtils.toJson(list) })
        }
        return list
    }
}
