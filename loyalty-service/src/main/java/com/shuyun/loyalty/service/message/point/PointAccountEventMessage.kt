package com.shuyun.loyalty.service.message.point

import com.shuyun.fx.schema.JsonFinder
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.message.EventStreamEventMessage
import com.shuyun.loyalty.service.message.MessageHandlerType
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.model.*
import com.shuyun.loyalty.service.service.ChannelTypeBaseService
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.DateUtils
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.name.Name
import org.apache.logging.log4j.LogManager
import java.util.*

@Name
open class PointAccountEventMessage(
    val executeOrder: PointAccountExecuteOrder,
    val event: Event,
    val matchingTime: Date
): EventStreamEventMessage() {

    private val log = LogManager.getLogger(PointAccountEventMessage::class.java)

    val isEffectiveRecalculate: Boolean get() {
        return eventType.operation == EventOperationEnum.RECALCULATE || eventType.operation == EventOperationEnum.FREEZE_RECALCULATE
    }

    private var _memberId: String? = null
    val memberId: String get() {
        return if (_memberId != null) {
            _memberId!!
        } else {
            memberId()
        }
    }

    val pointAccountType: PointAccountType get() = executeOrder.pointAccountType

    val plan: Plan get() {
        return executeOrder.plan
    }

    val subject: Subject get() {
        return executeOrder.subject
    }

    val eventType: EventType get() {
        return executeOrder.eventType
    }

    val ruleGroups: List<PointRuleGroup> get() {
        return eventType.pointRuleGroups[pointAccountType.id!!]!!
    }

    private fun memberId(): String {
        try {
            val metas = eventType.eventStreamMetas!!
            return JsonFinder.find(event.asObjectNode(), metas.fetchPathByDsFqn(subject.dataType!!, event)).asText()
        } catch (e: Exception) {
            throw LoyaltyException(LoyaltyExceptionCode.EVENT_NOT_MATCH, e)
        }
    }

    override fun eventHandlerType(): MessageHandlerType = throw  NotImplementedError()


    override fun findChannelType(): String {
        var channelCode = ConstantValue.DEFAULT_CHANNEL_TYPE
        return if (eventType.channelTypePath == null) {
            channelCode
        } else {
            try {
                channelCode = JsonFinder.find(event.asObjectNode(), eventType.channelTypePath!!).asText()
                ApplicationContextHolder.getBean(ChannelTypeBaseService::class.java).check(channelCode)
            } catch (e: LoyaltyException) {
                log.warn("获取渠道失败:{}", e.message)
            } catch (e: Throwable) {
                log.warn("获取渠道失败", e)
            }
            return channelCode
        }
    }

    override fun findEffectEventType(): Boolean {
        return true
    }


    /**
     * 从时机中获取发放积分周期字段路径
     */
    fun findPointSendCycleTimePath(): Calendar? {
        if (eventType.pointSendCycleTimePath.isNullOrEmpty()) {
            return null
        }
        var pointSendCycleTime = ""
        try {
            pointSendCycleTime = JsonFinder.find(event.asObjectNode(), eventType.pointSendCycleTimePath!!).asText()
        } catch (e: Throwable) {
            log.warn("获取积分发放周期失败", e)
        }
        return if (pointSendCycleTime.isNotEmpty()) {
            DateUtils.getCalendarAndIncrement(pointSendCycleTime)
        } else null
    }

}