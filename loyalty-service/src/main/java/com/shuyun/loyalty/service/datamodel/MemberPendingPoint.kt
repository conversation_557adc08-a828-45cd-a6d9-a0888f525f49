package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.repository.DataModelRepository
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.Table


@Table(name = "data.loyalty.member.MemberPendingPoint{*}")
@DataServiceModel
@FqnVariableModel
class MemberPendingPoint: BaseDataModel()  {

    @Column
    var planId: Long? = null

    @Column
    var pointAccountTypeId: Long? = null

    @Column
    var subjectFqn: String? = null

    @Column
    var memberPointId: String? = null

    @Column
    var memberId: String? = null

    @Column
    var point: Double? = null

    @Column
    var effectiveDate: ZonedDateTime? = null

    @Column
    var overdueDate: ZonedDateTime? = null

    @Column
    var version: Long? = null

    @Column
    var created: ZonedDateTime? = null

    @Column
    var modified: ZonedDateTime? = null

    companion object
}


@Component
class MemberPendingPointRepository: DataModelRepository<MemberPendingPoint>() {
    private val log = LogManager.getLogger(MemberPendingPointRepository::class.java)
    override fun log(): Logger = log
    companion object
}