package com.shuyun.loyalty.service.model

import io.swagger.v3.oas.annotations.media.Schema
import java.util.*
import javax.persistence.MappedSuperclass

@MappedSuperclass
abstract class BaseModel {


    @Schema(title = "是否已删除，暂时不用", type = "String")
    var disabled: Boolean? = null

    @Schema(title = "创建人ID", type = "String")
    var creatorId: String? = null

    @Schema(title = "创建人名称", type = "String")
    var creatorName: String? = null

    @Schema(title = "创建时间", type = "date")
    var createTime: Date? = null

    @Schema(title = "更新人ID", type = "String")
    var updaterId: String? = null

    @Schema(title = "更新人姓名", type = "String")
    var updaterName: String? = null

    @Schema(title = "更新时间", type = "date")
    var updateTime: Date? = null

    /**将数据库中的旧记录对应的对象的属性值更新为此对象的属性值，更新前根据计划是否已发布进行校验*/
    abstract fun copyToOldOne(old: BaseModel, backup: Boolean)
}

