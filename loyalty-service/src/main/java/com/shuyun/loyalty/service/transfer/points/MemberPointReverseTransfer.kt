package com.shuyun.loyalty.service.transfer.points

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.api.constants.ReversePointEvent
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.service.datamodel.MemberPointRecord
import com.shuyun.loyalty.service.datamodel.MemberPointValidStatement
import com.shuyun.loyalty.service.datamodel.PointAction
import com.shuyun.loyalty.service.datamodel.PointRecordItem
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.service.MemberPointService
import com.shuyun.loyalty.service.service.PointTransactionBaseService
import com.shuyun.loyalty.service.service.PointTransactionCalcBaseService
import com.shuyun.loyalty.service.util.ConstantValue.LONG_TERM_OVERDUE_DATE
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.ZonedDateTime

// 反向操作
@Service
class MemberPointReverseTransfer: MemberPointBaseTransfer() {

    private val logger = LogManager.getLogger(MemberPointReverseTransfer::class.java)

    @Autowired
    private lateinit var ptcService: PointTransactionCalcBaseService

    @Autowired
    private lateinit var ptService: PointTransactionBaseService


    @Autowired
    private lateinit var memberPointService: MemberPointService


    fun transfer(lp: LoyaltyPoints, action: PointAction) {
        lp.date = ZonedDateTime.now()
        val mp = memberPointService.getByMemberPointId(lp.hierarchy.id, lp.member.id) ?: return
        lp.member.apply {
            this.point = mp.point
            this.memberPoint = mp
        }
        var totalPoints = mp.point
        val beforePoints = mp.point
        when (action) {
            PointAction.REVERSE_SEND -> {
                val records = ArrayList<MemberPointRecord>()
                val items = ArrayList<PointRecordItem>()
                val vss = ArrayList<MemberPointValidStatement>()
                val record = initPointRecord(
                    lp, Uuid.uuid,
                    FSMPointEvent.REVERSE_SEND,
                    points = lp.afterPoints,
                    changePoints = lp.afterPoints,
                    totalPoints = totalPoints + lp.afterPoints,
                    PCStatus.VALID
                )

                if (lp.attr.reverseType == ReversePointEvent.USE_FREEZE.name && lp.attr.autoFillShopId == true) {
                    // 查询冻结时的ShopId
                    val oldRecord = recordService.findByTraceIdList(lp.member.id, lp.attr.traceId, lp.hierarchy.id, limit = 100)
                        .filter { it.recordType == FSMPointEvent.OPEN_FREEZE }.maxByOrNull { it.created }
                    record.shopId = oldRecord?.shopId ?: lp.attr.shopId
                }

                records.add(record)

                val pointTransaction = ptService.getByBusinessId(lp.member.memberId, lp.attr.traceId, FSMPointEvent.DEDUCT.name, lp.hierarchy.id)
                if (pointTransaction == null) {
                    logger.info("没有找到可以撤销扣减的记录，traceId:{}", lp.attr.traceId)
                    return
                }
                if (pointTransaction.point > lp.pointValue) {
                    pointTransaction.point -= lp.pointValue
                    ptService.saveOrUpdate(pointTransaction, lp.hierarchy.id.toString())
                } else {
                    ptService.delete(pointTransaction.id!!, lp.hierarchy.id.toString())
                }

                val ptcs = ptcService.getByBusinessId(lp.attr.sourceId!!, lp.attr.traceId, lp.hierarchy.id)
                if (ptcs.isEmpty()) {
                    logger.warn("没有找到可以反向操作的明细 sourceId={} businessId={}", lp.attr.sourceId, lp.attr.traceId)
                    return
                }
                val gsIds = ptcs.map { it.gainStatementId!! }
                val gainStatements = gainStatementService.findByIds(gsIds, lp.hierarchy.id)
                val effectiveDates = ArrayList<ZonedDateTime>()
                val overdueDates = ArrayList<ZonedDateTime>()
                var p = lp.pointValue
                for (ptc in ptcs) {
                    val effectiveDate = ptc.effectiveDate ?: ZonedDateTime.now()
                    val overdueDate = ptc.overdueDate ?: LONG_TERM_OVERDUE_DATE
                    lp.attr.effectiveDate = effectiveDate
                    lp.attr.overdueDate = overdueDate
                    effectiveDates.add(effectiveDate)
                    overdueDates.add(overdueDate)
                    val point = if (ptc.point > p) p else ptc.point
                    ptc.point -= point
                    ptc.modified = ZonedDateTime.now()
                    p -= point

                    mp.plus(point, effectiveDate, overdueDate, lp.date)

                    // 有效积分记录
                    val vs = initValidPoints(
                        lp,
                        Uuid.uuid,
                        ptc.gainStatementId!!,
                        point,
                        PointStateEnum.VALID,
                        effectiveDate,
                        overdueDate
                    ).apply {
                        this.backId = ptc.backId
                        this.shopId = ptc.shopId
                        this.kzzd1 = ptc.ext1
                        this.kzzd2 = ptc.ext2
                        this.kzzd3 = ptc.ext3
                    }
                    val gs = gainStatements.find { it.id == vs.gainStatementId }
                    offset(lp, totalPoints, vs.point, record.id!!, FSMPointEvent.SEND) { offsetItems, afterVsPoints ->
                        items.addAll(offsetItems)
                        vs.point = afterVsPoints
                        if (afterVsPoints > BigDecimal.ZERO) {
                            val sendItem = initRecordItem(
                                lp,
                                Uuid.uuid,
                                record.id!!,
                                afterVsPoints,
                                FSMPointEvent.SEND,
                                backId = vs.id,
                                parentBackId = vs.backId,
                                effectiveDate = vs.effectiveDate,
                                overdueDate = vs.overdueDate
                            )
                            items.add(sendItem)
                        }
                    }

                    totalPoints += point

                    if (vs.point > BigDecimal.ZERO && vs.overdueDate.shDate() < lp.date.shDate()) {
                        // 已经过期的积分，额外生效一条过期记录
                        val expiredId = Uuid.uuid
                        val expiredRecord = initPointRecord(
                            lp, expiredId,
                            FSMPointEvent.EXPIRE,
                            points = vs.point,
                            changePoints = -vs.point,
                            totalPoints = (totalPoints - vs.point),
                            PCStatus.EXPIRE,
                            vs.effectiveDate,
                            vs.overdueDate
                        ).apply {
                            this.desc = "过期"
                            this.shopId = vs.shopId
                            this.channel = vs.channel
                            this.recordDetail = "积分过期-${vs.gainStatementId}"
                            this.traceId = gs?.traceId
                            this.key = vs.id
                            this.KZZD1 = vs.kzzd1
                            this.KZZD2 = vs.kzzd2
                            this.KZZD3 = vs.kzzd3
                        }

                        records.add(expiredRecord)
                        val expiredItem = initRecordItem(
                            lp,
                            Uuid.uuid,
                            expiredId,
                            -vs.point,
                            FSMPointEvent.EXPIRE,
                            parentBackId = vs.id,
                            effectiveDate = vs.effectiveDate,
                            overdueDate = vs.overdueDate
                        )
                        items.add(expiredItem)
                        gs?.status = FSMPointEvent.EXPIRE
                        gs?.modified = ZonedDateTime.now()
                        vs.point = BigDecimal.ZERO
                    } else {
                        gs?.status = FSMPointEvent.SEND
                        gs?.modified = ZonedDateTime.now()
                    }

                    vss.add(vs)

                    if (ptc.point.compareTo(BigDecimal.ZERO) == 0) {
                        ptc.delete(lp.hierarchy.id.toString())
                    } else {
                        ptc.saveOrUpdate(lp.hierarchy.id.toString())
                    }

                    if (p <= BigDecimal.ZERO) {
                        break
                    }
                }
                if (effectiveDates.size > 1 && effectiveDates.distinctBy { it.shDate() }.size == 1
                    && overdueDates.size > 1 && overdueDates.distinctBy { it.shDate() }.size == 1) {
                    record.effectiveDate = lp.attr.effectiveDate
                    record.overdueDate = lp.attr.overdueDate
                }
                vss.filter { it.point > BigDecimal.ZERO }.also {
                    validService.batchInsert(it, lp.hierarchy.id.toString())
                }
                gainStatements.forEach { it.saveOrUpdate(lp.hierarchy.id) }
                batchUpdateRecordAndSegment(lp.hierarchy.id, records, items, listOf())

                // 更新通知积分
                lp.out = OutNotification(
                    requestId = lp.attr.uniqueId,
                    type = action,
                    memberId = mp.memberId,
                    accountTypeId = mp.pointPlanId!!,
                    expectPoints = lp.pointValue,
                    actualPoints = (mp.point - beforePoints).abs()
                )
            }
            PointAction.REVERSE_DEDUCT -> {
                val pointTransaction = ptService.getByBusinessId(lp.member.memberId, lp.attr.traceId, listOf(FSMPointEvent.SEND.name, FSMPointEvent.DELAY_SEND.name), lp.hierarchy.id)
                if (pointTransaction == null) {
                    logger.info("没有找到可以撤销发放操作的记录，traceId:{}", lp.attr.traceId)
                    return
                }
                if (pointTransaction.point > lp.pointValue) {
                    pointTransaction.point -= lp.pointValue
                    ptService.saveOrUpdate(pointTransaction, lp.hierarchy.id.toString())
                } else {
                    ptService.delete(pointTransaction.id!!, lp.hierarchy.id.toString())
                }

                val ptcs = ptcService.getByBusinessId(lp.attr.sourceId!!, lp.attr.traceId, lp.hierarchy.id)
                if (ptcs.isEmpty()) {
                    logger.warn("没有找到可以反向操作的明细 sourceId={} businessId={}", lp.attr.sourceId, lp.attr.traceId)
                    return
                }
                val gs = gainStatementService.findByIds(ptcs.map { it.gainStatementId!! }, lp.hierarchy.id).firstOrNull() ?: return
                if (gs.status == FSMPointEvent.REVERSE_DEDUCT) {
                    logger.warn("已经反向操作过了 sourceId={} businessId={}", lp.attr.sourceId, lp.attr.traceId)
                    return
                }
                if (gs.status == FSMPointEvent.EXPIRE) {
                    logger.warn("已经过期了 sourceId={} businessId={}", lp.attr.sourceId, lp.attr.traceId)
                    return
                }
                val p = gs.point - lp.pointValue
                if (p <= BigDecimal.ZERO) {
                    gs.point = BigDecimal.ZERO
                    gs.status = FSMPointEvent.REVERSE_DEDUCT
                } else {
                    gs.point = p
                }
                gs.modified = ZonedDateTime.now()
                gs.saveOrUpdate(lp.hierarchy.id)

                if (gs.effectiveDate.isAfter(lp.date)) {
                    val recordId = Uuid.uuid
                    val record = initPointRecord(
                        lp, recordId, FSMPointEvent.REVERSE_DEDUCT,
                        points = gs.point,
                        changePoints = BigDecimal.ZERO,
                        totalPoints = totalPoints,
                        pcStatus = PCStatus.DELAY_ABOLISH
                    )
                    val item = initRecordItem(lp, Uuid.uuid, recordId, BigDecimal.ZERO, FSMPointEvent.DEDUCT, parentBackId = gs.id)
                    var pp = lp.pointValue
                    for (ptc in ptcs) {
                        if (ptc.point <= pp) {
                            ptc.point = BigDecimal.ZERO
                            pp -= ptc.point
                        } else {
                            ptc.point -= pp
                            ptc.modified = ZonedDateTime.now()
                            ptc.saveOrUpdate(lp.hierarchy.id.toString())
                            break
                        }
                    }
                    ptcService.deleteByIds(ptcs.filter { it.point.compareTo(BigDecimal.ZERO) == 0 }.map { it.id!! }, lp.hierarchy.id)
                    batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), listOf(item), listOf())
                    return
                }
                val record = initPointRecord(
                    lp, Uuid.uuid,
                    FSMPointEvent.REVERSE_DEDUCT,
                    points = lp.afterPoints,
                    changePoints = -lp.afterPoints,
                    totalPoints = totalPoints - lp.afterPoints,
                    PCStatus.USED
                )
                var pp = lp.pointValue
                for (ptc in ptcs) {
                    if (ptc.point <= pp) {
                        ptc.point = BigDecimal.ZERO
                        pp -= ptc.point
                    } else {
                        ptc.point -= pp
                        ptc.modified = ZonedDateTime.now()
                        ptc.saveOrUpdate(lp.hierarchy.id.toString())
                        pp = BigDecimal.ZERO
                    }
                    if (pp.compareTo(BigDecimal.ZERO) == 0) {
                        break
                    }
                }

                var segments = segmentService.findDateAfter(lp.hierarchy.id, lp.member.id, lp.date.shDate())
                val fetch = fun(_: LoyaltyPoints): List<MemberPointValidStatement> {
                    val vss = validService.findListByGainStatementIds(lp.hierarchy.id, listOf(gs.id!!))
                    return vss.ifEmpty {
                        vssFetch(lp)
                    }
                }

                // 顺序扣减
                val items = sequentiallyDeduct(
                    lp,
                    lp.pointValue,
                    fetch,
                    segments,
                    lp.hierarchy.negativeStrategy!!,
                    record.id!!,
                    FSMPointEvent.DEDUCT,
                    FSMPointEvent.REVERSE_DEDUCT,
                    FSMPointEvent.DEDUCT,
                    cb1 = { x ->
                        segmentService.deleteByMemberId(mp.memberId, mp.pointPlanId!!)
                        val segment = mp.buildSegment(-x, LONG_TERM_OVERDUE_DATE.shDate())
                        segmentService.save(segment)
                        segments = listOf(segment)
                    },
                )
                mp.apply {
                    point = segments.firstOrNull()?.point ?: BigDecimal.ZERO
                    modified = lp.date
                }.also {
                    it.update()
                    it.saveLog()
                    // 更新通知积分
                    lp.out = OutNotification(
                        requestId = lp.attr.uniqueId,
                        type = action,
                        memberId = mp.memberId,
                        accountTypeId = mp.pointPlanId!!,
                        expectPoints = lp.pointValue,
                        actualPoints = (mp.point - beforePoints).abs()
                    )
                }
                ptcService.deleteByIds(ptcs.filter { it.point.compareTo(BigDecimal.ZERO) == 0 }.map { it.id!! }, lp.hierarchy.id)
                batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), items, segments)
            }
            else -> return
        }
    }
}