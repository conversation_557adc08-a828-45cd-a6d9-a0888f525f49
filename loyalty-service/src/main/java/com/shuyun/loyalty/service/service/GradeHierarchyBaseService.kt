package com.shuyun.loyalty.service.service


import com.shuyun.loyalty.entity.api.constants.PublishStatusEnum
import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.exception.GradeException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.EventStreamMetasStatusEnum
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.model.DerivationEventMetas
import com.shuyun.loyalty.service.model.DerivationEventType
import com.shuyun.loyalty.service.model.GradeHierarchy
import com.shuyun.loyalty.service.repository.GradeHierarchyRepository
import com.shuyun.loyalty.service.repository.SubjectRepository
import com.shuyun.loyalty.service.util.DateUtils
import org.apache.commons.io.IOUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.support.PathMatchingResourcePatternResolver
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import java.util.*


@Service
class GradeHierarchyBaseService {

    @Autowired
    private lateinit var subjectRepository: SubjectRepository

    @Autowired
    private lateinit var gradeHierarchyRepository: GradeHierarchyRepository

    @Autowired
    private lateinit var gradeDefinitionBaseService: GradeDefinitionBaseService

    @Autowired
    private lateinit var derivationEventMetasBaseService: DerivationEventMetasBaseService

    @Autowired
    private lateinit var remindConfigBaseService: RemindConfigBaseService

    fun findById(id: String): Optional<GradeHierarchy> {
        return gradeHierarchyRepository.findById(id)
    }

    fun findAll(): List<GradeHierarchy> {
        return gradeHierarchyRepository.findAll()
    }

    /**分页用：主体ID获取等级体系,不查询等级信息*/
    fun findPageBySubjectVersionIdList(subjectVersionIdList: List<Long>): List<GradeHierarchy> {
        return gradeHierarchyRepository.findPageBySubjectVersionIdAndDisabledList(subjectVersionIdList, false)
    }

    /**根据主体ID获取等级体系详情*/
    fun findDetailBySubjectVersionId(subjectVersionId: Long, calc: Boolean = false): List<GradeHierarchy> {
        val gradeHierarchyList = gradeHierarchyRepository.findBySubjectVersionIdAndDisabled(subjectVersionId, false)
        gradeHierarchyList.forEach {
            it.gradeDefinitions = gradeDefinitionBaseService.findByEnabledHierarchyByVersionId(it.versionId!!)
        }
        if (calc) return gradeHierarchyList
        gradeHierarchyList.forEach {
            it.remindConfigList = remindConfigBaseService.findParentVersionId(it.versionId!!)
        }
        return gradeHierarchyList
    }

    /**删除等级体系本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun delete(gradeHierarchy: GradeHierarchy) {
        gradeHierarchy.disabled = true
        gradeHierarchyRepository.save(gradeHierarchy)
    }

    /**新增等级体系本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun insert(gradeHierarchy: GradeHierarchy) {
        gradeHierarchy.name = gradeHierarchy.name!!.trim()
        gradeHierarchy.versionId = null
        gradeHierarchy.status = PublishStatusEnum.DRAFT
        gradeHierarchyRepository.save(gradeHierarchy)
        gradeHierarchy.id = gradeHierarchy.versionId
        gradeHierarchyRepository.save(gradeHierarchy)

        derivationEventMetasBaseService.save(
            initGradeHierarchyDerivation(
                gradeHierarchy,
                DerivationEventType.GRADE_EXPIRE_REMIND
            )
        )
        derivationEventMetasBaseService.save(
            initGradeHierarchyDerivation(
                gradeHierarchy,
                DerivationEventType.GRADE_RECORD
            )
        )
    }

    fun initGradeHierarchyDerivation(
        gradeHierarchy: GradeHierarchy,
        derivationEventType: DerivationEventType
    ): DerivationEventMetas {
        val subject = subjectRepository.findByVersionId(gradeHierarchy.subjectVersionId!!)
        val resource =
            PathMatchingResourcePatternResolver().getResource("classpath:planPublishInit/derivation.event.${derivationEventType.fileName}.json")
        var jsonEvent = IOUtils.toString(resource.inputStream, "UTF-8")!!
        jsonEvent =
            jsonEvent.replace("{id}", gradeHierarchy.id.toString()).replace("{dataModelFqn}", subject.get().dataType!!)
        val derivationEventMetas = DerivationEventMetas().apply {
            this.code = derivationEventType.code.plus("_").plus(gradeHierarchy.id)
            this.schema = jsonEvent
            this.fqn = "data.loyalty.member.hierarchy.${derivationEventType.fileName}".plus(gradeHierarchy.id)
            this.name = "等级体系衍生事件,${derivationEventType.fileName}: ".plus(gradeHierarchy.id)
            this.status = EventStreamMetasStatusEnum.VALID
            this.created = Date()
            this.modified = Date()
        }
        return derivationEventMetas
    }

    /**更新等级体系本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun update(gradeHierarchy: GradeHierarchy, derivationEventTypes: List<DerivationEventType>? = null) {
        gradeHierarchy.name = gradeHierarchy.name!!.trim()
        gradeHierarchyRepository.save(gradeHierarchy)
        val types = ArrayList<DerivationEventType>()
        if (derivationEventTypes == null) {
            val codes = ArrayList<String>()
            for (entry in DerivationEventType.entries) {
                if (entry == DerivationEventType.GRADE_EXPIRE_REMIND || entry == DerivationEventType.GRADE_RECORD) {
                    codes.add(entry.code.plus("_").plus(gradeHierarchy.id))
                }
            }
            val derivationEventMetaCodes = HashSet<String>()
            derivationEventMetaCodes.addAll(derivationEventMetasBaseService.findByCodeIn(codes).map { it.code!! })
            codes.forEach { c ->
                if (c !in derivationEventMetaCodes) {
                    DerivationEventType.fromCode(c)?.let { types.add(it) }
                }
            }
        } else {
            types.addAll(derivationEventTypes)
        }
        for (derivationEventType in types) {
            derivationEventMetasBaseService.save(initGradeHierarchyDerivation(gradeHierarchy, derivationEventType))
        }
    }

    /**根据时间及生效ID获取生效对象*/
    fun getEffectiveOne(id: Long, date: ZonedDateTime): GradeHierarchy {
        val gradeHierarchy = gradeHierarchyRepository.findByIdAndDate(id, date)
        return if (gradeHierarchy.isPresent) gradeHierarchy.get() else throw GradeException(LoyaltyExceptionCode.GRADE_HIERARCHY_NOT_FOUND)
    }

    /**事件计算*/
    fun getEffectiveOneCache(id: Long, date: ZonedDateTime): GradeHierarchy = getEffectiveOne(id, date)

    fun getEffectiveOneCache(id: Long): GradeHierarchy =
        getEffectiveOne(id, DateUtils.formatMinutesCacheKey(ZonedDateTime.now()))

}