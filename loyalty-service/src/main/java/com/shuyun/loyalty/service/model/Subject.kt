package com.shuyun.loyalty.service.model

import com.alibaba.druid.util.StringUtils
import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.entity.api.constants.PublishStatusEnum
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.util.ModelInitUtil.copyPropertiesIgnoreNull
import io.swagger.v3.oas.annotations.media.Schema
import javax.persistence.*

@Schema(title = "主体")
@Table(name = "data.loyalty.manager.subject")
@DataServiceModel
data class Subject(

    @Schema(title = "名称", type = "String")
    @Column
    var name: String? = null,//主体名称

    @Schema(title = "后端使用计划ID，前端勿用", type = "String")
    @Column
    var planVersionId: Long? = null,//主体归属计划的ID

    @Schema(title = "Data Type", type = "String")
    @Column
    var dataType: String? = null,

    @Schema(title = "主体ID", type = "String")
    @Column
    var id: Long? = null,

    @Schema(title = "属性", type = "object")
    @Transient
    var customizedPropertyList: List<CustomizedProperty>? = null,//属性

    @Transient
    @Schema(title = "属性模板", type = "object")
    var propertyTemplateList: List<PropertyTemplate>? = null,

    @Schema(title = "时机", type = "object")
    @Transient
    var eventTypeList: List<EventType>? = null,//时机

    @Schema(title = "账户类型", type = "object")
    @Transient
    var pointAccountTypeList: List<PointAccountType>? = null,//账户类型

    @Schema(title = "等级体系", type = "object")
    @Transient
    var gradeHierarchyList: List<GradeHierarchy>? = null,//等级体系

    @Schema(title = "勋章体系", type = "object")
    @Transient
    var medalHierarchyList: List<MedalHierarchy>? = null,// 勋章体系

    @Schema(title = "状态", type = "String")
    @Enumerated(EnumType.STRING)
    @Column
    var status: EnableStatusEnum? = null,

    @Schema(title = "前端排序号", type = "Int")
    @Column
    var sort: Int? = null,

    @Schema(title = "合并模型", type = "String")
    @Column
    var mergeFqn: String? = null,
    @Schema(title = "是否合卡", type = "Boolean")
    @Column
    var hasMerge: Boolean? = null,

    @Schema(title = "主体下的导入配置", type = "Object")
    @Transient
    var importFileConfig: ImportFileConfig? = null,

    @Schema(title = "来源主体下的导入配置", type = "Object")
    @Transient
    var sourceImportFileConfig: ImportFileConfig? = null,

    @Schema(title = "来源主体Data Type", type = "String")
    @Transient
    var sourceDataType: String? = null

) : BaseDataServiceModel() {
    override fun copyToOldOne(old: BaseDataServiceModel, backup: Boolean) {
        old as Subject
        if (backup) {
            //已发布
            if (EnableStatusEnum.DRAFT == old.status) {
                status = EnableStatusEnum.DRAFT
            } else {
                if (!arrayOf(EnableStatusEnum.ENABLED, EnableStatusEnum.DISABLED).contains(status)) {
                    throw IllegalArgumentException("已发布的计划的非草稿状态的主体，状态只能为启用或者禁用")
                }
                if (!StringUtils.equals(dataType, old.dataType)) {
                    throw IllegalArgumentException("已发布的计划的非草稿状态的主体，不允许修改Datatype")
                }

            }
        } else {
            //未发布
            status = EnableStatusEnum.DRAFT
        }
        planVersionId = null
        copyPropertiesIgnoreNull(this, old)
    }

    /**校验账户类型或者等级体系是否存在*/
    fun validatePointAccountTypeOrGradeHierarchyExist(): Boolean {
        var pointAccountTypeOrGradeHierarchyCount = 0
        if (pointAccountTypeList is List) {
            pointAccountTypeOrGradeHierarchyCount +=
                pointAccountTypeList!!.filter {
                    PublishStatusEnum.FILED != it.status
                }.size
        }
        if (gradeHierarchyList is List) {
            pointAccountTypeOrGradeHierarchyCount +=
                gradeHierarchyList!!.filter {
                    PublishStatusEnum.FILED != it.status
                }.size
        }
        if (medalHierarchyList is List) {
            pointAccountTypeOrGradeHierarchyCount += medalHierarchyList!!.filter {
                PublishStatusEnum.FILED != it.status
            }.size
        }
        return pointAccountTypeOrGradeHierarchyCount != 0
    }

    /**校验主体是否存在时机、时机或者主体属性是否存在*/
    fun validateEventTypeAndCustomizedPropertyExist(): Boolean {
        var customizedPropertyCount = 0
        if (customizedPropertyList is List) {
            customizedPropertyCount += customizedPropertyList!!.filter {
                EnableStatusEnum.DISABLED != it.status
            }.size
        }
        val notDisabledEvent = eventTypeList?.filter { eventType ->
            EnableStatusEnum.DISABLED != eventType.status
        }
        if (notDisabledEvent !is List || notDisabledEvent.isEmpty()) {
            return false
        } else if (customizedPropertyCount == 0) {
            notDisabledEvent.forEach { eventType ->
                if (eventType.customizedPropertyList !is List || eventType.customizedPropertyList!!.none {
                        EnableStatusEnum.DISABLED != it.status
                    }) {
                    return false
                }
            }
        }
        return true
    }
}