package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.datamodel.ProcessRecord
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

@Component
class ProcessRecordRepository: DataModelRepository<ProcessRecord>() {
    private val log = LogManager.getLogger(ProcessRecordRepository::class.java)
    override fun log(): Logger = this.log
}