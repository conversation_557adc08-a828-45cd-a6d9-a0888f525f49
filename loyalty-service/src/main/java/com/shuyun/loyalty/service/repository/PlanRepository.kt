package com.shuyun.loyalty.service.repository

import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.loyalty.service.meta.PlanStatusEnum
import com.shuyun.loyalty.service.model.Plan
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import com.shuyun.pip.util.converter.ZonedDateTime2StringConverter
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Component
import java.time.ZonedDateTime
import java.util.*

@Component
class PlanRepository : DataModelCarryRepository<Plan>() {

    private val log = LogManager.getLogger(PlanRepository::class.java)
    override fun log(): Logger = log


    fun findByIdAndDisabled(id: Long, disabled:Boolean, statuses: List<PlanStatusEnum> = emptyList()): List<Plan> {
        val params = HashMap<String, Any?>()
        params["id"] = id
        params["disabled"] = disabled
        if (statuses.isNotEmpty()) {
            val list = ArrayList<HashMap<String,Any?>>()
            statuses.forEach {
                val paramsItem = HashMap<String, Any?>()
                paramsItem["status"] = it.name
                list.add(paramsItem)
            }
            params["\$or"] = list
        }
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun selfDefinedFindById(id: Long): List<Plan> {
        val params = HashMap<String, Any?>()
        params["id"] = id
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findByIdAndStatusInAndDisabled(id: Long, statusList: List<PlanStatusEnum>, disabled:Boolean): Optional<Plan> {
        val params = HashMap<String, Any?>()
        val list = ArrayList<HashMap<String,Any?>>()
        if(statusList.size ==1) {
            params["status"] = statusList[0].name
        } else {
            statusList.forEach {
                val paramsItem = HashMap<String, Any?>()
                paramsItem["status"] = it.name
                list.add(paramsItem)
            }
            params["\$or"] = list
        }
        params["id"] = id
        params["disabled"] = disabled

        return findOneByFilter(JsonUtils.toJson(params))
    }

    fun findByNameAndDisabled(name: String,disabled:Boolean): List<Plan> {
        val params = HashMap<String, Any?>()
        params["name"] = name
        params["disabled"] = disabled
        val paramsItem = HashMap<String, Any?>()
        paramsItem["\$ne"] = PlanStatusEnum.FILED
        params["status"] = paramsItem
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findByNameContainingAndStatusInAndDisabledOrderByCreateTimeDesc(name:String,statusList: List<PlanStatusEnum>,disabled:Boolean,pageable: Pageable): Page<Plan> {
        val params = HashMap<String, Any?>()
        val list = ArrayList<HashMap<String,Any?>>()
        if(statusList.size ==1) {
            params["status"] = statusList[0].name
        } else {
            statusList.forEach {
                val paramsItem = HashMap<String, Any?>()
                paramsItem["status"] = it.name
                list.add(paramsItem)
            }
            params["\$or"] = list
        }

        val paramsLike = HashMap<String, Any?>()
        paramsLike["\$like"] = "%"+name+"%"
        params["name"] = paramsLike

        params["disabled"] = disabled

        return findPageByFilter(JsonUtils.toJson(params),"{\"createTime\": \"desc\"}",pageable)
    }

    fun findByStatusInAndDisabledOrderByCreateTimeDesc(statusList: List<PlanStatusEnum>,disabled:Boolean,pageable: Pageable): Page<Plan> {
        val params = HashMap<String, Any?>()
        val list = ArrayList<HashMap<String,Any?>>()
        if(statusList.size ==1) {
            params["status"] = statusList[0].name
        } else {
            statusList.forEach {
                val paramsItem = HashMap<String, Any?>()
                paramsItem["status"] = it.name
                list.add(paramsItem)
            }
            params["\$or"] = list
        }
        params["disabled"] = disabled
        return findPageByFilter(JsonUtils.toJson(params),"{\"createTime\": \"desc\"}",pageable)
    }



    fun findAllPublishByPlan(statusList: List<PlanStatusEnum>,disabled:Boolean): List<Plan> {
        val params = HashMap<String, Any?>()
        val list = ArrayList<HashMap<String,Any?>>()
        if(statusList.size ==1) {
            params["status"] = statusList[0].name
        } else {
            statusList.forEach {
                val paramsItem = HashMap<String, Any?>()
                paramsItem["status"] = it.name
                list.add(paramsItem)
            }
            params["\$or"] = list
        }
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params),"")
    }

    fun findByStatusInAndDisabled(statusList: List<PlanStatusEnum>,disabled:Boolean): List<Plan> {
        val params = HashMap<String, Any?>()
        val list = ArrayList<HashMap<String,Any?>>()
        if(statusList.size ==1) {
            params["status"] = statusList[0].name
        } else {
            statusList.forEach {
                val paramsItem = HashMap<String, Any?>()
                paramsItem["status"] = it.name
                list.add(paramsItem)
            }
            params["\$or"] = list
        }
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params),"{\"publishedTime\": \"desc\"}")
    }

    //@Query("select p.* from plan p where p.disabled=0 and p.id = ?1 and (p.status='PUBLISHED' OR p.status = 'FILED') and p.published_time<=?2 and (p.filed_time>=?2 or p.filed_time is null)", nativeQuery = true)
    fun findByIdAndDate(id:Long, date: ZonedDateTime): Optional<Plan> {

        val sql = "select p.id,p.name,p.description,p.sortType,p.filedTime,p.publishedTime,p.backup,p.status,p.versionId from data.loyalty.manager.plan p where p.disabled=0 and p.id = :id and (p.status='PUBLISHED' OR p.status = 'FILED') " +
                "and p.publishedTime<=:publishedTime and (p.filedTime>=:publishedTime or p.filedTime is null)"

        val params = HashMap<String, Any>()
        params["id"] = id
        params["publishedTime"] = ZonedDateTime2StringConverter().convert(date)!!

        return executeOne(sql, params)
    }
    

    fun findByVersionId(versionId: Long): Optional<Plan> {
        val params = HashMap<String, Any?>()
        params["versionId"] = versionId
        return findOneByFilter(JsonUtils.toJson(params))
    }


    fun findSubjectIdByPlanId(subjectId: Long): Long?{
        val sql = " select a1.id,a1.name,a1.description,a1.sortType,a1.filedTime,a1.publishedTime,a1.backup,a1.status,a1.versionId " +
                "from data.loyalty.manager.plan a1 " +
                "inner join data.loyalty.manager.subject b on b.planVersionId=a1.versionId where b.id=:id"
        val params = HashMap<String, Any>()
        params["id"] = subjectId
        val optional = executeOne(sql, params)
        if(optional.isPresent) {
            return optional.get().id
        }
        return null
    }


    fun findByName(name: String): List<Plan> {
        val params = HashMap<String, Any?>()
        params["name"] = name
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findFxReferenceTimeByIdAndDate(id: Long, date: ZonedDateTime,fqn: String): Optional<Plan> {
        val sql = "select p.gradeReferenceTime, p.pointReferenceTime, p.medalReferenceTime " +
                "from " + fqn + " h " +
                "inner join data.loyalty.manager.subject s on h.subjectVersionId = s.versionId " +
                "inner join data.loyalty.manager.plan p on s.planVersionId = p.versionId " +
                "where p.disabled=0 and h.disabled=0 and s.disabled=0 and h.id = :id and (p.status='PUBLISHED' OR p.status = 'FILED')  and h.status = 'PUBLISHED' " +
                "and p.publishedTime<=:date and (p.filedTime>=:date or p.filedTime is null) order by h.subjectVersionId desc limit 1"
        val params = HashMap<String, Any>()
        params["id"] = id
        params["date"] = ZonedDateTime2StringConverter().convert(date)!!
        log.trace("find by id and date, {}", {JsonUtils.toJson(params)})
        return executeOne(sql, params)
    }


    /**
     * 查询历史是否开启过积分上限规则
     */
    fun findSendLimitRuleSwitch(id: Long): Boolean {
        val sql ="select id from data.loyalty.manager.plan where id=:id and `status`='PUBLISHED' and sendLimitRuleSwitch=true limit 1"
        val resultResponse = DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.execute(sql, mapOf("id" to id)) }
        val resultObject = resultResponse!!.data
        return !resultObject.isNullOrEmpty()
    }
}