package com.shuyun.loyalty.service.extension

import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.lite.context.GlobalContext
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.exception.AbstractPipException
import com.shuyun.pip.exception.ExceptionConstant
import com.shuyun.pip.exception.ExceptionUtils
import com.shuyun.pip.exception.ResponseErrorData
import com.shuyun.pip.i18n.LocaleI18nContextHolder
import org.slf4j.LoggerFactory

fun ResponseErrorData.toJson(): String = JsonUtils.toJson(this)

fun AbstractPipException.buildResponseEntity(): ResponseErrorData {
    val msg = toMsgByErrorCode(trackErrorCode, args, code, msg, message)
    return ResponseErrorData(
        trackErrorCode,
        msg,
        (module ?: ExceptionUtils.ERROR_RESPONSE_MODULE),
        (service ?: GlobalContext.serviceName()),
        (url ?: ExceptionUtils.ERROR_RESPONSE_HELP_URL),
        args
    )
}

fun Throwable.isSdkDuplicate(): Boolean {
    if (this is SdkException) {
        return this.error_code == "151110" || this.error_code == "151111"
    }
    return false
}


private val logger = LoggerFactory.getLogger(AbstractPipException::class.java)

private fun toMsgByErrorCode(
    trackErrorCode: String,
    args: Array<*>? = null,
    code: Int? = null,
    message: String? = null,
    defaultMessage: String? = null
): String {
    return try {
        val default = defaultMessage ?: message
        var msg = LocaleI18nContextHolder.getMessage(trackErrorCode, args, default, LocaleI18nContextHolder.getLocale())
        if (msg.isEmpty()) {
            if (trackErrorCode == defaultMessage || code != null) {
                msg = LocaleI18nContextHolder.getMessage(code.toString(), args, defaultMessage)
            }
            if (msg.isEmpty()) {
                msg = LocaleI18nContextHolder.getMessage(ExceptionConstant.COMMON_BASE_UNKNOWN_EXCEPTION)
            }
        }
        msg
    } catch (e: Exception) {
        logger.warn(
            "对异常码进行国际化处理失败，缺少该异常码的国际化定义:{}，将返回空，可能会导致没有异常信息提示",
            code,
            e
        )
        LocaleI18nContextHolder.getMessage(ExceptionConstant.COMMON_BASE_UNKNOWN_EXCEPTION)
    }
}
