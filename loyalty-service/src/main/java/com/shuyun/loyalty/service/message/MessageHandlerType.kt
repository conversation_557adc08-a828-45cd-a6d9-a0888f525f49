package com.shuyun.loyalty.service.message

import com.shuyun.loyalty.service.util.ConstantValue.RULE_EVENT_FQN_MATCH

enum class MessageHandlerType(val fqn: String, val handler: String, val message: String, val desc: String) {
    GRADE_UPGRADE_HANDLER(RULE_EVENT_FQN_MATCH,"GradeHierarchyUpgradeHandler", "", "会员等级计算"),
    GRADE_OVER_HANDLER("event.loyalty.grade.over.event", "GradeHierarchyOverDegradeHandler", "GradeHierarchyDegradeEventMessage", "会员等级降级和重算计算"),
    GRADE_MERGE_HANDLER("event.loyalty.grade.merge.event", "GradeHierarchyMergeDegradeHandler", "GradeHierarchyMergeMessage", "会员等级合卡重算计算"),

    MEDAL_OBTAIN_HANDLER(RULE_EVENT_FQN_MATCH, "MedalHierarchyObtainHandler", "", "会员勋章获取"),
    MEDAL_RECALCULATE_HANDLER(RULE_EVENT_FQN_MATCH, "MedalHierarchyRecalculateHandler", "", "会员勋章重算"),
    MEDAL_OVER_HANDLER("event.loyalty.medal.over.event", "MedalHierarchyOverHandler", "MedalHierarchyOverMessage", "会员勋章过期计算"),


    GRADE_DEGRADE_OR_RECAL_HANDLER(RULE_EVENT_FQN_MATCH, "GradeHierarchyDegradeOrRecalHandler", "", "会员等级降级和重算分发计算"),

    ;

    companion object {
        fun getEventTypeByFqn(fqn: String): MessageHandlerType? = entries.firstOrNull { it.fqn.equals(fqn, ignoreCase = true)}

        private fun matched(fqn: String) = entries.any { it.fqn.equals(fqn, true) }

        fun isMessage(fqn: String) = matched(fqn)

        fun isEvent(fqn: String) = !isMessage(fqn)
    }
}