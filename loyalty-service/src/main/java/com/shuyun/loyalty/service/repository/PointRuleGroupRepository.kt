package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.model.PointRuleGroup
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.util.*

@Repository
interface PointRuleGroupRepository : JpaRepository<PointRuleGroup, Long>, JpaSpecificationExecutor<PointRuleGroup> {

    fun save(pointRuleGroup: PointRuleGroup)
    fun findByIdAndDisabled(id:Long,disabled:Boolean) : Optional<PointRuleGroup>

    @Query("select g from PointRuleGroup g where g.planId = ?1 and g.pointAccountTypeId = ?2 and g.eventTypeId = ?3 and g.scoreType = ?4 and g.disabled=false and g.fileStatus=false and g.startTime <= ?5 and (g.endTime is null or g.endTime >= ?5) and (g.processId is null or g.processStatus = 'PASS')")
    fun findGroup(planId: Long, pointAccountTypeId: Long, eventTypeId: Long, scoreType: EventOperationEnum, startTime: Date): List<PointRuleGroup>

    @Query("select g from PointRuleGroup g where g.planId = ?1 and g.pointAccountTypeId = ?2 and g.eventTypeId = ?3 and g.scoreType = ?4 and g.disabled=false and g.fileStatus=false and (g.processId is null or g.processStatus = 'PASS')")
    fun findGroup(planId: Long, pointAccountTypeId: Long, eventTypeId: Long, scoreType: EventOperationEnum): List<PointRuleGroup>

    @Query("select count(id) from point_rule_group where plan_id=?1 and point_account_type_id=?2 and group_name=?3 and disabled= false ",nativeQuery=true)
    fun countGroupName(planId:Long,pointAccountTypeId:Long,groupName:String):Long

    @Query("select count(id) from point_rule_group where plan_id=?1 and point_account_type_id=?2 and id!=?3 and group_name=?4 and disabled= false ",nativeQuery=true)
    fun countGroupName(planId:Long, pointAccountTypeId:Long, id:Long, groupName:String):Long

    fun findByPointAccountTypeId(accountId:Long):List<PointRuleGroup>


    @Query("select b.* from point_rule_group b where b.file_status=0 and b.disabled=0 and (b.end_time >= sysdate() or b.end_time is null)  ",nativeQuery=true)
    fun findValidPointRuleGroupList():List<PointRuleGroup>

    @Modifying
    @Query("update point_rule_group set used_point = ifnull(used_point, 0) + ?1 where id = ?2", nativeQuery=true)
    fun updateUsedPoint(point: BigDecimal, id: Long)
}
