package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.model.RemindConfig
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

@Component
class RemindConfigRepository : DataModelCarryRepository<RemindConfig>() {

    private val log = LogManager.getLogger(RemindConfigRepository::class.java)
    override fun log(): Logger = log

    fun findParentVersionId(parentVersionId: Long, disabled: Boolean): List<RemindConfig> {
        val params = HashMap<String, Any?>()
        params["parentVersionId"] = parentVersionId
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params))
    }


    fun findByParentVersionIdAndDisabled(parentVersionId: Long, disabled: Boolean): List<RemindConfig> {
        val params = HashMap<String, Any?>()
        params["parentVersionId"] = parentVersionId
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params))
    }
}

