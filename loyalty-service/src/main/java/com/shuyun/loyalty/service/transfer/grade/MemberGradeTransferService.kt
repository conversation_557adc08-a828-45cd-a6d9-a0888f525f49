package com.shuyun.loyalty.service.transfer.grade

import com.google.common.util.concurrent.ThreadFactoryBuilder
import com.shuyun.loyalty.entity.api.constants.RequestType
import com.shuyun.loyalty.entity.api.request.MemberGradeModifyRequest
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.service.datamodel.InterfaceRecord
import com.shuyun.loyalty.service.datamodel.MemberGrade
import com.shuyun.loyalty.service.exception.*
import com.shuyun.loyalty.service.extension.toZonedDateTime
import com.shuyun.loyalty.service.kafka.GradeModifyMessage
import com.shuyun.loyalty.service.kafka.PointOrGradeModify
import com.shuyun.loyalty.service.meta.TypeEnum
import com.shuyun.loyalty.service.model.GradeDefinition
import com.shuyun.loyalty.service.model.TargetGrade
import com.shuyun.loyalty.service.service.PlanBaseService
import com.shuyun.loyalty.service.util.ModelInitUtil
import com.shuyun.loyalty.service.util.PropertyUtils
import com.shuyun.loyalty.service.util.VisitorInfoUtil
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.i18n.LocaleI18nContextHolder
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.time.ZonedDateTime
import java.util.*
import java.util.concurrent.Future
import java.util.concurrent.SynchronousQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import kotlin.concurrent.withLock

/**
 */
@Component
class MemberGradeTransferService {

    private val log = LogManager.getLogger(MemberGradeTransferService::class.java)

    companion object {
        val fixedThreadPool = ThreadPoolExecutor(30, 30,
            30L, TimeUnit.SECONDS,
            SynchronousQueue(),
            ThreadFactoryBuilder().setNameFormat("loyalty-manager-modifyMemberGrade-%d").build(),
            ThreadPoolExecutor.CallerRunsPolicy())
    }

    @Autowired(required = false)
    private lateinit var pointOrGradeModify: PointOrGradeModify

    @Autowired
    private lateinit var memberGradeService: BaseMemberGradeService

    @Autowired
    private lateinit var planBaseService: PlanBaseService

    fun modifyMemberGrade(memberGradeModifyRequest: MemberGradeModifyRequest, requestType: RequestType, forbiddenPort: ForbiddenPort, hashMerge: Boolean = false){
        log.info("接口调用变更等级开始, {} {}", JsonUtils.toJson(memberGradeModifyRequest), requestType)
        val start = System.currentTimeMillis()
        val locker = ApplicationContextHolder.getBean(Locker::class.java)
        val gradeLock = locker.getLock("grade_calculate_${memberGradeModifyRequest.gradeHierarchyId}-${memberGradeModifyRequest.memberId}")
        gradeLock.withLock {
            val overdue = memberGradeModifyRequest.overdueDate
            if(overdue is ZonedDateTime && overdue.isBefore(ZonedDateTime.now())) throw GradeException(LoyaltyExceptionCode.GRADE_OVERDUE_TIME_ERROR)

            val plan = planBaseService.findPlanByAccountOrHierarchyId(
                memberGradeModifyRequest.gradeHierarchyId!!,
                TypeEnum.GRADE
            ) ?: throw IllegalArgumentException("等级体系ID(${memberGradeModifyRequest.gradeHierarchyId})关联的计划不存在")
            val subject = plan.subjectList!!.first()
            val gradeHierarchy = subject.gradeHierarchyList!!.first()
            val gradeDefinitions = gradeHierarchy.gradeDefinitions!!

            //查询当前等级
            val originalMemberGrade = memberGradeService.findOneByMemberGrade(memberGradeModifyRequest)

            if (originalMemberGrade != null) {
                if (gradeHierarchy.id != originalMemberGrade.gradeHierarchyId || plan.id != originalMemberGrade.planId || memberGradeModifyRequest.memberId != originalMemberGrade.memberId) {
                    throw GradeException(LoyaltyExceptionCode.EVENT_NOT_MATCH)
                }
            }

            //匹配当前等级
            val originalGradeDefinition = gradeDefinitions.find { it.id == originalMemberGrade?.currentGradeDefinitionId }
            //匹配变更等级
            var targetGradeDefinition = gradeDefinitions.find { it.id == memberGradeModifyRequest.gradeDefinitionId }
            if (memberGradeModifyRequest.gradeDefinitionId == -1L)
                targetGradeDefinition = GradeDefinition().apply {
                    this.id = -1
                    this.sort = 0
                }
            //等级找不到
            targetGradeDefinition?: throw GradeException(LoyaltyExceptionCode.TARGET_GRADE_NOT_FOUND)
            //判断等级动作
            val recordType = memberGradeService.findRecordType(originalGradeDefinition, targetGradeDefinition)

            //插入幂等表
            var interfaceRecordFuture: Future<InterfaceRecord>? = null
            if (!hashMerge) interfaceRecordFuture = memberGradeService.saveInterfaceRecordFuture(memberGradeModifyRequest)
            //判断黑名单类型
            val forbiddenOperation = memberGradeService.findForbiddenOperation(recordType)

            var futureMember: Future<Unit>? = null
            //查询会员是否存在
            if (PropertyUtils.getCheckMemberExist()) {
                futureMember = memberGradeService.findFutureMemberFuture(memberGradeModifyRequest, subject)
            }
            //查询黑名单
            val checklistFuture = memberGradeService.findChecklistFuture(
                memberGradeModifyRequest,
                subject,
                gradeHierarchy,
                forbiddenOperation,
                forbiddenPort,
                subject.versionId!!
            )

            val interfaceRecord = try {
                interfaceRecordFuture?.get()
            } catch (ex: Throwable) {
                log.warn("插入幂等表失败",ex)
                if(!ex.message.isNullOrEmpty() && ex.message!!.indexOf("重复") > -1) throw GradeException(LoyaltyExceptionCode.TOKEN_REPEATED) else throw GradeException(LoyaltyExceptionCode.UNKNOWN_EXCEPTION)
            }
            try {
                futureMember?.get()
            } catch (e: Throwable) {
                interfaceRecord?.delete()
                throw MemberException(LoyaltyExceptionCode.MEMBER_NOT_FOUND,e)
            }
            if (checklistFuture.get()) {
                interfaceRecord?.delete()
                throw MemberException(LoyaltyExceptionCode.BLACKLIST_EXIST, LocaleI18nContextHolder.getMessage("MODIFY_MEMBER_GRADE"))
            }

            val targetGrade = TargetGrade().apply {
                this.effectForever = memberGradeModifyRequest.overdueDate == null
                this.effectTime = if (memberGradeModifyRequest.useOriginalEffectTime == true) {
                    originalMemberGrade?.effectDate?.toInstant()?.let { Date.from(it) } ?: Date()
                } else {
                    Date()
                }
                this.overdueTime = if(null == overdue) null else Date.from(overdue.toInstant())
                this.targetGradeDefinition = targetGradeDefinition
            }
            val memberGrade = MemberGrade()
            if (originalMemberGrade != null) {
                ModelInitUtil.copyPropertiesIgnoreNull(originalMemberGrade, memberGrade)
            } else {
                memberGrade.created = Date().toZonedDateTime()
            }
            memberGrade.build(subject, plan, gradeHierarchy, targetGrade, memberGradeModifyRequest.memberId!!)
            val memberGradeFuture: Future<*>
            //同步变更等级
            if(RequestType.SYNC == requestType) {
                if(memberGrade.id == null) {
                    //插入等级表
                    memberGrade.id = Uuid.uuid
                    memberGradeFuture = memberGradeService.saveMemberGradeFuture(memberGrade,memberGradeModifyRequest)
                } else memberGradeFuture = memberGradeService.updateMemberGradeFuture(memberGrade,memberGradeModifyRequest)

                //插入等级明细表
                val operator = VisitorInfoUtil.username.get()
                val memberGradeRecordFuture = memberGradeService.saveMemberGradeRecordFuture(
                    gradeHierarchy,
                    memberGradeModifyRequest,
                    originalMemberGrade,
                    memberGrade,
                    recordType,
                    operator
                )
                //4异步获取值
                memberGradeFuture.get()
                memberGradeRecordFuture.get()
            }

            //异步变更等级，放kafka
            if (RequestType.SYNC != requestType) {
                val gradeModifyMessage = GradeModifyMessage()
                gradeModifyMessage.memberGrade = memberGrade
                gradeModifyMessage.memberGradeModifyRequest = memberGradeModifyRequest
                gradeModifyMessage.originalMemberGrade = Optional.ofNullable(originalMemberGrade)
                gradeModifyMessage.recordType = recordType
                pointOrGradeModify.gradeModify(gradeModifyMessage)
            }
            log.info("接口调用变更等级完成, 耗时{}ms", (System.currentTimeMillis() - start))
        }
    }


}
