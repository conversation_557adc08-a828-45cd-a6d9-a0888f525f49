package com.shuyun.loyalty.service.calculate.sendlimit

import com.shuyun.loyalty.entity.bo.SendLimitRuleCalcResult
import com.shuyun.loyalty.service.calculate.params.SendLimitParams
import com.shuyun.loyalty.service.datamodel.MemberPointSendLimitCalc
import com.shuyun.loyalty.service.meta.PointSendLimitType
import com.shuyun.loyalty.service.model.PointSendLimitRule
import java.math.BigDecimal
import kotlin.properties.Delegates

interface IMemberPointSendLimitFilter {

    fun before(dto: SendLimitFilterDTO)

    fun filter(dto: SendLimitFilterDTO)

    fun after(dto: SendLimitFilterDTO)

    fun pointSendLimitType(): PointSendLimitType

    fun order(): Int

    class SendLimitFilterDTO {
        // 公共参数,单一filter控制是否继续
        var isContinue: Boolean = true
        // 公共参数
        var sendLimitParams: SendLimitParams? = null
        // 是否重算
        var isRecalculate: Boolean = false
        // 是否重算新加的积分（原没有获得
        var isReissue: Boolean = false
        // 仅检查次数
        var onlyFilterCount: Boolean = false
        // 待计算 引用ID, 真实计算具体 时机、规则组、规则、会员 时才赋值
        var refId: String? = null
        // 待计算积分
        var pointValue: BigDecimal? = null
        // 待计算规则
        var sendLimitRuleList: ArrayList<PointSendLimitRule>? = null
        // 待计算已经累计上限
        var memberPointSendLimitCalc: MemberPointSendLimitCalc? = null
        // 拦截后剩余积分
        var remainingPointValue: BigDecimal? = null

        // 时机入参
        var eventTypeId: Long? = null
        // 规则组入参
        var groupId: Long? = null
        // 会员积分账号入参
        var memberPointId: String? = null
        // 积分账号的拦截规则入参
        var sendLimitRule: String? = null
        // 拦截结果
        var sendLimitRuleCalcResult: SendLimitRuleCalcResult by Delegates.notNull()
    }
}