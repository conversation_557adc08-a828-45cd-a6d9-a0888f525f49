package com.shuyun.loyalty.service.model

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.shuyun.loyalty.service.datamodel.MemberMedal
import com.shuyun.loyalty.service.message.medal.AbstractMedalMessage
import com.shuyun.loyalty.service.meta.ValidTimeTypeEnum
import com.shuyun.loyalty.service.service.SubjectBaseService
import com.shuyun.loyalty.service.util.ExpressionIdentUtil
import com.shuyun.loyalty.service.util.ModelInitUtil
import com.shuyun.pip.ApplicationContextHolder
import org.apache.logging.log4j.LogManager
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime

class MatchMedal {

    @JsonIgnore
    private val log = LogManager.getLogger(MatchMedal::class.java)

    var matchMedal: MedalDefinition? = null

    var overdueTime: LocalDateTime? = null

    var effectForever: Boolean = false

    var effectRuleId: Long? = null

    var effectRuleName: String? = null

    var effectEventTypeId: Long? = null

    var effectEventTypeName: String? = null

    var channelType: String? = null

    var effectDate: ZonedDateTime? = null

    fun matchMedalRule(
        eventType: EventType, medalRule: MedalRule, medalDefinition: MedalDefinition, groupName: String,
        message: AbstractMedalMessage, memberMedal: MemberMedal? = null
    ): MatchMedal? {
        val eventTypeId = eventType.id!!
        val subjectVersionString = message.subjectVersionId.toString()
        val subject = ApplicationContextHolder.getBean(SubjectBaseService::class.java).findById(subjectVersionString)
        message.eventTypeId = eventTypeId
        val validate = ExpressionIdentUtil.eppById(medalRule.expressionFxId!!, message.event(),
            JsonNodeFactory.instance.objectNode().put("subject", subject.get().dataType))
        log.debug(
            "进行勋章计算fxId:{},subject:{},result:{}",
            medalRule.expressionFxId,
            subject.get().dataType,
            validate
        )
        if (validate.toString().toBoolean()) {
            try {
                val overdueTime = medalRule.calculateOverdueTime(message.event(), subject.get().dataType,memberMedal)
                if (!updateEffectTime(medalDefinition, overdueTime, groupName, eventType)) return null
                this.effectDate = if (medalRule.effectValidTimeType == ValidTimeTypeEnum.RESET) {
                    ZonedDateTime.now()
                } else memberMedal?.effectDate
                this.channelType = message.findChannelType()
                this.effectRuleId = medalRule.id
                return this
            } catch (e: Exception) {
                log.error("时间计算错误:", e)
            }
        }
        return null
    }

    private fun updateEffectTime(medalDefinition: MedalDefinition, overdueTime: LocalDateTime?, groupName: String,
                                 eventType: EventType): Boolean {
        log.debug("勋章有效期:{}, 是否永久:{}", overdueTime, this.effectForever)
        this.matchMedal = medalDefinition
        var result = false
        overdueTime?.let {
            if (!this.effectForever && (null == this.overdueTime || this.overdueTime!!.isBefore(overdueTime))) {
                this.overdueTime = overdueTime
                this.effectRuleName = groupName
                this.effectEventTypeName = eventType.name
                result = true
            } else {
                result = false
            }
        } ?:run {
            this.overdueTime = null
            this.effectForever = true
            this.effectRuleName = groupName
            this.effectEventTypeName = eventType.name
            result = true
        }
        return result
    }

    fun moreThan(matchMedal: MatchMedal?): Boolean {
        if (matchMedal == null || this.effectForever) return true
        if (matchMedal.effectForever) return false
        return this.overdueTime!!.isAfter(matchMedal.overdueTime)
    }

    fun initMemberMedal(originMemberMedal: MemberMedal?, message: AbstractMedalMessage): MemberMedal {
        val medalHierarchyId = message.medalHierarchyId
        val memberId = message.memberId()
        val subjectVersionId = message.subjectVersionId
        val subject = ApplicationContextHolder.getBean(SubjectBaseService::class.java).findByVersionId(subjectVersionId)

        val memberMedal = MemberMedal()
        if (originMemberMedal != null) {
            ModelInitUtil.copyPropertiesIgnoreNull(originMemberMedal, memberMedal)
        } else {
            memberMedal.created = ZonedDateTime.now()
            memberMedal.effectDate = ZonedDateTime.now()
        }
        memberMedal.planId = message.plan.id
        memberMedal.planName = message.plan.name
        memberMedal.medalHierarchyId = medalHierarchyId
        memberMedal.medalHierarchyName = message.medalHierarchy.name
        memberMedal.memberId = memberId
        memberMedal.medalDefinitionId = this.matchMedal!!.id
        memberMedal.medalDefinitionName = this.matchMedal!!.name
        memberMedal.overdueDate = if (this.effectForever) null else this.overdueTime!!.atZone(ZoneId.systemDefault())
        memberMedal.subjectFqn = subject.dataType
        memberMedal.disabled = false
        return memberMedal
    }
}