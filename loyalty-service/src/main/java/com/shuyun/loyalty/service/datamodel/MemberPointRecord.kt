package com.shuyun.loyalty.service.datamodel

import com.fasterxml.jackson.annotation.JsonProperty
import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.bo.SendLimitRuleCalcResult
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.model.LoyaltyEventBusMessage
import com.shuyun.loyalty.service.service.MemberPointRecordService
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.pip.ApplicationContextHolder
import java.math.BigDecimal
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.Table
import javax.persistence.Transient


/** 积分操作变更记录 */
@DataServiceModel
@FqnVariableModel
@Table(name = "data.loyalty.member.account.PointRecord{*}")
class MemberPointRecord(): SubMemberPoint(), LoyaltyEventBusMessage {


    /** 触发动作 */
    @Column
    lateinit var recordType: FSMPointEvent

    /** 业务模板描述信息，时机名称+规则组名称 */
    @Column
    var recordSourceDetail: String? = null

    /** 积分生效时间，如果是锁定、冻结则没有有效期 */
    @Column
    var effectiveDate: ZonedDateTime? = null

    /** 积分过期时间，如果是锁定、冻结则没有有效期 */
    @Column
    var overdueDate: ZonedDateTime? = null

    /** 操作人、操作原记录id，订单id，规则id，规则组id，参与规则组条件、参与规则计算数据，interfaceSource：接口调用来源 */
    @Column
    var extralInfo: String? = null

    /** 用于显示描述记录变更信息 */
    @Column
    var recordDetail: String? = null

    /** recordDetail为系统自动根据规则生成的明细信息。而desc为可接口，或特殊场景添加ras */
    @Column
    var desc: String? = null

    /** 操作人，默认系统 */
    @Column
    var operator: String = ConstantValue.DEFAULT_OPERATOR

    /** 操作人用户ID */
    @Column
    var operatorId: String = ConstantValue.DEFAULT_OPERATOR

    /**数据来源*/
    @Column
    var channel: String? = null
        set(value) {
            field = if (value == "null") null else value
        }

    /** 最后一次修改时间 */
    @Column
    var modified: ZonedDateTime = ZonedDateTime.now()

    /**事件ID*/
    @Column
    var key: String? = null

    /**规则组*/
    @Column
    var ruleGroup: String? = null

    /**变更方式*/
    @Column
    var changeMode: String? = null

    /**计划名称*/
    @Column
    var planName: String? = null

    /**积分帐号名称*/
    @Column
    var pointPlanName: String? = null

    /**原单ID*/
    @Column
    var traceId: String? = null

    /** 总积分 */
    @Column
    var totalPoint: BigDecimal = BigDecimal.ZERO

    /**时机名称*/
    @Column
    var eventTypeName: String? = null

    /**活动ID*/
    @Column
    var actionId: String? = null

    /**活动名称*/
    @Column
    var actionName: String? = null

    /**活动节点ID*/
    @Column
    var actionNodeId: String? = null

    /**活动节点名称*/
    @Column
    var actionNodeName: String? = null

    /** 规则Id */
    @Column
    var ruleId: Long? = null

    /**规则名称*/
    @Column
    var ruleName: String? = null

    /**积分状态，对账使用*/
    @Column
    var status: PCStatus? = null

    /**店铺ID*/
    @Column
    var shopId: String? = null

    /**扩展字段1*/
    @Column
    @set:JsonProperty("KZZD1")
    @get:JsonProperty("KZZD1")
    var KZZD1: String? = null

    /**扩展字段2*/
    @Column
    @set:JsonProperty("KZZD2")
    @get:JsonProperty("KZZD2")
    var KZZD2: String? = null

    /**扩展字段3*/
    @Column
    @set:JsonProperty("KZZD3")
    @get:JsonProperty("KZZD3")
    var KZZD3: String? = null

    /**积分变动值,带正负号*/
    @Column
    var changePoint: BigDecimal = BigDecimal.ZERO

    @Transient
    var sendLimitRuleCalcResult: SendLimitRuleCalcResult? = null

    @Transient
    var ruleGroupId: Long? = null


    fun saveOrUpdate(accountTypeId: Long): MemberPointRecord {
        val effectiveDate = this.effectiveDate
        val overdueDate = this.overdueDate
        if (this.changePoint <= BigDecimal.ZERO
            && this.recordType != FSMPointEvent.EXPIRE
            && this.recordType != FSMPointEvent.SEND
            && this.recordType != FSMPointEvent.DELAY_SEND) {
            this.effectiveDate = null
            this.overdueDate = null
        }
        val record = ApplicationContextHolder.getBean(MemberPointRecordService::class.java).saveOrUpdate(this, replacePattern = accountTypeId.toString())
        record.effectiveDate = effectiveDate
        record.overdueDate = overdueDate
        return record
    }

    fun insert(accountTypeId: Long): MemberPointRecord {
        return ApplicationContextHolder.getBean(MemberPointRecordService::class.java).save(this, replacePattern = accountTypeId.toString())
    }

}