package com.shuyun.loyalty.service.service

import com.pip.mybatisplus.annotation.NotTransactionSafe
import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.service.datamodel.MemberPointFrozenValidStatement
import com.shuyun.loyalty.service.datamodel.MemberPointGainStatement
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.ZonedDateTime


@NotTransactionSafe
@Component
class MemberPointFrozenValidStatementService : MemberPointDmBaseService<MemberPointFrozenValidStatement>() {
    private val log = LogManager.getLogger(MemberPointFrozenValidStatementService::class.java)
    override fun log() = log

    @Autowired
    private lateinit var memberPointFrozenStatementService: MemberPointFrozenStatementService

    /**
     * 根据业务判断是否增加关联关系表
     */
    fun add(gainStatement: MemberPointGainStatement, recordId: String) {
        val refList = ArrayList<Pair<String, BigDecimal>>() // frozenStatementId-point
        val list = memberPointFrozenStatementService.findNegativeByMemberPointId(gainStatement.memberPointId!!,gainStatement.pointPlanId!!)
        // 查询是否已经存在关联关系
        val frozenValidList = findByMemberPointId(gainStatement.memberPointId!!, gainStatement.pointPlanId!!)
        list.forEach{ frozenStatement ->
            val point = frozenValidList.filter { it.frozenStatementId == frozenStatement.id }.sumOf { it.point }
            val remaining = frozenStatement.point.minus(point)
            if(remaining > ConstantValue.defaultZeroLine) {
                refList.add(Pair(frozenStatement.id!!, remaining))
            }
        }

        run outside@{
            refList.forEach {
                var frozenValidPoint = it.second
                val oldGainStatementPoint = gainStatement.point
                gainStatement.point = gainStatement.point.minus(it.second)
                if (gainStatement.point <= BigDecimal.ZERO) {
                    frozenValidPoint = oldGainStatementPoint
                }
                log.info("存在未关联的冻结负积分:{}", frozenValidPoint)
                save(build(gainStatement, frozenValidPoint, recordId, it.first), gainStatement.pointPlanId.toString())
                if (gainStatement.point <= BigDecimal.ZERO) {
                    return@outside
                }
            }
        }
    }


    fun findByMemberPointId(memberPointId: String,accountTypeId:Long, frozenStatementId: String? = null): List<MemberPointFrozenValidStatement> {
        val params = HashMap<String, Any?>()
        params["memberPointId"] = memberPointId
        if(frozenStatementId != null) {
            params["frozenStatementId"] = frozenStatementId
        }
        return findListByFilter(JsonUtils.toJson(params), replacePattern = accountTypeId.toString())
    }

    private fun build(gainStatement: MemberPointGainStatement, point: BigDecimal, recordId: String,frozenStatementId: String):MemberPointFrozenValidStatement {
        return MemberPointFrozenValidStatement().apply {
            gainStatementId = gainStatement.id!!
            this.point = point
            subjectFqn = gainStatement.subjectFqn
            memberId = gainStatement.memberId
            memberPointId = gainStatement.memberPointId
            pointPlanId = gainStatement.pointPlanId
            planId = gainStatement.planId
            overdueDate = gainStatement.overdueDate
            effectiveDate = gainStatement.effectiveDate
            modified = ZonedDateTime.now()
            created = ZonedDateTime.now()
            fromStatus = PointStateEnum.START
            validRecordId = recordId
            this.frozenStatementId = frozenStatementId
        }
    }

}