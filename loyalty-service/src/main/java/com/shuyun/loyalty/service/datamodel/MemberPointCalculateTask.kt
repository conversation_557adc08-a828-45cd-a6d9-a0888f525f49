package com.shuyun.loyalty.service.datamodel

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.JsonMappingException
import com.github.f4b6a3.tsid.Tsid
import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.extension.isSdkDuplicate
import com.shuyun.loyalty.service.extension.utcStr
import com.shuyun.loyalty.service.transfer.points.*
import com.shuyun.loyalty.service.util.SQL
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import javax.persistence.Column
import javax.persistence.Table
import javax.persistence.Transient

// 积分任务
@Table(name = "data.loyalty.member.PointCalculateJournal{*}")
@DataServiceModel
@FqnVariableModel
class MemberPointCalculateTask: BaseDataModel() {
    @Column
    var hierarchyId: Long = 0L

    @Column
    lateinit var  traceId: String

    @Column
    lateinit var  uniqueId: String

    @Column
    lateinit var  memberId: String

    @Column
    lateinit var memberPointId: String

    @Column
    lateinit var  action: PointAction

    @Column
    var status: PointStatus = PointStatus.NEW

    @Column
    var comment: String = ""

    @Column
    var data: String = "{}"

    @Column
    var version: Long = 0

    @Column
    var created: ZonedDateTime = ZonedDateTime.now()

    @Column
    var modified: ZonedDateTime = ZonedDateTime.now()

    @Transient
    @JsonIgnore
    var lp: LoyaltyPoints? = null
        get() {
            if (field == null) {
                if (data == "{}") return null
                try {
                    field = Json.parse(data)
                } catch (_: IllegalArgumentException) {
                    // 脏数据兼容处理
                    var newData = data.replace(""""sendLimitCalc":"\{(.+?)}"""".toRegex()) { "\"sendLimitCalc\":{${it.groupValues[1]}}" }
                    newData = newData.replace(""""timeBasedRule":"\{(.+?)}"""".toRegex()) { "\"timeBasedRule\":{${it.groupValues[1]}}" }
                    field = Json.parse(newData)
                } catch (_: JsonMappingException) {
                    val newData = data.replace("""(?<!\\)\\(?!\\)""".toRegex(), """\\\\""")
                    field = Json.parse(newData)
                }
            }
            return field
        }

    fun isAsyncAction(): Boolean {
        return action in asyncActions
    }

    companion object {
        private val asyncActions = setOf(
            PointAction.UNFREEZE,
            PointAction.REVERSE_SEND,
            PointAction.REVERSE_DEDUCT
        )
    }
}


enum class PointAction {
    SEND,
    DEDUCT,
    FREEZE,
    UNFREEZE,
    USE_FROZEN,
    INVALID_SEND,
    INVALID_FROZEN,
    INVALID_ALL,
    REVERSE_SEND,
    REVERSE_DEDUCT,
    RECALCULATE,
    UNFREEZE_RECALCULATE,
    EFFECT,
    EXPIRE,
    MIGRATE,
    ;

    fun transfer(task: MemberPointCalculateTask) {
        when (this) {
            SEND                  -> ApplicationContextHolder.getBean(MemberPointSendTransfer::class.java).transfer(task.lp!!)
            DEDUCT                -> ApplicationContextHolder.getBean(MemberPointDeductTransfer::class.java).transfer(task.lp!!)

            FREEZE                -> ApplicationContextHolder.getBean(MemberPointFreezeTransfer::class.java).transfer(task.lp!!)
            UNFREEZE              -> ApplicationContextHolder.getBean(MemberPointUnfreezeTransfer::class.java).transfer(task.lp!!)
            USE_FROZEN            -> ApplicationContextHolder.getBean(MemberPointUseFrozenTransfer::class.java).transfer(task.lp!!)

            INVALID_SEND,
            INVALID_FROZEN,
            INVALID_ALL           -> ApplicationContextHolder.getBean(MemberPointInvalidTransfer::class.java).transfer(task.lp!!, this)

            REVERSE_SEND,
            REVERSE_DEDUCT        -> ApplicationContextHolder.getBean(MemberPointReverseTransfer::class.java).transfer(task.lp!!, this)

            RECALCULATE,
            UNFREEZE_RECALCULATE  -> ApplicationContextHolder.getBean(MemberPointRecalibrateTransfer::class.java).transfer(task.lp!!)

            EFFECT                -> ApplicationContextHolder.getBean(MemberPointEffectTransfer::class.java).transfer(task.lp!!)
            EXPIRE                -> ApplicationContextHolder.getBean(MemberPointExpireTransfer::class.java).transfer(task.lp!!)
            else                  -> {}
        }
    }
}

@Suppress("unused")
enum class PointStatus {
    NEW,
    FAILED,
    COMPLETED,
}


fun MemberPointCalculateTask.save(): MemberPointCalculateTask {
    for (c in 0..3) {
        try {
            this.id = this.id ?: Tsid.fast().toLowerCase()
            MemberPointCalculateTask.batchSave(hierarchyId, listOf(this))
            break
        } catch (e: SdkException) {
            if (e.isSdkDuplicate()) {
                if (c == 3) {
                    throw e
                } else {
                    this.id = Tsid.fast().toLowerCase()
                    continue
                }
            } else {
                throw e
            }
        }
    }
    return this
}


fun MemberPointCalculateTask.update() {
    val sql = """
        UPDATE data.loyalty.member.PointCalculateJournal$hierarchyId 
        SET status = :status, comment = :comment, version = version + 1, modified = :modified
        WHERE id = :id and version = :version
    """.trimIndent()
    val params = mapOf(
        "status" to status,
        "comment" to comment,
        "id" to id,
        "version" to version,
        "modified" to ZonedDateTime.now().utcStr(),
    )
    val r = DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
        val response = sdk.execute(sql, params)
        response.affectedRows
    }!!
    if (r == 0) {
        throw ConcurrentModificationException()
    }
}


fun MemberPointCalculateTask.updateComment() {
    val sql = """
        UPDATE data.loyalty.member.PointCalculateJournal$hierarchyId 
        SET comment = :comment, modified = :modified 
        WHERE id = :id
    """.trimIndent()
    val params = mapOf(
        "comment" to comment,
        "id" to id,
        "modified" to ZonedDateTime.now().utcStr()
    )
    DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
        sdk.execute(sql, params)
    }
}

fun MemberPointCalculateTask.Companion.batchSave(hierarchyId: Long, list: List<MemberPointCalculateTask>) {
    if (list.isEmpty()) return
    val sql = SQL.build {
        var sql = """
            INSERT INTO data.loyalty.member.PointCalculateJournal$hierarchyId 
            (id, hierarchyId, traceId, uniqueId, memberId, memberPointId, action, status, comment, data, version, created, modified)
            VALUES 
        """.trimIndent()
        var i = 0L
        var j = 0L
        for (cj in list) {
            sql += """
                (
                ${cj.id.escaped()},
                ${cj.hierarchyId.escaped()},
                ${cj.traceId.escaped()},
                ${cj.uniqueId.escaped()},
                ${cj.memberId.escaped()},
                ${cj.memberPointId.escaped()},
                ${cj.action.name.escaped()},
                ${cj.status.name.escaped()},
                ${cj.comment.escaped()},
                ${cj.data.escaped()},
                ${cj.version.escaped()},
                ${cj.modified.plus(i++, ChronoUnit.MILLIS).escaped()},
                ${cj.created.plus(j++, ChronoUnit.MILLIS).escaped()}
                ),
            """.trimIndent()
        }
        sql.dropLast(1)
    }
    DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
        sdk.execute(sql, mapOf())
    }
}


fun MemberPointCalculateTask.Companion.deleteBy(hierarchyId: Long, memberPointId: String) {
    val sql = """
        DELETE FROM data.loyalty.member.PointCalculateJournal$hierarchyId 
        WHERE memberPointId = :memberPointId
    """.trimIndent()
    val params = mapOf("memberPointId" to memberPointId)
    DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
        sdk.execute(sql, params)
    }
}

fun MemberPointCalculateTask.Companion.deleteByIds(hierarchyId: Long, ids: List<String>) {
    if (ids.isEmpty()) return
    ids.chunked(100).forEach {
        val sql = "DELETE FROM data.loyalty.member.PointCalculateJournal$hierarchyId WHERE id in (:ids)"
        val params = mapOf("ids" to it)
        DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
            sdk.execute(sql, params)
        }
    }
}


private fun find(sql: String, params: Map<String, Any?>): List<MemberPointCalculateTask> {
    val response = DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
        sdk.execute(sql, params)
    }!!
    if (!response.isSuccess || response.data.isEmpty()) {
        return emptyList()
    }
    return Json.parse(Json.toJson(response.data))
}


fun MemberPointCalculateTask.Companion.findList(
    hierarchyId: Long,
    memberPointId: String,
    statusList: List<PointStatus>,
    idAfter: String? = null,
    sortedBy: String = "id",
    limit: Int = 200
): List<MemberPointCalculateTask> {
    if (statusList.isEmpty()) return emptyList()
    val idAfterCondition = idAfter?.let { "AND id > :id" } ?: ""
    val sort = " ORDER BY $sortedBy asc "
    val sql = """
        SELECT id, hierarchyId, traceId, uniqueId, memberId, memberPointId, action, status, comment, data, version, created, modified 
        FROM data.loyalty.member.PointCalculateJournal$hierarchyId
        WHERE memberPointId = :memberPointId AND status in (:statusList) $idAfterCondition
        $sort
        LIMIT $limit
    """.trimIndent()
    val params = mutableMapOf(
        "statusList" to statusList,
        "memberPointId" to memberPointId
    ).also { if (idAfter != null) it["id"] = idAfter }
    return find(sql, params)
}

fun MemberPointCalculateTask.Companion.find(
    hierarchyId: Long,
    memberPointId: String,
    actionNotIn: List<PointAction>,
    limit: Int = 1
): List<MemberPointCalculateTask> {
    if (actionNotIn.isEmpty()) return emptyList()
    val sql = """
        SELECT id, hierarchyId, traceId, uniqueId, memberId, memberPointId, action, status, comment, data, version, created, modified 
        FROM data.loyalty.member.PointCalculateJournal$hierarchyId
        WHERE memberPointId = :memberPointId AND action not in (:actionList) 
        LIMIT $limit
    """.trimIndent()
    val params = mutableMapOf(
        "actionList" to actionNotIn,
        "memberPointId" to memberPointId
    )
    return find(sql, params)
}


fun MemberPointCalculateTask.Companion.findByTraceId(
    hierarchyId: Long,
    traceId: String,
    limit: Int = 200
): List<MemberPointCalculateTask> {
    val sql = """
        SELECT id, hierarchyId, traceId, uniqueId, memberId, memberPointId, action, status, comment, data, version, created, modified 
        FROM data.loyalty.member.PointCalculateJournal$hierarchyId
        WHERE traceId = :traceId
        LIMIT $limit
    """.trimIndent()
    val params = mutableMapOf("traceId" to traceId)
    return find(sql, params)
}


fun MemberPointCalculateTask.Companion.findNewList(hierarchyId: Long, memberPointId: String, limit: Int = 200): List<MemberPointCalculateTask> {
    return findList(hierarchyId, memberPointId, listOf(PointStatus.NEW), limit = limit)
}

