package com.shuyun.loyalty.service.exception

import com.shuyun.pip.exception.AbstractPipException
import com.shuyun.pip.i18n.LocaleI18nContextHolder
import javax.ws.rs.core.Response

/**
 */
open class LoyaltyException : AbstractPipException{
    constructor(exceptionCode: Int, vararg params: String) : super(exceptionCode,LocaleI18nContextHolder.getMessage(exceptionCode.toString(),params), params)

    constructor(exceptionCode:LoyaltyExceptionCode): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString())){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }

    constructor(exceptionCode:LoyaltyExceptionCode, cause: Throwable): super(exceptionCode.trackErrorCode, LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString()), cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, vararg params: String): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString(),params), params){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, message:String?, cause: Throwable): super(exceptionCode.trackErrorCode, message, cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
}

class PointException : AbstractPipException {
    constructor(exceptionCode: Int, vararg params: String) : super(exceptionCode,LocaleI18nContextHolder.getMessage(exceptionCode.toString(),params), params)

    constructor(exceptionCode:LoyaltyExceptionCode): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString())){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }

    constructor(exceptionCode:LoyaltyExceptionCode, cause: Throwable): super(exceptionCode.trackErrorCode, LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString()), cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, vararg params: String): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString(),params), params){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, message:String?, cause: Throwable): super(exceptionCode.trackErrorCode, message, cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
}
class SendPointException : AbstractPipException{
    constructor(exceptionCode: Int, vararg params: String) : super(exceptionCode,LocaleI18nContextHolder.getMessage(exceptionCode.toString(),params), params)

    constructor(exceptionCode:LoyaltyExceptionCode): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString())){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }

    constructor(exceptionCode:LoyaltyExceptionCode, cause: Throwable): super(exceptionCode.trackErrorCode, LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString()), cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, vararg params: String): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString(),params), params){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, message:String?, cause: Throwable): super(exceptionCode.trackErrorCode, message, cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
}

class UsedPointException : AbstractPipException{
    constructor(exceptionCode: Int, vararg params: String) : super(exceptionCode,LocaleI18nContextHolder.getMessage(exceptionCode.toString(),params), params)

    constructor(exceptionCode:LoyaltyExceptionCode): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString())){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }

    constructor(exceptionCode:LoyaltyExceptionCode, cause: Throwable): super(exceptionCode.trackErrorCode, LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString()), cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, vararg params: String): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString(),params), params){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, message:String?, cause: Throwable): super(exceptionCode.trackErrorCode, message, cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
}

class DelayPointException : AbstractPipException{
    constructor(exceptionCode: Int, vararg params: String) : super(exceptionCode,LocaleI18nContextHolder.getMessage(exceptionCode.toString(),params), params)

    constructor(exceptionCode:LoyaltyExceptionCode): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString())){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }

    constructor(exceptionCode:LoyaltyExceptionCode, cause: Throwable): super(exceptionCode.trackErrorCode, LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString()), cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, vararg params: String): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString(),params), params){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, message:String?, cause: Throwable): super(exceptionCode.trackErrorCode, message, cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
}

class GradeException : AbstractPipException{
    constructor(exceptionCode: Int, vararg params: String) : super(exceptionCode,LocaleI18nContextHolder.getMessage(exceptionCode.toString(),params), params)

    constructor(exceptionCode:LoyaltyExceptionCode): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString())){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }

    constructor(exceptionCode:LoyaltyExceptionCode, cause: Throwable): super(exceptionCode.trackErrorCode, LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString()), cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, vararg params: String): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString(),params), params){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, message:String?, cause: Throwable): super(exceptionCode.trackErrorCode, message, cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
}

class MedalException : AbstractPipException{
    constructor(exceptionCode: Int, vararg params: String) : super(exceptionCode,LocaleI18nContextHolder.getMessage(exceptionCode.toString(),params), params)

    constructor(exceptionCode:LoyaltyExceptionCode): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString())){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }

    constructor(exceptionCode:LoyaltyExceptionCode, cause: Throwable): super(exceptionCode.trackErrorCode, LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString()), cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, vararg params: String): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString(),params), params){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, message:String?, cause: Throwable): super(exceptionCode.trackErrorCode, message, cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
}

class MemberException : AbstractPipException{
    constructor(exceptionCode: Int, vararg params: String) : super(exceptionCode,LocaleI18nContextHolder.getMessage(exceptionCode.toString(),params), params)

    constructor(exceptionCode:LoyaltyExceptionCode): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString())){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }

    constructor(exceptionCode:LoyaltyExceptionCode, cause: Throwable): super(exceptionCode.trackErrorCode, LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString()), cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, vararg params: String): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString(),params), params){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, message:String?, cause: Throwable): super(exceptionCode.trackErrorCode, message, cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
}

class MemberPointSegmentException : AbstractPipException{
    constructor(exceptionCode: Int, vararg params: String) : super(exceptionCode,LocaleI18nContextHolder.getMessage(exceptionCode.toString(),params), params)

    constructor(exceptionCode:LoyaltyExceptionCode): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString())){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }

    constructor(exceptionCode:LoyaltyExceptionCode, cause: Throwable): super(exceptionCode.trackErrorCode, LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString()), cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, vararg params: String): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString(),params), params){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, message:String?, cause: Throwable): super(exceptionCode.trackErrorCode, message, cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
}

class FileException : AbstractPipException{
    constructor(exceptionCode: Int, vararg params: String) : super(exceptionCode,LocaleI18nContextHolder.getMessage(exceptionCode.toString(),params), params)

    constructor(exceptionCode:LoyaltyExceptionCode): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString())){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }

    constructor(exceptionCode:LoyaltyExceptionCode, cause: Throwable): super(exceptionCode.trackErrorCode, LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString()), cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, vararg params: String): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString(),params), params){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, message:String?, cause: Throwable): super(exceptionCode.trackErrorCode, message, cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
}

class PointResendQueueTailException : AbstractPipException{
    constructor(exceptionCode: Int, vararg params: String) : super(exceptionCode,LocaleI18nContextHolder.getMessage(exceptionCode.toString(),params), params)

    constructor(exceptionCode:LoyaltyExceptionCode): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString())){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }

    constructor(exceptionCode:LoyaltyExceptionCode, cause: Throwable): super(exceptionCode.trackErrorCode, LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString()), cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, vararg params: String): super(exceptionCode.trackErrorCode,LocaleI18nContextHolder.getMessage(exceptionCode.trackErrorCode.toString(),params), params){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
    constructor(exceptionCode:LoyaltyExceptionCode, message:String?, cause: Throwable): super(exceptionCode.trackErrorCode, message, cause){
        this.status = Response.Status.BAD_REQUEST
        this.url = ExceptionUrl.EXCEPTION_URL
    }
}

class LPException(override val message: String?): RuntimeException(message)

object ExceptionUrl{
    val EXCEPTION_URL = "http://wiki.yunat.com/pages/viewpage.action?pageId=109559429"
}

enum class LoyaltyExceptionCode(val trackErrorCode: Int, val code : String) {
    UNKNOWN_EXCEPTION(1401,"UNKNOWN_EXCEPTION_1401"),
    NOT_FOUND(1402,"NOT_FOUND_1402"),
    REQUEST_EXCEPTION(1403,"REQUEST_EXCEPTION_1403"),
    VIOLATION_PARAMS(1404,"VIOLATION_PARAMS_1404"),
    UNDECLARED_EXCEPTION(1405,"UNDECLARED_EXCEPTION_1405"),
    CONFLICT_EXCEPTION(1409,"CONFLICT_EXCEPTION_1409"),
    SYSTEM_BUSY(1905,"SYSTEM_BUSY_1905"),

    //============================ 事件专有 ===========================
    EVENT_NOT_MATCH(1601,"EVENT_NOT_MATCH_1601"),
    EVENT_NOT_GROUP(1602,"EVENT_NOT_GROUP_1602"),

    //============================ 对外接口专有 ===========================
    TOKEN_NOT_FOUND(1501,"TOKEN_NOT_FOUND_1501"),
    TOKEN_REPEATED(1502,"TOKEN_REPEATED_1502"),
    CHANNEL_TYPE_ERROR(1503,"CHANNEL_TYPE_ERROR_1503"),
    OVER_MAX_NUMBER(1504,"OVER_MAX_NUMBER_1504"),
    NOT_FOUND_VICE_MEMBER(1505,"NOT_FOUND_VICE_MEMBER_1505"),
    NOT_ALLOWED_MERGE(1506,"NOT_FOUND_VICE_MEMBER_1506"),
    NOT_FOUND_MERGE_FQN(1507,"NOT_FOUND_MERGE_FQN_1507"),
    CHANGE_MODEL_ERROR(1508,"CHANGE_MODEL_ERROR_1508"),
    USED_NOT_EQUAL_FROZEN(1509,"USED_NOT_EQUAL_FROZEN_1509"),
    TRACE_ID_REPEATED(1510,"TRACE_ID_REPEATED_1510"),
    POINT_EXTEND_NULL(1511, "POINT_EXTEND_NULL_1125"),
    POINT_EXTEND_TIME_ERROR(1512, "POINT_EXTEND_TIME_ERROR_1125"),

    //============================ 等级专有 ===========================
    GRADE_HIERARCHY_NOT_FOUND(1201,"GRADE_HIERARCHY_NOT_FOUND_1201"),
    TARGET_GRADE_NOT_FOUND(1202,"TARGET_GRADE_NOT_FOUND_1202"),
    GRADE_RECORD_TYPE_NOT_FOUND(1203,"MEMBER_FOUND_1203"),
    GRADE_OVERDUE_TIME_ERROR(1204,"GRADE_RECORD_TYPE_NOT_FOUND_1204"),
    CURRENT_GRADE_NOT_FOUND(1205,"CURRENT_GRADE_NOT_FOUND_1205"),
    GRADE_TIME_TYPE_ERROR(1206,"GRADE_TIME_TYPE_ERROR_1206"),


    //========================= 勋章专有 ============================//
    MEDAL_RECORD_TYPE_NOT_FOUND(1901,"MEDAL_TIME_TYPE_ERROR_1901"),
    MEMBER_MEDAL_REPEAT(1902,"MEMBER_MEDAL_REPEAT_1902"),
    MEMBER_MEDAL_NOT_FOUND(1903,"MEMBER_MEDAL_NOT_FOUND_1903"),
    MEMBER_MEDAL_IS_EMPTY(1904,"MEMBER_MEDAL_IS_EMPTY_1904"),

    //============================ 积分专有 ===========================
    POINT_NOT_FOUND(1101,"POINT_NOT_FOUND_1101"),
    ACCOUNT_NOT_FOUND(1102,"ACCOUNT_NOT_FOUND_1102"),
    POINT_IS_EXPIRE(1103,"POINT_IS_EXPIRE_1103"),
    POINT_RECORD_NOT_FOUND(1104,"POINT_RECORD_NOT_FOUND_1104"),
    POINT_RECORD_TYPE_NOT_FOUND(1105,"POINT_RECORD_TYPE_NOT_FOUND_1105"),
    POINT_LESS_THAN_ZERO(1106,"POINT_LESS_THAN_ZERO_1106"),
    GAIN_ID_NOT_FOUND(1107,"GAIN_ID_NOT_FOUND_1107"),
    NOT_DEDUCT_ALLOWED(1108,"NOT_DEDUCT_ALLOWED_1108"),
    POINT_LESS_THAN_DEDUCT(1109,"POINT_LESS_THAN_DEDUCT_1109"),
    DEDUCT_POINT_EMPTY(1110,"DEDUCT_POINT_EMPTY_1110"),
    NOT_ABOLISH_ALLOWED(1111,"NOT_ABOLISH_ALLOWED_1111"),
    NOT_FROZEN_ALLOWED(1112,"NOT_FROZEN_ALLOWED_1112"),
    NOT_FROM_FROZEN_ALLOWED(1113,"NOT_FROM_FROZEN_ALLOWED_1113"),
    VALID_POINT_NOT_FOUND(1114,"VALID_POINT_NOT_FOUND_1114"),
    FROZEN_POINT_NOT_FOUND(1115,"FROZEN_POINT_NOT_FOUND_1115"),
    POINT_STATUS_ERROR(1116,"POINT_STATUS_ERROR_1116"),
    INIT_POINT_FAIL(1117,"INIT_POINT_FAIL_1117"),
    POINT_NOT_DEDUCT_NEGATIVE(1118,"POINT_NOT_DEDUCT_NEGATIVE_1118"),
    POINT_TIME_ERROR(1119,"POINT_TIME_ERROR_1119"),
    POINT_LESS_THAN_OR_EQUAL_ZERO(1120,"POINT_LESS_THAN_OR_EQUAL_ZERO_1120"),
    USED_POINT_LESS_THAN_DEDUCT(1121,"USED_POINT_LESS_THAN_DEDUCT_1121"),
    FROZEN_POINT_LESS_THAN_DEDUCT(1122,"FROZEN_POINT_LESS_THAN_DEDUCT_1122"),
    BEYOND_MANUALLY_POINT_LIMIT(1123,"BEYOND_MANUALLY_POINT_LIMIT_1123"),
    BEYOND_MANUALLY_POINT_LIMIT_NEED_APPROVE(1124,"BEYOND_MANUALLY_POINT_LIMIT_NEED_APPROVE_1124"),
    POINT_STATUS_CHANGED(1906,"POINT_STATUS_CHANGED_1906"),
    DATA_CHANGED(1907,"STATUS_CHANGED_1907"),
    POINT_TRIAL_DEDUCT_FAILED(1908,"POINT_TRIAL_DEDUCT_FAILED_1908"),
    POINT_TRIAL_PLUS_FAILED(1909,"POINT_TRIAL_PLUS_FAILED_1909"),
    POINT_TRIAL_SIGN_FAILED(1910,"POINT_TRIAL_SIGN_FAILED_1910"),

    //============================ 积分块功能 ===========================
    POINT_SEGMENT_DEDUCT_FAILED(1125,"POINT_SEGMENT_DEDUCT_FAILED_1125"),
    POINT_SEGMENT_PLUS_FAILED(1126,"POINT_SEGMENT_PLUS_FAILED_1126"),
    POINT_SEGMENT_CREATE_FAILED(1127,"POINT_SEGMENT_CREATE_FAILED_1127"),
    POINT_SEGMENT_RESEND_FAILED(1128,"POINT_SEGMENT_RESEND_FAILED_1128"),
    //============================ 积分逆向操作错误码 ===========================
    POINT_REVERSE_NOT_FOUND(1129,"POINT_REVERSE_NOT_FOUND_1129"),
    POINT_REVERSE_USED(1130,"POINT_REVERSE_USED_1130"),
    POINT_REVERSE_NOT_ENOUGH(1131,"POINT_REVERSE_NOT_ENOUGH_1131"),

    //============================ 会员专有 ===========================
    MEMBER_NOT_FOUND(1301,"MEMBER_NOT_FOUND_1301"),
    BLACKLIST_EXIST(1302,"BLACKLIST_EXIST_1302"),
    OPERATE_MEMBER_REPEAT(1303,"OPERATE_MEMBER_REPEAT_1303"),

    //============================ 实施端专有 ===========================
    PLAN_IS_FILE(1001,"PLAN_IS_FILE_1001"),
    PLAN_NOT_FOUND(1002,"PLAN_NOT_FOUND_1002"),
    PLAN_REPEATED(1003,"PLAN_REPEATED_1003"),
    SUBJECT_REPEATED(1004,"SUBJECT_REPEATED_1004"),
    DATA_TYPE_REPEATED(1005,"DATA_TYPE_REPEATED_1005"),
    SUBJECT_NOT_ENABLE(1006,"SUBJECT_NOT_ENABLE_1006"),
    SUBJECT_NOT_DISABLED(1007,"SUBJECT_NOT_DISABLED_1007"),
    CURRENT_SUBJECT_NOT_DISABLED(1008,"CURRENT_SUBJECT_NOT_DISABLED_1008"),
    EVENT_TYPE_NOT_DISABLED(1009,"EVENT_TYPE_NOT_DISABLED_1009"),
    CURRENT_EVENT_TYPE_NOT_DISABLED(1010,"CURRENT_EVENT_TYPE_NOT_DISABLED_1010"),
    EVENT_TYPE_NOT_FOUND(1011,"EVENT_TYPE_NOT_FOUND_1011"),
    SUBJECT_NOT_FOUND(1012,"SUBJECT_NOT_FOUND_1012"),
    EVENT_TYPE_CUSTOMIZED_PROPERTY_NOT_DISABLED(1013,"EVENT_TYPE_CUSTOMIZED_PROPERTY_NOT_DISABLED_1013"),
    SUBJECT_CUSTOMIZED_PROPERTY_NOT_DISABLED(1014,"SUBJECT_CUSTOMIZED_PROPERTY_NOT_DISABLED_1014"),
    CURRENT_CUSTOMIZED_PROPERTY_TMP_NOT_DISABLED(1015,"CURRENT_CUSTOMIZED_PROPERTY_TMP_NOT_DISABLED_1015"),
    CURRENT_CUSTOMIZED_PROPERTY_NOT_DISABLED(1016,"CURRENT_CUSTOMIZED_PROPERTY_NOT_DISABLED_1016"),
    ACCOUNT_TYPE_NOT_DISABLED(1017,"ACCOUNT_TYPE_NOT_DISABLED_1017"),
    GRADE_GROUP_REPEATED(1018,"GRADE_GROUP_REPEATED_1018"),
    VERSION_NOT_FOUND(1019,"VERSION_NOT_FOUND_1019"),
    PREVIOUS_VERSION_NOT_FOUND(1020,"PREVIOUS_VERSION_NOT_FOUND_1020"),
    SUBJECT_ID_NOT_FOUND(1021,"SUBJECT_ID_NOT_FOUND_1021"),
    POINT_GROUP_NOT_FOUND(1022,"POINT_GROUP_NOT_FOUND_1022"),
    POINT_GROUP_REPEATED(1023,"POINT_GROUP_REPEATED_1023"),
    CONFIG_NOT_FOUND(1024,"CONFIG_NOT_FOUND_1024"),
    FILED_NOT_OPERATION_BACK_TO_PUBLISH(1025,"FILED_NOT_OPERATION_BACK_TO_PUBLISH_1025"),
    DRAFT_NOT_OPERATION_BACK_TO_PUBLISH(1026,"DRAFT_NOT_OPERATION_BACK_TO_PUBLISH_1026"),
    PUBLISHING_NOT_OPERATION_BACK_TO_PUBLISH(1027,"PUBLISHING_NOT_OPERATION_BACK_TO_PUBLISH_1027"),
    PUBLISH_FAILED_NOT_OPERATION_BACK_TO_PUBLISH(1028,"PUBLISH_FAILED_NOT_OPERATION_BACK_TO_PUBLISH_1028"),
    NOT_OPERATION_AFRESH_PUBLISH(1029,"NOT_OPERATION_AFRESH_PUBLISH_1029"),
    NOT_DRAFT_OPERATION_AFRESH_PUBLISH(1030,"NOT_DRAFT_OPERATION_AFRESH_PUBLISH_1030"),
    FILED_NOT_OPERATION_AFRESH_PUBLISH(1031,"FILED_NOT_OPERATION_AFRESH_PUBLISH_1031"),
    PUBLISHING_NOT_OPERATION_AFRESH_PUBLISH(1032,"PUBLISHING_NOT_OPERATION_AFRESH_PUBLISH_1032"),
    PUBLISH_FAILED_NOT_OPERATION_AFRESH_PUBLISH(1033,"PUBLISH_FAILED_NOT_OPERATION_AFRESH_PUBLISH_1033"),
    DRAFT_NOT_OPERATION_FILE(1034,"DRAFT_NOT_OPERATION_FILE_1034"),
    FILED_NOT_OPERATION_FILE(1035,"FILED_NOT_OPERATION_FILE_1035"),
    PUBLISHING_NOT_OPERATION_FILE(1036,"PUBLISHING_NOT_OPERATION_FILE_1036"),
    PUBLISH_FAILED_NOT_OPERATION_FILE(1037,"PUBLISH_FAILED_NOT_OPERATION_FILE_1037"),
    GRADE_JUDGE_FILE_STATUS(1038,"GRADE_JUDGE_FILE_STATUS_1038"),
    PLAN_NOT_OPERATION_FILE(1039,"PLAN_NOT_OPERATION_FILE_1039"),
    POINT_ACCOUNT_TYPE_NOT_OPERATION_FILE(1040,"POINT_ACCOUNT_TYPE_NOT_OPERATION_FILE_1040"),
    POINT_RULE_GROUP_NOT_OPERATION_FILE(1041,"POINT_RULE_GROUP_NOT_OPERATION_FILE_1041"),
    GRADE_NOT_OPERATION_FILE(1042,"GRADE_NOT_OPERATION_FILE_1042"),
    NOT_OPERATION_DELETE_EVENT_TYPE(1043,"NOT_OPERATION_DELETE_EVENT_TYPE_1043"),
    NOT_OPERATION_DELETE_POINT_ACCOUNT_TYPE(1044,"NOT_OPERATION_DELETE_POINT_ACCOUNT_TYPE_1044"),
    NOT_OPERATION_DELETE_PROPERTY_TEMPLATE(1045,"NOT_OPERATION_DELETE_PROPERTY_TEMPLATE_1045"),
    NOT_OPERATION_DELETE_NO_DRAFT(1046,"NOT_OPERATION_DELETE_NO_DRAFT_1046"),
    NOT_OPERATION_DELETE_SUBJECT(1047,"NOT_OPERATION_DELETE_SUBJECT_1047"),
    NOT_OPERATION_DELETE_POINT_GROUP(1048,"NOT_OPERATION_DELETE_POINT_GROUP_1048"),
    NOT_OPERATION_DELETE_CURRENT_GRADE(1049,"NOT_OPERATION_DELETE_CURRENT_GRADE_1049"),
    NOT_OPERATION_DELETE_CURRENT_MEMBER(1050,"NOT_OPERATION_DELETE_CURRENT_MEMBER_1050"),
    NOT_OPERATION_DELETE_GRADE(1051,"NOT_OPERATION_DELETE_GRADE_1051"),
    PUBLISHED_PLAN_NOT_OPERATION_DELETE(1052,"PUBLISHED_PLAN_NOT_OPERATION_DELETE_1052"),
    FILED_PLAN_NOT_OPERATION_DELETE(1053,"FILED_PLAN_NOT_OPERATION_DELETE_1053"),
    PUBLISHING_PLAN_NOT_OPERATION_DELETE(1054,"PUBLISHING_PLAN_NOT_OPERATION_DELETE_1054"),
    PUBLISH_FAILED_PLAN_NOT_OPERATION_DELETE(1055,"PUBLISH_FAILED_PLAN_NOT_OPERATION_DELETE_1055"),
    PUBLISHED_PLAN_NOT_OPERATION_UPDATE(1056,"PUBLISHED_PLAN_NOT_OPERATION_UPDATE_1056"),
    HISTORY_PLAN_NOT_OPERATION_UPDATE(1057,"HISTORY_PLAN_NOT_OPERATION_UPDATE_1057"),
    FILED_CONFIG_NOT_OPERATION_UPDATE(1058,"FILED_CONFIG_NOT_OPERATION_UPDATE_1058"),
    FILED_PLAN_NOT_OPERATION_UPDATE(1059,"FILED_PLAN_NOT_OPERATION_UPDATE_1059"),
    PUBLISHING_PLAN_NOT_OPERATION_UPDATE(1060,"PUBLISHING_PLAN_NOT_OPERATION_UPDATE_1060"),
    PUBLISH_FAILED_PLAN_NOT_OPERATION_UPDATE(1061,"PUBLISHING_PLAN_NOT_OPERATION_UPDATE_1061"),
    PLAN_INFO_INCOMPLETE(1064,"CURRENT_GROUP_NOT_DISABLED_1064"),
    SUBJECT_TMP_REPEATED(1065,"SUBJECT_TMP_REPEATED_1065"),
    POINT_ACCOUNT_TYPE_REPEATED(1066,"POINT_ACCOUNT_TYPE_REPEATED_1065"),
    EVENT_TYPE_REPEATED(1067,"EVENT_TYPE_REPEATED_1065"),
    GRADE_HIERARCHY_REPEATED(1069,"EVENT_TYPE_TO_FQN_REPEATED_1065"),
    ONELY_SAVE_ONE(1070,"ONLEY_SAVE_ONE_1070"),
    GROUP_LT_NOW_TIME(1071,"GROUP_LT_NOW_TIME_1071"),
    GROUP_GT_END_TIME(1072,"GROUP_GT_END_TIME_1072"),
    POINT_LT_GROUP_TIME(1073,"POINT_LT_GROUP_TIME_1073"),
    POINT_LT_END_TIME(1074,"POINT_LT_END_TIME_1074"),
    CONFIG_HISTORY(1075,"CONFIG_HISTORY_1075"),
    NOT_ALLOWED_NEW(1076,"NOT_ALLOWED_NEW_1076"),
    NOT_ALLOWED_DISABLED_DRAF(1077,"NOT_ALLOWED_DISABLED_DRAF_1077"),
    NOT_ALLOWED_DISABLED(1078,"NOT_ALLOWED_DISABLED_1078"),
    NOT_ALLOWED_START_DRAF(1079,"NOT_ALLOWED_START_DRAF_1079"),
    NOT_ALLOWED_START(1080,"NOT_ALLOWED_START_1080"),
    CONFIG_ENABLE_FAIL(1081,"CONFIG_ENABLE_FAIL_1081"),
    NO_PUBLISHED_PLAN(1082,"NO_PUBLISHED_PLAN_1082"),
    GRADE_TIME_ERROR(1083,"GRADE_TIME_ERROR_1083"),
    GRADE_GROUP_NULL(1084,"GRADE_GROUP_NULL_1084"),
    OVERATE_LE_NOW(1085,"OVERATE_LE_NOW_1085"),
    POINT_PROCESS_STATUS_NOT_FOUND(1086,"POINT_PROCESS_STATUS_NOT_FOUND_1086"),
    POINT_SEND_LIMIT_NOT_FOUND(1087,"POINT_SEND_LIMIT_NOT_FOUND_1087"),
    MEDAL_GROUP_REPEATED(1088,"MEDAL_GROUP_REPEATED_1088"),
    MEDAL_GROUP_NULL(1089,"MEDAL_GROUP_NULL_1089"),
    MEDAL_TIME_ERROR(1090,"MEDAL_TIME_ERROR_1090"),
    MEDAL_HIERARCHY_NOT_FOUND(1091, "MEDAL_HIERARCHY_NOT_FOUND_1091"),
    MEDAL_OVERDUE_TIME_ERROR(1092, "MEDAL_OVERDUE_TIME_ERROR_1092"),
    NOT_OPERATION_DELETE_MEDAL(1093, "NOT_OPERATION_DELETE_MEDAL_1093"),
    MEDAL_NOT_OPERATION_FILE(1094, "MEDAL_NOT_OPERATION_FILE_1094"),
    MEDAL_HIERARCHY_REPEATED(1095, "MEDAL_HIERARCHY_REPEATED_1095"),
    MEDAL_DEFINITION_NOT_FOUND(1096, "MEDAL_DEFINITION_NOT_FOUND_1096"),
    NOT_OPERATION_DELETE_CURRENT_MEDAL(1097, "NOT_OPERATION_DELETE_CURRENT_MEDAL_1097"),
    MEDAL_JUDGE_FILE_STATUS(1098, "MEDAL_JUDGE_FILE_STATUS_1098"),



    //============================ 操作第三方 ===========================
    FILE_WRITE_ERROR(1701,"FILE_WRITE_ERROR_1701"),
    FILE_CLOSE_ERROR(1702,"FILE_CLOSE_ERROR_1702"),
    MYSQL_ERROR(1703,"MYSQL_ERROR_1703"),
    FILE_EXPORT_ERROR(1707,"FILE_EXPORT_ERROR_1707"),
    FILE_UPLOAD_ERROR(1708,"FILE_UPLOAD_ERROR_1708"),
    DATA_SERVICE_ERROR(1709,"DATA_SERVICE_ERROR_1709"),


    //============================ 操作文件导入导出功能 ===========================
    POINT_ACCOUNT_TYPE(1801,"POINT_ACCOUNT_TYPE_1801"),
    CHANGE_VALUE(1802,"CHANGE_VALUE_1802"),
    POINT_VALID_TIME(1803,"POINT_VALID_TIME_1803"),
    POINT_EXPIRE_TIME(1804,"POINT_EXPIRE_TIME_1804"),
    DESC(1805,"DESC_1805"),
    CHANGE(1806,"CHANGE_1806"),
    CHANGE_POINT_ACCOUNT_NAME(1807,"CHANGE_POINT_ACCOUNT_NAME_1807"),
    START_EFFECTIVE(1808,"START_EFFECTIVE_1808"),
    PERMANENT_EFFECT(1809,"PERMANENT_EFFECT_1809"),
    REWARD_POINT(1810,"REWARD_POINT_1810"),
    OFFLINE_EXCHANGE(1811,"OFFLINE_EXCHANGE_1811"),
    GRADE_HIERARCHY(1812,"GRADE_HIERARCHY_1812"),
    CHANGE_GRADE(1813,"CHANGE_GRADE_1813"),
    GRADE_EXPIRE_TIME(1814,"GRADE_EXPIRE_TIME_1814"),
    CHANGE_GRADE_NAME(1815,"CHANGE_GRADE_1815"),
    VIP_DEMO_1(1816,"VIP_DEMO_1_1816"),
    CONSUMPTION_UPGRADE(1817,"CONSUMPTION_UPGRADE_1817"),
    SENIOR_MEMBR(1818,"SENIOR_MEMBR_18018"),
    DOUBLE_11_ORDER(1819,"DOUBLE_11_ORDER_18019"),
    FAIL_RECORD(1820,"FAIL_RECORD_18020"),
    LINE_CODE_MATCH(1821,"FAIL_RECORD_18020"),
    POINT_FORMAT_ERROR(1822,"FAIL_RECORD_18020"),
    EFFECTIVE_TIME_EMPTY(1823,"FAIL_RECORD_18020"),
    OVERATE_LET_NOW_TIME(1824,"FAIL_RECORD_18020"),
    OVERATE_TIME_EMPTY(1825,"FAIL_RECORD_18020"),
    OVERATE_TIME_ERROR(1826,"FAIL_RECORD_18020"),
    START_LET_NOW_TIME(1827,"FAIL_RECORD_18020"),
    START_LET_OVERATE_TIME(1828,"FAIL_RECORD_18020"),
    DEDUCT_POINT_TIME_NOT_EMPTY(1829,"FAIL_RECORD_18020"),
    OVERATE_LT_NOW_TIME(1830,"FAIL_RECORD_18020"),
    POINT_TYPE_NAME_MATCH(1831,"FAIL_RECORD_18020"),
    GRADE_NAME_MATCH(1832,"FAIL_RECORD_18020"),
    DESC_NOT_ALLOWED_GT_200(1833,"FAIL_RECORD_18020"),
    GRADE_HIERARCHY_NAME_MATCH(1834,"FAIL_RECORD_18020"),
    LINE_GRADE_NOT_FOUND(1835,"FAIL_RECORD_18020"),
    LINE_PLAN_FILE_NOT_FOUND(1836,"LINE_PLAN_FILE_NOT_FOUND_1836"),
    LINE_POINT_ACCOUNT_NOT_FOUND(1837,"LINE_POINT_ACCOUNT_NOT_FOUND_1837"),
    LINE_UPLOAD_INTERRUPT(1838,"LINE_UPLOAD_INTERRUPT_1838"),
    ERROR_INFO(1839,"ERROR_INFO_1839"),
    SAVE_FILE_ERROR(1840,"SAVE_FILE_ERROR_1840"),
    LINE_UPLOAD_ERROR(1841,"LINE_UPLOAD_ERROR_1841"),
    CALCUATE_ERROR(1842,"CALCUATE_ERROR_1842"),
    PLAN(1843,"PLAN_1843"),
    SHOP_ID(1844,"SHOP_ID_1843"),
    EFFECTIVE_TIME_ERROR(1845,"FAIL_RECORD_18020"),
    ENTER_SEND_POINT_VALUE(1846,"ENTER_SEND_POINT_VALUE_1846"),
    ENTER_DEDUCTION_POINT_VALUE(1847,"ENTER_DEDUCTION_POINT_VALUE_1847"),
    ENTER_START_EFFECTIVE_TEXT(1848,"ENTER_START_EFFECTIVE_TEXT_1848"),
    ENTER_EXPIRATION_TIME_TEXT(1849,"ENTER_EXPIRATION_TIME_TEXT_1849"),
    NOT_FILL_TEXT(1850,"NOT_FILL_TEXT_1850"),
    TURNTABLE_REWARD_POINT(1851,"TURNTABLE_REWARD_POINT_1851"),
    MEDAL_HIERARCHY(1852, "MEDAL_HIERARCHY_1852"),
    MEDAL_DEFINITION(1853, "MEDAL_DEFINITION_1853"),
    MEDAL_EXPIRE_TIME(1854, "MEDAL_EXPIRE_TIME_1854"),
    INPUT_MEDAL_HIERARCHY(1855, "INPUT_MEDAL_HIERARCHY_1855"),
    INPUT_MEDAL_DEFINITION(1856, "INPUT_MEDAL_DEFINITION_1856"),
    CHANGE_MEDAL_DESC(1857, "CHANGE_MEDAL_DESC_1857"),
    OBTAIN_MEDAL_DESC(1858, "OBTAIN_MEDAL_DESC_1858"),
    LINE_MEDAL_NOT_FOUND(1859, "LINE_MEDAL_NOT_FOUND_1859"),
    MEDAL_HIERARCHY_NAME_MATCH(1860, "MEDAL_HIERARCHY_NAME_MATCH_1860"),
    MEDAL_NAME_MATCH(1861, "MEDAL_NAME_MATCH_1861")
}
