package com.shuyun.loyalty.service.repository

import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.loyalty.service.meta.LimitActionEnum
import com.shuyun.loyalty.service.meta.LimitCycleEnum
import com.shuyun.loyalty.service.model.ManuallyPointRecord
import com.shuyun.loyalty.service.util.DateUtils
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.LocalDate

@Component
class ManuallyPointRecordRepository: DataModelRepository<ManuallyPointRecord>() {
    private val log = LogManager.getLogger(ManuallyPointRecordRepository::class.java)
    override fun log(): Logger = this.log

    fun getTotalPointByUserId(userId: String?, importFileConfigId: String?, limitCycle: LimitCycleEnum?, action: LimitActionEnum): BigDecimal {
        if (limitCycle == null) return BigDecimal.ZERO
        val startDate = DateUtils.getStartDateByType(LocalDate.now(), limitCycle)
        val endDate = LocalDate.now()
        val sql = "select ifnull(sum(`changePoint`), 0) as sum from data.loyalty.manager.manuallyPointRecord " +
            "where importFileConfigId = '$importFileConfigId' " +
            "and action = '$action' " +
            "and `changeDate` >= '$startDate' and `changeDate` <= '$endDate'"
        log().debug("execute one sql: {}",sql)
        val result = DmPoolFactory.execute { it.execute(sql, mutableMapOf()).data }
        result?.let {
            val sumMap = it[0]
            if (sumMap is Map<*, *>)
                return sumMap["sum"].toString().toBigDecimal()
        }
        return BigDecimal.ZERO
    }

}