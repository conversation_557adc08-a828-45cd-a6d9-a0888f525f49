package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.model.PointSendRule
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface PointSendRuleRepository : JpaRepository<PointSendRule, Long> {

    fun deleteByRuleGroupId(ruleGroupId: Long)
    fun findByRuleGroupIdAndDisabled(ruleGroupId: Long,disabled:Boolean):List<PointSendRule>
    fun findByRuleGroupId(ruleGroupId: Long):List<PointSendRule>
    fun deleteByIdIn(ids: List<Long>)
    fun findByRuleGroupIdIn(ruleGroupIds: List<Long>): List<PointSendRule>
    fun findByRuleGroupIdAndId(ruleGroupId: Long, id: Long): Optional<PointSendRule>

}