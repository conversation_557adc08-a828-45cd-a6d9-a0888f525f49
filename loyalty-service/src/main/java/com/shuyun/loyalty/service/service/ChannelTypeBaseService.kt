package com.shuyun.loyalty.service.service

import com.github.benmanes.caffeine.cache.Cache
import com.github.benmanes.caffeine.cache.Caffeine
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.model.ChangeModeModel
import com.shuyun.loyalty.service.model.ChannelTypeModel
import com.shuyun.loyalty.service.repository.ChangeModeRepository
import com.shuyun.loyalty.service.repository.ChannelTypeRepository
import com.shuyun.pip.ApplicationContextHolder
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit

@Service
class ChannelTypeBaseService {

    private val cache: Cache<String, ChannelTypeModel> = Caffeine.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(30, TimeUnit.DAYS)
        .build()

    @Autowired
    private lateinit var channelTypeRepository: ChannelTypeRepository

    fun check(code: String) {
        var channel = cache.getIfPresent(code)
        if (channel == null) {
            cache.invalidateAll()
            val list = ApplicationContextHolder.getBean(ChannelTypeBaseService::class.java).findAll()
                .filter { it.code == code && !it.disabled }
            list.forEach {
                cache.put(it.code!!, it)
            }
            channel = cache.getIfPresent(code)
        }
        if (channel == null) {
            throw LoyaltyException(LoyaltyExceptionCode.CHANNEL_TYPE_ERROR)
        }
    }

    fun findAll(): List<ChannelTypeModel> {
        val listPair = channelTypeRepository.findAll()
        return listPair.map { ChannelTypeModel(it.first, it.second, it.third) }
    }
}


@Service
class ChangeModeBaseService {

    private val cache: Cache<String, ChangeModeModel> = Caffeine.newBuilder()
        .maximumSize(1000)
        .expireAfterWrite(30, TimeUnit.DAYS)
        .build()

    @Autowired
    private lateinit var changeModeRepository: ChangeModeRepository

    fun check(code: String) {
        var changeMode = cache.getIfPresent(code)
        if (changeMode == null) {
            cache.invalidateAll()
            val list = ApplicationContextHolder.getBean(ChangeModeBaseService::class.java).findAll()
                .filter { it.code == code && !it.disabled }
            list.forEach {
                cache.put(it.code!!, it)
            }
            changeMode = cache.getIfPresent(code)
        }
        if (changeMode == null) {
            throw LoyaltyException(LoyaltyExceptionCode.CHANGE_MODEL_ERROR)
        }
    }

    fun findAll(): List<ChangeModeModel> {
        val listPair = changeModeRepository.findAll()
        return listPair.map { ChangeModeModel(it.first, it.second, it.third) }
    }

}