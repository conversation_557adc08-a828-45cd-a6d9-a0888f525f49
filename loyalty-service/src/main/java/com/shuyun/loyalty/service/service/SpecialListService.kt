package com.shuyun.loyalty.service.service

import com.github.benmanes.caffeine.cache.Caffeine
import com.github.benmanes.caffeine.cache.LoadingCache
import com.github.benmanes.caffeine.cache.Scheduler
import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.exception.MemberException
import com.shuyun.loyalty.service.meta.TypeEnum
import com.shuyun.loyalty.service.model.ForbiddenConfig
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.transfer.points.PointForbiddenConfig
import com.shuyun.loyalty.service.util.PropertyUtils
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import org.apache.logging.log4j.LogManager
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit

@Service
class SpecialListService {

    private val logger = LogManager.getLogger(SpecialListService::class.java)

    private val cache: LoadingCache<CacheKey, List<PointForbiddenConfig>> =
        Caffeine.newBuilder()
            .maximumSize(10_000)
            .refreshAfterWrite(1, TimeUnit.MINUTES)
            .scheduler(Scheduler.systemScheduler())
            .build { key ->
                loadForbiddenConfig(key.subjectId, key.hierarchyId, key.type)
            }

    data class CacheKey(val subjectId: Long, val hierarchyId: Long, val type: TypeEnum)
    fun find(subjectId: Long, hierarchyId: Long, type: TypeEnum): List<PointForbiddenConfig> {
        logger.debug("查询黑名单配置：subjectId={}, hierarchyId={}", subjectId, hierarchyId)
        val configs = cache.get(CacheKey(subjectId, hierarchyId, type)) ?: emptyList()
        logger.debug("查询黑名单配置结果：{}", { JsonUtils.toJson(configs) })
        return  configs
    }
    fun loadForbiddenConfig(subjectId: Long, hierarchyId: Long, type: TypeEnum): List<PointForbiddenConfig> {
        val svc = ApplicationContextHolder.getBean(SpecialListConfigBaseService::class.java)
        val specialListConfigs = svc.findBySubjectIdAndDisabledAndStatus(subjectId)
        val forbiddenConfigs = specialListConfigs.map {
            var forbiddens: Map<ForbiddenPort, List<ForbiddenOperation>> = emptyMap()
            val forbiddenConfig = Json.parse<Map<String, List<ForbiddenConfig>>>(it.forbiddenConfigString!!)
            forbiddenConfig[type.type]?.firstOrNull { config -> config.id == hierarchyId }?.let { x ->
                forbiddens = x.forbiddens
            }
            PointForbiddenConfig(it.columnPath, it.specialListGroupIds, forbiddens)
        }.filter { it.forbiddens.isNotEmpty() }
        return forbiddenConfigs
    }

    // 1. 检查会员是否存在，如果不存在则抛出异常
    // 2. 检查操作是否在特殊名单中
    fun check(
        subjectFqn: String,
        memberId: String,
        forbiddenOperation: ForbiddenOperation,
        forbiddenPort: ForbiddenPort,
        pointForbiddenConfigs: List<PointForbiddenConfig>?
    ): Boolean {
        if (pointForbiddenConfigs.isNullOrEmpty()) {
            return false
        }
        if (PropertyUtils.getCheckMemberExist()) {
            try {
                val id = DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
                    sdk.getObject(subjectFqn, memberId, "id")
                }
                if (id == null) {
                    logger.warn("会员信息查询失败，fqn:{}, memberId:{}", subjectFqn, memberId)
                    throw MemberException(LoyaltyExceptionCode.MEMBER_NOT_FOUND)
                }
            } catch (e: Exception) {
                logger.warn("会员信息查询失败，fqn:{}, memberId:{}", subjectFqn, memberId, e)
                throw MemberException(LoyaltyExceptionCode.MEMBER_NOT_FOUND, e)
            }
        }
        val specialListConfigBaseService = ApplicationContextHolder.getBean(SpecialListConfigBaseService::class.java)
        var inCheckList = false
        @Suppress("UNCHECKED_CAST")
        for (config in pointForbiddenConfigs) {
            if (config.forbiddens[forbiddenPort]?.contains(forbiddenOperation) == true) {
                val fqn = config.columnPath!!.substringBeforeLast(".")
                val columnPath = config.columnPath!!.substringAfterLast(".")
                val result = try {
                    DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
                        sdk.getObject(fqn, memberId, columnPath) as Map<String, Any?>
                    }!!
                } catch (e: Exception) {
                    throw MemberException(LoyaltyExceptionCode.MEMBER_NOT_FOUND, e)
                }
                val columnValue = result[columnPath] ?: continue
                inCheckList = specialListConfigBaseService.hashCheckList(columnValue.toString(), fqn, config.specialListGroupIds!!)
                if (inCheckList) {
                    break
                }
            }
        }

        return inCheckList
    }
}