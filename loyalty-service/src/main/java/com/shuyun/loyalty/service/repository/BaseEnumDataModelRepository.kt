package com.shuyun.loyalty.service.repository

import com.pip.mybatisplus.toolkit.MetadataFactory
import com.shuyun.dm.api.metadata.request.GetModelRequest
import java.lang.reflect.ParameterizedType
import javax.persistence.Table

abstract class BaseEnumDataModelRepository<T> {

    fun findAll(): List<Triple<String, String, Boolean>> {
        val typeImpl = this.javaClass.superclass.genericSuperclass as ParameterizedType
        val table = (typeImpl.actualTypeArguments[0] as Class<*>).getAnnotation(Table::class.java)
        val request = GetModelRequest()
        request.fqn = table!!.name
        val model = MetadataFactory.sdk().getModel(request)
        return model.enums?.map { Triple(it.text, it._i18nPayload["zh-CN"]?:"", it.disabled) } ?: emptyList()
    }

}