package com.shuyun.loyalty.service.message

import com.shuyun.fx.schema.JsonFinder
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.service.ChannelTypeBaseService
import com.shuyun.loyalty.service.service.EventTypeBaseService
import com.shuyun.pip.ApplicationContextHolder
import org.apache.logging.log4j.LogManager
import kotlin.properties.Delegates

abstract class EventStreamEventMessage: EventMessage {
    private val log = LogManager.getLogger(EventStreamEventMessage::class.java)

    private lateinit var event: Event
    //会在规则组处理时设置该值
    var eventTypeId: Long by Delegates.notNull()

    fun setEvent(event: Event) {
        this.event = event
    }

    override fun event(): Event = event

    open fun findChannelType(): String {
        val svc = ApplicationContextHolder.getBean(EventTypeBaseService::class.java)
        val eventType = svc.getEffectiveOne(eventTypeId, event.getOccurrenceZonedDateTime())
        var channelCode = "loyalty"
        return if (eventType.channelTypePath == null) {
            channelCode
        } else {
            try {
                channelCode = JsonFinder.find(event.asObjectNode(), eventType.channelTypePath!!).asText()
                ApplicationContextHolder.getBean(ChannelTypeBaseService::class.java).check(channelCode)
            } catch (e: LoyaltyException) {
                log.warn("获取渠道失败:{}", e.message)
            } catch (e: Throwable) {
                log.warn("获取渠道失败", e)
            }
            return channelCode
        }
    }

    open fun findEffectEventType(): Boolean {
        try {
            // 通过时机ID和时间查询时机项
            val eventType = ApplicationContextHolder.getBean(EventTypeBaseService::class.java).getEffectiveOne(eventTypeId,event.getOccurrenceZonedDateTime())
            if(eventType.eventStream != event.getFqn()){
                log.debug("事件流FQN(${event.getFqn()})与时机FQN(${eventType.eventStream })不匹配")
                return true
            }
        } catch (e: Exception) {
            if (e is LoyaltyException) {
                log.warn("查询时机出现错误: {}", e.msg)
            } else {
                log.warn("查询时机出现错误", e)
            }
        }
        return false
    }
}