package com.shuyun.loyalty.service.model

import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.datamodel.BaseDataModel
import com.shuyun.pip.i18n.I18nPayload
import io.swagger.v3.oas.annotations.media.Schema
import javax.persistence.Column
import javax.persistence.Table

@Schema(title = "导入扩展字段配置")
@Table(name = "data.loyalty.manager.importExtensionSetting")
@DataServiceModel
data class ImportExtensionSetting(

    @Schema(title = "导入文件配置ID", type = "String")
    @Column
    var importFileConfigId: String? = null,

    @Schema(title = "字段名", type = "String")
    @Column
    var fieldName: String? = null,

    @Schema(title = "字段显示名称", type = "String")
    @Column
    var fieldTitle: String? = null,

    @Schema(title = "设置", type = "String")
    @Column
    var settings: String? = null,

    @Schema(title = "国际化信息", type = "java.lang.Object")
    @Column
    var _i18nPayload: I18nPayload? = null

) : BaseDataModel()
