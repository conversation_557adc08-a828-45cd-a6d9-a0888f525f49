package com.shuyun.loyalty.service.infrastructure.epassport

import com.shuyun.epassport.sdk.RegisterRuleRequest
import com.shuyun.epassport.sdk.enums.Operation
import com.shuyun.epassport.sdk.enums.ResourceType
import com.shuyun.epassport.sdk.register.RequiresPermissions
import com.shuyun.lite.context.GlobalContext
import org.springframework.context.ApplicationContext
import org.springframework.core.MethodIntrospector
import org.springframework.core.annotation.AnnotationUtils
import org.springframework.stereotype.Controller
import org.springframework.util.ClassUtils
import org.springframework.util.ReflectionUtils
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod


object RegisterPathUtil {

    fun getRegisterRuleList(applicationContext: ApplicationContext, requestMappingTool: RequestMappingTool): List<RegisterRuleRequest> {
        val ruleList = ArrayList<RegisterRuleRequest>()
        val beanNames = applicationContext.getBeanNamesForAnnotation(Controller::class.java)
        for (beanName in beanNames) {
            if (isHandler(applicationContext.getType(beanName)!!)) {
                val rules = detectHandlerMethods(applicationContext, requestMappingTool, beanName)
                ruleList.addAll(rules)
            }
        }
        return ruleList
    }

    private fun isHandler(beanType: Class<*>): Boolean {
        return AnnotationUtils.findAnnotation(
            beanType,
            Controller::class.java
        ) != null || AnnotationUtils.findAnnotation(beanType, RequestMapping::class.java) != null
    }


    private fun detectHandlerMethods(ctx: ApplicationContext, requestMappingTool: RequestMappingTool, handler: String): List<RegisterRuleRequest> {
        val ruleList = ArrayList<RegisterRuleRequest>()
        val handlerType = ctx.getType(handler)
        val userType: Class<*> = ClassUtils.getUserClass(handlerType!!)
        val contextPath = ("/" + GlobalContext.serviceName()) + "/*"
        MethodIntrospector.selectMethods(userType, ReflectionUtils.MethodFilter { method ->
            val mapping = requestMappingTool.getMappingForMethod(method, userType)
            if (mapping != null) {
                //所有匹配路径
                // Spring 5.x: when PathPatternParser is enabled, patternsCondition is null and pathPatternsCondition is used instead
                val patterns: Set<String> = mapping.patternsCondition
                    ?.patterns
                    ?: mapping.pathPatternsCondition?.patterns
                        ?.map { it.patternString }  // PathPattern -> plain string
                        ?.toSet()
                        ?: emptySet()
                //所有访问类型
                val requestMethods = mapping.methodsCondition.methods
                //权限配置
                var permissions = method.getAnnotation(RequiresPermissions::class.java)
                if (permissions == null) {
                    permissions = userType.getAnnotation(RequiresPermissions::class.java)
                }
                if (patterns.isNotEmpty() && requestMethods.isNotEmpty() && permissions != null) {
                    for (p in patterns) {
                        val pattern = p.replace("\\{.*?}".toRegex(), "*")
                        val permissionSize = permissions.value
                        if (permissionSize.isEmpty()) {
                            val rule = RegisterRuleRequest()
                            rule.resourceType = ResourceType.URI
                            rule.resourceId = normalizePath(contextPath, pattern)
                            rule.isAllowAnonymous = permissions.allowAnonymous
                            rule.isAllowAuthenticated = permissions.allowAuthenticated
                            rule.operations = transOperationSet(requestMethods)
                            rule.permissionCode = null
                            ruleList.add(rule)
                        } else {
                            for (permission in permissionSize) {
                                val rule =
                                    RegisterRuleRequest()
                                rule.resourceType = ResourceType.URI
                                rule.resourceId = normalizePath(contextPath, pattern)
                                rule.isAllowAnonymous = permissions.allowAnonymous
                                rule.isAllowAuthenticated = permissions.allowAuthenticated
                                rule.permissionCode = permission
                                rule.operations = transOperationSet(requestMethods)
                                ruleList.add(rule)
                            }
                        }
                    }
                }
                return@MethodFilter true
            } else {
                return@MethodFilter false
            }
        })
        return ruleList
    }


    private fun transOperationSet(methods: Set<RequestMethod?>): Set<Operation> {
        val operationSet: MutableSet<Operation> = HashSet()
        for (requestMethod in methods) {
            when (requestMethod) {
                RequestMethod.GET -> operationSet.add(Operation.VIEW)
                RequestMethod.POST -> operationSet.add(Operation.ADD)
                RequestMethod.PUT -> operationSet.add(Operation.UPDATE)
                RequestMethod.PATCH -> operationSet.add(Operation.UPDATE)
                RequestMethod.DELETE -> operationSet.add(Operation.DELETE)
                else -> {}
            }
        }
        return operationSet
    }


    private fun normalizePath(basePath: String, path: String?): String {
        if (path == null) {
            return basePath
        }
        if (basePath.endsWith("/")) {
            return if (path.startsWith("/")) basePath + path.substring(1) else basePath + path
        }
        return if (path.startsWith("/")) basePath + path else "$basePath/$path"
    }
}