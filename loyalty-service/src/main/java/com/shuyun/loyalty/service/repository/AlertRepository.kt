package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.datamodel.Alert
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

/**
 *
 * @Author: sky
 */
@Component
class AlertRepository : DataModelRepository<Alert>() {
    private val log = LogManager.getLogger(AlertRepository::class.java)
    override fun log(): Logger = this.log
}