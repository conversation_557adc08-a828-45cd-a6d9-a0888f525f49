package com.shuyun.loyalty.service.infrastructure.log

import com.shuyun.epassport.sdk.register.RequiresPermissions
import io.swagger.v3.oas.annotations.Operation
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.core.config.Configurator
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

/**
 * 日志管理接口
 */
@RestController
@RequestMapping("/public/log")
@RequiresPermissions(allowAuthenticated = true)
class LogMgtController {
    private val log = LogManager.getLogger(LogMgtController::class.java)

    @Operation(summary = "日志", tags = ["实施工具"])
    @GetMapping("")
    fun log() {
        log.debug("debug日志")
        log.info("info日志")
        log.error("error日志")
        log.trace("trace日志")
        log.warn("warn日志")
    }

    @Operation(summary = "变更日志级别", tags = ["实施工具"])
    @GetMapping("/update")
    fun update(@RequestParam("level") level: String, @RequestParam("packageName", required = false) packageName: String?=null) {
        Configurator.setLevel(packageName, level)
        log.info("日志级别修改为: {}", level)
    }
}