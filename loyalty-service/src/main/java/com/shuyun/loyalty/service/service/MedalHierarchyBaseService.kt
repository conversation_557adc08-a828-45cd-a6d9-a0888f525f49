package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.entity.api.constants.PublishStatusEnum
import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.exception.MedalException
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.model.MedalHierarchy
import com.shuyun.loyalty.service.repository.MedalHierarchyRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.lang.Boolean.FALSE
import java.time.ZonedDateTime
import java.util.*

@Service
class MedalHierarchyBaseService {

    @Autowired
    private lateinit var medalHierarchyRepository: MedalHierarchyRepository

    @Autowired
    private lateinit var medalDefinitionBaseService: MedalDefinitionBaseService

    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun insert(medalHierarchy: MedalHierarchy) {
        medalHierarchy.name = medalHierarchy.name!!.trim()
        medalHierarchy.versionId = null
        medalHierarchy.status = PublishStatusEnum.DRAFT
        medalHierarchyRepository.save(medalHierarchy)
        medalHierarchy.id = medalHierarchy.versionId
        medalHierarchyRepository.save(medalHierarchy)
    }

    fun findDetailBySubjectVersionId(subjectVersionId: Long): List<MedalHierarchy> {
        val medalHierarchyList = medalHierarchyRepository.findBySubjectVersionIdAndDisabled(subjectVersionId, FALSE)
        medalHierarchyList.forEach {
            it.medalDefinitions = medalDefinitionBaseService.findEnabledMedalHierarchyByVersionId(it.versionId!!)
        }
        return medalHierarchyList
    }

    fun findPageBySubjectVersionIdList(subjectVersionIdList: List<Long>): List<MedalHierarchy> {
        return medalHierarchyRepository.pageListBySubjectVersionIdAndDisabled(subjectVersionIdList, FALSE)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun delete(medalHierarchy: MedalHierarchy) {
        medalHierarchy.disabled = true
        medalHierarchyRepository.save(medalHierarchy)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun save(medalHierarchy: MedalHierarchy) {
        medalHierarchyRepository.save(medalHierarchy)
    }

    fun findById(id: Long?): Optional<MedalHierarchy> {
        return if (null == id) return Optional.empty() else medalHierarchyRepository.findById(id.toString())
    }

    fun getEffectiveOneCache(id: Long): MedalHierarchy {
        return getEffectiveOne(id, ZonedDateTime.now())
    }

    fun getEffectiveOneCache(id: Long, eventTime: ZonedDateTime): MedalHierarchy {
        return getEffectiveOne(id, eventTime)
    }

    fun getEffectiveOne(id: Long, date: ZonedDateTime): MedalHierarchy {
        return medalHierarchyRepository.findByIdAndDate(id, date).orElseThrow {
            throw MedalException(LoyaltyExceptionCode.MEDAL_HIERARCHY_NOT_FOUND)
        }
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun update(medalHierarchy: MedalHierarchy) {
        medalHierarchy.name = medalHierarchy.name!!.trim()
        medalHierarchyRepository.save(medalHierarchy)
    }


    fun findAll(): List<MedalHierarchy> {
        return medalHierarchyRepository.findAll()
    }

}