package com.shuyun.loyalty.service.repository

import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.dm.api.dataapi.request.QueryDataRequest
import com.shuyun.dm.api.vo.PageQueryResponse
import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.service.datamodel.BaseDataModelVersion
import com.shuyun.loyalty.service.infrastructure.idGenerator.IdSdkFactory
import com.shuyun.loyalty.service.model.BaseDataModelAnalyzer
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import com.shuyun.pip.i18n.LocaleI18nContextHolder
import org.apache.logging.log4j.Logger
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.util.ObjectUtils
import java.lang.reflect.ParameterizedType
import java.util.*
import javax.persistence.Table

@Suppress("UNCHECKED_CAST")
abstract class DataModelCarryRepository<T : BaseDataModelVersion> {

    private var currentModelClazz = getCurrentActualTypeArgument()

    private var modelFqn = currentModelClazz.getDeclaredAnnotation(Table::class.java)!!.name

    private var analyzer = BaseDataModelAnalyzer(currentModelClazz)
    private var fields = analyzer.getFields()
    private var fieldsNoI18 = analyzer.getFields().replace(",_i18nPayload", "")
    private var manyToOneFieldList = analyzer.getManyToOneFields().split(",")
    private var idField = analyzer.getIdField()
    private var fieldList = fields.split(",")

    open fun getFieldListNoI18() = this.fieldsNoI18
    open fun getFieldList() = this.fieldList
    open fun getIdField() = this.idField
    open fun getManyToOneFieldList() = this.manyToOneFieldList
    open fun getFields() = this.fields
    open fun getAnalyzer() = this.analyzer
    open fun getModelFqn(replacePattern: String?): String {
        return this.modelFqn

    }

    open fun getCurrentModelClazz() = this.currentModelClazz

    abstract fun log(): Logger


    fun executeList(sql: String, params: Map<String, Any>): List<T> {
        val resultResponse = DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.execute(sql.plus(" limit 5000"), params) }
        return resultResponse!!.data.mapNotNull {
            JsonUtils.parse(JsonUtils.toJson(it), getCurrentModelClazz())
        }
    }


    fun executeOne(sql: String, params: Map<String, Any>): Optional<T> {
        val resultResponse = DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.execute(sql, params) }
        val resultObject = resultResponse!!.data
        return if (resultObject.isEmpty()) Optional.empty() else Optional.of(JsonUtils.parse(JsonUtils.toJson(resultObject[0]), getCurrentModelClazz()))
    }

    /** 查询所有记录，findAll只返回最大1000 */
    fun findAll(replacePattern: String? = null): List<T> {
        val fields = getFieldListNoI18()
        val resultResponse = queryObjects(getModelFqn(replacePattern), fields, "{}", "{}", false, 0, 5000)
        return resultResponse!!.data.asSequence().map {
            JsonUtils.parse(JsonUtils.toJson(it), getCurrentModelClazz())
        }.filterNotNull().toList()
    }


    fun findById(id: String, replacePattern: String? = null): Optional<T> {
        val fields = getFieldListNoI18()
        val result = try {
            DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.getObject(getModelFqn(replacePattern), id, fields) }
        } catch (e: SdkException) {
            if (null != e.code && 404 == e.code!!.toInt()) {
                return Optional.empty()
            } else {
                throw e
            }
        }
        val resultString = JsonUtils.toJson(result)
        val resultObject = JsonUtils.parse(resultString, getCurrentModelClazz())
        return Optional.of(resultObject)
    }

    fun findListByFilter(filter: String, sort: String = "{}", offset: Int = 0, limit: Int = 2000, replacePattern: String? = null): List<T> {
        val fields = getFieldListNoI18()
        val resultResponse = queryObjects(getModelFqn(replacePattern), fields, filter, sort, false, offset, limit)
        return resultResponse!!.data.mapNotNull {
            JsonUtils.parse(JsonUtils.toJson(it), getCurrentModelClazz())
        }
    }


    fun findListByFilter(filter: String, sort: String = "{}", offset: Int = 0, limit: Int = 2000, withTotals: Boolean, replacePattern: String? = null): Map<String, Any?> {
        val fields = getFieldListNoI18()
        val resultResponse = queryObjects(getModelFqn(replacePattern), fields, filter, sort, withTotals, offset, limit)
        val data = resultResponse!!.data.mapNotNull {
            JsonUtils.parse(JsonUtils.toJson(it), getCurrentModelClazz())
        }
        return mapOf<String, Any?>(Pair("data", data), Pair("totals", resultResponse.totals))
    }


    fun findPageByFilter(filter: String, sort: String = "{}", pageable: Pageable, replacePattern: String? = null): Page<T> {
        val fields = getFieldListNoI18()
        val resultResponse = queryObjects(getModelFqn(replacePattern), fields, filter, sort, true, pageable.pageNumber * pageable.pageSize, pageable.pageSize)
        val page = PageImpl(
            resultResponse!!.data.mapNotNull {
                JsonUtils.parse(JsonUtils.toJson(it), getCurrentModelClazz())
            },
            pageable,
            resultResponse.totals.toLong()
        )
        return page
    }


    fun findOneByFilter(filter: String, replacePattern: String? = null): Optional<T> {
        val fields = getFieldListNoI18()
        val resultResponse = queryObjects(getModelFqn(replacePattern), fields, filter, "{}", false, 0, 1)
        val resultObject = resultResponse!!.data
        return if (resultObject.isEmpty()) Optional.empty() else Optional.of(JsonUtils.parse(JsonUtils.toJson(resultObject[0]), getCurrentModelClazz()))
    }


    @Suppress("UNCHECKED_CAST")
    fun save(t: T, replacePattern: String? = null): T {
        log().debug("save fqn {} manyToOne fields {} values: {}", {getModelFqn(replacePattern)}, {getFields()}, {JsonUtils.toJson(t)})
        val dataMap = JsonUtils.parse(JsonUtils.toJson(t), Map::class.java) as Map<String, Any?>
        var columnMap = HashMap<String, Any?>()
        val fieldList = getFieldList()
        listOf(getManyToOneFieldList(), fieldList).flatten().forEach { key ->
            if (fieldList.contains(key)) {
                columnMap[key] = dataMap[key]

            } else if (getManyToOneFieldList().contains(key)) {
                var manyToOneValue = JsonUtils.parse(JsonUtils.toJson(dataMap[key]), Map::class.java) as Map<String, Any?>?
                if (manyToOneValue == null) {
                    val foreignKey = getAnalyzer().getManyToOneFieldForeignKey(key)
                    if (foreignKey is String) {
                        manyToOneValue = mapOf(Pair("id", dataMap[foreignKey]))
                        columnMap[key] = mapOf(Pair("id", manyToOneValue["id"]))
                    }
                } else {
                    columnMap[key] = mapOf(Pair("id", manyToOneValue["id"]))
                }
            }
        }
        columnMap = toI18NullMap(fieldList, columnMap)
        val idValue = dataMap[getIdField()]
        val id = if (null == idValue) {
            columnMap["versionId"] = IdSdkFactory.nextVersionId()
            log().debug("save data of fqn {} params {}", getModelFqn(replacePattern), JsonUtils.toJson(columnMap))
            val insertResponse = DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.insert(getModelFqn(replacePattern), columnMap, false, false) }
            insertResponse!!.id
        } else {
            log().debug("update data of fqn {} by id {} values {}", getModelFqn(replacePattern), idValue, JsonUtils.toJson(columnMap))
            val update = DmPoolFactory.execute { sdk -> sdk.update(getModelFqn(replacePattern), idValue.toString(), columnMap, false) }!!
            if (update.affectedRows == 0) {
                log().warn("update value effect row count 0 of fqn {} by id {} values {}", getModelFqn(replacePattern), idValue, JsonUtils.toJson(columnMap))
            }
            idValue
        }
        t.versionId = id.toString().toLong()
        return t
    }


    fun delete(id: String, replacePattern: String? = null) {
        log().debug("delete fqn {} value by id {}", getModelFqn(replacePattern), id)
        DmPoolFactory.execute { sdk -> sdk.delete(getModelFqn(replacePattern), id, false) }
    }

    fun deleteByIds(ids: List<String>, replacePattern: String? = null) {
        if (ids.isEmpty()) return
        val bodyParams = mapOf(
            "id" to mapOf("\$in" to ids)
        )
        deleteByFilter(JsonUtils.toJson(bodyParams), replacePattern = replacePattern)
    }

    fun deleteByFilter(filter: String, replacePattern: String? = null) {
        log().debug("delete fqn {} value by filter {}", getModelFqn(replacePattern), filter)
        DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.deleteByFilter(getModelFqn(replacePattern), filter) }
    }

    @Suppress("UNCHECKED_CAST")
    private fun getCurrentActualTypeArgument(): Class<T> = this.javaClass.genericSuperclass.let {
        when (it) {
            is ParameterizedType -> it.actualTypeArguments[0]
            else -> ((it as Class<*>).genericSuperclass as ParameterizedType).actualTypeArguments[0]
        } as Class<T>
    }

    @Suppress("UNCHECKED_CAST")
    private fun toI18NullMap(fieldList: List<String>, columnMap: HashMap<String, Any?>): HashMap<String, Any?> {
        val i18nPayloadMap = HashMap<String, Any>()
        fieldList.forEach { key ->
            if (!columnMap.containsKey(key)) {
                columnMap[key] = null
            }
            if (key == "_i18nPayload") {
                if (columnMap[key] == null) return@forEach
                val map = (columnMap[key] as HashMap<String, Any?>)
                val acceptLanguage = LocaleI18nContextHolder.getAcceptLanguage()
                val iter = map.iterator()
                while (iter.hasNext()) {
                    val entry = iter.next()
                    val i18nMap = JsonUtils.objectToMap(entry.value)
                    if (i18nMap!!["zh-CN"] == null) i18nMap["zh-CN"] = null
                    if (i18nMap["en-US"] == null) i18nMap["en-US"] = null
                    if (i18nMap["zh-HK"] == null) i18nMap["zh-HK"] = null
                    if (i18nMap[acceptLanguage] == null) i18nMap[acceptLanguage] = columnMap[entry.key]
                    i18nPayloadMap[entry.key] = i18nMap
                }
            }
        }
        if (!ObjectUtils.isEmpty(columnMap["_i18nPayload"])) columnMap["_i18nPayload"] = i18nPayloadMap
        return columnMap
    }

    private fun queryObjects(
        fqn: String?,
        fields: String?,
        filter: String?,
        sort: String?,
        withTotals: Boolean?,
        offset: Int?,
        limit: Int?
    ): PageQueryResponse? {
        val request = QueryDataRequest()
        request.fqn = fqn
        request.fields = fields
        request.filter = filter
        request.sort = sort
        request.withTotals = withTotals
        request.offset = offset
        request.limit = limit
        return DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.queryObjects(request) }
    }
}