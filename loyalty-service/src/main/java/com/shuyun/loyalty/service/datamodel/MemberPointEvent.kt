package com.shuyun.loyalty.service.datamodel

import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.util.SQL
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.Table

// 原始订单事件
@Table(name = "data.loyalty.member.MemberPointEvent{*}")
@DataServiceModel
@FqnVariableModel
class MemberPointEvent: BaseDataModel() {

    @Column
    lateinit var fqn: String

    @Column
    var planId: Long = 0L

    @Column
    var subjectId: Long = 0L

    @Column
    var hierarchyId: Long = 0L

    @Column
    var eventTypeId: Long = 0L

    @Column
    lateinit var  memberId: String

    @Column
    lateinit var traceId: String

    @Column
    lateinit var uniqueId: String

    @Column
    lateinit var operation: EventOperationEnum

    @Column
    var occurrenceTs: Long = 0L

    // 渠道
    @Column
    var channel: String? = null

    // 店铺
    @Column
    var shopId: String? = null

    // 扩展字段1
    @Column
    var kzzd1: String? = null

    // 扩展字段1
    @Column
    var kzzd2: String? = null

    // 扩展字段3
    @Column
    var kzzd3: String? = null


    @Column
    lateinit var event: String


    @Column
    var created: ZonedDateTime = ZonedDateTime.now()

    companion object
}


// 保存积分任务记录
fun MemberPointEvent.save() {
    listOf(this).save(hierarchyId)
}


fun MemberPointEvent.Companion.findById(hierarchyId: Long, id: String): MemberPointEvent? {
    val sql = """
        SELECT 
            `id`,`fqn`,`planId`,`subjectId`,`hierarchyId`,`eventTypeId`,
            `memberId`,`traceId`,`uniqueId`,`operation`,`occurrenceTs`,
            `channel`,`shopId`,`kzzd1`,`kzzd2`,`kzzd3`,`event`,`created` 
        FROM data.loyalty.member.MemberPointEvent$hierarchyId 
        WHERE id = :id
    """.trimIndent()
    val result = DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
        sdk.execute(sql, mapOf("id" to id))
    }!!
    if (!result.isSuccess || result.data.isEmpty()) {
        return null
    }
    val m = result.data[0]
    return JsonUtils.convert(m, MemberPointEvent::class.java)
}


fun List<MemberPointEvent>.save(hierarchyId: Long) {
    if (this.isEmpty()) return
    val self = this
    val sql = SQL.build {
        var sql = """
            INSERT INTO data.loyalty.member.MemberPointEvent$hierarchyId 
                (
                    id, fqn, planId, subjectId, hierarchyId, eventTypeId, memberId, traceId, uniqueId,
                    operation, occurrenceTs, channel, shopId, kzzd1, kzzd2,
                    kzzd3, event, created
                )
            VALUES 
        """.trimIndent()
        for (e in self) {
            sql += """
                (
                ${e.id.escaped()},
                ${e.fqn.escaped()},
                ${e.planId.escaped()},
                ${e.subjectId.escaped()},
                ${e.hierarchyId.escaped()},
                ${e.eventTypeId.escaped()},
                ${e.memberId.escaped()},
                ${e.traceId.escaped()},
                ${e.uniqueId.escaped()},
                ${e.operation.escaped()},
                ${e.occurrenceTs.escaped()},
                ${e.channel.escaped()},
                ${e.shopId.escaped()},
                ${e.kzzd1.escaped()},
                ${e.kzzd2.escaped()},
                ${e.kzzd3.escaped()},
                ${e.event.escaped()},
                ${e.created.escaped()}
                ),
            """.trimIndent()
        }
        sql.trim().dropLast(1)
    }
    DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.execute(sql, mapOf()) }
}

// 查询关联时机
fun MemberPointEvent.Companion.find(hierarchyId: Long, eventTypeId: Long, traceId: String, operation: EventOperationEnum): MemberPointEvent? {
    val sql = """
        SELECT 
            id, fqn, planId, subjectId, hierarchyId, eventTypeId, memberId, traceId, uniqueId,
            operation, occurrenceTs, created
        FROM data.loyalty.member.MemberPointEvent$hierarchyId
        WHERE traceId = :traceId and eventTypeId = :eventTypeId and operation = :operation 
        ORDER BY occurrenceTs DESC
        LIMIT 1 
    """.trimIndent()
    val result = DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
        sdk.execute(sql, mapOf("eventTypeId" to eventTypeId, "traceId" to traceId, "operation" to operation.name))
    }!!
    if (!result.isSuccess || result.data.isEmpty()) {
        return null
    }
    val m = result.data[0]
    return JsonUtils.convert(m, MemberPointEvent::class.java)
}


// 查询关联时机
fun MemberPointEvent.Companion.find(hierarchyId: Long, eventTypeId: Long, operation: EventOperationEnum, limit: Long, includeRawEvent: Boolean = false): List<MemberPointEvent> {
    val eventColumn = if (includeRawEvent) ", event " else ""
    val sql = """
        SELECT 
            id, fqn, planId, subjectId, hierarchyId, eventTypeId, memberId, traceId, uniqueId,
            operation, occurrenceTs, created $eventColumn
        FROM data.loyalty.member.MemberPointEvent$hierarchyId
        WHERE eventTypeId = :eventTypeId and operation = :operation 
        ORDER BY created DESC
        LIMIT $limit 
    """.trimIndent()
    val result = DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
        sdk.execute(sql, mapOf("eventTypeId" to eventTypeId, "operation" to operation.name))
    }!!
    if (!result.isSuccess || result.data.isEmpty()) {
        return emptyList()
    }
    return result.data.map { JsonUtils.convert(it, MemberPointEvent::class.java) }
}
