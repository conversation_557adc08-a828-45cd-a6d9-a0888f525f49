package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.model.ImportExtensionSetting
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

@Component
class ImportExtensionSettingRepository : DataModelRepository<ImportExtensionSetting>() {

    private val log = LogManager.getLogger(ImportExtensionSettingRepository::class.java)
    override fun log(): Logger = log

}