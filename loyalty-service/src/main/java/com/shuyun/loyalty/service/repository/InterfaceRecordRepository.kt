package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.datamodel.InterfaceRecord
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

@Component
class InterfaceRecordRepository: DataModelRepository<InterfaceRecord>() {
    private val log = LogManager.getLogger(InterfaceRecordRepository::class.java)
    override fun log(): Logger = this.log
}