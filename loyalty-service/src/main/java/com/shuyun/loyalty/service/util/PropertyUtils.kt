package com.shuyun.loyalty.service.util

import com.shuyun.lite.util.Common

/**
 * 配置中心参数
 */
object PropertyUtils {

    private const val SCHEDULED_EXPIRE_POINT = "scheduled.expire.point.enabled"
    private const val SCHEDULED_EFFECT_POINT = "scheduled.effect.point.enabled"
    private const val SCHEDULED_RETRY_POINT = "scheduled.retry.point.enabled"
    private const val SCHEDULED_EXPIRE_GRADE = "scheduled.expire.grade.enabled"
    private const val SCHEDULED_EXPIRE_MEDAL = "scheduled.expire.medal.enabled"

    // 创建积分账号时是否开启积分账账号
    private const val SEGMENT_ENABLED = "segment.enabled"

    // 检查会员是否存在
    private const val CHECK_MEMBER_EXIST_KEY = "member.pointAccount.checkExist"
    // 事件流schema是否使用本地缓存
    private const val EVENT_STREAM_METAS_LOCAL = "event.stream.metas.local"

    fun getScheduledExpirePoint():Boolean {
        return try {
            Common.getSysOrEnv(SCHEDULED_EXPIRE_POINT, "true").toString().toBoolean()
        } catch (_: Exception) {
            true
        }
    }

    fun getScheduledExpireGrade(): Boolean {
        return try {
            Common.getSysOrEnv(SCHEDULED_EXPIRE_GRADE, "true").toString().toBoolean()
        } catch (_: Exception) {
            true
        }
    }


    fun getScheduledExpireMedal(): Boolean {
        return try {
            Common.getSysOrEnv(SCHEDULED_EXPIRE_MEDAL, "true").toString().toBoolean()
        } catch (_: Exception) {
            true
        }
    }



    fun getScheduledEffectPoint():Boolean {
        return try {
            Common.getSysOrEnv(SCHEDULED_EFFECT_POINT, "true").toString().toBoolean()
        } catch (_: Exception) {
            true
        }
    }

    fun getScheduledRetryPoint():Boolean {
        return try {
            Common.getSysOrEnv(SCHEDULED_RETRY_POINT, "true").toString().toBoolean()
        } catch (_: Exception) {
            true
        }
    }

    fun getCheckMemberExist(): Boolean {
        return try {
            Common.getSysOrEnv(CHECK_MEMBER_EXIST_KEY, "true").toString().toBoolean()
        } catch (_: Exception) {
            true
        }
    }

    fun getEventStreamMetasLocal():Boolean {
        return Common.getSysOrEnv(EVENT_STREAM_METAS_LOCAL, "true").toString().toBoolean()
    }

}
