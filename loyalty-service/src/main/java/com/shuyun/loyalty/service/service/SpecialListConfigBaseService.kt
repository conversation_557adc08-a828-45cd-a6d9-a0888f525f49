package com.shuyun.loyalty.service.service

import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.dm.api.response.BaseResponse
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.sdk.RiskCtrlC
import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.exception.MemberException
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.meta.TypeEnum
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.model.SpecialListConfig
import com.shuyun.loyalty.service.model.SpecialListConfigStatus
import com.shuyun.loyalty.service.model.Subject
import com.shuyun.loyalty.service.repository.SpecialListConfigRepository
import com.shuyun.loyalty.service.util.ModelInitUtil
import com.shuyun.loyalty.service.util.PropertyUtils
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.retrofit.RetrofitProxy
import com.shuyun.pip.util.converter.ZonedDateTime2StringConverter
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import java.time.ZonedDateTime


@Service
class SpecialListConfigBaseService{

    private val logger = LogManager.getLogger(SpecialListConfigBaseService::class.java)


    @Autowired
    private lateinit var specialListConfigRepository: SpecialListConfigRepository

    fun getDisplaySpecialListConfigPage(pageable: Pageable): Page<SpecialListConfig> {
        val page = specialListConfigRepository.findByDisplayAndDisabledOrderByCreateTimeDesc(
            display = true,
            disabled = false,
            pageable = pageable
        )
        page.content.forEach {
            it.fullFillConfigByConfigString()
        }
        return page
    }

    fun getDisplaySpecialListConfig(id: Long): SpecialListConfig {
        val config = specialListConfigRepository.findByIdAndDisplayAndDisabled(id, display = true, disabled = false)
            .orElseThrow { LoyaltyException(LoyaltyExceptionCode.CONFIG_NOT_FOUND, "配置不存在") }
        config.fullFillConfigByConfigString()
        return config
    }

    fun findById(id:Long): List<SpecialListConfig> {
        val list =  specialListConfigRepository.findByIdAndDisabledOrderByVersionId(id, disabled = false)
        list.forEach {
            it.fullFillConfigByConfigString()
        }
        return list
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun insert(specialListConfig: SpecialListConfig){
        specialListConfig.fullFillConfigStringByConfig()
        specialListConfig.versionId = null
        specialListConfigRepository.save(specialListConfig)
        specialListConfig.id = specialListConfig.versionId
        specialListConfigRepository.save(specialListConfig)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun update(specialListConfig: SpecialListConfig){
        specialListConfig.fullFillConfigStringByConfig()
        specialListConfigRepository.save(specialListConfig)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun delete(specialListConfig: SpecialListConfig) {
        specialListConfig.disabled = true
        specialListConfigRepository.save(specialListConfig)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun publish(specialListConfig: SpecialListConfig){
        specialListConfig.status = specialListConfig.status!!.publish()
        specialListConfig.publishedTime = ZonedDateTime2StringConverter().convert(ZonedDateTime.now())
        specialListConfig.display = true
        specialListConfigRepository.save(specialListConfig)
    }

    fun copy(specialListConfig: SpecialListConfig):SpecialListConfig{
        val specialListConfigCopy = SpecialListConfig().apply {
            ModelInitUtil.copyPropertiesIgnoreNull(specialListConfig,this)
            this.versionId = null
        }
        specialListConfigRepository.save(specialListConfigCopy)
        return specialListConfigCopy
    }

    fun physicalDelete(specialListConfig: SpecialListConfig) {
        specialListConfigRepository.delete(specialListConfig.id.toString())
    }

    fun findBySubjectId(subjectId: Long): List<SpecialListConfig> {
        return specialListConfigRepository.findBySubjectIdAndDisabled(subjectId,false).map{
            it.fullFillConfigByConfigString()
            it
        }
    }

    fun findByPlanId(planId: Long): List<SpecialListConfig> {
        return specialListConfigRepository.findByPlanIdAndDisabled(planId,false).map{
            it.fullFillConfigByConfigString()
            it
        }
    }


    fun findMemberId(memberId:String, subject: Subject){
        try {
            @Suppress("UNCHECKED_CAST")
            DmPoolFactory.execute { sdk -> sdk.getObject(subject.dataType!!, memberId, "id") as Map<String, Any?> }
        } catch (e: Exception) {
            logger.warn("会员信息查询失败，fqn:${subject.dataType},memberId:$memberId")
            throw e
        }
    }


    fun futureMemberId(memberId:String, subjectFqn: String){
        try {
            @Suppress("UNCHECKED_CAST")
            DmPoolFactory.execute { sdk -> sdk.getObject(subjectFqn,memberId,"id") as Map<String,Any?> }
        } catch (e: Exception) {
            logger.warn("会员信息查询失败，fqn:$subjectFqn,memberId:$memberId",e)
            throw MemberException(LoyaltyExceptionCode.MEMBER_NOT_FOUND,e)
        }
    }

    @Throws(Exception::class)
    fun checkMemberInSpecialList(
        memberId: String,
        subjectId: Long,
        accountOrHierarchyId: Long,
        forbiddenOperationType: ForbiddenOperation,
        forbiddenPort: ForbiddenPort,
        subjectVersionId: Long,
        hashFindMember: Boolean = true,
        type:TypeEnum
    ): Boolean {
        logger.debug("黑名单处理参数: $memberId, $subjectId, $accountOrHierarchyId, ${forbiddenOperationType.name}")
        val subject = ApplicationContextHolder.getBean(SubjectBaseService::class.java).findByVersionId(subjectVersionId)
        return checkMemberInSpecialList(memberId, subject, accountOrHierarchyId, forbiddenOperationType, forbiddenPort, hashFindMember,type)
    }


    @Throws(Exception::class)
    fun checkMemberInSpecialList(
        memberId: String,
        subject: Subject,
        accountOrHierarchyId: Long,
        forbiddenOperationType: ForbiddenOperation,
        forbiddenPort: ForbiddenPort,
        hashFindMember: Boolean = true,
        type:TypeEnum
    ): Boolean {
        logger.debug("黑名单处理参数: $memberId, ${subject.id}, $accountOrHierarchyId, ${forbiddenOperationType.name}")

        if (hashFindMember && PropertyUtils.getCheckMemberExist()) findMemberId(memberId, subject)

        val list = ApplicationContextHolder.getBean(SpecialListConfigBaseService::class.java).findBySubjectIdAndDisabledAndStatus(subject.id!!)

        logger.debug("查询黑名单配置结果: {}", { JsonUtils.toJson(list) })

        list.forEach { specialListConfig ->
            val configNeedToCheck = specialListConfig.checkAccountOrHierarchyAndOperationInConfig(
                accountOrHierarchyId,
                forbiddenOperationType,
                forbiddenPort,
                type
            )
            if (configNeedToCheck) {
                val fqn = specialListConfig.columnPath!!.substringBeforeLast(".")
                val columnPath = specialListConfig.columnPath!!.substringAfterLast(".")
                val dataResult = try {
                    @Suppress("UNCHECKED_CAST")
                    DmPoolFactory.execute { sdk -> sdk.getObject(fqn, memberId, columnPath) as Map<String, Any?> }!!
                } catch (e: Exception) {
                    throw MemberException(LoyaltyExceptionCode.MEMBER_NOT_FOUND, e)
                }
                val columnValue = dataResult[columnPath] ?: return@forEach
                if (hashCheckList(columnValue.toString(), fqn, specialListConfig.specialListGroupIds!!)) {
                    return true
                }
            }
        }
        return false
    }

    @Suppress("UNCHECKED_CAST")
    fun hashCheckList(columnValue: String, fqn: String, specialListGroupIds: String): Boolean {
        val useLegacyCheckListAPI = Property.getSysOrEnv("use.legacy.checklist.api", false)
        if (useLegacyCheckListAPI) {
            val inCheckList = RiskCtrlC.existBlack(
                columnValue,
                fqn,
                specialListGroupIds.split(",").toList()
            ) { sql, params ->
                (DmPoolFactory.execute(false) { sdk ->
                    logger.debug("sql: {}, params:{}", sql, params)
                    sdk.execute(sql, params)
                } as BaseResponse<Map<String, Any?>>).data
            }
            return inCheckList
        }

        val req = CheckExistRequest(
            ChecklistType.BLACK,
            specialListGroupIds,
            listOf(columnValue)
        )

        val checkListApi = RetrofitProxy.apiProxy(CheckListApi::class.java).checkMember(req)
        val response = checkListApi.execute()

        return if (response.isSuccessful) {
            response.body()?.isNotEmpty() ?: false
        } else {
            logger.error("调用黑名单服务失败: req: {} e: {}", req, response.errorBody()?.string())
            throw LoyaltyException(LoyaltyExceptionCode.UNKNOWN_EXCEPTION, "调用黑名单服务失败")
        }
    }

    fun findBySubjectIdAndDisabledAndStatus(subjectId: Long) = specialListConfigRepository.findBySubjectIdAndDisabledAndStatus(subjectId,false, SpecialListConfigStatus.ENABLED)
}
