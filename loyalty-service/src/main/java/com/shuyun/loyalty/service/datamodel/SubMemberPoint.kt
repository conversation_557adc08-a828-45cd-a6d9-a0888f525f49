package com.shuyun.loyalty.service.datamodel

import javax.persistence.Column
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne
import javax.validation.constraints.NotEmpty

abstract class SubMemberPoint : BaseMemberPoint() {
    /** 会员积分账户id */
    @Column
    @NotEmpty
    var memberPointId: String? = null
    @ManyToOne
    @JoinColumn(name = "memberPointId")
    var memberPoint: MemberPoint? = null

}
