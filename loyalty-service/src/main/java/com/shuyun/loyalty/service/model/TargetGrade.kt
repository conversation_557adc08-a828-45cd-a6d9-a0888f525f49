package com.shuyun.loyalty.service.model

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.shuyun.loyalty.service.datamodel.MemberGrade
import com.shuyun.loyalty.service.exception.GradeException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.extension.setDayEnd
import com.shuyun.loyalty.service.extension.toZonedDateTime
import com.shuyun.loyalty.service.message.grade.AbstractGradeMessage
import com.shuyun.loyalty.service.meta.ChangeTimeType
import com.shuyun.loyalty.service.meta.ChangeUnitType
import com.shuyun.loyalty.service.meta.PlanStatusEnum
import com.shuyun.loyalty.service.meta.ValidTimeTypeEnum
import com.shuyun.loyalty.service.repository.MemberGradeRepository
import com.shuyun.loyalty.service.service.GradeDefinitionBaseService
import com.shuyun.loyalty.service.service.GradeHierarchyBaseService
import com.shuyun.loyalty.service.service.PlanBaseService
import com.shuyun.loyalty.service.service.SubjectBaseService
import com.shuyun.loyalty.service.util.ExpressionIdentUtil
import com.shuyun.loyalty.service.util.ModelInitUtil
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import java.time.ZonedDateTime
import java.util.*

/**
 * 属于BO
 */
class TargetGrade {

    @JsonIgnore
    private val logger = LogManager.getLogger(TargetGrade::class.java)

    var targetGradeDefinition: GradeDefinition? = null

    var effectTime: Date = Date()

    var overdueTime: Date? = null

    var effectForever: Boolean = false

    var ruleId: Long? = null

    var ruleName: String? = null

    var effectRuleName: String? = null

    var effectEventTypeName: String? = null

    var channelType: String? = null

    /**
     * 根据传入的降级规则降级，只校验原等级sort在目标等级sort之上，原等级与等级规则的匹配，原等级、目标等级与等级体系的匹配，调用者需自己保证
     * 如果成功，返回true
     * */
    fun updateTargetGradeWhenDegrade(
        gradeRule: GradeRule,
        hierarchy: GradeHierarchy,
        originalGrade: GradeDefinition,
        ruleGroupName: String,
        eventTypeName: String,
        message: AbstractGradeMessage? = null,
        memberGrade: MemberGrade?
    ): Boolean {
        val subject = ApplicationContextHolder.getBean(SubjectBaseService::class.java).findById(message!!.subjectVersionId.toString())
        try {
            val overdueTime =
                gradeRule.calculateOverdueTime(message.event(), subject.get().dataType, memberGrade)//null为无穷大
            val targetGradeDefinition = if (gradeRule.degradeId!! < 0) GradeDefinition().apply {
                id = gradeRule.degradeId
            } else hierarchy.gradeDefinitions!!.find {
                it.id == gradeRule.degradeId && it.sort!! < originalGrade.sort!!
            }
            if (targetGradeDefinition is GradeDefinition) {
                updateTargetGradeEffectTime(targetGradeDefinition, overdueTime, ruleGroupName, eventTypeName)
                return true
            } else {
                logger.error("大于原始等级的目标等级不存在,Id:{},规则:{}，原始等级ID：{}", {gradeRule.degradeId}, {JsonUtils.toJson(gradeRule)}, {originalGrade.id})
            }
        } catch (e: Exception) {
            logger.error("等级有效期已经过期", e)
        }
        return false
    }


    /**根据传入的等级和规则判断依据，在需要时（规则符合）更新目标等级有效时间
     * （如果不存在原有等级，更新目标等级的等级；如果存在原有等级，则必须与传入的等级相等）
     * 如果匹配，返回true*/
    fun updateTargetGradeEffectTimeWhenMatched(
        eventTypeId: Long,
        gradeRule: GradeRule,
        gradeDefinition: GradeDefinition,
        ruleGroupName: String,
        eventTypeName: String,
        message: AbstractGradeMessage,
        gradeTypeName: String? = null,
        memberGrade: MemberGrade?
    ): Boolean {
        if (this.targetGradeDefinition is GradeDefinition && this.targetGradeDefinition!!.id!! != gradeDefinition.id!! && gradeTypeName != "gradeCalculate") {
            logger.debug("不存在原等级")
            return false
        }
        message.eventTypeId = eventTypeId
        val subject = ApplicationContextHolder.getBean(SubjectBaseService::class.java)
            .findById(message.subjectVersionId.toString())
        val validate = ExpressionIdentUtil.eppById(
            gradeRule.expressionFxId!!,
            message.event(),
            JsonNodeFactory.instance.objectNode().put("subject", subject.get().dataType)
        ).toString().toBoolean()
        logger.debug("进行等级计算fxId${gradeRule.expressionFxId} subject:${subject.get().dataType}, validate result:${validate}")
        if (validate) {
            try {
                val overdueTime =
                    gradeRule.calculateOverdueTime(message.event(), subject.get().dataType, memberGrade)//null为无穷大
                val updated = updateTargetGradeEffectTime(
                    gradeDefinition,
                    overdueTime,
                    ruleGroupName,
                    eventTypeName,
                    gradeRule,
                    message,
                    gradeTypeName
                )
                this.channelType = message.findChannelType()
                return updated
            } catch (e: Exception) {
                logger.error("等级有效期已经过期", e)
            }
        }
        return false
    }

    /**根据传入的等级和过期时间，在需要时（传入的过期时间晚于原有过期时间,入参overdueTime为null时代表永久有效）更新目标等级有效时间
     * （如果不存在原有等级，更新目标等级的等级；如果存在原有等级，则必须与传入的等级相等）
     * 如果更新了，返回true*/
    private fun updateTargetGradeEffectTime(gradeDefinition: GradeDefinition, overdueTime: Date?, ruleGroupName: String, eventTypeName: String,
                                    gradeRule: GradeRule? = null, message: AbstractGradeMessage? = null, gradeTypeName:String? = null): Boolean {
        if (gradeDefinition.id != -1L && this.targetGradeDefinition is GradeDefinition && this.targetGradeDefinition!!.id!! != gradeDefinition.id!!) {
            throw GradeException(LoyaltyExceptionCode.TARGET_GRADE_NOT_FOUND)
        }
        var newEffectTime = Date()
        var newOverdueTime = overdueTime
        if(gradeTypeName == "gradeCalculate"){
            val memberId = message!!.memberId()
            val filterJson = """{"${"$"}and":[{"memberId": "$memberId" }, {"gradeHierarchyId": ${message.gradeHierarchyId}}]}"""
            val memberGradeOptional = ApplicationContextHolder.getBean(MemberGradeRepository::class.java).findOneByFilter(filterJson, message.gradeHierarchyId.toString())
            if(!memberGradeOptional.isPresent) {
                logger.error("找不到会员:${memberId} 原始等级")
                throw GradeException(LoyaltyExceptionCode.CURRENT_GRADE_NOT_FOUND,"等级体系{0} 找不到会员:{1} 原始等级", message.gradeHierarchyId.toString(), memberId)
            }
            val memberGrade = memberGradeOptional.get()
            newOverdueTime = changeCalculateOverdueTime(newOverdueTime, gradeRule!!, memberGrade)

            if (gradeRule.effectValidTimeType == ValidTimeTypeEnum.UNCHANGE) {
                newEffectTime = Date.from(memberGrade.effectDate!!.toInstant())
            }
        }
        this.effectTime = newEffectTime
        this.targetGradeDefinition = gradeDefinition
        logger.debug("等级有效期 {}  是否永久 {}", newOverdueTime, this.effectForever)
        if (newOverdueTime is Date) {
            if (!this.effectForever) {
                if (null == this.overdueTime || this.overdueTime!!.before(newOverdueTime)) {
                    this.overdueTime = newOverdueTime
                    this.effectRuleName = ruleGroupName
                    this.effectEventTypeName = eventTypeName
                    return true
                }
            }
            return false
        } else {
            this.effectForever = true
            this.overdueTime = null
            this.effectRuleName = ruleGroupName
            this.effectEventTypeName = eventTypeName
            return true
        }
    }

    /**根据过期类型，判断过期时间的计算方式*/
    private fun changeCalculateOverdueTime(overdueTime: Date?, gradeRule: GradeRule, memberGrade: MemberGrade): Date? {
        if (gradeRule.validTimeType == ValidTimeTypeEnum.RESET) return overdueTime

        // 无论是保持不变，延长还是缩短原来如果是永久或者将来是永久，那么改变后还是永久
        if (memberGrade.overdueDate == null) return null
        // 如果为unchanged保持不变返回原有过期时间
        if (gradeRule.validTimeType == ValidTimeTypeEnum.UNCHANGE) return Date.from(memberGrade.overdueDate!!.toInstant())

        if(gradeRule.validTimeType == ValidTimeTypeEnum.CHANGE) {
            val calendar = Calendar.getInstance()
            calendar.time = Date.from(memberGrade.overdueDate!!.toInstant())

            val timeSign = if(gradeRule.changeTimeType == ChangeTimeType.LENGTHEN) 1 else -1
            val unitType = when(gradeRule.changeUnitType) {
                ChangeUnitType.DAY -> Calendar.DAY_OF_MONTH
                ChangeUnitType.MONTH -> Calendar.MONTH
                ChangeUnitType.YEAR -> Calendar.YEAR
                else -> throw GradeException(LoyaltyExceptionCode.GRADE_TIME_TYPE_ERROR)
            }
            calendar.set(unitType, calendar.get(unitType).plus(gradeRule.changeTimeValue!! * timeSign))

            if(calendar.time < Date()) {
                val calendarLt = Calendar.getInstance()
                calendarLt.setDayEnd()
                return calendarLt.time
            }
            return calendar.time
        }
        return overdueTime
    }

    /**根据目标等级初始化会员等级对象，如果该会员在该等级体系下存在原有等级，作为参数传入,过期时间以目标等级为准*/
    fun initMemberGrade(originalMemberGrade: MemberGrade?, message: AbstractGradeMessage): MemberGrade {
        val subjectVersionId = message.subjectVersionId
        val fqn = ApplicationContextHolder.getBean(SubjectBaseService::class.java).findByVersionId(subjectVersionId).dataType
        if (originalMemberGrade is MemberGrade) {
            if (message.gradeHierarchyId != originalMemberGrade.gradeHierarchyId ||
                    message.memberId() != originalMemberGrade.memberId) {
                logger.debug("当前等级{}, 事件等级{}",JsonUtils.toJson(originalMemberGrade),JsonUtils.toJson(message))
                throw GradeException(LoyaltyExceptionCode.EVENT_NOT_MATCH)
            }
        }
        val memberGrade = MemberGrade()
        if (originalMemberGrade is MemberGrade) {
            ModelInitUtil.copyPropertiesIgnoreNull(originalMemberGrade, memberGrade)
        } else {
            memberGrade.created = Date().toZonedDateTime()
        }
        val hierarchy = ApplicationContextHolder.getBean(GradeHierarchyBaseService::class.java).getEffectiveOneCache(message.gradeHierarchyId, ZonedDateTime.now())
        hierarchy.gradeDefinitions = ApplicationContextHolder.getBean(GradeDefinitionBaseService::class.java).findByEnabledHierarchyByVersionId(hierarchy.versionId!!)
        return memberGrade.apply {
            this.planId = message.plan.id!!
            this.gradeHierarchyId = message.gradeHierarchyId
            this.memberId = message.memberId()
            this.currentGradeDefinitionId = if (targetGradeDefinition is GradeDefinition) targetGradeDefinition!!.id else -1
            this.planName = ApplicationContextHolder.getBean(PlanBaseService::class.java).findPlanByName(message.plan.id!!, PlanStatusEnum.PUBLISHED)
            this.currentGradeName = if (this.currentGradeDefinitionId == -1L) {
                "无等级"
            } else {
                hierarchy.gradeDefinitions!!.find { it.id == this.currentGradeDefinitionId }?.name
            }
            this.gradeHierarchyName = hierarchy.name
            this.effectDate = effectTime.toZonedDateTime()
            this.subjectFqn = fqn
            this.overdueDate = if (effectForever)
                null
            else
                overdueTime!!.toZonedDateTime()
        }
    }


    fun getMaxGrade(targetGradeInfoList: ArrayList<TargetGrade>): TargetGrade{

        if(targetGradeInfoList.isEmpty()) {
            return TargetGrade()
        }

        //只有一个满足，只变更满足一条的等级
        if(targetGradeInfoList.size == 1){
            return targetGradeInfoList[0]
        }

        //多个满足，判断哪个等级最大
        //按照等级从大到小排序
        val targetGradeList = targetGradeInfoList.sortedByDescending { it.targetGradeDefinition!!.sort }

        //判断集合中最大等级是否唯一
        val maxGradeList = targetGradeList.filter { targetGradeList[0].targetGradeDefinition!!.sort == it.targetGradeDefinition!!.sort }
        logger.debug("change grade value, big is only one? , size = {}",maxGradeList.size)

        //最大等级唯一
        if (maxGradeList.size == 1) {
            logger.debug("change grade value, big and only one , {}",{JsonUtils.toJson(maxGradeList[0])})
            return maxGradeList[0]
        }
        //不唯一
        //多个相等的等级，且等级都是最大，判断有效期是否有永久
        val foreverGradeList = maxGradeList.filter { it.effectForever }

        //有永久
        if (foreverGradeList.isNotEmpty()) {
            logger.debug("change grade have forever end time , {}",{JsonUtils.toJson(foreverGradeList[0])})
            return foreverGradeList[0]
        }
        //没有永久,按照等级有效期排序
        val noForeverPointList = maxGradeList.sortedByDescending { it.overdueTime }
        //返回等级有效期最大值
        logger.debug("not forever end time , change grade {}",{JsonUtils.toJson(noForeverPointList[0])})
        return noForeverPointList[0]
    }

}