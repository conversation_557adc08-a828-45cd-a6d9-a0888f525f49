package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.meta.PropertyBelongerTypeEnum
import com.shuyun.loyalty.service.model.PropertyTemplate
import com.shuyun.loyalty.service.repository.PropertyTemplateRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.ZonedDateTime

@Service
class PropertyTemplateBaseService {
    @Autowired
    private lateinit var propertyTemplateRepository: PropertyTemplateRepository

    /**新增属性本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun insert(propertyTemplate: PropertyTemplate) {
        propertyTemplate.versionId = null
        propertyTemplate.status = EnableStatusEnum.DRAFT
        propertyTemplateRepository.save(propertyTemplate)
        propertyTemplate.id = propertyTemplate.versionId
        propertyTemplateRepository.save(propertyTemplate)
    }

    /**更新属性本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun update(propertyTemplate: PropertyTemplate) {
        propertyTemplateRepository.save(propertyTemplate)
    }

    /**删除属性本身(不包括归属于它的实体)*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun delete(propertyTemplate: PropertyTemplate) {
        propertyTemplate.disabled = true
        propertyTemplateRepository.save(propertyTemplate)
    }

    /**根据所有者ID及所有者类别获取属性详情*/
    fun findDetailByBelongerVersionIdAndBelongerType(belongerVersionId: Long, belongerType: PropertyBelongerTypeEnum): List<PropertyTemplate> {
        return propertyTemplateRepository.findByBelongerVersionIdAndBelongerTypeAndDisabled(belongerVersionId, belongerType, false)
    }

    fun findByVersionNameList(i18n: String, updateTime: ZonedDateTime): List<PropertyTemplate> {
        return propertyTemplateRepository.findByVersionNameList(i18n, updateTime)
    }
}