package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.datamodel.MemberMedal.Companion.DATA_MODEL_FQN
import com.shuyun.loyalty.service.model.MedalDefinition
import com.shuyun.loyalty.service.model.MedalHierarchy
import com.shuyun.loyalty.service.model.Plan
import com.shuyun.loyalty.service.model.Subject
import com.shuyun.loyalty.service.service.MemberMedalBaseService
import com.shuyun.pip.ApplicationContextHolder
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne
import javax.persistence.Table


@Table(name = DATA_MODEL_FQN)
@DataServiceModel
@FqnVariableModel
class MemberMedal : BaseDataModel() {

    /**
     * 计划ID
     */
    @Column
    var planId: Long? = null

    /**
     * 计划名称
     */
    @Column
    var planName: String? = null

    /**
     * 勋章体系ID
     */
    @Column
    var medalHierarchyId: Long? = null

    /**
     * 勋章体系名称
     */
    @Column
    var medalHierarchyName: String? = null

    /**
     * 会员全网id
     */
    @Column
    var memberId: String? = null

    /**
     * 勋章id
     */
    @Column
    var medalDefinitionId: Long? = null

    /**
     * 勋章名称
     */
    @Column
    var medalDefinitionName: String? = null

    /**
     * 有效期开始时间
     */
    @Column
    var effectDate: ZonedDateTime? = null

    /**
     * 有效期结束时间
     */
    @Column
    var overdueDate: ZonedDateTime? = null

    /**
     * 归属性主体
     */
    @Column
    var subjectFqn: String? = null

    /**
     * 版本号
     */
    @Column
    var version: Int? = null

    /**
     * 创建时间
     */
    @Column
    var created: ZonedDateTime? = null

    /**
     * 是否已删除
     */
    @Column
    var disabled: Boolean? = false

    @ManyToOne
    @JoinColumn(name = "memberId")
    var member: Any? = null

    companion object {
        const val DATA_MODEL_FQN_PREFIX = "data.loyalty.member.hierarchy.Medal"
        const val DATA_MODEL_FQN = "$DATA_MODEL_FQN_PREFIX{*}"
    }

    fun saveOrUpdate(medalHierarchyId: Long): MemberMedal {
        return ApplicationContextHolder.getBean(MemberMedalBaseService::class.java).saveOrUpdate(this, medalHierarchyId)
    }

    fun build(plan: Plan, subject: Subject, hierarchy: MedalHierarchy, definition: MedalDefinition, overdue: ZonedDateTime?, memberId: String) {
        this.memberId = memberId
        this.planId = plan.id
        this.planName = plan.name
        this.overdueDate = overdue
        this.medalHierarchyId = hierarchy.id
        this.medalHierarchyName = hierarchy.name
        this.medalDefinitionId = definition.id
        this.medalDefinitionName = definition.name
        this.effectDate = ZonedDateTime.now()
        this.subjectFqn = subject.dataType
    }


}