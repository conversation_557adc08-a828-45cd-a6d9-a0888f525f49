package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.meta.RuleGroupStatusEnum
import com.shuyun.loyalty.service.model.MedalRuleGroup
import com.shuyun.loyalty.service.repository.MedalRuleGroupRepository
import com.shuyun.loyalty.service.util.DateUtils
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.i18n.LocaleI18nContextHolder
import com.shuyun.pip.util.ObjectUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Example
import org.springframework.data.domain.ExampleMatcher
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import java.lang.Boolean.FALSE

@Service
class MedalRuleGroupBaseService {

    private val log = LogManager.getLogger(MedalRuleGroupBaseService::class.java)

    @Autowired
    private lateinit var medalRuleGroupRepository: MedalRuleGroupRepository

    @Autowired
    private lateinit var customizedPropertyBaseService: CustomizedPropertyBaseService

    @Autowired
    private lateinit var propertyTemplateBaseService: PropertyTemplateBaseService

    @Autowired
    private lateinit var medalRuleBaseService: MedalRuleBaseService

    fun findAllByExample(condition: MedalRuleGroup, matcher: ExampleMatcher): List<MedalRuleGroup> {
        return transferEventTypeName(medalRuleGroupRepository.findAll(Example.of(condition, matcher)))
    }

    fun findAllByExample(condition: MedalRuleGroup, matcher: ExampleMatcher, sort: Sort): List<MedalRuleGroup> {
        return transferEventTypeName(medalRuleGroupRepository.findAll(Example.of(condition, matcher)))
    }

    fun findByMedalHierarchyIdOrderByCreateTimeDesc(medalHierarchyId: Long): List<MedalRuleGroup> {
        val groupList = medalRuleGroupRepository.findByMedalHierarchyIdAndDisabledOrderByCreateTimeDesc(medalHierarchyId, FALSE)
        return transferEventTypeName(groupList)
    }

    fun findByMedalHierarchyIdAndNameOrderByCreateTimeDesc(medalHierarchyId: Long, name: String): List<MedalRuleGroup> {
        val groupList = medalRuleGroupRepository.findByMedalHierarchyIdAndNameAndDisabledOrderByCreateTimeDesc(medalHierarchyId, name, FALSE)
        return transferEventTypeName(groupList)
    }

    fun findById(id: Long): MedalRuleGroup {
        val group = medalRuleGroupRepository.findByIdAndDisabled(id, FALSE)
            .orElseThrow { throw IllegalArgumentException("规则组不存在") }

        if (-1L == group.eventTypeId) return findEventTypeName(listOf(group))[0]

        group.transferEventTypeName()
        return findEventTypeName(listOf(group))[0]
    }

    @Suppress("UNCHECKED_CAST")
    private fun findEventTypeName(groupList: List<MedalRuleGroup>): List<MedalRuleGroup> {
        groupList.map { group ->
            val customizedPropertyList = customizedPropertyBaseService.findSubjectCustomizedPropertyByAllDate(
                LocaleI18nContextHolder.getAcceptLanguage(), DateUtils.date2DateTime(group.createTime!!))
            val propertyTemplateNameList = propertyTemplateBaseService.findByVersionNameList(
                LocaleI18nContextHolder.getAcceptLanguage(), DateUtils.date2DateTime(group.createTime!!))

            group.ruleList?.map ruleMap@ { rule ->
                try {
                    val infoList = JsonUtils.parse2List(rule.displayInfo, Map::class.java)
                    val newInfoList = infoList.map {
                        val customizedPropertyName = customizedPropertyList.find { customizedProperty ->
                            customizedProperty.id ==  it["id"].toString().toLong() }!!.name!!
                        val map = mutableMapOf<String, Any>(Pair("name", customizedPropertyName))
                        map.putAll(it as Map<out String, Any>)
                        map
                    }
                    rule.displayInfo = JsonUtils.toJson(newInfoList)
                } catch (e: Throwable) {
                    log.warn("解析表达式${rule.displayInfo}", e.message)
                }

                if (rule.expression.isNullOrBlank()) return@ruleMap

                try {
                    val expressionList = JsonUtils.parse2List(rule.expression, Map::class.java)
                    val newExpressionList = mutableListOf<Map<String, Any>>()
                    expressionList.forEach {
                        val map = mutableMapOf<String, Any>()
                        map.putAll(it as Map<String, Any>)

                        if (ObjectUtils.isEmpty(it["pathItem"])) {
                            newExpressionList.add(map)
                            return@forEach
                        }

                        if (ObjectUtils.isEmpty(it["expressionDesc"])) {
                            map["name"] = customizedPropertyList.find { customizedProperty ->
                                customizedProperty.id == it["code"].toString().toLong() }!!.name!!
                        } else {
                            val id = it["expressionDesc"].toString().substringAfter("\"id\":")
                                .substringBefore(",").toLong()
                            map["name"] = propertyTemplateNameList.find { template -> template.id == id }!!.name!!
                        }
                        newExpressionList.add(map)
                    }
                    rule.expression = JsonUtils.toJson(newExpressionList)
                } catch (e: Throwable) {
                    log.warn("解析表达式${rule.expression}", e.message)
                }

            }

        }
        return groupList
    }

    fun findByMedalDefinition(medalDefinitionId: Long): List<MedalRuleGroup> {
        val groupList = medalRuleGroupRepository.findByMedalDefinitionIdAndDisabledOrderBySort(medalDefinitionId, FALSE)
        return transferEventTypeName(groupList)
    }

    @InsertOrUpdateAnnotation(OperationType.UPDATE)
    fun delete(medalRuleGroup: MedalRuleGroup) {
        medalRuleGroupRepository.save(medalRuleGroup.apply { this.disabled = true })
    }

    fun delete(id: Long) {
        val medalRuleGroup = medalRuleGroupRepository.findByIdAndDisabled(id, FALSE)
            .orElseThrow { throw IllegalArgumentException("规则组不存在") }
        delete(medalRuleGroup)
        medalRuleGroup.ruleList?.forEach {
            medalRuleBaseService.delete(it)
        }
    }

    fun file(id: Long) {
        val medalRuleGroup = medalRuleGroupRepository.findByIdAndDisabled(id, FALSE)
            .orElseThrow { throw IllegalArgumentException("规则组不存在") }.apply {
                this.status = RuleGroupStatusEnum.FILED
                this.name = this.name!!.trim()
            }
        medalRuleGroupRepository.save(medalRuleGroup)
    }

    fun findByHierarchyId(medalHierarchyId: Long): List<MedalRuleGroup> {
        return transferEventTypeName(medalRuleGroupRepository.findByMedalHierarchyIdAndDisabled(medalHierarchyId, FALSE))
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun insert(ruleGroup: MedalRuleGroup) {
        medalRuleGroupRepository.save(ruleGroup.apply {
            this.name = this.name!!.trim()
            this.id = null
            this.status = ruleGroup.status ?: RuleGroupStatusEnum.WAIT_EFFECT
        })
    }

    @InsertOrUpdateAnnotation(OperationType.UPDATE)
    fun update(ruleGroup: MedalRuleGroup) {
        medalRuleGroupRepository.save(ruleGroup.apply { this.name = this.name!!.trim() })
    }

    fun findGroupByDefinition(medalDefinitionId: Long): List<MedalRuleGroup> {
        return findMedalGroupCache(medalDefinitionId)
    }

    fun findMedalGroupCache(medalDefinitionId: Long): List<MedalRuleGroup> {
        val groupList = medalRuleGroupRepository.findByMedalDefinitionIdAndDisabledOrderBySort(medalDefinitionId, FALSE)
        return transferEventTypeName(groupList)
    }

    private fun transferEventTypeName(groupList: List<MedalRuleGroup>): List<MedalRuleGroup> {
        groupList.forEach { it.transferEventTypeName() }
        findEventTypeName(groupList)
        return groupList.map { it.renewStatus() }
    }

}