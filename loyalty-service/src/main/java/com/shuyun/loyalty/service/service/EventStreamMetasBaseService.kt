package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.service.fx.IExtendEventMetasProvider
import com.shuyun.loyalty.service.infrastructure.es.EventClient
import com.shuyun.loyalty.service.meta.EventStreamMetasStatusEnum
import com.shuyun.loyalty.service.model.EventStreamMetas
import com.shuyun.loyalty.service.repository.EventStreamMetasRepository
import com.shuyun.pip.frameworkext.filter.VisitTenantInfoHolder
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.LocalDateTime

@Service
class EventStreamMetasBaseService {

    private val logger = LogManager.getLogger(EventStreamMetasBaseService::class)

    @Autowired
    private lateinit var provider: IExtendEventMetasProvider

    @Autowired
    private lateinit var eventStreamMetasRepository: EventStreamMetasRepository


    fun findByFqn(fqn: String): EventStreamMetas {
        val eventMeta = provider.getOne(fqn)
        return EventStreamMetas().apply {
            this.fqn = eventMeta.fqn
            this.name = eventMeta.name
            this.references = eventMeta.references
            this.schema = eventMeta.schema
        }
    }


    fun findAll(): List<EventStreamMetas> {
        val eventMetaList = provider.findAll()
        return eventMetaList.map {
            val eventStreamMetas = EventStreamMetas()
            eventStreamMetas.fqn = it.fqn
            eventStreamMetas.name = it.name
            eventStreamMetas.references = it.references
            eventStreamMetas.schema = it.schema
            eventStreamMetas
        }
    }


    fun saveOrUpdate(eventStreamMetas: EventStreamMetas) {
        eventStreamMetas.tenantId = VisitTenantInfoHolder.getTenantId()
        eventStreamMetas.status = EventStreamMetasStatusEnum.VALID
        eventStreamMetas.modified = LocalDateTime.now()
        eventStreamMetasRepository.save(eventStreamMetas)
    }


    // 事件模型转换为事件流元数据
    fun findESStreamMetas(fqn: String, subjectFqn: String): EventStreamMetas? {
        val meta =  EventClient.findESStreamMetas(fqn)
        val references = Json.parse<Map<String, String>>(meta.references!!)
        if (!references.contains(subjectFqn)) {
            logger.info("事件模型(${fqn})没有主体(${subjectFqn})引用")
            return null
        }
        meta.references = Json.toJson(references[subjectFqn])
        return meta
    }

}
