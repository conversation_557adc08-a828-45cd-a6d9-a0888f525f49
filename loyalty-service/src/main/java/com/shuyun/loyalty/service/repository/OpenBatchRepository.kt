package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.model.OpenBatchLog
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional

@Repository
interface OpenBatchRepository : JpaRepository<OpenBatchLog, Long> {

    @Query("select * from open_batch_log where trigger_id = ?1 and parent_id = 0", nativeQuery = true)
    fun findOpenBatchLogByTriggerId(triggerId: String): OpenBatchLog?

    @Query("select * from open_batch_log where parent_id = ?1", nativeQuery = true)
    fun findOpenBatchLogById(id: Long): List<OpenBatchLog>

    @Modifying
    @Transactional
    @Query("update open_batch_log set success_number = success_number +1, process_status = 'RUNNING' where id = ?1", nativeQuery = true)
    fun incrSuccessNumber(id: Long)

    @Modifying
    @Transactional
    @Query("update open_batch_log set fail_number = fail_number +1, process_status = ?2 where id = ?1", nativeQuery = true)
    fun incrFailNumber(id: Long, status: String)

    @Modifying
    @Transactional
    @Query("update open_batch_log set process_status = 'SUCCESS' where total_number <= success_number + fail_number and id=?1", nativeQuery = true)
    fun updateStatusSuccess(id: Long)

    @Modifying
    @Transactional
    @Query("update open_batch_log set process_status = 'FAIL' where total_number <= success_number + fail_number and id=?1", nativeQuery = true)
    fun updateStatusFail(id: Long)
}
