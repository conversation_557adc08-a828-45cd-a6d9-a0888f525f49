package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.model.GradeHierarchy
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.util.converter.ZonedDateTime2StringConverter
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component
import java.time.ZonedDateTime
import java.util.*

@Component
class GradeHierarchyRepository : DataModelCarryRepository<GradeHierarchy>() {

    private val log = LogManager.getLogger(GradeHierarchyRepository::class.java)
    override fun log(): Logger = log

    fun findPageBySubjectVersionIdAndDisabledList(subjectVersionIdList: List<Long>, disabled: Boolean): List<GradeHierarchy> {
        val params = HashMap<String, Any?>()
        params["disabled"] = disabled
        val list = ArrayList<HashMap<String, Any?>>()
        if (subjectVersionIdList.size <= 0) return arrayListOf()
        if (subjectVersionIdList.size == 1) {
            params["subjectVersionId"] = subjectVersionIdList[0]
        } else {
            subjectVersionIdList.forEach {
                val paramsItem = HashMap<String, Any?>()
                paramsItem["subjectVersionId"] = it
                list.add(paramsItem)
            }
            params["\$or"] = list
        }
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findBySubjectVersionIdAndDisabled(subjectVersionId: Long, disabled: Boolean): List<GradeHierarchy> {
        val params = HashMap<String, Any?>()
        params["subjectVersionId"] = subjectVersionId
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findBySubjectVersionId(subjectVersionId: Long): List<GradeHierarchy> {
        val params = HashMap<String, Any?>()
        params["subjectVersionId"] = subjectVersionId
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findByIdAndDate(id: Long, date: ZonedDateTime): Optional<GradeHierarchy> {
        val sql = "select h.name,h.subjectVersionId,h.id,h.sort,h.executeOrder,h.status,h.defaultDegradeNum," +
                "h.defaultDegradeOverdue,h.lowestDegradeTarget,h.lowestDegradeType,h.versionId " +
                "from data.loyalty.manager.gradeHierarchy h " +
                "inner join data.loyalty.manager.subject s on h.subjectVersionId = s.versionId " +
                "inner join data.loyalty.manager.plan p on s.planVersionId = p.versionId " +
                "where p.disabled=0 and h.disabled=0 and s.disabled=0 and h.id = :id and (p.status='PUBLISHED' OR p.status = 'FILED')  and h.status = 'PUBLISHED' " +
                "and p.publishedTime<=:date and (p.filedTime>=:date or p.filedTime is null) order by h.subjectVersionId desc"
        val params = HashMap<String, Any>()
        params["id"] = id
        params["date"] = ZonedDateTime2StringConverter().convert(date)!!
        log.trace("find by id and date, {}", {JsonUtils.toJson(params)})
        return executeOne(sql, params)
    }

    fun findGradeHierarchyIdBySubjectId(subjectId: Long): Long? {
        val sql = "select c.id from data.loyalty.manager.gradeHierarchy c inner join data.loyalty.manager.subject e on e.versionId=c.subjectVersionId where e.id=:id"
        val params = HashMap<String, Any>()
        params["id"] = subjectId
        val optional = executeOne(sql, params)
        if (optional.isPresent) {
            return optional.get().id
        }
        return null
    }


    fun findByGradeHierarchyIdAll(): List<GradeHierarchy> {
        val sql = "select c.id from data.loyalty.manager.gradeHierarchy c " +
                "inner join data.loyalty.manager.subject e on e.versionId=c.subjectVersionId " +
                "inner join data.loyalty.manager.plan f on f.versionId=e.planVersionId"
        return executeList(sql, mapOf())
    }

    fun findGradeHierarchyIdByPlanId(planId: Long): List<GradeHierarchy> {

        val sql = "select c.id from data.loyalty.manager.gradeHierarchy c " +
                "inner join data.loyalty.manager.subject e on e.versionId=c.subjectVersionId " +
                "inner join data.loyalty.manager.plan f on f.versionId=e.planVersionId where f.id = :planId"

        val params = HashMap<String, Any>()
        params["planId"] = planId

        return executeList(sql, params)
    }
}