package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.meta.ImportFileConfigEnum
import com.shuyun.loyalty.service.model.ImportFileConfig
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.util.converter.ZonedDateTime2StringConverter
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Component
import java.time.ZonedDateTime


@Component
class ImportFileConfigRepository : DataModelRepository<ImportFileConfig>(){

    private val log = LogManager.getLogger(PropertyTemplateRepository::class.java)
    override fun log(): Logger = log

    fun findByStatusAndDisabledAndParentIdGreaterThan(): List<ImportFileConfig> {
        val sql = "select shopIdName,importMatchMultiEnabled,repeatType,shopSelectorId,oneSelectorReturnField,batchSelectorReturnField,componentType,oneSelectorId,oneSelectorName,batchSelectorId,batchSelectorName,planId,subjectId,parentId,status,historyRecord,splitCode,fileType,matchFiled,matchName,planName,subjectName,publishedTime,disabled,creatorId,creatorName,createTime,updaterId,updaterName,updateTime,id,shopSettings,manuallyPointLimitSettings,transferApproveSetting from data.loyalty.manager.importFileConfig where historyRecord = 0 and id in ( " +
                "select id from data.loyalty.manager.importFileConfig where subjectId " +
                "in ( select subjectId from data.loyalty.manager.importFileConfig where historyRecord = 0 and ( status='ENABLE' or status='ENABLE_DRAFT') " +
                "and parentId != '0' and disabled=0 )  and disabled = 0 and parentId != '0' and (status = 'ENABLE' or status = 'BANISH') group by planId, subjectId  order by updateTime desc) order by id asc"
        return executeList(sql, mapOf())
    }


    fun findSubjectIdAndTime(subjectId: Long, time: ZonedDateTime): List<ImportFileConfig> {

        val sql = "select shopIdName,importMatchMultiEnabled,repeatType,shopSelectorId,oneSelectorReturnField,batchSelectorReturnField,componentType,oneSelectorId,oneSelectorName,batchSelectorId,batchSelectorName,planId,subjectId,parentId,status,historyRecord,splitCode,fileType,matchFiled,matchName,planName,subjectName,publishedTime,disabled,creatorId,creatorName,createTime,updaterId,updaterName,updateTime,id,shopSettings,manuallyPointLimitSettings, transferApproveSetting " +
                "from data.loyalty.manager.importFileConfig where id in " +
                "( select id from data.loyalty.manager.importFileConfig where subjectId " +
                "in ( select subjectId from data.loyalty.manager.importFileConfig where historyRecord = 0 and ( status='ENABLE' or status='ENABLE_DRAFT' or status = 'FILED') and parentId != '0' and disabled=0 )  " +
                "and disabled = 0 and parentId != '0' and (status = 'ENABLE' or status = 'BANISH' or status = 'FILED') " +
                "and subjectId = :subjectId and publishedTime <= :publishedTime group by planId, subjectId  order by updateTime desc) order by id asc"
        val params = HashMap<String, Any>()
        params["subjectId"] = subjectId
        params["publishedTime"] = ZonedDateTime2StringConverter().convert(time)!!
        return executeList(sql, params)
    }

    fun findByParentIdAndStatusAndDisabled(parentId: String,status: ImportFileConfigEnum, disabled: Boolean): List<ImportFileConfig> {
        val params = HashMap<String, Any?>()
        params["parentId"] = parentId
        params["status"] = status
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params))
    }

   fun findBySubjectIdAndDisabled(subjectId: Long, disabled: Boolean): List<ImportFileConfig> {
        val params = HashMap<String, Any?>()
        params["subjectId"] = subjectId
        params["disabled"] = disabled
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findByPlanId(planId: Long): List<ImportFileConfig> {
        val params = HashMap<String, Any?>()
        params["planId"] = planId
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findByHistoryRecordAndParentIdGreaterThanAndDisabledAndStatusNotOrderByParentIdDesc(historyRecord:Boolean, parentId: Long,disabled: Boolean, status: ImportFileConfigEnum,pageable: Pageable): Page<ImportFileConfig> {
        val params = HashMap<String, Any?>()
        params["historyRecord"] = historyRecord

        val paramsItem1 = HashMap<String, Any?>()
        paramsItem1["\$ne"] = parentId
        params["parentId"] = paramsItem1

        params["disabled"] = disabled

        val paramsItem2 = HashMap<String, Any?>()
        paramsItem2["\$ne"] = status
        params["status"] = paramsItem2

        return findPageByFilter(JsonUtils.toJson(params),"{\"createTime\": \"desc\"}",pageable)
    }

    fun deleteByPlanId(planId: Long) {
        val params = HashMap<String, Any?>()
        params["planId"] = planId
        deleteByFilter(JsonUtils.toJson(params))
    }

}