package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.model.PointSendRule
import com.shuyun.loyalty.service.repository.PointSendRuleRepository
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class PointSendRuleBaseService {

    private val logger = LogManager.getLogger(PointSendRuleBaseService::class.java)

    @Autowired
    private lateinit var pointSendRuleRepository: PointSendRuleRepository


    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun insertPointRule(pointRule: PointSendRule){
        logger.debug("保存发放积分规则{}",{JsonUtils.toJson(pointRule)})
        pointSendRuleRepository.save(pointRule)
    }


    fun deleteByIds(ids:List<Long>) {
        pointSendRuleRepository.deleteByIdIn(ids)
    }


    fun findByRuleGroupIds(ruleGroupIds:List<Long>): List<PointSendRule> {
        return pointSendRuleRepository.findByRuleGroupIdIn(ruleGroupIds)
    }


    fun findPointSendRuleList(ruleGroupId: Long):List<PointSendRule> {
        logger.debug("直接查询规则组:{} 有效规则", { ruleGroupId })
        return findByRuleGroupIdAndDisabled(ruleGroupId)
    }

    fun findPointSendRule(ruleGroupId: Long, id: Long): PointSendRule? {
        logger.debug("直接查询具体规则 规则组ID:{} ID: {}", { ruleGroupId }, { id })
        return pointSendRuleRepository.findByRuleGroupIdAndId(ruleGroupId, id).orElse(null)
    }

    fun findByRuleGroupIdAndDisabled(ruleGroupId: Long): List<PointSendRule> {
        val list = pointSendRuleRepository.findByRuleGroupIdAndDisabled(ruleGroupId,false).sortedBy { it.sort }
        logger.trace("查询发放积分规则 规则组ID: $ruleGroupId,  查询结果: {}",{JsonUtils.toJson(list)})
        return list
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun deletePointRule(rule: PointSendRule){
        rule.disabled = true
        logger.debug("删除发放积分规则 规则组ID: ${rule.ruleGroupId}， 规则ID: ${rule.id}")
        pointSendRuleRepository.save(rule)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun updatePointSendRuleStatus(rule: PointSendRule){
        rule.fileStatus = true
        logger.debug("归档发放积分规则 规则组ID: ${rule.ruleGroupId}， 规则ID: ${rule.id}")
        pointSendRuleRepository.save(rule)
    }

}