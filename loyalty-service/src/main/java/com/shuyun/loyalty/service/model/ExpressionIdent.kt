package com.shuyun.loyalty.service.model

import io.swagger.v3.oas.annotations.media.Schema
import javax.persistence.MappedSuperclass


@MappedSuperclass
data class ExpressionIdent(
    @Schema(title = "属性编码", type = "String")
    var code: String? = null,
    @Schema(title = "属性名称", type = "String")
    var name: String? = null,
    @Schema(title = "属性类型(符号:sign/常量:value/变量:number/tmp等)", type = "String")
    var type: String? = null,
    @Schema(title = "返回类型", type = "String")
    var returnType: String? = null,
    @Schema(title = "时机事件流(如：data.at.ww.Order)", type = "String")
    var fqn: String? = null,
    @Schema(title = "属性值(如：orderItem[].num)", type = "String")
    var pathItem: String? = null,
    @Schema(title = "前端使用", type = "String")
    var orCode: String? = null,
    @Schema(title = "前端使用", type = "String")
    var andCode: String? = null,
    @Schema(title = "前端使用", type = "String")
    var expressionDesc: String? = null
)