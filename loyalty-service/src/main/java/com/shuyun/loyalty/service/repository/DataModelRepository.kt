package com.shuyun.loyalty.service.repository

import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.dm.api.dataapi.request.QueryDataRequest
import com.shuyun.dm.api.dataapi.request.UpdateByFilterRequest
import com.shuyun.dm.api.response.BaseResponse
import com.shuyun.dm.api.vo.PageQueryResponse
import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.datamodel.BaseDataModel
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.model.BaseDataModelAnalyzer
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import com.shuyun.pip.i18n.LocaleI18nContextHolder
import org.apache.logging.log4j.Logger
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.util.ObjectUtils
import java.lang.reflect.ParameterizedType
import java.util.*
import javax.persistence.Table

@Suppress("UNCHECKED_CAST")
abstract class DataModelRepository<T : BaseDataModel> {

    private var currentModelClazz = getCurrentActualTypeArgument()

    private var modelFqn = currentModelClazz.getDeclaredAnnotation(Table::class.java)!!.name

    private var analyzer = BaseDataModelAnalyzer(currentModelClazz)
    private var fields = analyzer.getFields()
    private var manyToOneFieldList = analyzer.getManyToOneFields().split(",")
    private var idField = analyzer.getIdField()
    private var fieldList = fields.split(",")
    private var fqnVariable = currentModelClazz.getDeclaredAnnotation(FqnVariableModel::class.java) is FqnVariableModel
    private var fieldsNoI18 = analyzer.getFields().replace(",_i18nPayload", "")
    open fun getFieldListNoI18() = this.fieldsNoI18
    open fun getFieldList() = this.fieldList
    open fun getIdField() = this.idField
    open fun getManyToOneFieldList() = this.manyToOneFieldList
    open fun getFields() = this.fields
    open fun getAnalyzer() = this.analyzer
    open fun getModelFqn(replacePattern: String?): String {
        return if (fqnVariable) {
            if (replacePattern is String)
                this.modelFqn.replace("{*}", replacePattern)
            else throw LoyaltyException(LoyaltyExceptionCode.UNKNOWN_EXCEPTION)
        } else {
            this.modelFqn
        }
    }
    open fun getModelFqn(replacePattern: Long?): String {
        return getModelFqn(replacePattern?.toString())
    }

    open fun getCurrentModelClazz() = this.currentModelClazz
    

    abstract fun log(): Logger

    fun executeList(sql: String, params: Map<String, Any>): List<T> {
        val resultResponse = DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.execute(sql, params) }
        return resultResponse!!.data.mapNotNull {
            JsonUtils.parse(JsonUtils.toJson(it), getCurrentModelClazz())
        }
    }

    fun execute(sql: String, params: Map<String, Any?>): BaseResponse<*> {
        return DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.execute(sql, params) }!!
    }

    fun executeOne(sql: String, params: Map<String, Any>): Optional<T> {
        val resultResponse = DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.execute(sql, params) }
        val resultObject = resultResponse!!.data
        return if (resultObject.isEmpty()) Optional.empty() else Optional.of(JsonUtils.parse(JsonUtils.toJson(resultObject[0]), getCurrentModelClazz()))
    }

    /** 查询所有记录，findAll只返回最大1000 */
    fun findAll(replacePattern: String? = null): List<T> {
        val fields = getFieldListNoI18()
        log().debug("find all value of fqn {} fields {}", getModelFqn(replacePattern), fields)
        val resultResponse = queryObjects(getModelFqn(replacePattern), fields, "{}", "{}", false, 0, 1_000)
        return resultResponse!!.data.asSequence().map {
            JsonUtils.parse(JsonUtils.toJson(it), getCurrentModelClazz())
        }.filterNotNull().toList()
    }

    fun findById(id: String, replacePattern: String? = null): Optional<T> {
        val fields = getFieldListNoI18()
        log().debug("find value of fqn {} fields {} by id {}", getModelFqn(replacePattern), fields, id)
        val result = try {
            DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.getObject(getModelFqn(replacePattern), id, fields) }
        } catch (e: Exception) {
            if (e is SdkException) {
                if (null != e.code && 404 == e.code!!.toInt()) {
                    return Optional.empty()
                } else {
                    throw e
                }
            } else if (e.cause is SdkException) {
                if ((e.cause as SdkException).error_code == "151143") {
                    return Optional.empty()
                } else {
                    throw e
                }
            } else {
                throw e
            }
        }
        val resultString = JsonUtils.toJson(result)
        val resultObject = JsonUtils.parse(resultString, getCurrentModelClazz())
        return Optional.of(resultObject)
    }


    @Suppress("unused")
    fun getByFqnOne(memberId: String, fields: String, fqn: String? = null): Map<String, Any> {
        val request = QueryDataRequest().apply {
            this.fqn = fqn
            this.filter = """
                {"memberId": "$memberId"}
            """.trimIndent()
            this.fields = fields
            this.withTotals = false
            this.offset = 0
            this.limit = 1
        }
        return DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.queryObjects(request) }!!.data!!.first()
    }


    @Suppress("unused")
    fun getOne(filter: String, replacePattern: String? = null): MutableList<MutableMap<String, Any>> {
        val fields = getFieldListNoI18()
        val request = QueryDataRequest().apply {
            this.fqn = getModelFqn(replacePattern)
            this.filter = filter
            this.fields = fields
            this.withTotals = false
            this.offset = 0
            this.limit = 1
        }
        return DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.queryObjects(request) }!!.data!!
    }

    fun findListByFilter(
        filter: String,
        sort: String = "{}",
        offset: Int = 0,
        limit: Int = 3000,
        replacePattern: String? = null
    ): List<T> {
        val fields = getFieldListNoI18()
        val resultResponse = queryObjects(getModelFqn(replacePattern), fields, filter, sort, false, offset, limit)
        return resultResponse!!.data.mapNotNull {
            JsonUtils.parse(JsonUtils.toJson(it), getCurrentModelClazz())
        }
    }

    fun findListByFilter(filter: String, sort: String = "{}", offset: Int = 0, limit: Int = 3000, withTotals: Boolean, replacePattern: String? = null): Map<String, Any?> {
        val fields = getFieldListNoI18()
        log().debug("find values of fqn {} fields {} by filter {}", getModelFqn(replacePattern), fields, filter)
        val resultResponse = queryObjects(getModelFqn(replacePattern), fields, filter, sort, withTotals, offset, limit)
        val data = resultResponse!!.data.mapNotNull {
            JsonUtils.parse(JsonUtils.toJson(it), getCurrentModelClazz())
        }
        return mapOf<String, Any?>(Pair("data", data), Pair("totals", resultResponse.totals))
    }


    @Suppress("unused")
    fun findCountByFilter(filterSql: String, params: Map<String, Any>, replacePattern: String? = null): Int {
        log().debug("find count of fqn {} by filter {} param {}", getModelFqn(replacePattern), filterSql, params)
        val resultResponse = DmPoolFactory.execute {
            it.execute("select count(id) as count from ${getModelFqn(replacePattern)} where 1=1 $filterSql", params)
        } ?: return 0
        val resList = resultResponse.data.mapNotNull { JsonUtils.objectToMap(it)["count"] }
        if (resList.isEmpty()) return 0
        return if (resList[0] is Int) resList[0] as Int else 0
    }

    fun findListByFilterNull(filter: String, sort: String = "{}", nullsOrderType: String? = null, offset: Int = 0, limit: Int = 3000, replacePattern: String? = null): List<T> {
        val fqn = getModelFqn(replacePattern)
        val fields = getFieldListNoI18()
        val queryDataRequest = QueryDataRequest().apply {
            this.fqn = fqn
            this.fields = fields
            this.filter = filter
            this.sort = sort
            this.withTotals = false
            this.offset = offset
            this.limit = limit
        }

        if (nullsOrderType != null) queryDataRequest.nullsOrderType = nullsOrderType

        log().debug("find values of fqn {} fields {} by filter {}", fqn, queryDataRequest.fields, filter)

        val resultResponse = DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.queryObjects(queryDataRequest) }

        return resultResponse!!.data.mapNotNull {
            JsonUtils.parse(JsonUtils.toJson(it), getCurrentModelClazz())
        }
    }

    fun findPageByFilter(filter: String, sort: String = "{}", pageable: Pageable, replacePattern: String? = null): Page<T> {
        val fields = getFieldListNoI18()
        log().debug("find values of fqn {} fields {} by filter {}", getModelFqn(replacePattern), fields, filter)
        val resultResponse = DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) {
            it.queryObjects(QueryDataRequest().apply {
                this.fqn = getModelFqn(replacePattern)
                this.fields = fields
                this.filter = filter
                this.sort = sort
                this.withTotals = true
                this.offset = pageable.pageNumber * pageable.pageSize
                this.limit = pageable.pageSize
            })
        }
        return PageImpl(resultResponse!!.data.mapNotNull { JsonUtils.parse(JsonUtils.toJson(it), getCurrentModelClazz()) },
            pageable, resultResponse.totals.toLong())
    }

    fun findOneByFilter(filter: String, replacePattern: String? = null, sort: String = "{}"): Optional<T> {
        val fields = getFieldListNoI18()
        log().debug("find value of fqn {} fields {} by filter {}", getModelFqn(replacePattern), fields, filter)
        val resultResponse = queryObjects(getModelFqn(replacePattern), fields, filter, sort, false, 0, 1)
        val resultObject = resultResponse!!.data
        return if (resultObject.isEmpty()) Optional.empty() else Optional.of(JsonUtils.parse(JsonUtils.toJson(resultObject[0]), getCurrentModelClazz()))
    }

    fun saveList(data: List<T>, replacePattern: String? = null) {
        data.forEach { saveOrUpdate(it, replacePattern) }
    }

    open fun batchInsert(dataList: List<T>, replacePattern: String? = null) {
        val modelFqn = getModelFqn(replacePattern)
        val manyToOneFieldList = getManyToOneFieldList()
        val fieldList = getFieldList()

        val columnList = ArrayList<Map<String, Any?>>()
        dataList.forEach {
            val dataMap = JsonUtils.parse(JsonUtils.toJson(it), Map::class.java) as Map<String, Any?>
            var columnMap = HashMap<String, Any?>()
            listOf(manyToOneFieldList, fieldList).flatten().forEach { key ->
                if (fieldList.contains(key)) {
                    columnMap[key] = dataMap[key]
                } else if (getManyToOneFieldList().contains(key)) {
                    var manyToOneValue = JsonUtils.parse(JsonUtils.toJson(dataMap[key]), Map::class.java) as Map<String, Any?>?
                    if (manyToOneValue == null) {
                        val foreignKey = getAnalyzer().getManyToOneFieldForeignKey(key)
                        if (foreignKey is String) {
                            manyToOneValue = mapOf(Pair("id", dataMap[foreignKey]))
                            columnMap[key] = mapOf(Pair("id", manyToOneValue["id"]))
                        }
                    } else {
                        columnMap[key] = mapOf(Pair("id", manyToOneValue["id"]))
                    }
                }
            }
            columnMap = toI18NullMap(fieldList, columnMap)
            columnList.add(columnMap)
        }
        log().debug("save data of fqn {} params size {}", modelFqn, columnList.size)
        DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.batchInsert(modelFqn, columnList, false, false) }
        log().debug("result data of fqn {} params size {}", modelFqn, columnList.size)
    }

//    @CostTime(printSlowLog = true, slowLoadTime = 3000)
//    open fun batchInsert(dataList: List<T>, replacePattern: String? = null) {
//        val modelFqn = getModelFqn(replacePattern)
//        if (dataList.isEmpty()) {
//            log().debug("data list is empty of fqn $modelFqn")
//        }
//
//        val batchInsertSQL = StringBuilder()
//        batchInsertSQL.append("INSERT INTO ")
//        var first = false
//
//        dataList.forEach {
//            var columnMap = getColumMapFromData(it)
//            if (!first) {
//                val manyToOneFields = if (analyzer.getManyToOneFields().isNotBlank())
//                    ",${analyzer.getManyToOneFields()}"
//                else ""
//                batchInsertSQL.append("$modelFqn ($fields $manyToOneFields) VALUES ")
//                first = true
//            }
//            batchInsertSQL.append("(")
//            if (columnMap[idField] == null) columnMap[idField] = Uuid.uuid
//            columnMap = toI18NullMap(fieldList, columnMap)
//            fieldList.forEach { field -> batchInsertSQL.append(convertValueToSql(columnMap[field])) }
//            manyToOneFieldList.filter { field -> field.isNotBlank() }.forEach {
//                    field -> batchInsertSQL.append(convertValueToSql(columnMap[field]))
//            }
//            batchInsertSQL.deleteAt(batchInsertSQL.length - 1)
//            batchInsertSQL.append("),")
//        }
//
//        batchInsertSQL.deleteAt(batchInsertSQL.length - 1)
//
//        log().debug("save data of fqn {} params size {}", modelFqn, dataList.size)
//        DmPoolFactory.execute{it.execute(batchInsertSQL.toString(), mapOf())}
//        log().debug("save data of fqn {} params size {}", modelFqn, dataList.size)
//    }

    open fun save(t: T, replacePattern: String? = null): T {
        val modelFqn = getModelFqn(replacePattern)
        val manyToOneFieldList = getManyToOneFieldList()
        val fieldList = getFieldList()
        val dataMap = JsonUtils.parse(JsonUtils.toJson(t), Map::class.java) as Map<String, Any?>
        var columnMap = HashMap<String, Any?>()
        listOf(manyToOneFieldList, fieldList).flatten().forEach { key ->
            if (fieldList.contains(key)) {
                columnMap[key] = dataMap[key]
            } else if (getManyToOneFieldList().contains(key)) {
                var manyToOneValue = JsonUtils.parse(JsonUtils.toJson(dataMap[key]), Map::class.java) as Map<String, Any?>?
                if (manyToOneValue == null) {
                    val foreignKey = getAnalyzer().getManyToOneFieldForeignKey(key)
                    if (foreignKey is String) {
                        manyToOneValue = mapOf(Pair("id", dataMap[foreignKey]))
                        columnMap[key] = mapOf(Pair("id", manyToOneValue["id"]))
                    }
                } else {
                    columnMap[key] = mapOf(Pair("id", manyToOneValue["id"]))
                }
            }
        }
        columnMap = toI18NullMap(fieldList, columnMap)
        log().debug("save data of fqn {} params {}", modelFqn, JsonUtils.toJson(columnMap))
        val id = DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.insert(modelFqn, columnMap, false, false).id }
        log().debug("result data of fqn {} params {}", modelFqn, JsonUtils.toJson(columnMap))
        t.id = id
        return t
    }


    private fun toI18NullMap(fieldList: List<String>, columnMap: HashMap<String, Any?>): HashMap<String, Any?> {
        val i18nPayloadMap = HashMap<String, Any>()
        fieldList.forEach { key ->
            if (!columnMap.containsKey(key)) {
                columnMap[key] = null
            }
            if (key == "_i18nPayload") {
                if (columnMap[key] == null) return@forEach
                val map = (columnMap[key] as HashMap<String, Any?>)
                val iter = map.iterator()
                val acceptLanguage = LocaleI18nContextHolder.getAcceptLanguage()
                while (iter.hasNext()) {
                    val entry = iter.next()
                    val i18nMap = JsonUtils.objectToMap(entry.value)
                    if (i18nMap!!["zh-CN"] == null) i18nMap["zh-CN"] = null
                    if (i18nMap["en-US"] == null) i18nMap["en-US"] = null
                    if (i18nMap["zh-HK"] == null) i18nMap["zh-HK"] = null
                    if (i18nMap[acceptLanguage] == null) i18nMap[acceptLanguage] = columnMap[entry.key]
                    i18nPayloadMap[entry.key] = i18nMap
                }
            }
        }
        if (!ObjectUtils.isEmpty(columnMap["_i18nPayload"])) columnMap["_i18nPayload"] = i18nPayloadMap
        return columnMap
    }

    open fun saveOrUpdate(t: T, replacePattern: String? = null): T {

        val modelFqn = getModelFqn(replacePattern)
        val manyToOneFieldList = getManyToOneFieldList()
        val fieldList = getFieldList()
        val dataMap = JsonUtils.parse(JsonUtils.toJson(t), Map::class.java) as Map<String, Any?>
        var columnMap = HashMap<String, Any?>()
        listOf(manyToOneFieldList, fieldList).flatten().forEach { key ->
            if (fieldList.contains(key)) {
                columnMap[key] = dataMap[key]
            } else if (manyToOneFieldList.contains(key)) {
                var manyToOneValue = JsonUtils.parse(JsonUtils.toJson(dataMap[key]), Map::class.java) as Map<String, Any?>?
                if (manyToOneValue == null) {
                    val foreignKey = getAnalyzer().getManyToOneFieldForeignKey(key)
                    if (foreignKey is String) {
                        manyToOneValue = mapOf(Pair("id", dataMap[foreignKey]))
                        columnMap[key] = mapOf(Pair("id", manyToOneValue["id"]))
                    }
                } else {
                    columnMap[key] = mapOf(Pair("id", manyToOneValue["id"]))
                }
            }
        }
        columnMap = toI18NullMap(fieldList, columnMap)
        val idValue = dataMap[getIdField()]
        val id = if (null == idValue) {
            val insertResponse = DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.insert(modelFqn, columnMap, false, false) }
            insertResponse!!.id
        } else {
            val update = DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.update(modelFqn, idValue as String, columnMap, false) }
            if (update!!.affectedRows == 0) {
                log().warn("update value effect row count 0 of fqn {} by id {}", modelFqn, idValue)
            }
            idValue
        }
        t.id = (id as String)
        return t
    }

    @Suppress("unused")
    fun updateByFilter(map: Map<String, Any>, filter: String, replacePattern: String? = null): Int {
        log().debug("update fqn {} value by filter {}, body {}", getModelFqn(replacePattern), JsonUtils.toJson(filter), JsonUtils.toJson(map))
        val request = UpdateByFilterRequest()
        request.filter = filter
        request.fqn = getModelFqn(replacePattern)
        request.body = map
        val response = DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.updateByFilter(getModelFqn(replacePattern), request) }
        return response?.affectedRows ?: 0
    }

    fun delete(id: String, replacePattern: String? = null) {
        log().debug("delete fqn {} value by id {}", getModelFqn(replacePattern), id)
        DmPoolFactory.execute { sdk -> sdk.delete(getModelFqn(replacePattern), id, false) }
    }

    fun deleteByFilter(filter: String, replacePattern: String? = null) {
        log().debug("delete fqn {} value by filter {}", getModelFqn(replacePattern), filter)
        DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.deleteByFilter(getModelFqn(replacePattern), filter) }
    }

    @Suppress("unused")
    fun updateById(id: String, map: Map<String, Any>, replacePattern: String? = null) {
        log().debug("update fqn {} value by id {}", getModelFqn(replacePattern), id)
        DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.update(getModelFqn(replacePattern), id, map, false) }
    }

    /** 删除表中所有数据 */
    @Suppress("unused")
    fun deleteAll(replacePattern: String? = null) {
        log().warn("delete all value of fqn {}", getModelFqn(replacePattern))
        try {
            DmPoolFactory.execute (UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.deleteByFilter(getModelFqn(replacePattern), "{}") }
        } catch (e: Exception) {
            log().warn("delete all ", e)
        }
    }

    private fun getCurrentActualTypeArgument(): Class<T> = this.javaClass.genericSuperclass.let {
        when (it) {
            is ParameterizedType -> it.actualTypeArguments[0]
            else -> ((it as Class<*>).genericSuperclass as ParameterizedType).actualTypeArguments[0]
        } as Class<T>
    }

    private fun queryObjects(
        fqn: String?,
        fields: String?,
        filter: String?,
        sort: String?,
        withTotals: Boolean?,
        offset: Int?,
        limit: Int?
    ): PageQueryResponse? {
        val request = QueryDataRequest()
        request.fqn = fqn
        request.fields = fields
        request.filter = filter
        request.sort = sort
        request.withTotals = withTotals
        request.offset = offset
        request.limit = limit
        return DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk -> sdk.queryObjects(request) }
    }

}


