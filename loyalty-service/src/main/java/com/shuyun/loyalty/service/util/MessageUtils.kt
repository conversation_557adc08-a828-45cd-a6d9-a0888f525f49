package com.shuyun.loyalty.service.util

import com.shuyun.loyalty.service.event.Event
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager


/**
 * 事件辅助类
 */
object MessageUtils {

    private var threadLocal = ThreadLocal.withInitial{ HashMap<String,Any>() }

    private var log = LogManager.getLogger(MessageUtils::class.java)


    fun setEvent(event: Event) {
        threadLocal.get()["event"] = event
    }

    fun getEventTypeIds():ArrayList<String>? {
        val value = threadLocal.get()["eventTypeIds"] ?: return null
        @Suppress("UNCHECKED_CAST")
        return value as ArrayList<String>
    }

    fun isIgnoreIdempotent():Boolean {
        return getEventTypeIds() == null
    }


    fun clear() {
        try{
            threadLocal.get().clear()
            threadLocal.remove()
        }catch(e:Exception) {
            log.error("清除事件辅助类异常: ", e)
        }
    }

    fun convert(event: Event) {
        try{
            setEvent(event)
            val node = event.get(com.shuyun.loyalty.entity.api.constants.Constants.LOYALTY_MESSAGE_EXTEND) ?: return
            val list = JsonUtils.parse(JsonUtils.parse(node.toString(),Map::class.java)["eventTypeIds"].toString(), ArrayList::class.java)
            if(list.size != 0) {
                val ids = ArrayList<String>()
                list.forEach { ids.add(it.toString()) }
                setEventTypeIds(ids)
            }
        } catch (e : Exception){
            log.error("获取退单优于下单扩展字段异常", e)
        }
    }

    private fun setEventTypeIds(list: ArrayList<String>) {
        threadLocal.get()["eventTypeIds"] = list
    }

}