package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.model.GradeRule
import com.shuyun.loyalty.service.repository.GradeRuleRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service


@Service
class GradeRuleBaseService {

    @Autowired
    private lateinit var gradeRuleRepository: GradeRuleRepository

    private val logger = org.slf4j.LoggerFactory.getLogger(GradeRuleBaseService::class.java)

    /**根据Id获取规则， 暂没有地方调用，缓存问题不考虑*/
    fun findById(id: Long): GradeRule {
        val group = gradeRuleRepository.findByIdAndDisabled(id, false)
        return if (group.isPresent) group.get() else throw IllegalArgumentException("规则不存在")
    }

    /**插入规则本身*/
    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun insert(gradeRule: GradeRule, gradeDefinitionId: Long) {
        logger.debug("insert gradeRule: {} gradeDefinitionId: {}", gradeRule, gradeDefinitionId)
        gradeRule.id = null
        gradeRuleRepository.save(gradeRule)
    }

    /**更新规则本身*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun update(gradeRule: GradeRule, gradeDefinitionId: Long) {
        logger.debug("update gradeRule: {} gradeDefinitionId: {}", gradeRule, gradeDefinitionId)
        gradeRuleRepository.save(gradeRule)
    }

    /**删除规则本身*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun delete(gradeRule: GradeRule) {
        logger.debug("delete gradeRule: {}", gradeRule)
        gradeRule.disabled = true
        gradeRuleRepository.save(gradeRule)
    }
}