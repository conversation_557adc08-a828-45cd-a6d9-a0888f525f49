package com.shuyun.loyalty.service.datamodel

import java.math.BigDecimal
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.Id
import javax.persistence.MappedSuperclass
import javax.validation.constraints.NotEmpty

@MappedSuperclass
abstract class BaseDataModel {
    /** 业务唯一id，自动生成 */
    @Id
    @Column
    var id: String? = null
}

@MappedSuperclass
abstract class BaseDataModelVersion {
    /** 业务唯一id，自动生成 */
    @Id
    @Column
    var versionId: Long? = null
}

/** 会员积分记录基本信息，其中记录比较冗余，不会变更，主要是为了查询方便 */
@MappedSuperclass
abstract class BaseMemberPoint : BaseDataModel() {

    companion object;

    /** 计划id */
    @Column
    @NotEmpty
    var planId: Long? = null

    /** 积分计划id */
    @Column
    @NotEmpty
    var pointPlanId: Long? = null

    /** 会员全网id */
    @Column
    @NotEmpty
    lateinit var memberId: String

    /** 归属性主体，如果为空，则根据当前积分账户查询归属主体 */
    @Column
    @NotEmpty
    lateinit var subjectFqn: String


    /** 积分值 */
    @Column
    var point: BigDecimal = BigDecimal.ZERO

    /** 记录创建时间 */
    @Column
    @NotEmpty
    var created: ZonedDateTime = ZonedDateTime.now()

}