package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.model.PointSendLimitRule
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface PointSendLimitRuleRepository : JpaRepository<PointSendLimitRule, Long>, JpaSpecificationExecutor<PointSendLimitRule> {

    fun findByIdAndDisabled(id:Long,disabled:Boolean) : Optional<PointSendLimitRule>

    @Query("select * from point_send_limit_rule where (type = ?2 and FIND_IN_SET(?1,nlms_ids)) or group_id=?1", nativeQuery = true)
    fun findByNlmsIdsAndGroupId(nlmsId:String, type : String): List<PointSendLimitRule>


    @Query("select * from point_send_limit_rule where group_id=?1 and type = ?2 ", nativeQuery = true)
    fun findByGroupId(groupId:String, type : String): List<PointSendLimitRule>


    @Query("select * from point_send_limit_rule where point_account_id=?5 and type = ?2 and status=?3 and disabled=?4 and kind='INCLUDE' and FIND_IN_SET(?1,nlms_ids) ", nativeQuery = true)
    fun findLimitRuleListInclude(nlmsId: String, type: String, status: String, disabled: Boolean,pointAccountTypeId:Long): List<PointSendLimitRule>

}