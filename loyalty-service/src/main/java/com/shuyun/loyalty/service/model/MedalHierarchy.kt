package com.shuyun.loyalty.service.model

import com.shuyun.loyalty.entity.api.constants.PublishStatusEnum
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.PlanStatusEnum
import com.shuyun.loyalty.service.service.PlanBaseService
import com.shuyun.loyalty.service.service.SubjectBaseService
import com.shuyun.loyalty.service.util.ModelInitUtil
import com.shuyun.pip.ApplicationContextHolder
import io.swagger.v3.oas.annotations.media.Schema
import org.apache.logging.log4j.LogManager
import javax.persistence.*

@Schema(title = "勋章体系")
@DataServiceModel
@Table(name = "data.loyalty.manager.medalHierarchy")
data class MedalHierarchy(

    @Schema(title = "id", type = "Long")
    @Column
    var id: Long? = null,

    @Schema(title = "名称", type = "String")
    @Column
    var name: String? = null,

    @Schema(title = "主体ID", type = "Long")
    @Column
    var subjectVersionId: Long? = null,

    @Schema(title = "等级", type = "List")
    @Transient
    var medalDefinitions: List<MedalDefinition>? = null,

    @Schema(title = "前端排序号", type = "Int")
    @Column
    var sort: Int? = null,

    @Schema(title = "执行顺序(倒序)", type = "Int")
    @Column
    var executeOrder: Int? = null,

    @Schema(title = "状态", type = "String")
    @Enumerated(EnumType.STRING)
    @Column
    var status: PublishStatusEnum? = null,

    ): BaseDataServiceModel() {

    companion object {
        @Transient
        private val log = LogManager.getLogger(MedalHierarchy::class.java)
    }

    override fun copyToOldOne(old: BaseDataServiceModel, backup: Boolean) {
        old as MedalHierarchy
        if (backup) {
            if (PublishStatusEnum.DRAFT == old.status) {
                status = PublishStatusEnum.DRAFT
            }
            else {
                if (!arrayOf(PublishStatusEnum.PUBLISHED, PublishStatusEnum.FILED).contains(status))
                    throw LoyaltyException(LoyaltyExceptionCode.PUBLISHED_PLAN_NOT_OPERATION_UPDATE)
                else if (PublishStatusEnum.FILED == old.status && PublishStatusEnum.PUBLISHED == status)
                    throw LoyaltyException(LoyaltyExceptionCode.MEDAL_JUDGE_FILE_STATUS)
            }
        } else {
            status = PublishStatusEnum.DRAFT
        }

        subjectVersionId = null
        ModelInitUtil.copyPropertiesIgnoreNull(this, old)
    }

    fun checkHierarchyPlanPublishedOne(): Boolean {
        return try {
            val subject = ApplicationContextHolder.getBean(SubjectBaseService::class.java).findByVersionId(this.subjectVersionId!!)
            val plan = ApplicationContextHolder.getBean(PlanBaseService::class.java).findByVersionId(subject.planVersionId!!)
            log.trace("查询勋章相关参数: status_{} plan_{} subject_{} subjectVersionId_{} planVersionId_{}",
                plan.status, plan.id, subject.id, this.subjectVersionId, subject.planVersionId)
            plan.status == PlanStatusEnum.PUBLISHED
        } catch (e: LoyaltyException) {
            log.debug("过滤无效的勋章相关数据:{}", e.msg)
            false
        } catch (e: NoSuchElementException) {
            log.debug("过滤无效的勋章相关数据:{}", e.message)
            false
        } catch (e: Exception) {
            log.debug("过滤无效的勋章相关数据:{}", e.toString())
            false
        }
    }


}