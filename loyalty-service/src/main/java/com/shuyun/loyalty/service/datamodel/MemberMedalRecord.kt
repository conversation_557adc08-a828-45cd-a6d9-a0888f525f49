package com.shuyun.loyalty.service.datamodel

import com.fasterxml.jackson.annotation.JsonProperty
import com.shuyun.loyalty.entity.api.constants.MedalChangeMode
import com.shuyun.loyalty.entity.api.constants.MedalRecordType
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.datamodel.MemberMedalRecord.Companion.dataModelFqn
import com.shuyun.loyalty.service.model.LoyaltyEventBusMessage
import com.shuyun.loyalty.service.service.MemberMedalBaseService
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.pip.ApplicationContextHolder
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne
import javax.persistence.Table

@Table(name = dataModelFqn)
@DataServiceModel
@FqnVariableModel
class MemberMedalRecord: BaseDataModel(), LoyaltyEventBusMessage {

    @Column
    var planId: Long? = null

    @Column
    var planName: String? = null

    @Column
    var medalHierarchyId: Long? = null

    @Column
    var medalHierarchyName: String? = null

    @Column
    var medalDefinitionId: Long? = null

    @Column
    var medalDefinitionName: String? = null

    @Column
    var currentMedalDefinitionIds: String? = null

    @Column
    var memberMedalId: String? = null

    @Column
    var memberId: String? = null

    @Column
    var originalEffectDate: ZonedDateTime? = null

    @Column
    var originalOverdueDate: ZonedDateTime? = null

    @Column
    var currentEffectDate: ZonedDateTime? = null

    @Column
    var currentOverdueDate: ZonedDateTime? = null

    @Column
    var recordType: MedalRecordType? = null

    @Column
    var changeWay: MedalChangeMode? = null

    @Column
    var triggerId: String? = null

    @Column
    var key: String? = null

    @Column
    var traceId: String? = null

    @Column
    var recordSourceDetail: String? = null

    @Column
    var operator: String? = null

    @Column
    var subjectFqn: String? = null

    @Column
    var description: String? = null

    @Column
    var recordDetail: String? = null

    @Column
    var created: ZonedDateTime? = null

    @Column
    var channel: String? = null

    @Column
    var eventTypeId: Long? = null

    @Column
    var eventTypeName: String? = null

    @Column
    var eventFqn: String? = null

    @Column
    var ruleId: Long? = null

    @Column
    var ruleName: String? = null

    @Column
    @set:JsonProperty("KZZD1")
    @get:JsonProperty("KZZD1")
    var KZZD1: String? = null

    @Column
    @set:JsonProperty("KZZD2")
    @get:JsonProperty("KZZD2")
    var KZZD2: String? = null

    @Column
    @set:JsonProperty("KZZD3")
    @get:JsonProperty("KZZD3")
    var KZZD3: String? = null

    @ManyToOne
    @JoinColumn(name = "memberMedalId")
    var memberMedal: MemberMedal? = null


    companion object {

        const val dataModelFqn = "data.loyalty.member.hierarchy.MedalRecord{*}"

        fun init(memberMedal: MemberMedal, changeWay: MedalChangeMode, recordType: MedalRecordType, desc: String?): MemberMedalRecord {
            val record = MemberMedalRecord()
            record.planId = memberMedal.planId
            record.planName = memberMedal.planName
            record.medalHierarchyId = memberMedal.medalHierarchyId
            record.medalHierarchyName = memberMedal.medalHierarchyName
            record.medalDefinitionId = memberMedal.medalDefinitionId
            record.medalDefinitionName = memberMedal.medalDefinitionName
            record.currentMedalDefinitionIds = ApplicationContextHolder.getBean(MemberMedalBaseService::class.java)
                .currentMedalDefinitionIds(memberMedal.memberId!!, memberMedal.medalHierarchyId!!).joinToString(",")
            record.memberMedalId = memberMedal.id
            record.memberId = memberMedal.memberId
            record.currentEffectDate = memberMedal.effectDate
            record.currentOverdueDate = memberMedal.overdueDate
            record.subjectFqn = memberMedal.subjectFqn
            record.channel = "loyalty"
            record.operator = ConstantValue.DEFAULT_OPERATOR
            record.memberMedal = memberMedal
            record.recordType = recordType
            record.changeWay = changeWay
            record.description = desc
            record.created = ZonedDateTime.now()
            return record
        }

    }

}