package com.shuyun.loyalty.service.model

import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal

@Schema(title = "特殊变更积分")
data class ManualMemberPoint(

    @Schema(title = "主键id", type = "String")
    var id: String? = null,

    @Schema(title = "账户id", type = "String")
    var pointAccountId: Long? = null,

    @Schema(title = "账户名称", type = "String")
    var pointAccountName: String? = null,

    @Schema(title = "计划id", type = "String")
    var planId: Long? = null,

    @Schema(title = "计划名称", type = "String")
    var planName: String? = null,

    @Schema(title = "积分值", type = "String")
    var pointValue: BigDecimal? = null,

    @Schema(title = "会员id", type = "String")
    var memberId: String? = null,

    @Schema(title = "备注", type = "String")
    var desc: String? = null,

    @Schema(title = "店铺ID", type = "String")
    var shopId: String? = null,

    @Schema(title = "KZZD1", type = "String")
    var KZZD1: String? = null,

    @Schema(title = "KZZD2", type = "String")
    var KZZD2: String? = null,

    @Schema(title = "KZZD3", type = "String")
    var KZZD3: String? = null,

    //待生效积分标识
    @Schema(title = "isNotValidPoint", type = "String")
    var isNotValidPoint: Boolean? = false

)


