package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.service.datamodel.MemberPointNegativeStatement
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

/**
 * 负积分记录业务类
 */
@Component
class NegativeStatementService : MemberPointDmBaseService<MemberPointNegativeStatement>() {
    private val log = LogManager.getLogger(NegativeStatementService::class.java)
    override fun log(): Logger = log

    fun findByMemberPointIdList(memberPointId: String, accountTypeId:Long, limit: Int = 3000): List<MemberPointNegativeStatement> {
        val params = HashMap<String, Any?>()
        params["memberPointId"] = memberPointId
        val sort = HashMap<String, Any?>()
        sort["created"] = "ASC"
        val result = findListByFilterNull(JsonUtils.toJson(params), JsonUtils.toJson(sort), null, limit = limit, replacePattern = accountTypeId.toString())
        return result.ifEmpty { listOf() }
    }

    fun deleteByIds(ids: Array<String>, accountTypeId:Long) {
        if (ids.isEmpty()) {
            return
        }
        val bodyParams = HashMap<String, Any?>()
        val paramsItem = HashMap<String, Any?>()
        paramsItem["\$in"] = ids
        bodyParams["id"] = paramsItem
        deleteByFilter(JsonUtils.toJson(bodyParams),replacePattern = accountTypeId.toString())
    }

}