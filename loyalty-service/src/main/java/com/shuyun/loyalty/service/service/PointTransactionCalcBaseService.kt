package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.service.datamodel.PointTransactionCalc
import com.shuyun.loyalty.service.util.SQL
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Service
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit

@Service
class PointTransactionCalcBaseService : MemberPointDmBaseService<PointTransactionCalc>() {
    private val log = LogManager.getLogger(PointTransactionCalcBaseService::class.java)
    override fun log(): Logger = log

    /**
     * 根据transactionId查询影响信息
     */
    fun getByBusinessId(sourceId: String, tradeId: String, pointPlanId: Long): List<PointTransactionCalc> {
        val sort = mapOf<String, Any?>("overdueDate" to "DESC")
        val pointTransactionCalcList = findListByFilterNull(
            JsonUtils.toJson(mapOf("businessId" to tradeId, "sourceId" to sourceId)),
            JsonUtils.toJson(sort), "FIRST" ,limit = 10000,replacePattern = pointPlanId.toString())
        return pointTransactionCalcList
    }


    fun deleteByIds(ids: List<String>, pointPlanId: Long) {
        if (ids.isEmpty()) return
        val sql = "DELETE FROM data.loyalty.member.account.PointTransactionCalc$pointPlanId WHERE id in (:ids)"
        execute(sql, mapOf("ids" to ids))
    }


    override fun batchInsert(dataList: List<PointTransactionCalc>, replacePattern: String?) {
        if (dataList.isEmpty()) return
        val sql = SQL.build {
            var sql = """
                INSERT INTO data.loyalty.member.account.PointTransactionCalc$replacePattern  
                (
                    `id`, `businessId`, `gainStatementId`, `point`, `effectiveDate`, `overdueDate`, `sourceId`, `sort`, 
                    `recordId`, `shopId`, `backId`, `ext1`, `ext2`, `ext3`, `modified`, `created`
                ) 
                VALUES 
            """.trimIndent()
            var i = 0L
            for (item in dataList) {
                val now = ZonedDateTime.now().plus(i++, ChronoUnit.MILLIS)
                sql += """
                    (
                    ${item.id.escaped()},
                    ${item.businessId.escaped()}, 
                    ${item.gainStatementId.escaped()}, 
                    ${item.point.escaped()}, 
                    ${item.effectiveDate.escaped()}, 
                    ${item.overdueDate.escaped()}, 
                    ${item.sourceId.escaped()},
                    ${item.sort.escaped()}, 
                    ${item.recordId.escaped()}, 
                    ${item.shopId.escaped()},
                    ${item.backId.escaped()},
                    ${item.ext1.escaped()},
                    ${item.ext2.escaped()},
                    ${item.ext3.escaped()},
                    ${now.escaped()},
                    ${now.escaped()}
                    ),
                """.trimIndent()
            }
            sql.trim().dropLast(1)
        }

        super.execute(sql, mapOf())
    }

}