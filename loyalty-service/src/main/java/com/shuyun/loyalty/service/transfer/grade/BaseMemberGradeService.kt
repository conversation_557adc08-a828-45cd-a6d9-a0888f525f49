package com.shuyun.loyalty.service.transfer.grade

import com.shuyun.loyalty.entity.api.constants.ChangeMode
import com.shuyun.loyalty.entity.api.constants.GradeRecordType
import com.shuyun.loyalty.entity.api.request.MemberGradeModifyRequest
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.service.datamodel.InterfaceRecord
import com.shuyun.loyalty.service.datamodel.MemberGrade
import com.shuyun.loyalty.service.datamodel.MemberGradeRecord
import com.shuyun.loyalty.service.exception.GradeException
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.TypeEnum
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.model.GradeDefinition
import com.shuyun.loyalty.service.model.GradeHierarchy
import com.shuyun.loyalty.service.model.Subject
import com.shuyun.loyalty.service.repository.InterfaceRecordRepository
import com.shuyun.loyalty.service.repository.MemberGradeRecordRepository
import com.shuyun.loyalty.service.repository.MemberGradeRepository
import com.shuyun.loyalty.service.service.NotifyBaseService
import com.shuyun.loyalty.service.service.SpecialListConfigBaseService
import com.shuyun.loyalty.service.util.MDCUtils
import com.shuyun.pip.component.json.JsonUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.time.ZonedDateTime
import java.util.concurrent.Callable
import java.util.concurrent.Future
import kotlin.jvm.optionals.getOrNull

/**
 */
@Component
class BaseMemberGradeService {

    @Autowired
    private lateinit var memberGradeRepository: MemberGradeRepository
    @Autowired
    private lateinit var specialListConfigBaseService: SpecialListConfigBaseService
    @Autowired
    private lateinit var interfaceRecordRepository: InterfaceRecordRepository
    @Autowired
    private lateinit var memberGradeRecordRepository: MemberGradeRecordRepository
    @Autowired
    private lateinit var notifyBaseService: NotifyBaseService

    /**保存等级明细*/
    fun saveMemberGradeRecordFuture(
        hierarchy: GradeHierarchy,
        memberGradeModifyRequest: MemberGradeModifyRequest,
        originalMemberGrade: MemberGrade?,
        memberGrade: MemberGrade,
        recordType: GradeRecordType,
        operator: String?
    ) = MemberGradeTransferService.fixedThreadPool.submit(MDCUtils.wrap {
            try {
                val memberGradeRecord = MemberGradeRecord.init(
                    hierarchy,
                    originalMemberGrade,
                    memberGrade,
                    recordType,
                    memberGradeModifyRequest.triggerId,
                    memberGradeModifyRequest.description,
                    memberGradeModifyRequest.channelType,
                    memberGradeModifyRequest.changeWayType!!,
                    memberGradeModifyRequest.triggerId
                )

                memberGradeRecord.operator = operator

                memberGradeRecordRepository.saveOrUpdate(
                    memberGradeRecord,
                    memberGradeModifyRequest.gradeHierarchyId.toString()
                )
                notifyBaseService.postIntoBus(memberGradeRecord)
            } catch (e: Throwable) {
                throw LoyaltyException(LoyaltyExceptionCode.DATA_SERVICE_ERROR, e)
            }
    })

    /**初始化当前等级*/
    fun saveMemberGradeFuture(memberGrade: MemberGrade, memberGradeModifyRequest: MemberGradeModifyRequest) = MemberGradeTransferService.fixedThreadPool.submit(MDCUtils.wrap{
        try {
            memberGradeRepository.save(memberGrade,memberGradeModifyRequest.gradeHierarchyId.toString())
        } catch (e: Throwable) {
            throw LoyaltyException(LoyaltyExceptionCode.DATA_SERVICE_ERROR,e)
        }
    })

    fun updateMemberGradeFuture(memberGrade: MemberGrade, memberGradeModifyRequest: MemberGradeModifyRequest) = MemberGradeTransferService.fixedThreadPool.submit(MDCUtils.wrap{
        try {
            memberGradeRepository.saveOrUpdate(memberGrade,memberGradeModifyRequest.gradeHierarchyId.toString())
        } catch (e: Throwable) {
            throw LoyaltyException(LoyaltyExceptionCode.DATA_SERVICE_ERROR,e)
        }
    })

    /**查询是否在黑名单*/
    fun findChecklistFuture(
        memberGradeModifyRequest: MemberGradeModifyRequest,
        subject: Subject,
        hierarchy: GradeHierarchy,
        forbiddenOperation: ForbiddenOperation,
        forbiddenPort: ForbiddenPort,
        subjectVersionId: Long
    ) = MemberGradeTransferService.fixedThreadPool.submit(MDCUtils.wrap(Callable {
        try {
            specialListConfigBaseService.checkMemberInSpecialList(
                memberGradeModifyRequest.memberId!!,
                subject.id!!,
                hierarchy.id!!,
                forbiddenOperation,
                forbiddenPort,
                subjectVersionId,
                false,
                TypeEnum.GRADE
            )
        } catch (e: Throwable) {
            throw LoyaltyException(LoyaltyExceptionCode.DATA_SERVICE_ERROR, e)
        }
    }))

    /**查询是否在会员表*/
    fun findFutureMemberFuture(memberGradeModifyRequest: MemberGradeModifyRequest, subject: Subject) = MemberGradeTransferService.fixedThreadPool.submit(MDCUtils.wrap(Callable{
        try {
            specialListConfigBaseService.futureMemberId(memberGradeModifyRequest.memberId!!, subject.dataType!!)
        } catch (e: Throwable) {
            throw LoyaltyException(LoyaltyExceptionCode.DATA_SERVICE_ERROR,e)
        }
    }))





    /**查询变更前会员当前等级*/
    fun findOneByMemberGrade(memberGradeModifyRequest: MemberGradeModifyRequest): MemberGrade? {
        return memberGradeRepository.findOneByMemberGrade(memberGradeModifyRequest).getOrNull()
    }

    /**保存去重复表*/
    fun saveInterfaceRecordFuture(memberGradeModifyRequest: MemberGradeModifyRequest): Future<InterfaceRecord>? {
        val interfaceRecord = InterfaceRecord().apply {
            this.id = memberGradeModifyRequest.triggerId
            this.sequence = memberGradeModifyRequest.triggerId
            this.content = JsonUtils.toJson(memberGradeModifyRequest)
            this.created = ZonedDateTime.now()
        }

        var interfaceRecordFuture: Future<InterfaceRecord>? = null
        if(memberGradeModifyRequest.changeWayType != ChangeMode.MANUAL){
            interfaceRecordFuture = MemberGradeTransferService.fixedThreadPool.submit(MDCUtils.wrap(Callable{
                try {
                    interfaceRecordRepository.save(interfaceRecord)
                } catch (e: Throwable) {
                    throw e
                }
            }))
        }
        return interfaceRecordFuture
    }

    /**查询变更等级动作*/
    fun findRecordType(originalGradeDefinition: GradeDefinition?, targetGradeDefinition: GradeDefinition): GradeRecordType {
        return when (originalGradeDefinition) {
            null -> GradeRecordType.UPGRADE
            else -> when {
                originalGradeDefinition.sort!! == targetGradeDefinition.sort!! -> GradeRecordType.HOLD_BACK_GRADE
                originalGradeDefinition.sort!! > targetGradeDefinition.sort!! -> GradeRecordType.DEGRADE
                else -> GradeRecordType.UPGRADE
            }
        }
    }

    /**查询黑名单配置类型*/
    fun findForbiddenOperation(recordType: GradeRecordType): ForbiddenOperation {
        return when(recordType){
            GradeRecordType.HOLD_BACK_GRADE ->ForbiddenOperation.GRADE_HOLD
            GradeRecordType.DEGRADE -> ForbiddenOperation.GRADE_DEGRADE
            GradeRecordType.UPGRADE -> ForbiddenOperation.GRADE_UPGRADE
            else -> throw GradeException(LoyaltyExceptionCode.GRADE_RECORD_TYPE_NOT_FOUND)
        }
    }

}