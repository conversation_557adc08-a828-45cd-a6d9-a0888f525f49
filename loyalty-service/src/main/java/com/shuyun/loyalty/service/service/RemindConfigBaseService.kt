package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.model.RemindConfig
import com.shuyun.loyalty.service.repository.RemindConfigRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class RemindConfigBaseService {

    @Autowired
    private lateinit var remindConfigRepository: RemindConfigRepository


    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun insert(remindConfig: RemindConfig) {
        remindConfig.versionId = null
        remindConfig.status = EnableStatusEnum.DRAFT
        remindConfig.sort = 1
        remindConfigRepository.save(remindConfig)
        remindConfig.id = remindConfig.versionId
        remindConfigRepository.save(remindConfig)
    }


    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun update(remindConfig: RemindConfig) {
        remindConfig.sort = 1
        remindConfigRepository.save(remindConfig)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    fun delete(remindConfig: RemindConfig) {
        remindConfig.disabled = true
        remindConfigRepository.save(remindConfig)
    }


    fun findParentVersionId(versionId: Long) = remindConfigRepository.findParentVersionId(versionId,false)

    fun findDetailBySubjectVersionId(parentVersionId: Long): List<RemindConfig> {
        return remindConfigRepository.findByParentVersionIdAndDisabled(parentVersionId, false)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun save(remindConfig: RemindConfig) {
        remindConfigRepository.save(remindConfig)
    }
}

