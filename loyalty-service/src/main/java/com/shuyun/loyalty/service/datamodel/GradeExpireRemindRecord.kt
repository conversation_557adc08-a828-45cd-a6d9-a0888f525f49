package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne
import javax.persistence.Table

@Table(name = "data.loyalty.member.hierarchy.GradeExpireRemindRecord{*}")
@DataServiceModel
@FqnVariableModel
class GradeExpireRemindRecord : BaseDataModel() {

    @Column
    var planId: Long? = null

    @Column
    var gradeHierarchyId: Long? = null

    @Column
    var gradeHierarchyName: String? = null

    @Column
    var memberId: String? = null

    @Column
    var currentGradeId: Long? = null

    @Column
    var currentGradeName: String? = null

    @Column
    var currentEffectDate: ZonedDateTime? = null

    @Column
    var currentOverdueDate: ZonedDateTime? = null

    @Column
    var subjectFqn: String? = null

    @Column
    var desc: String? = null

    @Column
    var created: ZonedDateTime? = null

    @Column
    var remindId: Long? = null

    @Column
    var name: String? = null

    @ManyToOne
    @JoinColumn(name = "memberId")
    var member: Any? = null

}





