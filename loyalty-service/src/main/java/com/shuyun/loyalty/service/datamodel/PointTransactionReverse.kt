package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import javax.persistence.Column
import javax.persistence.Table

/**
 * 积分事物正向操作表
 */
@DataServiceModel
@FqnVariableModel
@Table(name = "data.loyalty.member.account.PointTransactionReverse{*}")
@Suppress("unused")
open class PointTransactionReverse :BasePointTransaction() {
    @Column
    var tradeId: String? = null
}