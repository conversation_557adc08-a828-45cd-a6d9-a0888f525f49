package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne
import javax.persistence.Table

@DataServiceModel
@FqnVariableModel
@Table(name = "data.loyalty.member.account.PointExpireRemindRecord{*}")
open class PointExpireRemindRecord: BaseMemberPoint() {

    @Column
    var effectiveDate: ZonedDateTime? = null

    @Column
    var overdueDate: ZonedDateTime? = null

    @Column
    var subjectId: Long? = null

    @Column
    var name: String? = null

    @Column
    var desc: String? = null

    @Column
    var pointPlanName: String? = null

    @Column
    var remindId: Long? = null

    @ManyToOne
    @JoinColumn(name = "memberId")
    var member: Any? = null

}