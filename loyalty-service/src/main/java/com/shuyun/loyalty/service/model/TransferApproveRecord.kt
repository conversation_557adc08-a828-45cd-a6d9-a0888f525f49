package com.shuyun.loyalty.service.model

import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.datamodel.BaseDataModel
import com.shuyun.loyalty.service.repository.TransferApproveRecordRepository
import com.shuyun.pip.ApplicationContextHolder
import io.swagger.v3.oas.annotations.media.Schema
import java.time.LocalDateTime
import javax.persistence.Column
import javax.persistence.Table

@Schema(title = "变更审批")
@Table(name = "data.loyalty.manager.transferApproveRecord")
@DataServiceModel
data class TransferApproveRecord(
    @Schema(title = "变更审批id", type = "String")
    @Column
    var transferApproveId: String? = null,

    @Schema(title = "审批用户id", type = "String")
    @Column
    var approveUserId: String? = null,

    @Schema(title = "审批时间", type = "DateTime")
    @Column
    var approveTime: LocalDateTime? = null
): BaseDataModel() {
    fun save() { ApplicationContextHolder.getBean(TransferApproveRecordRepository::class.java).save(this) }
}