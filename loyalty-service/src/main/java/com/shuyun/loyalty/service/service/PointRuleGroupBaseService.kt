package com.shuyun.loyalty.service.service

import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.service.calculate.params.CalcParams
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.loyalty.service.repository.PointRuleGroupRepository
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal
import java.util.*
import javax.persistence.criteria.Path
import javax.persistence.criteria.Predicate
import kotlin.jvm.optionals.getOrNull


@Service
class PointRuleGroupBaseService {

    private val logger = LogManager.getLogger(PointRuleGroupBaseService::class.java)

    @Autowired
    private lateinit var pointRuleGroupRepository: PointRuleGroupRepository


    fun update(pointRuleGroup: PointRuleGroup) {
        pointRuleGroupRepository.save(pointRuleGroup)
    }

    fun findById(id: Long): PointRuleGroup? {
        return pointRuleGroupRepository.findById(id).getOrNull()
    }

    fun findByIdAndDisabled(id: Long): Optional<PointRuleGroup> {
        val list= pointRuleGroupRepository.findByIdAndDisabled(id,false)
        logger.trace("查询积分规则组 查询参数 groupId: {}  查询结果: {}",{id},{JsonUtils.toJson(list)})
        return list
    }


    fun findPointRuleGroup(planId: Long, pointAccountTypeId: Long, eventTypeId: Long, scoreType: EventOperationEnum, startTime: Date): List<PointRuleGroup> {
        return pointRuleGroupRepository.findGroup(planId, pointAccountTypeId, eventTypeId, scoreType, startTime)
    }

    fun findPointRuleGroup(planId: Long, pointAccountTypeId: Long, eventTypeId: Long, scoreType: EventOperationEnum): List<PointRuleGroup> {
        return pointRuleGroupRepository.findGroup(planId, pointAccountTypeId, eventTypeId, scoreType)
    }

    fun findByIds(ids: List<Long>): List<PointRuleGroup> {
        if (ids.isEmpty()) {
            return emptyList()
        }
        val list = pointRuleGroupRepository.findAllById(ids)
        logger.trace("查询积分规则组 查询参数 groupIds: {}  查询结果: {}",{ids},{JsonUtils.toJson(list)})
        return list
    }



    fun countByGroupName(planId:Long,pointAccountTypeId:Long,id:Long,groupName:String):Long{
        val count = pointRuleGroupRepository.countGroupName(planId,pointAccountTypeId,id,groupName)
        logger.trace("修改时，判断积分规则组数是否存在(大于0为存在) 查询参数 planId: {}, pointAccountTypeId: {}, groupId: {}, " +
                "groupName: {}  查询结果: {}" , planId,pointAccountTypeId,id,groupName,count)
        return count
    }

    fun countByGroupName(planId:Long,pointAccountTypeId:Long,groupName:String):Long{
        val count = pointRuleGroupRepository.countGroupName(planId,pointAccountTypeId,groupName)
        logger.trace("新建时，判断积分规则组数是否存在(大于0为存在) 查询参数 planId: {}," +
                "pointAccountTypeId: {}, groupName: {}   查询结果: {}",planId,pointAccountTypeId,groupName,count)
        return count
    }



    fun findPointRuleGroup(params: CalcParams): List<PointRuleGroup>{
        val list = pointRuleGroupRepository.findAll { root, query, cb ->
            val pCreateTime: Path<EventOperationEnum> = root.get("createTime")
            val ruleGroup = PointRuleGroup()
            //获取查询条件
            val list = ruleGroup.findRuleGroupPredicate(params, root, cb)
            query.where(cb.and(*list.toArray(arrayOfNulls<Predicate>(list.size))))
            query.orderBy(cb.desc(pCreateTime))
            query.groupRestriction
        }
        return list
    }


    @Transactional
    fun addUsedPoint(point: BigDecimal, ruleGroupId: Long) {
        if (Property.getSysOrEnv("loyalty.rule.group.used.point.disabled", false)) {
            return
        }
        pointRuleGroupRepository.updateUsedPoint(point, ruleGroupId)
    }

}