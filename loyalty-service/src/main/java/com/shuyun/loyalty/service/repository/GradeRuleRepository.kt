package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.model.GradeRule
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface GradeRuleRepository : JpaRepository<GradeRule, Long> {

    fun findByIdAndDisabled(id: Long, disabled: Boolean): Optional<GradeRule>

    fun findByGroupId(id: Long): List<GradeRule>

    fun save(gradeRule: GradeRule)

    fun deleteByGroupId(group: Long)
}