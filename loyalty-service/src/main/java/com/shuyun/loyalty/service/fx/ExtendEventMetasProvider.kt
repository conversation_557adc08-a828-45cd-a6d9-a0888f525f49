package com.shuyun.loyalty.service.fx

import com.shuyun.fx.dtsv.EventStreamMetas
import com.shuyun.loyalty.service.exception.LPException
import com.shuyun.loyalty.service.infrastructure.eventStreamMeta.LocalEventMetasRepository
import com.shuyun.loyalty.service.repository.EventStreamMetasRepository
import com.shuyun.loyalty.service.service.LoyaltyPrograms
import com.shuyun.loyalty.service.util.PropertyUtils
import com.shuyun.pip.frameworkext.filter.VisitTenantInfoHolder
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Service
class ExtendEventMetasProvider : IExtendEventMetasProvider {

    @Autowired
    private lateinit var eventStreamMetasRepository: EventStreamMetasRepository

    @Autowired
    private lateinit var localEventMetasRepository: LocalEventMetasRepository

    /** 查询默认及租户定义的事件流 */
    override fun findAll(): List<EventStreamMetas> {
        return eventStreamMetasRepository.findAll { root, _, cb ->
            cb.and(root.get<String>("tenantId").`in`("default", VisitTenantInfoHolder.getTenantId()))
        }
    }

    override fun findByFqn(fqn: String): Optional<EventStreamMetas> {
        if (PropertyUtils.getEventStreamMetasLocal()) {
            return Optional.ofNullable(localEventMetasRepository.findById(fqn))
        }
        return findById(fqn).map { it as EventStreamMetas } ?: Optional.ofNullable(LoyaltyPrograms.findEsMetas(fqn))
    }

    override fun getOne(fqn: String): EventStreamMetas {
        val opt = findByFqn(fqn)
        if (opt.isEmpty) {
            throw LPException("事件流元数据不存在 fqn: $fqn")
        }
        return opt.get()
    }

    @Transactional
    override fun saveOrUpdate(metas: EventStreamMetas): EventStreamMetas {
        return findById(metas.fqn).map {
            it.update(metas)
        }.orElseGet {
            val trans = com.shuyun.loyalty.service.model.EventStreamMetas().update(metas)
            trans.tenantId = VisitTenantInfoHolder.getTenantId()
            eventStreamMetasRepository.save(trans)
        }
    }

    private fun findById(fqn: String): Optional<com.shuyun.loyalty.service.model.EventStreamMetas> {
        return eventStreamMetasRepository.findOne { root, _, cb ->
            cb.and(cb.equal(root.get<String>("fqn"), fqn), root.get<String>("tenantId").`in`("default", VisitTenantInfoHolder.getTenantId()))
        }
    }
}