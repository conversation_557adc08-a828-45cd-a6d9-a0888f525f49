package com.shuyun.loyalty.service.transfer.points

import com.pip.shuyun.pool.transaction.DmTransaction
import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.api.util.Uuid
import org.apache.logging.log4j.LogManager
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.ZonedDateTime

@Service
class MemberPointExpireTransfer: MemberPointBaseTransfer() {

    private val logger = LogManager.getLogger(MemberPointExpireTransfer::class.java)



    @DmTransaction
    fun expire(lp: LoyaltyPoints) {

    }


    fun transfer(lp: LoyaltyPoints) {
        val vsId = lp.attr.businessId ?: return
        val vs = validService.findById(vsId, lp.hierarchy.id) ?: return

        val recordId = Uuid.uuid
        val record = initPointRecord(
            lp, recordId, FSMPointEvent.EXPIRE,
            points = lp.afterPoints,
            changePoints = -lp.afterPoints,
            totalPoints = lp.afterTotalPoints,
            pcStatus = PCStatus.EXPIRE,
            effectiveDate = vs.effectiveDate,
            overdueDate = vs.overdueDate
        )
        val item = initRecordItem(
            lp,
            Uuid.uuid,
            recordId,
            -lp.afterPoints,
            FSMPointEvent.EXPIRE,
            parentBackId = vs.id,
            effectiveDate = vs.effectiveDate,
            overdueDate = vs.overdueDate,
        )

        vs.delete(lp.hierarchy.id)

        val gss = gainStatementService.findByIds(listOf(vs.gainStatementId), lp.hierarchy.id)
        gss.forEach {
            it.status = FSMPointEvent.EXPIRE
            it.modified = ZonedDateTime.now()
            it.saveOrUpdate(lp.hierarchy.id)
        }
        if (vs.point <= BigDecimal.ZERO) {
            return
        }
        batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), listOf(item), listOf())
        return
    }
}