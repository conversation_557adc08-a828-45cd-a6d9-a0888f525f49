package com.shuyun.loyalty.service.model

import io.swagger.v3.oas.annotations.media.Schema
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.io.Serializable
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.Entity
import javax.persistence.Id
import javax.persistence.Table

@Entity
@Schema(title = "积分过期时序表")
@Table(name = "point_expire_sequence")
class PointExpireSequence : Serializable {
    @Id
    @Schema(title = "积分账号ID", type = "Long")
    var id: Long? = null

    @Schema(title = "最后一次执行时间", type = "Date")
    @Column(name = "last_exec_time")
    var lastExecTime: ZonedDateTime? = null

    @Schema(title = "创建时间", type = "Date")
    @CreationTimestamp
    var created: ZonedDateTime? = null

    @Schema(title = "修改时间", type = "Date")
    @UpdateTimestamp
    var modified: ZonedDateTime? = null
}

