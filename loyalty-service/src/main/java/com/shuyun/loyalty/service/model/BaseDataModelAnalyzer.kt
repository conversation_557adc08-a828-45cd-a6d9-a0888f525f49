package com.shuyun.loyalty.service.model

import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.pip.component.ClassFinder
import com.shuyun.pip.component.json.JsonUtils
import org.apache.commons.lang3.StringUtils
import org.apache.logging.log4j.LogManager
import java.lang.reflect.Field
import javax.persistence.*

class BaseDataModelAnalyzer<T>(private val clazz: Class<T>) {

    fun getFields(): String {
        return getModel(clazz).fields!!
    }

    fun getManyToOneFields(): String {
        return getModel(clazz).manyToOneFields!!
    }

    fun getIdField(): String {
        return getModel(clazz).idField!!
    }


    fun getManyToOneFieldForeignKey(fieldName: String): String? {
        return getModel(clazz).manyToOneFieldForeignKeys!![fieldName]
    }

    companion object {
        private val log = LogManager.getLogger(BaseDataModelAnalyzer::class.java)
        private var jpaMap = HashMap<String, BaseDataModelInfo>()

        init {
            init()
        }

        private fun getFields(clazz: Class<*>): String {
            return StringUtils.join(
                    getDeclaredFieldList(clazz).filter {
                        val column = it.getDeclaredAnnotation(Column::class.java)
                        column is Column
                    }.map {
                        it.name
                    }, ",")
        }

        private fun getManyToOneFields(clazz: Class<*>): String {
            return StringUtils.join(
                    getDeclaredFieldList(clazz).filter {
                        val manyToOne = it.getDeclaredAnnotation(ManyToOne::class.java)
                        val oneToOne = it.getDeclaredAnnotation(OneToOne::class.java)
                        manyToOne is ManyToOne || oneToOne is OneToOne
                    }.map {
                        it.name
                    }, ",")
        }

        private fun getIdField(clazz: Class<*>): String? {
            return try {
                getDeclaredFieldList(clazz).filter {
                    val column = it.getDeclaredAnnotation(Id::class.java)
                    column is Id
                }.map {
                    it.name
                }.firstOrNull()
            } catch (e: Exception) {
                log.warn("get field not found schema, ${clazz.name}", e)
                null
            }
        }

        private fun getManyToOneFieldForeignKeys(clazz: Class<*>): Map<String, String> {
            return getDeclaredFieldList(clazz).asSequence().filter {
                it.getDeclaredAnnotation(JoinColumn::class.java) is JoinColumn
            }.map {
                it.name to it.getDeclaredAnnotation(JoinColumn::class.java)!!.name
            }.toList().toMap()
        }

        private fun getDeclaredFieldList(clazz: Class<*>): List<Field> {
            var currentSuperClass: Class<*>? = clazz
            val fieldList = ArrayList<Field>()
            while (currentSuperClass != null) {
                fieldList.addAll(currentSuperClass.declaredFields)
                currentSuperClass = currentSuperClass.superclass
            }
            return fieldList
        }

        fun init(){
            ClassFinder.getInstance().findClassByAnnotation<Any, DataServiceModel>(DataServiceModel::class.java).forEach {
                addModel(it)
            }
            log.info("prepare model schema: "+JsonUtils.toJsonPretty(jpaMap))
        }

        private fun addModel(clazz: Class<*>){
            jpaMap[clazz.name] = BaseDataModelInfo().apply {
                fields = getFields(clazz)
                manyToOneFields = getManyToOneFields(clazz)
                idField = getIdField(clazz)
                //allFields = getAllFields(clazz)
                manyToOneFieldForeignKeys = getManyToOneFieldForeignKeys(clazz)
            }
        }

        fun getModel(clazz: Class<*>) = jpaMap[clazz.name]!!
    }
}

class BaseDataModelInfo{
    var fields : String? = null
    var manyToOneFields : String? = null
    var idField : String? = null
    var manyToOneFieldForeignKeys : Map<String,String>? = null
}