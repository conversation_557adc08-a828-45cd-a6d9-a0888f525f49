package com.shuyun.loyalty.service.transfer.points

import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.pip.shuyun.pool.transaction.TransactionInfoHolder
import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.entity.enums.FromTypeEnum
import com.shuyun.loyalty.entity.enums.NegativeStrategyEnum
import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Limits
import com.shuyun.loyalty.service.calculate.MemberPointSendLimitService
import com.shuyun.loyalty.service.datamodel.*
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.exception.PointException
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.fx.ExtendFxVariableProvider
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.meta.PointSendLimitType
import com.shuyun.loyalty.service.meta.RuleSortTypeEnum
import com.shuyun.loyalty.service.model.*
import com.shuyun.loyalty.service.service.*
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.ConstantValue.LONG_TERM_OVERDUE_DATE
import com.shuyun.loyalty.service.util.ExpressionIdentUtil
import com.shuyun.loyalty.service.util.RecordDescUtils
import com.shuyun.loyalty.service.util.sendNotify
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.i18n.LocaleI18nContextHolder
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.*

@Component
abstract class MemberPointBaseTransfer {

    @Autowired
    protected lateinit var negativeStatementService: NegativeStatementService

    @Autowired
    protected lateinit var frozenPointService: MemberFrozenPointService

    @Autowired
    protected lateinit var frozenStatementService: MemberPointFrozenStatementService

    @Autowired
    protected lateinit var validService: MemberPointValidStatementService

    @Autowired
    protected lateinit var gainStatementService: MemberPointGainStatementService

    @Autowired
    protected lateinit var segmentService: PointSegmentService

    @Autowired
    protected lateinit var recordService: MemberPointRecordService

    @Autowired
    protected lateinit var itemService: PointRecordItemService

    @Autowired
    protected lateinit var specialListService: SpecialListService

    @Autowired
    protected lateinit var pointLimitService: PointLimitService

    @Autowired
    protected lateinit var memberPointSendLimitService: MemberPointSendLimitService

    @Autowired
    protected lateinit var pointRuleGroupService: PointRuleGroupBaseService

    @Autowired
    protected lateinit var pointSendRuleService: PointSendRuleBaseService

    @Autowired
    protected lateinit var memberPointLogService: MemberPointLogService


    companion object {
        const val SUBJECT = "subject"
        private val logger = LogManager.getLogger(MemberPointBaseTransfer::class.java)
    }

    // 初始化上限拦截记录
    private fun initLimitRecord(
        lp: LoyaltyPoints,
        recordType: FSMPointEvent,
        beforePoints: BigDecimal,
        afterPoints: BigDecimal,
        totalPoints: BigDecimal, sendLimitRule: Any?): MemberLimitPointRecord {
        val maxPoint = 9999999999.99.toBigDecimal()
        val limitRecord = MemberLimitPointRecord()
        limitRecord.memberId = lp.member.memberId
        limitRecord.planId = lp.plan.id
        limitRecord.planName = lp.plan.name
        limitRecord.pointPlanId = lp.hierarchy.id
        limitRecord.pointPlanName = lp.hierarchy.name
        limitRecord.point = if (afterPoints > maxPoint) maxPoint else afterPoints
        limitRecord.changePoint = if (beforePoints > maxPoint) maxPoint else beforePoints
        limitRecord.totalPoint = if (totalPoints > maxPoint) maxPoint else totalPoints
        limitRecord.key = lp.attr.uniqueId
        limitRecord.traceId = lp.attr.traceId
        limitRecord.subjectFqn = lp.subject.dataType
        limitRecord.memberPointId = lp.member.id
        limitRecord.recordType = recordType
        limitRecord.shopId = lp.attr.shopId
        limitRecord.KZZD1 = lp.attr.kzzd1
        limitRecord.KZZD2 = lp.attr.kzzd2
        limitRecord.KZZD3 = lp.attr.kzzd3
        limitRecord.actionName = lp.attr.actionName
        limitRecord.ruleId = lp.sendRule?.id
        limitRecord.ruleName = lp.sendRule?.name
        limitRecord.ruleGroup = lp.pointRuleGroup?.name
        limitRecord.effectiveDate = lp.attr.effectiveDate
        limitRecord.overdueDate = lp.attr.overdueDate
        limitRecord.eventTypeName = lp.eventType?.name
        limitRecord.status = if (recordType == FSMPointEvent.DELAY_SEND) PCStatus.DELAY else PCStatus.VALID
        limitRecord.channel = lp.attr.channel
        limitRecord.changeMode = lp.attr.changeMode
        limitRecord.desc = lp.attr.desc
        limitRecord.created = lp.date.plus(lp.counter.get().toLong(), ChronoUnit.MILLIS)
        limitRecord.modified = limitRecord.created
        limitRecord.sendLimitRule = sendLimitRule?.let { JsonUtils.toJson(sendLimitRule) }
        return limitRecord
    }

    // 初始化积分获取记录
    fun initGainPoints(
        lp: LoyaltyPoints,
        id: String,
        status: FSMPointEvent,
        points: BigDecimal,
        effectiveDate: ZonedDateTime,
        overdueDate: ZonedDateTime
    ): MemberPointGainStatement {
        val gs = MemberPointGainStatement()
        gs.id = id
        gs.planId = lp.plan.id
        gs.pointPlanId = lp.hierarchy.id
        gs.memberPointId = lp.member.id
        gs.memberId = lp.member.memberId
        gs.subjectFqn = lp.subject.dataType
        gs.created = lp.date.plus(lp.counter.get().toLong(), ChronoUnit.MILLIS)
        gs.modified = gs.created
        gs.point = points
        gs.status = status
        gs.effectiveDate = effectiveDate
        gs.overdueDate = overdueDate
        gs.traceId = lp.attr.traceId
        gs.eventTypeId = lp.eventType?.id
        gs.ruleId = lp.sendRule?.id
        gs.ruleGroupId = lp.pointRuleGroup?.id
        gs.occurrenceTs = lp.event?.occurrenceTs?.toString()
        return gs
    }


    // 初始化变更记录
    fun initPointRecord(
        lp: LoyaltyPoints,
        recordId: String,
        recordType: FSMPointEvent,
        points: BigDecimal,
        changePoints: BigDecimal,
        totalPoints: BigDecimal,
        pcStatus: PCStatus,
        effectiveDate: ZonedDateTime? = null,
        overdueDate: ZonedDateTime? = null,
        ruleId: Long? = null,
        ruleName: String? = null,
        ruleGroupId: Long? = null,
        ruleGroupName: String? = null,
    ): MemberPointRecord {
        val record = MemberPointRecord()
        record.id = recordId
        record.planId = lp.plan.id
        record.planName = lp.plan.name
        record.pointPlanId = lp.hierarchy.id
        record.pointPlanName = lp.hierarchy.name
        record.memberPointId = lp.member.id
        record.memberId = lp.member.memberId
        record.subjectFqn = lp.subject.dataType
        record.traceId = lp.attr.traceId
        record.key = lp.attr.uniqueId
        record.changeMode = lp.attr.changeMode
        record.channel = lp.attr.channel
        record.desc = lp.attr.desc
        record.shopId = lp.attr.shopId
        record.KZZD1 = lp.attr.kzzd1
        record.KZZD2 = lp.attr.kzzd2
        record.KZZD3 = lp.attr.kzzd3
        record.actionId = lp.attr.actionId
        record.actionName = lp.attr.actionName
        record.actionNodeId = lp.attr.actionNodeId
        record.actionNodeName = lp.attr.actionNodeName
        record.ruleId = ruleId ?: lp.sendRule?.id ?: lp.deductRule?.id
        record.ruleName = ruleName ?: lp.sendRule?.name ?: lp.deductRule?.name
        record.ruleGroupId = ruleGroupId ?: lp.pointRuleGroup?.id
        record.ruleGroup = ruleGroupName ?: lp.pointRuleGroup?.name
        record.eventTypeName = lp.eventType?.name
        record.operatorId = lp.attr.operatorId
        record.operator = lp.attr.operator
        record.recordType = recordType
        record.created = lp.getDateAndIncr()
        record.modified = ZonedDateTime.now()
        record.effectiveDate = effectiveDate
        record.overdueDate = if (overdueDate?.toLocalDate() == LONG_TERM_OVERDUE_DATE.toLocalDate()) null else overdueDate
        record.point = points
        record.changePoint = changePoints
        record.totalPoint = totalPoints
        record.status = pcStatus
        record.recordDetail = lp.attr.recordDetail
        return record
    }


    // 初始化追溯记录
    fun initRecordItem(
        lp: LoyaltyPoints,
        id: String,
        recordId: String,
        points: BigDecimal,
        status: FSMPointEvent,
        sort: Long = 0L,
        effectiveDate: ZonedDateTime? = null,
        overdueDate: ZonedDateTime? = null,
        backId: String? = null,
        parentBackId: String? = null
    ): PointRecordItem {
        var effectiveDateTime = effectiveDate
        var overdueDateTime = overdueDate
        if (status != FSMPointEvent.SEND
            && status != FSMPointEvent.DELAY_SEND
            && status != FSMPointEvent.EXPIRE
            && status != FSMPointEvent.REVERSE_SEND
            && status != FSMPointEvent.UNFREEZE
            && (status != FSMPointEvent.RECALCULATE && points < BigDecimal.ZERO)
        ) {
            effectiveDateTime = null
            overdueDateTime = null
        }
        val item = PointRecordItem()
        item.id = id
        item.planId = lp.plan.id
        item.pointPlanId = lp.hierarchy.id
        item.memberId = lp.member.memberId
        item.traceId = lp.attr.traceId
        item.point = points
        item.sort = sort
        item.recordId = recordId
        item.status = status.name
        item.effectiveDate = effectiveDateTime
        item.overdueDate = overdueDateTime
        item.backId = backId
        item.parentBackId = parentBackId
        item.created = ZonedDateTime.now()
        return item
    }


    // 初始化有效积分记录
    fun initValidPoints(
        lp: LoyaltyPoints,
        id: String,
        gainStatementId: String,
        points: BigDecimal,
        status: PointStateEnum,
        effectiveDate: ZonedDateTime,
        overdueDateTime: ZonedDateTime,
        data: String? = null
    ): MemberPointValidStatement {
        val vs = MemberPointValidStatement()
        vs.id = id
        vs.gainStatementId = gainStatementId
        vs.point = points
        vs.memberId = lp.member.memberId
        vs.memberPointId = lp.member.id
        vs.pointPlanId = lp.hierarchy.id
        vs.planId = lp.plan.id
        vs.overdueDate = overdueDateTime
        vs.effectiveDate = effectiveDate
        vs.fromStatus = status
        vs.subjectFqn = lp.subject.dataType
        if (!data.isNullOrEmpty()) {
            val map = JsonUtils.parse(data, Map::class.java)
            vs.channel = map["channel"]?.toString()
            vs.shopId = map["shopId"]?.toString()
            vs.kzzd1 = map["kzzd1"]?.toString()
            vs.kzzd2 = map["kzzd2"]?.toString()
            vs.kzzd3 = map["kzzd3"]?.toString()
        } else {
            vs.channel = lp.attr.channel
            vs.shopId = lp.attr.shopId
            vs.kzzd1 = lp.attr.kzzd1
            vs.kzzd2 = lp.attr.kzzd2
            vs.kzzd3 = lp.attr.kzzd3
        }
        vs.created = lp.date.plus(lp.counter.get().toLong(), ChronoUnit.MILLIS)
        vs.modified = vs.created
        return vs
    }


    // 初始化不够扣的积分记录（不用还的积分）
    private fun initBeyondPointRecord(lp: LoyaltyPoints, recordId: String, expectPoints: BigDecimal, actualPoint:BigDecimal, recordType: FSMPointEvent, status: PCStatus): BeyondPointRecord {
        val beyondPointRecord = BeyondPointRecord()
        beyondPointRecord.memberId = lp.member.memberId
        beyondPointRecord.planId = lp.plan.id
        beyondPointRecord.planName = lp.plan.name
        beyondPointRecord.pointPlanId = lp.hierarchy.id
        beyondPointRecord.pointPlanName = lp.hierarchy.name

        // 不够扣除的积分值
        beyondPointRecord.diffPoint = expectPoints - actualPoint

        // 真实扣除的积分值
        beyondPointRecord.point = actualPoint

        // 预计扣除的积分值
        beyondPointRecord.changePoint = expectPoints

        beyondPointRecord.created = lp.date.plus(lp.counter.get().toLong(), ChronoUnit.MILLIS)
        beyondPointRecord.modified = beyondPointRecord.created
        beyondPointRecord.subjectFqn = lp.subject.dataType
        beyondPointRecord.memberPointId = lp.member.id
        beyondPointRecord.recordType = recordType
        beyondPointRecord.eventTypeName = lp.eventType?.name
        beyondPointRecord.ruleGroup = lp.pointRuleGroup?.name
        beyondPointRecord.ruleId = lp.deductRule?.id
        beyondPointRecord.ruleName = lp.deductRule?.name
        beyondPointRecord.shopId = lp.attr.shopId
        beyondPointRecord.channel = lp.attr.channel
        beyondPointRecord.KZZD1 = lp.attr.kzzd1
        beyondPointRecord.KZZD2 = lp.attr.kzzd2
        beyondPointRecord.KZZD3 = lp.attr.kzzd3
        beyondPointRecord.traceId = lp.attr.traceId
        beyondPointRecord.actionName = lp.attr.actionName
        beyondPointRecord.desc = lp.attr.desc
        beyondPointRecord.key = lp.attr.uniqueId
        beyondPointRecord.changeMode = lp.attr.changeMode
        beyondPointRecord.status = status
        beyondPointRecord.type = "DEDUCT"
        beyondPointRecord.pointRecordId = recordId
        return beyondPointRecord
    }


    // 初始化负积分记录
    fun initNegativePoints(lp: LoyaltyPoints, id: String, recordId: String, point: BigDecimal): MemberPointNegativeStatement {
        val negativeStatement = MemberPointNegativeStatement()
        negativeStatement.id = id
        negativeStatement.memberId = lp.member.memberId
        negativeStatement.memberPointId = lp.member.id
        negativeStatement.planId = lp.plan.id
        negativeStatement.pointPlanId = lp.hierarchy.id
        negativeStatement.point = point
        negativeStatement.created = lp.date.plus(lp.counter.get().toLong(), ChronoUnit.MILLIS)
        negativeStatement.recordId = recordId
        negativeStatement.subjectFqn = lp.subject.dataType
        return negativeStatement
    }


    // 初始化用于反向操作的记录
    fun initTransactionCalc(lp: LoyaltyPoints, recordId: String, txId: String, backId: String, vs: MemberPointValidStatement, sort: Int): PointTransactionCalc {
        val now = lp.date
        val ptc = PointTransactionCalc().apply {
            this.id = Uuid.uuid
            this.businessId = lp.attr.traceId
            this.gainStatementId = vs.gainStatementId
            this.effectiveDate = vs.effectiveDate
            this.overdueDate = vs.overdueDate
            this.point = vs.subPoints.abs()
            this.sourceId = txId
            this.created = now.plus(sort.toLong(), ChronoUnit.MILLIS)
            this.modified = now.plus(sort.toLong(), ChronoUnit.MILLIS)
            this.sort = sort
            this.recordId = recordId
            this.shopId = lp.attr.shopId
            this.backId = backId
            this.ext1 = lp.attr.kzzd1
            this.ext2 = lp.attr.kzzd2
            this.ext3 = lp.attr.kzzd3
        }
        return ptc
    }


    // 初始化用于反向操作的记录
    fun initPointTransactionStatement(txId: String, backId: String, vs: MemberPointValidStatement, sort: Int): PointTransactionStatement {
        val now = ZonedDateTime.now()
        val pts = PointTransactionStatement().apply {
            this.id = Uuid.uuid
            this.gainStatementId = vs.gainStatementId
            this.effectiveDate = vs.effectiveDate
            this.overdueDate = vs.overdueDate
            this.point = vs.subPoints.abs()
            this.pointTransactionId = txId
            this.created = now.plus(sort.toLong(), ChronoUnit.MILLIS)
            this.modified = now.plus(sort.toLong(), ChronoUnit.MILLIS)
            this.sort = sort
            this.shopId = vs.shopId
            this.backId = backId
            this.ext1 = vs.kzzd1
            this.ext2 = vs.kzzd2
            this.ext3 = vs.kzzd3
        }
        return pts
    }

    // 初始化事件冻结积分
    fun initMemberFrozenPoint(lp: LoyaltyPoints, id: String, point: BigDecimal, fromStatus: FSMPointEvent, gainStatementId: String): MemberFrozenPoint {
        val f = MemberFrozenPoint()
        f.id = id
        f.planId = lp.plan.id
        f.pointPlanId = lp.hierarchy.id
        f.subjectFqn = lp.subject.dataType
        f.memberPointId = lp.member.id
        f.memberId = lp.member.memberId
        f.fromStatus = fromStatus
        f.gainStatementId = gainStatementId
        f.point = point
        f.created = lp.date.plus(lp.counter.get().toLong(), ChronoUnit.MILLIS)
        return f
    }


    // 初始化事件冻结明细
    fun initFrozenStatement(
        lp: LoyaltyPoints,
        id: String,
        frozenId: String,
        recordId: String,
        vs: MemberPointValidStatement,
        fromStatus: PointStateEnum,
        fromType: FromTypeEnum? = null
    ): MemberPointFrozenStatement {
        val f = MemberPointFrozenStatement()
        f.id = id
        f.planId = lp.plan.id
        f.pointPlanId = lp.hierarchy.id
        f.memberPointId = lp.member.id
        f.memberId = lp.member.memberId
        f.subjectFqn = lp.subject.dataType
        f.point = vs.subPoints
        f.gainStatementId = vs.gainStatementId
        f.effectiveDate = vs.effectiveDate
        f.overdueDate = vs.overdueDate
        f.fromStatus = fromStatus
        f.fromStatementId = vs.id
        f.frozenId = frozenId
        f.fromType = fromType
        f.created = lp.date.plus(lp.counter.get().toLong(), ChronoUnit.MILLIS)
        f.modified = f.created

        val map = HashMap<String, String?>()
        map["recordId"] = recordId
        vs.channel?.let { map["channel"] = it }
        vs.shopId?.let { map["shopId"] = it }
        vs.kzzd1?.let { map["kzzd1"] = it }
        vs.kzzd2?.let { map["kzzd2"] = it }
        vs.kzzd3?.let { map["kzzd3"] = it }
        f.data = JsonUtils.toJson(map)

        return f
    }


    // 抵消负积分
    fun offset(
        lp: LoyaltyPoints,
        beforeTotalPoints: BigDecimal,
        point: BigDecimal,
        recordId: String,
        status: FSMPointEvent,
        callback: (List<PointRecordItem>, BigDecimal) -> Unit
    ) {
        val items = ArrayList<PointRecordItem>()

        // 如果前剩余积分大于0则不需要检查是否存在负积分
        if (beforeTotalPoints >= BigDecimal.ZERO) {
            callback(items, point)
            return
        }

        val negatives = negativeStatementService.findByMemberPointIdList(lp.member.id, lp.hierarchy.id, limit = 10000)
        val ids = ArrayList<String>()

        var afterPoints = point
        for (negative in negatives) {
            var itemPoint = negative.point
            if (negative.point > afterPoints) {
                negative.point -= afterPoints
                negative.saveOrUpdate()
                itemPoint = afterPoints
                afterPoints = BigDecimal.ZERO
            } else {
                afterPoints -= negative.point
                ids.add(negative.id!!)
            }

            val item = initRecordItem(
                lp, Uuid.uuid, recordId, itemPoint, status,
                parentBackId = negative.id,
                effectiveDate = ZonedDateTime.now(),
                overdueDate = ZonedDateTime.now()
            )

            items.add(item)

            if (afterPoints.compareTo(BigDecimal.ZERO) == 0) {
                break
            }
        }

        negativeStatementService.deleteByIds(ids.toTypedArray(), lp.hierarchy.id)

        callback(items, afterPoints)
    }

    // 优先查询原单关联的有效积分
    // 再次查询其他的
    fun vssFetchFunc(gainStatementId: String): (LoyaltyPoints) -> List<MemberPointValidStatement> {
        // 查找重算关联的所有原单获取记录
        val ids = HashSet<String>()
        val fetch = fun(lp2: LoyaltyPoints): List<MemberPointValidStatement> {
            // 优先查询原单关联的有效积分
            val vss = vssFetch(lp2, gainStatementId)
            ids.add(gainStatementId)
            return vss.ifEmpty { vssFetch(lp2, excludesGsIds = ids.toList()) }
        }
        return fetch
    }


    private fun vssFetch(lp: LoyaltyPoints, gainStatementId: String): List<MemberPointValidStatement> {
        return validService.findByPriorities(
            lp.hierarchy.id,
            lp.member.id,
            lp.hierarchy.deductionPriorities!!.inject(lp.attr),
            lp.date,
            gainStatementId = gainStatementId,
            statusIn = listOf(PointStateEnum.START, PointStateEnum.VALID, PointStateEnum.FURTHER_VALID),
            limit = 100
        )
    }

    private fun vssFetch(lp: LoyaltyPoints, excludesGsIds: List<String>): List<MemberPointValidStatement> {
        return validService.findByPriorities(
            lp.hierarchy.id,
            lp.member.id,
            lp.hierarchy.deductionPriorities!!.inject(lp.attr),
            lp.date,
            excludesGsIds = excludesGsIds.toSet(),
            statusIn = listOf(PointStateEnum.START, PointStateEnum.VALID, PointStateEnum.FURTHER_VALID),
            limit = 100
        )
    }


    fun vssFetch(lp: LoyaltyPoints): List<MemberPointValidStatement> {
        return validService.findByPriorities(
            lp.hierarchy.id,
            lp.member.id,
            lp.hierarchy.deductionPriorities!!.inject(lp.attr),
            lp.date,
            statusIn = listOf(PointStateEnum.START, PointStateEnum.VALID, PointStateEnum.FURTHER_VALID),
            limit = 100
        )
    }

    fun batchUpdateRecordAndSegment(hierarchyId: Long, records: List<MemberPointRecord>, items: List<PointRecordItem>, segments: List<MemberPointSegment>) {
        // 保存扣减记录
        recordService.batchInsert(records, hierarchyId.toString())
        itemService.batchInsert(items, hierarchyId.toString())

        // 更新积分块
        segments.sortedBy { it.id }.forEach {
            when {
                it.initial -> segmentService.save(it)
                it.updated && it.point <= BigDecimal.ZERO -> {
                    if (it.expireDate != null && !it.expireDate!!.isEqual(LONG_TERM_OVERDUE_DATE.shDate())) {
                        segmentService.delete(it.id!!, replacePattern = hierarchyId.toString())
                    } else {
                        segmentService.saveOrUpdate(it, replacePattern = hierarchyId.toString())
                    }
                }
                it.updated && it.point > BigDecimal.ZERO -> {
                    segmentService.saveOrUpdate(it, replacePattern = hierarchyId.toString())
                }
            }
        }
        // 向外部通知变更消息
        TransactionInfoHolder.afterCommit { records.sendNotify() }
    }


    // 特殊名单检测
    fun checkSpecialList(lp: LoyaltyPoints, forbiddenOperation: ForbiddenOperation, forbiddenPort: ForbiddenPort) {

        val inCheckList = specialListService.check(
            lp.subject.dataType,
            lp.member.memberId,
            forbiddenOperation,
            forbiddenPort,
            lp.forbiddenConfigs
        )
        if (inCheckList) {
            // 积分冻结被特殊名单拦截
            throw PointException(LoyaltyExceptionCode.BLACKLIST_EXIST, LocaleI18nContextHolder.getMessage(forbiddenOperation.name))
        }
    }

    fun checkSpecial(lp: LoyaltyPoints,forbiddenOperation: ForbiddenOperation,forbiddenPort: ForbiddenPort): Boolean {
        return specialListService.check(
            lp.subject.dataType,
            lp.member.memberId,
            forbiddenOperation,
            forbiddenPort,
            lp.forbiddenConfigs
        )
    }

    // 积分发放上限过滤
    fun filterLimit(
        lp: LoyaltyPoints,
        p: BigDecimal,
        recordType: FSMPointEvent,
        sendEventId: Long? = null,
        sendRuleGroupId: Long? = null,
        isReissue: Boolean = false
    ) {
        if (p <= BigDecimal.ZERO) {
            return
        }
        val isDelay = lp.attr.effectiveDate!!.isAfter(lp.date)
        var point = p
        val isEvent = lp.type == LoyaltyRequestType.EVENT
        if (isEvent) {
            val record = MemberPointRecord().apply {
                this.planId = lp.plan.id
                this.pointPlanId = lp.hierarchy.id
                this.memberPointId = lp.member.id
                this.memberId = lp.member.memberId
                this.subjectFqn = lp.subject.dataType
                this.recordType = recordType
                this.point = point
            }
            val pointAccountType =
                PointAccountType().apply { this.sendLimitRule = lp.hierarchy.limitRules?.let { JsonUtils.toJson(it) } }
            val enableSendLimit = lp.plan.enableSendLimit ?: false
            memberPointSendLimitService.filter(
                record,
                pointAccountType,
                sendEventId,
                sendRuleGroupId,
                enableSendLimit = enableSendLimit,
                isRecalculate = recordType == FSMPointEvent.RECALCULATE,
                isReissue = isReissue,
                onlyFilterCount = isDelay
            )
            if (record.sendLimitRuleCalcResult != null) {
                val cr = record.sendLimitRuleCalcResult!!
                if (cr.result) {
                    val de = cr.sendLimitRuleResultDetail.lastOrNull()
                    lp.hierarchy.sendLimitResults = listOf(PointLimitResult(
                        beforeTotalPoints = lp.member.point,
                        beforePoints = point,
                        afterPoints = cr.realPoint,
                        limitRuleType = de?.limitRuleType?.let { PointLimitRuleType.valueOf(it) },
                        sendLimitCalc = de?.sendLimitCalc,
                    ))
                    point = cr.realPoint
                }
            }

            // 检查全局规则组发放上限
            if (lp.pointRuleGroup!!.limit) {
                val ruleGroup = pointRuleGroupService.findByIds(listOf(lp.pointRuleGroup!!.id))[0]
                val usedPoint = if (ruleGroup.usedPoint == null) BigDecimal.ZERO else BigDecimal(ruleGroup.usedPoint.toString())
                if (usedPoint.add(lp.afterPoints) > BigDecimal(ruleGroup.limitPoint.toString())) {
                    val desc = "超出规则组发放积分限制,规则组(${ruleGroup.id}:${ruleGroup.groupName})发放总积分:${ruleGroup.limitPoint},涉及发放规则:${JsonUtils.toJson(lp.sendRule!!.id)}"
                    logger.debug(desc)
                    val status = if (lp.attr.effectiveDate!!.isAfter(lp.date)) PCStatus.DELAY else PCStatus.VALID
                    MemberLimitPointRecordMapper.buildMemberLimitPointRecord(lp, lp.afterPoints, BigDecimal.ZERO, lp.member.point, desc, recordType, status).save()
                    lp.afterPoints = BigDecimal.ZERO
                    return
                }
            }
        }

        // 不再累计次数
        val flag = (recordType == FSMPointEvent.RECALCULATE || (recordType == FSMPointEvent.TIMER && sendEventId != null))

        var filterResults: List<PointLimitResult> = emptyList()
        if (!isDelay || recordType == FSMPointEvent.TIMER) {
            // 积分上限过滤
            filterResults = pointLimitService.filter(
                lp.member.memberPoint!!,
                point,
                lp.hierarchy.unit,
                lp.plan.enableSendLimit,
                lp.hierarchy.limitRules,
                lp.hierarchy.singleTopLimit,
                lp.hierarchy.topLimit,
                flag
            )
        }
        if (filterResults.isNotEmpty()) {
            lp.hierarchy.sendLimitResults = filterResults + (lp.hierarchy.sendLimitResults ?: emptyList())
            lp.afterPoints = filterResults.minOf { it.afterPoints }
        } else {
            lp.afterPoints = point
        }

        // 更新累计事件和规则组积分
        if (lp.type == LoyaltyRequestType.EVENT && lp.afterPoints > ConstantValue.defaultZeroLine) {
            if (lp.plan.enableSendLimit == true) {
                val record = MemberPointRecord().apply {
                    this.planId = lp.plan.id
                    this.pointPlanId = lp.hierarchy.id
                    this.memberId = lp.member.memberId
                    this.memberPointId = lp.member.id
                    this.subjectFqn = lp.subject.dataType
                    this.recordType = if (isDelay) FSMPointEvent.DELAY_SEND else FSMPointEvent.SEND
                    this.point = lp.afterPoints
                }
                memberPointSendLimitService.cumulative(record, sendEventId, sendRuleGroupId, flag)
            }
            sendRuleGroupId?.let {
                // 更新规则组已发积分
                pointRuleGroupService.addUsedPoint(lp.afterPoints, sendRuleGroupId)
            }
        }
        lp.pointValue = p
    }

    // 释放积分上限
    fun releaseLimit(lp: LoyaltyPoints, oldGSSList: List<MemberPointGainStatement>, newGSSList: List<MemberPointGainStatement>, eventTypeId: Long? = null) {
        val oldGSS = oldGSSList.filter { it.status != FSMPointEvent.DELAY_SEND }
        val newGSS = newGSSList.filter { it.status != FSMPointEvent.DELAY_SEND }
        val beforePoints = oldGSS.sumOf { it.point }
        val afterPoints = newGSS.sumOf { it.point }
        val groupIds = newGSS.map { it.ruleGroupId }.toSet() + oldGSS.map { it.ruleGroupId }.toSet()
        val ruleIds = newGSS.map { it.ruleId }.toSet() + oldGSS.map { it.ruleId }.toSet()

        // 查询单日已发上限
        val limitCalcService = ApplicationContextHolder.getBean(MemberPointSendLimitCalcService::class.java)
        var limits = limitCalcService.findByMemberId(lp.hierarchy.id, lp.member.memberId)
        if (lp.type != LoyaltyRequestType.EVENT) {
            limits = limits.filter { it.type == PointSendLimitType.MEMBER }
        }
        fun updateLimit(limit: MemberPointSendLimitCalc, before: BigDecimal, after: BigDecimal) {
            if (after >= before) return
            val newPoints = before - after
            val baseCount = if(limit.type == PointSendLimitType.MEMBER) oldGSS.size else 1
            val count = if (after.compareTo(BigDecimal.ZERO) == 0) baseCount else 0
            limit.value = Limits.decrementAndGet(limit.value!!, newPoints, count, oldGSS.first().created).toString()
            limit.modified = lp.date
        }
        for (limit in limits) {
            when (limit.type) {
                PointSendLimitType.RULE_GROUP -> {
                    for (groupId in groupIds) {
                        if (groupId == null) continue
                        if (limit.refIdType == "${groupId}|RULE_GROUP") {
                            val before = oldGSS.filter { it.ruleGroupId == groupId }.sumOf { it.point }
                            val after = newGSS.filter { it.ruleGroupId == groupId }.sumOf { it.point }
                            updateLimit(limit, before, after)
                            if (after < before) {
                                // 释放规则组上限
                                val p = before - after
                                pointRuleGroupService.addUsedPoint(p, groupId)
                            }
                        }
                    }
                }
                PointSendLimitType.RULE -> {
                    for (ruleId in ruleIds) {
                        if (ruleId == null) continue
                        if (limit.refIdType == "${ruleId}|RULE") {
                            val before = oldGSS.sumOf { if (it.ruleId == ruleId) it.point else BigDecimal.ZERO }
                            val after = newGSS.sumOf { if (it.ruleId == ruleId) it.point else BigDecimal.ZERO }
                            updateLimit(limit, before, after)
                        }
                    }
                }
                PointSendLimitType.EVENT -> {
                    if (eventTypeId != null && limit.refIdType == "${eventTypeId}|EVENT") {
                        updateLimit(limit, beforePoints, afterPoints)
                    }
                }
                PointSendLimitType.MEMBER -> {
                    updateLimit(limit, beforePoints, afterPoints)
                }
                else -> {}
            }
            if (limit.modified == lp.date) {
                limitCalcService.saveOrUpdate(limit, lp.hierarchy.id.toString())
            }
        }
    }

    // 从有效积分列表里(vssFetch)扣减p积分
    // vssFetch 从数据库中获取有效积分，处理后会从数据库中删除或者更新
    // pointSegments 处理后会直接更新元素
    fun sequentiallyDeduct(
        lp: LoyaltyPoints,
        p: BigDecimal,
        vssFetch: (LoyaltyPoints) -> List<MemberPointValidStatement>,
        pointSegments: List<MemberPointSegment>,
        negativeStrategy: NegativeStrategyEnum,
        recordId: String,
        recordType: FSMPointEvent,
        gainStatus: FSMPointEvent,
        itemStatus: FSMPointEvent,
        pcStatus: PCStatus = PCStatus.VALID,
        primaryGSId: String? = null,
        cb1: ((BigDecimal) -> Unit)? = null, //不够的
        callback: ((MemberPointValidStatement, PointRecordItem) -> Unit)? = null,
    ): List<PointRecordItem> {
        if (p < BigDecimal.ZERO) throw IllegalArgumentException("扣减积分必须大于等于0 p: $p")
        val segments = pointSegments.sortedBy { it.expireDate!! }
        val items = ArrayList<PointRecordItem>()
        if (lp.member.point <= BigDecimal.ZERO) {
            // 扣减之前的剩余积分小于等于0
            // 直接清空有效积分, 存在就当脏数据清空掉
            val validStatements = vssFetch(lp)
            val ids = validStatements.map { it.id!! }
            val gainStatementIds = validStatements.map { it.gainStatementId }
            deleteBatchValidPoint(lp.hierarchy.id, ids, primaryGSId, gainStatementIds, FSMPointEvent.DEDUCT)

            if (negativeStrategy == NegativeStrategyEnum.TO_ZERO) {
                // 最多只允许扣减到0分, 预计扣减p分，实际扣除0分
                val beyondPointRecord = initBeyondPointRecord(lp, recordId, p, BigDecimal.ZERO, recordType, pcStatus)
                beyondPointRecord.save(lp.hierarchy.id)
            }

            if (negativeStrategy == NegativeStrategyEnum.TO_NEGATIVE) {
                // 生成追溯记录
                // 允许扣减到负数
                // 负积分冻结明细, 冻结仅保存负积分记录
                val negativeId = Uuid.uuid
                val negative = initNegativePoints(lp, negativeId, recordId, p)
                val negativeItem = initRecordItem(lp, Uuid.uuid, recordId, -p, FSMPointEvent.DEDUCT, backId = negativeId)
                items.add(negativeItem)

                // 负积分原地保存
                negative.save()
            }
            return items
        }

        // 扣减之前的积分大于0
        var deductPoints = p // 待扣减的积分
        while (deductPoints > BigDecimal.ZERO) {
            val ids = ArrayList<String>() // 用于保存待删除的有效积分id
            val gainStatementIds = ArrayList<String>() // 用于保存待更新的积分获取明细id
            val validStatements = vssFetch(lp) // 待扣减的有效积分列表
            if (validStatements.isEmpty()) {
                break
            }
            for (vs in validStatements) {
                var itemPoint = vs.point
                if (vs.point > deductPoints) {
                    itemPoint = deductPoints
                    vs.point -= deductPoints
                    vs.saveOrUpdate(lp.hierarchy.id)
                } else {
                    ids.add(vs.id!!)
                    gainStatementIds.add(vs.gainStatementId)
                }
                vs.subPoints = itemPoint
                deductPoints -= itemPoint

                segments.forEach {
                    // 小于等于过期时间的积分的积分块全部更新
                    if (!it.expireDate!!.isAfter(vs.overdueDate.shDate())) {
                        it.point -= itemPoint
                        it.modified = ZonedDateTime.now()
                        it.updated = true
                    }
                }

                // 积分追溯明细
                val item = initRecordItem(lp,
                    Uuid.uuid,
                    recordId, -itemPoint, itemStatus, parentBackId = vs.id)
                items.add(item)

                callback?.invoke(vs, item)
                if (deductPoints.compareTo(BigDecimal.ZERO) == 0) {
                    break
                }
            }

            deleteBatchValidPoint(lp.hierarchy.id, ids, primaryGSId, gainStatementIds, gainStatus)
        }

        // 经过明细扣减过后，还有剩余积分
        if (deductPoints > BigDecimal.ZERO) {
            if (lp.member.point - p >= BigDecimal.ZERO) {
                // 接口扣减的总分已经在facade处处理过
                // 如果存在有效积分和积分总账不一致，已总账为主，生成一笔填充记录
                val item = initRecordItem(lp,
                    Uuid.uuid,
                    recordId, -deductPoints, itemStatus, parentBackId = "-$recordId")
                items.add(item)
                segments.forEach {
                    it.point -= deductPoints
                    it.modified = ZonedDateTime.now()
                    it.updated = true
                }
            } else {
                if (lp.hierarchy.negativeStrategy == NegativeStrategyEnum.TO_ZERO) {
                    // 最多只允许扣减到0分, 预计扣减p分，实际扣除(lp.afterPoints)分
                    val beyondPointRecord = initBeyondPointRecord(lp, recordId, p, lp.afterPoints, recordType, pcStatus)
                    beyondPointRecord.save(lp.hierarchy.id)
                }
                if (lp.hierarchy.negativeStrategy == NegativeStrategyEnum.TO_NEGATIVE) {
                    // 生成追溯记录
                    // 允许扣减到负数
                    // 负积分冻结明细, 冻结仅保存负积分记录
                    val negativeId = Uuid.uuid
                    val negative = initNegativePoints(lp, negativeId, recordId, deductPoints)
                    val negativeItem = initRecordItem(lp, Uuid.uuid, recordId, -deductPoints, FSMPointEvent.DEDUCT, backId = negativeId)
                    items.add(negativeItem)

                    // 负积分原地保存
                    negative.save()

                    if (cb1 != null) {
                        cb1(deductPoints)
                    } else {
                        segments.forEach {
                            // 小于等于过期时间的积分的积分块全部设置为0，调用处删除等于0的数据
                            if (!it.expireDate!!.isAfter(LONG_TERM_OVERDUE_DATE.shDate())) {
                                it.point = BigDecimal.ZERO
                                it.modified = ZonedDateTime.now()
                                it.updated = true
                            }
                        }
                    }
                }
            }
        }
        return items
    }


    // 删除有效积分，同时更新有效积分对应的获取记录的状态
    private fun deleteBatchValidPoint(hierarchyId: Long, vssIds: List<String>, primaryGSId: String?, gainStatementIds: List<String>, gainStatus: FSMPointEvent) {
        validService.deleteBatchValidPoint(vssIds.toTypedArray(), hierarchyId)
        val gsIds = validService.findListByGainStatementIds(hierarchyId, gainStatementIds).map { it.gainStatementId }
        gainStatementService.findByIds(gainStatementIds, hierarchyId).forEach {
            val isSendOrDelay = it.status == FSMPointEvent.SEND || it.status == FSMPointEvent.DELAY_SEND
            if (!isSendOrDelay) return@forEach
            val isGSPointsUsedUp = it.id !in gsIds
            if (isGSPointsUsedUp) {
                if (primaryGSId != null) {
                    if (primaryGSId == it.id) {
                        it.status = gainStatus
                    } else {
                        it.status = FSMPointEvent.DEDUCT
                    }
                } else {
                    it.status = gainStatus
                }
                it.modified = ZonedDateTime.now()
                it.saveOrUpdate(hierarchyId)
            }
        }
    }


    // 积分发放规则
    fun calculate(lp: LoyaltyPoints, pointSendRuleGroups: List<PointRuleGroup>, baseTimePath: String? = null): List<MemberPointGainStatement> {

        // 规则组计算出的积分
        val gss = ArrayList<MemberPointGainStatement>()

        for (group in pointSendRuleGroups) {
            // 已归档的规则组不允许参与计算
            if (group.fileStatus == true) {
                continue
            }

            // 当前规则组计算出的积分
            val groupGss = ArrayList<MemberPointGainStatement>()

            // 规则组下的规则
            val rules = group.sendRuleList ?: continue

            for (rule in rules) {
                // 已归档的规则不允许参与计算
                if (rule.fileStatus == true) {
                    continue
                }
                // 积分发放规则的条件表达式计算过滤
                try {
                    val conditionResult = ExpressionIdentUtil.eppById(
                        rule.conditionExpressionFxId!!,
                        lp.rawEvent!!,
                        JsonNodeFactory.instance.objectNode().put(SUBJECT, lp.subject.dataType)
                    )
                    val conditionBooleanResult = conditionResult?.toString()?.toBoolean() ?: false
                    logger.debug(
                        "规则表达式条件过滤 规则组信息：规则组ID：{}, 规则名称：{}, 规则Id：{} 规则名称：{} fxId: {} 结果：{}",
                        group.id,
                        group.groupName,
                        rule.id,
                        rule.ruleName,
                        rule.conditionExpressionFxId,
                        conditionResult
                    )
                    if (!conditionBooleanResult) {
                        continue
                    }
                } catch (e: Exception) {
                    val fx = ApplicationContextHolder.getBean(ExtendFxVariableProvider::class.java).findById(rule.conditionExpressionFxId!!.toLong())
                    logger.error(
                        "表达式计算错误：规则组ID：{}, 规则组名称：{}, 规则ID：{},  规则名称：{},  表达式：{} ",
                        group.id,
                        group.groupName,
                        rule.id,
                        rule.ruleName,
                        JsonUtils.toJson(fx),
                        e
                    )
                    throw e
                }

                // 条件已经满足
                // 计算该规则的积分值
                //积分值
                val pointValue = ExpressionIdentUtil.eppById(
                    rule.actionExpressionFxId!!,
                    lp.rawEvent!!,
                    JsonNodeFactory.instance.objectNode().put(SUBJECT, lp.subject.dataType)
                )?.toString()?.toBigDecimalOrNull()
                if (pointValue == null) {
                    val fx = ApplicationContextHolder.getBean(ExtendFxVariableProvider::class.java).findById(rule.conditionExpressionFxId!!.toLong())
                    logger.error(
                        "计算出的积分值不是一个数值：规则组ID：{}, 规则名称：{}, 表达式：{}",
                        rule.ruleGroupId,
                        rule.ruleName,
                        JsonUtils.toJson(fx)
                    )
                    throw IllegalArgumentException("计算出的积分值不是一个数值")
                }

                // 重算允许积分值为0
                val point = lp.hierarchy.scale(pointValue.toString().toBigDecimal())
                if (point < BigDecimal.ZERO) {
                    val fx = ApplicationContextHolder.getBean(ExtendFxVariableProvider::class.java).findById(rule.conditionExpressionFxId!!.toLong())
                    logger.error(
                        "计算出的积分值({})小于0：规则组ID：{}, 规则名称：{}, 表达式：{}",
                        point,
                        rule.ruleGroupId,
                        rule.ruleName,
                        JsonUtils.toJson(fx)
                    )
                    throw IllegalArgumentException("计算出的积分值(${point})小于0")
                }
                // 生效时间和失效时间
                val (effectiveDateTime, overdueDateTime) = rule.calcPointDateTime(lp.subject.dataType, lp.rawEvent!!, lp.date, baseTimePath)
                if (overdueDateTime != null && overdueDateTime.isBefore(effectiveDateTime)) {
                    logger.warn("规则组信息：规则组ID：{}, 规则名称：{}, 规则Id：{} 规则名称：{} fxId: {} 结果：{}",
                        rule.ruleGroupId,
                        rule.ruleName,
                        rule.id,
                        rule.ruleName,
                        rule.conditionExpressionFxId,
                        "无效的积分过期时间 $overdueDateTime")
                    throw IllegalArgumentException("无效的积分过期时间(${overdueDateTime})")
                }

                // 重置北京时间的23:59:59结束
                val overdueDate = overdueDateTime ?: LONG_TERM_OVERDUE_DATE

                val isDelayedActive = effectiveDateTime.isAfter(lp.date)

                val status = if (isDelayedActive) FSMPointEvent.DELAY_SEND else FSMPointEvent.SEND

                val gainStatement = initGainPoints(
                    lp,
                    Uuid.uuid,
                    status,
                    point,
                    effectiveDateTime,
                    overdueDate
                ).apply {
                    this.ruleId = rule.id
                    this.ruleName = rule.ruleName
                    this.ruleGroupId = group.id
                    this.ruleGroupName = group.groupName
                    this.enabledGlobalGroupLimit = group.limitPoint != null
                    this.desc = RecordDescUtils.get(group.groupName, rule.remark).toString()
                    this.sort = rule.sort
                }
                groupGss.add(gainStatement)
            }

            if (groupGss.isEmpty()) {
                continue
            }

            // 规则组计算的积分取值策略，默认取累计值
            when (group.ruleSortType ?: RuleSortTypeEnum.TOTAL_VALUE) {
                RuleSortTypeEnum.MAX_VALUE -> {
                    // 取最大积分
                    // 如果等于最大值的元素存在多个，则优先取永久有效的积分，否则取失效时间最大的
                    val max = groupGss.maxOf { it.point }
                    val gs = groupGss.sortedByDescending { it.overdueDate }.find { it.point.compareTo(max) == 0 }
                    gss.add(gs!!)
                }
                RuleSortTypeEnum.MIN_VALUE -> {
                    // 取最小积分
                    // 如果等于最小值的元素存在多个，则优先取永久有效的积分，否则取失效时间最大的
                    val min = groupGss.minOf { it.point }
                    val gs = groupGss.sortedByDescending { it.overdueDate }.find { it.point.compareTo(min) == 0 }
                    gss.add(gs!!)
                }
                RuleSortTypeEnum.MAX_LEVEL -> {
                    // 按照规则顺序，取优先级高的 sort值越小有优先级越高
                    val gs = groupGss.minBy { it.sort!! }
                    gss.add(gs)
                }
                RuleSortTypeEnum.NO, RuleSortTypeEnum.TOTAL_VALUE -> {
                    gss.addAll(groupGss)
                }
            }
        }
        return gss
    }

    // 积分发放规则
    fun calculate(lp: LoyaltyPoints, group: PointRuleGroup): List<MemberPointGainStatement> {
        return calculate(lp, listOf(group),lp.eventType?.sendCycleTimePath)
    }

    data class RecalculateResult(
        val oldGSS: List<MemberPointGainStatement>,
        val newNotInOldGSS: List<MemberPointGainStatement>,
        val newInOldGSS: List<MemberPointGainStatement>,
        val oldNotInNewGSS: List<MemberPointGainStatement>
    )

    // 重算
    // 返回结果
    //      0. 原积分
    //      1. 原没有匹配成功的规则，重算后匹配到了，这部分执行新增（补发积分）
    //      2. 原匹配成功的规则，重算后再次匹配，这部分执行差值多退少补
    //      3. 原匹配成功的规则，重算后没有再次匹配到，这部分执行作废（原已发的全部扣除）
    fun recalculate(lp: LoyaltyPoints): RecalculateResult {
        // 发放时机
        val sendEventType = lp.eventType!!.relativePointEventType!!

        // 原积分获取记录
        val oldGSS = gainStatementService.findByTraceIdList(
            lp.hierarchy.id,
            sendEventType.id,
            lp.attr.traceId,
            lp.member.id
        )

        // 查询正单时间的规则组和规则
        // 规则的结束时间不需要限定，大于开始时间就行，因为重算的时候，规则组可能已经到了结束时间
        val occurrenceTs = sendEventType.occurrenceTs
        val startDate = Date(occurrenceTs)
        val groups0 = pointRuleGroupService.findPointRuleGroup(
            lp.plan.id,
            lp.hierarchy.id,
            sendEventType.id,
            EventOperationEnum.SEND,
            startDate
        ).filter { it.fileStatus != true }
        // 查找发放规则组下的所有规则
        for (group in groups0) {
            group.sendRuleList = pointSendRuleService.findByRuleGroupIdAndDisabled(group.id!!)
        }
        val groups = groups0.filter { it.fileStatus != true }
        // 新积分获取记录
        val newGSS = calculate(lp, groups, sendEventType.sendCycleTimePath).onEach { g ->
            g.eventTypeId = sendEventType.id
            g.eventTypeName = sendEventType.name
            g.occurrenceTs = sendEventType.occurrenceTs.toString()
        }

        // 原没有匹配成功的规则，重算后匹配到了，这部分执行新增（补发积分）
        val newNotInOldGSS = newGSS.filter { new ->
            val gs = oldGSS.find { old ->
                old.ruleGroupId == new.ruleGroupId && old.ruleId == new.ruleId && new.eventTypeId == old.eventTypeId
            }
            gs == null
        }.filter { it.point > BigDecimal.ZERO }

        // 原匹配成功的规则，重算后再次匹配，这部分执行差值多退少补
        val newInOldGSS = newGSS.filter { new ->
            val gs = oldGSS.find { old ->
                old.ruleGroupId == new.ruleGroupId && old.ruleId == new.ruleId && new.eventTypeId == old.eventTypeId
            }
            // 可以被重算的状态
            val ss = setOf(
                FSMPointEvent.SEND, FSMPointEvent.DELAY_SEND, FSMPointEvent.DEDUCT,
                FSMPointEvent.SPECIAL_FREEZE, FSMPointEvent.OPEN_FREEZE, FSMPointEvent.FREEZE
            )
            val b = gs != null && gs.status in ss && !gs.overdueDate.shDate().isBefore(lp.date.shDate())
            if (b) {
                new.ref = gs
            }
            b
        }.onEach {
            // 重算不修改原积分有效期和失效期
            it.effectiveDate = it.ref!!.effectiveDate
            it.overdueDate = it.ref!!.overdueDate
            it.status = it.ref!!.status
        }

        // 原匹配成功的规则，重算后没有再次匹配到，这部分执行作废（原已发的全部扣除）
        val oldNotInNewGSS = oldGSS.filter { old ->
            val gs = newGSS.find { new ->
                old.ruleGroupId == new.ruleGroupId && old.ruleId == new.ruleId && new.eventTypeId == old.eventTypeId
            }
            // 0分的不用执行作废
            val f = gs == null && old.point > BigDecimal.ZERO && !old.overdueDate.shDate().isBefore(lp.date.shDate())
            if (f) {
                val group = groups0.find { it.id == old.ruleGroupId }
                val rule = group?.sendRuleList?.find { it.id == old.ruleId }
                // ref 自己
                old.ruleName = rule?.ruleName ?: ""
                old.ruleGroupName = group?.groupName ?: ""
                old.ref = Json.copy(old)
            }
            f
        }
        return RecalculateResult(Json.copy(oldGSS), newNotInOldGSS, newInOldGSS, oldNotInNewGSS)
    }


    // 重算加总积分 加p分
    fun recalculatePlus(lp: LoyaltyPoints, p: BigDecimal, gs: MemberPointGainStatement): MemberPointCalculateTask {
        val memberPoint = lp.member.memberPoint!!
        // 新大于旧，需要把新多出来的部分补发
        lp.pointValue = p
        lp.afterPoints = p
        lp.attr.effectiveDate = gs.effectiveDate
        lp.attr.overdueDate = gs.overdueDate
        lp.sendRule = PointIssuanceRule(gs.ruleId!!, gs.ruleName!!)
        lp.deductRule = null
        // 积分上限过滤
        if (gs.status == FSMPointEvent.SEND) {
            filterLimit(lp, lp.afterPoints, FSMPointEvent.RECALCULATE, gs.eventTypeId, gs.ruleGroupId)
        }
        // 待生效的积分总账不发生变化
        val point = if (gs.status == FSMPointEvent.DELAY_SEND || gs.status == FSMPointEvent.EXPIRE) BigDecimal.ZERO else lp.afterPoints
        // 发放总积分
        // 外部更新总账和日志
        memberPoint.plus(point, gs.effectiveDate,  gs.overdueDate, lp.date, updateNow = false)
        lp.afterPoints = memberPoint.point - lp.member.point
        lp.afterTotalPoints = memberPoint.point
        lp.attr.sign = "+"

        // 加的积分后，原单的状态变为已生效或待生效
        gs.ref!!.status = gs.status

        val uniqueId = lp.attr.uniqueId + "-recalculate-"  + gs.ref!!.id
        return lp.buildJournal(PointAction.RECALCULATE, uniqueId)
    }

    // 重算减总积分 减p分
    fun recalculateMinus(lp: LoyaltyPoints, p: BigDecimal, gs: MemberPointGainStatement): MemberPointCalculateTask {
        val memberPoint = lp.member.memberPoint!!
        // 旧大于新，需要把旧多出来的部分扣除
        lp.pointValue = p
        lp.afterPoints = p
        lp.sendRule = null
        // 待生效的积分总账不发生变化
        val point = if (gs.status == FSMPointEvent.DELAY_SEND) BigDecimal.ZERO else lp.afterPoints
        // 扣减总积分
        val pair = memberPoint.minus(point, lp.hierarchy.negativeStrategy!!, updateNow = false)
        lp.afterPoints = pair.second
        lp.member.leftSegmentDate = pair.first
        lp.afterTotalPoints = memberPoint.point
        lp.attr.sign = "-"
        val uniqueId = lp.attr.uniqueId + "-recalculate-"  + gs.ref!!.id
        return lp.buildJournal(PointAction.RECALCULATE, uniqueId)
    }

    // 重算减总积分 不匹配原规则 作废重算
    fun invalidNotMatched(
        lp: LoyaltyPoints,
        gs: MemberPointGainStatement,
        recalculateRuleGroup: PointGroup?,
        recalculateRule: PointDeductionRule?,
        recalculateDesc: String?,
        action: PointAction
    ): MemberPointCalculateTask {
        val memberPoint = lp.member.memberPoint!!
        val uniqueId = lp.attr.uniqueId + "-invalid-" + gs.id
        lp.member.point = memberPoint.point
        lp.pointValue = gs.point.abs()
        lp.attr.abolish = true
        lp.attr.pendingPoints = gs.status == FSMPointEvent.DELAY_SEND
        lp.attr.businessId = gs.id
        lp.pointRuleGroup = recalculateRuleGroup
        lp.deductRule = recalculateRule
        lp.sendRule = null
        lp.attr.desc = recalculateDesc

        // 待生效的积分总账不发生变化
        val point = if (gs.status == FSMPointEvent.DELAY_SEND) BigDecimal.ZERO else gs.point
        // 扣减总积分
        val pair = memberPoint.minus(point, lp.hierarchy.negativeStrategy!!, updateNow = false)
        lp.member.leftSegmentDate = pair.first
        lp.afterPoints = pair.second
        lp.afterTotalPoints = memberPoint.point
        lp.attr.sign = "-"
        return lp.buildJournal(action, uniqueId)
    }

    // 事件解冻积分
    fun unFrozenEventPoint(lp: LoyaltyPoints, gs: MemberPointGainStatement, frozenPoint: MemberFrozenPoint) {
        val memberPoint = lp.member.memberPoint!!
        if (frozenPoint.fromStatus == FSMPointEvent.DELAY_SEND) {
            gs.status = FSMPointEvent.DELAY_SEND
            // 检查解冻的时候是否已经过期了
            if (gs.overdueDate.shDate().isBefore(lp.date.shDate())) {
                lp.attr.beforeGsStatus = FSMPointEvent.DELAY_SEND.name
                gs.status = FSMPointEvent.EXPIRE
            }
            //发放总积分
            memberPoint.plus(BigDecimal.ZERO, lp.date.plusSeconds(1), gs.overdueDate, lp.date)
            lp.pointValue = frozenPoint.point
            lp.afterPoints = BigDecimal.ZERO
            lp.afterTotalPoints = memberPoint.point
        } else {
            // 解冻已生效的积分
            // 从冻结明细中找到解冻的积分
            val frozenStatements = frozenStatementService.findByFrozenIds(lp.hierarchy.id, listOf(frozenPoint.id!!))
            var point = BigDecimal.ZERO
            for (fs in frozenStatements) {
                // 检查解冻的时候已经过期了
                if (fs.overdueDate.shDate().isBefore(lp.date.shDate())) {
                    // 解冻后已经过期了，解冻+0分
                    memberPoint.plus(BigDecimal.ZERO, fs.effectiveDate!!,  fs.overdueDate, lp.date, updateNow = false)
                    gs.status = FSMPointEvent.EXPIRE
                } else {
                    memberPoint.plus(fs.point, fs.effectiveDate!!,  fs.overdueDate, lp.date, updateNow = false)
                    point += fs.point
                    gs.status = FSMPointEvent.SEND
                }
            }
            // 保存总积分和变更日志
            memberPoint.update()
            memberPoint.saveLog()
            lp.afterPoints = point
            lp.afterTotalPoints = memberPoint.point
        }
        gs.saveOrUpdate(lp.hierarchy.id)
        // 暂时更新成负值，防止重复解冻
        frozenPoint.point = -frozenPoint.point.abs()
        frozenPoint.saveOrUpdate(lp.hierarchy.id)
    }



    // 保存积分发放拦截记录
    fun saveLimitRecord(lp: LoyaltyPoints, pointEvent: FSMPointEvent) {
        if (!lp.hierarchy.sendLimitResults.isNullOrEmpty()) {
            val sendLimitRules = lp.hierarchy.sendLimitResults!!.minBy { x -> x.afterPoints }.sendLimitCalc
            val limitRecord = initLimitRecord(lp, pointEvent, lp.pointValue, lp.afterPoints, lp.afterTotalPoints, sendLimitRules)
            limitRecord.save()
        }
    }

    // 处理立即生效积分发放记录
    fun transferSend(
        lp: LoyaltyPoints,
        recordId: String,
        gs: MemberPointGainStatement?,
        vs: MemberPointValidStatement,
        beforeTotalPoints: BigDecimal? = null,
    ): Triple<List<MemberPointRecord>, List<PointRecordItem>, List<MemberPointValidStatement>> {

        val records = ArrayList<MemberPointRecord>()
        val items = ArrayList<PointRecordItem>()
        val vss = ArrayList<MemberPointValidStatement>()
        val totalPoints = beforeTotalPoints ?: lp.member.point
        // 是否是过期的积分
        val isExpired = lp.attr.overdueDate!!.shDate() < lp.date.shDate()
        if (isExpired) {
            // 已经过期的积分，额外生效一条过期记录
            val expiredId = Uuid.uuid
            val expired = initPointRecord(
                lp, expiredId,
                FSMPointEvent.EXPIRE,
                points = vs.point,
                changePoints = -vs.point,
                totalPoints = (totalPoints - vs.point),
                PCStatus.EXPIRE,
                lp.attr.effectiveDate,
                lp.attr.overdueDate
            ).apply {
                this.desc = "过期"
            }

            records.add(expired)

            // 生成一条发放和过期的追溯记录
            val sendItem = initRecordItem(
                lp,
                Uuid.uuid,
                expiredId,
                vs.point,
                FSMPointEvent.SEND,
                backId = vs.id,
                parentBackId = vs.backId, // 反向的有值
                effectiveDate = vs.effectiveDate,
                overdueDate = vs.overdueDate
            )
            val expiredItem = initRecordItem(
                lp,
                Uuid.uuid,
                expiredId,
                -vs.point,
                FSMPointEvent.EXPIRE,
                parentBackId = sendItem.backId,
                effectiveDate = vs.effectiveDate,
                overdueDate = vs.overdueDate
            )
            items.add(sendItem)
            items.add(expiredItem)

            gs?.status = FSMPointEvent.EXPIRE
            gs?.modified = ZonedDateTime.now()

            vs.fromStatus = PointStateEnum.EXPIRED
            vs.point = BigDecimal.ZERO
        }

        // 检查是否存在负积分，存在则抵消
        if (vs.fromStatus == PointStateEnum.VALID && vs.point > BigDecimal.ZERO) {
            offset(lp, lp.member.point, vs.point, recordId, FSMPointEvent.SEND) { offsetItems, p ->
                items.addAll(offsetItems)
                vs.point = p
            }
        }

        if (vs.point > BigDecimal.ZERO) {
            // 保存有效积分记录
            vss.add(vs)
            // 积分追溯记录
            val item = initRecordItem(
                lp,
                Uuid.uuid,
                recordId,
                vs.point,
                FSMPointEvent.SEND,
                backId = vs.id,
                parentBackId = vs.backId,
                effectiveDate = vs.effectiveDate,
                overdueDate = vs.overdueDate
            )
            items.add(item)

            gs?.status = FSMPointEvent.SEND
            gs?.modified = ZonedDateTime.now()
        } else {
            if (gs?.status == FSMPointEvent.SEND || gs?.status == FSMPointEvent.REVERSE_SEND ) {
                gs.status = FSMPointEvent.DEDUCT
                gs.modified = ZonedDateTime.now()
            }
        }

        return Triple(records, items, vss)
    }
}