package com.shuyun.loyalty.service.datamodel

import com.fasterxml.jackson.annotation.JsonProperty
import com.shuyun.loyalty.entity.api.constants.ChangeMode
import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.repository.MemberLimitPointRecordRepository
import com.shuyun.loyalty.service.transfer.points.LoyaltyPoints
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.pip.ApplicationContextHolder
import java.math.BigDecimal
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne
import javax.persistence.Table


/** 积分发放限制记录表 */
@DataServiceModel
@FqnVariableModel
@Table(name = "data.loyalty.member.account.LimitPointRecord{*}")
class MemberLimitPointRecord : SubMemberPoint() {
    /** 触发动作 */
    @Column
    lateinit var recordType: FSMPointEvent

    /** 积分生效时间，如果是锁定、冻结则没有有效期 */
    @Column
    var effectiveDate: ZonedDateTime? = null

    /** 积分过期时间，如果是锁定、冻结则没有有效期 */
    @Column
    var overdueDate: ZonedDateTime? = null

    /** 操作人、操作原记录id，订单id，规则id，规则组id，参与规则组条件、参与规则计算数据，interfaceSource：接口调用来源 */
    @Column
    var extralInfo: String? = null

    /** 用于显示描述记录变更信息 */
    @Column
    var recordDetail: String? = null

    /** recordDetail为系统自动根据规则生成的明细信息。而desc为可接口，或特殊场景添加ras */
    @Column
    var desc: String? = null

    /** 操作人，默认系统 */
    @Column
    var operator: String = ConstantValue.DEFAULT_OPERATOR

    /** 操作人用户ID */
    @Column
    var operatorId: String = ConstantValue.DEFAULT_OPERATOR

    /**数据来源*/
    @Column
    var channel: String? = null

    /** 最后一次修改时间 */
    @Column
    var modified: ZonedDateTime = ZonedDateTime.now()

    /**事件ID*/
    @Column
    var key: String? = null

    /**规则组*/
    @Column
    var ruleGroup: String? = null

    /**变更方式*/
    @Column
    var changeMode: String? = null

    /**计划名称*/
    @Column
    var planName: String? = null

    /**积分帐号名称*/
    @Column
    var pointPlanName: String? = null

    /**原单ID*/
    @Column
    var traceId: String? = null

    /** 总积分 */
    @Column
    var totalPoint: BigDecimal = BigDecimal.ZERO

    /**时机名称*/
    @Column
    var eventTypeName: String? = null

    /**活动名称*/
    @Column
    var actionName: String? = null

    /** 规则Id */
    @Column
    var ruleId: Long? = null

    /**规则名称*/
    @Column
    var ruleName: String? = null

    /**店铺ID*/
    @Column
    var shopId: String? = null

    /**积分状态，对账使用*/
    @Column
    var status: PCStatus? = null

    /**扩展字段1*/
    @Column
    @set:JsonProperty("KZZD1")
    @get:JsonProperty("KZZD1")
    var KZZD1: String? = null

    /**扩展字段2*/
    @Column
    @set:JsonProperty("KZZD2")
    @get:JsonProperty("KZZD2")
    var KZZD2: String? = null

    /**扩展字段3*/
    @Column
    @set:JsonProperty("KZZD3")
    @get:JsonProperty("KZZD3")
    var KZZD3: String? = null
    @Column
    var changePoint: BigDecimal = BigDecimal.ZERO
    @Column
    var sendLimitRule: String? = null
    @ManyToOne
    @JoinColumn(name = "memberId")
    var member: Any? = null

    fun save() {
        val maxPoint = 9999999999.99.toBigDecimal()
        point = if (point > maxPoint) maxPoint else point
        changePoint = if (changePoint > maxPoint) maxPoint else changePoint
        totalPoint = if (totalPoint > maxPoint) maxPoint else totalPoint
        ApplicationContextHolder.getBean(MemberLimitPointRecordRepository::class.java).save(this,this.pointPlanId.toString())
    }
}

object MemberLimitPointRecordMapper {

    // 真实积分值 point
    // 预发积分值 changePoint
    // 变更后的总积分 totalPoint
    fun buildMemberLimitPointRecord(lp: LoyaltyPoints, changePoint: BigDecimal, point: BigDecimal, totalPoint: BigDecimal, desc: String, recordType: FSMPointEvent, status: PCStatus): MemberLimitPointRecord {
        return MemberLimitPointRecord().apply{
            this.effectiveDate = lp.attr.effectiveDate
            this.overdueDate = lp.attr.overdueDate
            this.recordType = recordType
            this.desc = desc
            this.channel = lp.attr.channel
            this.key = lp.attr.uniqueId
            this.ruleGroup = lp.pointRuleGroup!!.name
            this.changeMode = ChangeMode.AUTO_CALC.toString()
            this.planId = lp.plan.id
            this.planName = lp.plan.name
            this.pointPlanId = lp.hierarchy.id
            this.pointPlanName = lp.hierarchy.name
            this.traceId = lp.attr.traceId
            this.eventTypeName = lp.eventType!!.name
            this.ruleId = lp.sendRule!!.id
            this.ruleName = lp.sendRule!!.name
            this.shopId = lp.attr.shopId
            this.status = status
            this.extralInfo = point.toString()
            this.KZZD1 = lp.attr.kzzd1
            this.KZZD2 = lp.attr.kzzd2
            this.KZZD3 = lp.attr.kzzd3
            this.changePoint = changePoint
            this.point = point
            this.totalPoint = totalPoint
            this.memberId = lp.member.memberId
            this.created = ZonedDateTime.now()
            this.modified = ZonedDateTime.now()
            this.memberPointId = lp.member.id
            this.subjectFqn = lp.subject.dataType
        }
    }
}