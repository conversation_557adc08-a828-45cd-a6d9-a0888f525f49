package com.shuyun.loyalty.service.service

import com.pip.mybatisplus.annotation.NotTransactionSafe
import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.lite.annotation.DbNodeSelector
import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.api.request.ExtendMemberPointRequest
import com.shuyun.loyalty.entity.enums.FromTypeEnum
import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.sdk.Segments
import com.shuyun.loyalty.service.datamodel.*
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.extension.getIfPresent
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.extension.utcStr
import com.shuyun.loyalty.service.model.DeductionOrderEnum
import com.shuyun.loyalty.service.model.DeductionPriority
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.DateUtils
import com.shuyun.loyalty.service.util.SQL
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import org.apache.commons.lang3.StringUtils
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.beans.BeanUtils
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import kotlin.jvm.optionals.getOrNull

/**
 * 会员积分记录
 */
@NotTransactionSafe
@Component
class MemberPointService: MemberPointDmBaseService<MemberPoint>() {
    private val log = LogManager.getLogger(MemberPointService::class.java)
    override fun log(): Logger = this.log


    // 需要在事务中执行，在会员锁中执行
    fun getOrCreate(
        accountTypeId: Long,
        memberId: String,
        date: LocalDate = ZonedDateTime.now().shDate()
    ): MemberPoint {
        return getByMemberId(accountTypeId, memberId, date) ?: create(accountTypeId, memberId)
    }


    // 需要在事务中执行，在会员锁中执行
    fun getByMemberId(
        accountTypeId: Long,
        memberId: String,
        date: LocalDate = ZonedDateTime.now().shDate(),
        refreshSegmentPoints: Boolean = true
    ): MemberPoint? {
        val params = mapOf(
            "memberId" to memberId,
            "pointPlanId" to accountTypeId
        )
        val memberPoint = findOneByFilter(
            filter = JsonUtils.toJson(params),
            replacePattern = accountTypeId.toString()
        ).getOrNull()
        memberPoint?.let {
            if (refreshSegmentPoints) {
                refreshSegmentPoints(it, date)
            }
        }
        return memberPoint
    }


    // 需要在事务中执行，在会员锁中执行
    fun getByMemberPointId(
        accountTypeId: Long,
        memberPointId: String,
        date: LocalDate = ZonedDateTime.now().shDate(),
        refreshSegmentPoints: Boolean = true
    ): MemberPoint? {
        val memberPoint = findById(memberPointId, accountTypeId.toString()).getOrNull()
        memberPoint?.let {
            if (refreshSegmentPoints) {
                refreshSegmentPoints(it, date)
            }
        }
        return memberPoint
    }


    // 需要在事务中执行，在会员锁中执行
    private fun create(accountTypeId: Long, memberId: String): MemberPoint {
        val plan = LoyaltyPrograms.findPlanByAccountTypeId(accountTypeId) ?: throw LoyaltyException(LoyaltyExceptionCode.REQUEST_EXCEPTION)
        val memberPoint = MemberPoint()
        memberPoint.planId = plan.id
        memberPoint.pointPlanId = accountTypeId
        memberPoint.memberId = memberId
        memberPoint.subjectFqn = plan.subjectList!!.first().dataType!!
        memberPoint.point = BigDecimal.ZERO
        memberPoint.version = 0
        memberPoint.openSegmentFlag = true
        return saveOrUpdate(memberPoint, replacePattern = accountTypeId.toString())
    }


    // 需要在事务中执行，在会员锁中执行
    private fun refreshSegmentPoints(memberPoint: MemberPoint, date: LocalDate) {
        if (!memberPoint.openSegmentFlag) {
            DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
                val points = Segments.rebuildSegment(
                    sdk,
                    pointAccountId = memberPoint.pointPlanId!!,
                    memberId = memberPoint.memberId,
                    reference = 0
                )
                memberPoint.point = points
            }
        } else {
            val segment = ApplicationContextHolder.getBean(PointSegmentService::class.java)
                .findFirstDateAfter(
                    hierarchyId = memberPoint.pointPlanId!!,
                    memberPointId = memberPoint.id!!, date = date
                )
            memberPoint.point = segment?.point ?: BigDecimal.ZERO
        }
    }

}

@NotTransactionSafe
@Component
class MemberFrozenPointService: MemberPointDmBaseService<MemberFrozenPoint>() {
    private val log = LogManager.getLogger(MemberFrozenPointService::class.java)
    override fun log(): Logger = log


    /** 根据积分获取记录获取积分冻结记录 */
    fun findFrozenPointByGainStatementId(gainStatementId: String,accountTypeId:Long): MemberFrozenPoint? {
        val params = HashMap<String, Any?>()
        params["gainStatementId"] = gainStatementId
        val result = findListByFilter(JsonUtils.toJson(params), replacePattern = accountTypeId.toString())
        return result.firstOrNull()
    }

    fun findFrozenPointByGainStatementIds(accountTypeId:Long, gainStatementIds: List<String>): List<MemberFrozenPoint> {
        if (gainStatementIds.isEmpty()) return emptyList()
        val params = LinkedHashMap<String, Any>()
        params["gainStatementIds"] = gainStatementIds
        val sql = """
            SELECT ${getFields()} 
            FROM ${getModelFqn(accountTypeId)} 
            WHERE 
                gainStatementId IN (:gainStatementIds)
            LIMIT 10000
        """.trimIndent()
        return executeList(sql, params)
    }


    override fun batchInsert(dataList: List<MemberFrozenPoint>, replacePattern: String?) {
        if (dataList.isEmpty()) return
        val sql = SQL.build {
            var sql = """
                INSERT INTO data.loyalty.member.account.FrozenPoint$replacePattern  
                (
                    `id`, `planId`, `pointPlanId`, `memberPointId`, `memberId`, `subjectFqn`, `point`, `fromStatus`, 
                    `gainStatementId`, `created`, `memberPoint`
                ) 
                VALUES 
            """.trimIndent()
            var i = 0L
            for (item in dataList) {
                sql += """
                    (
                    ${item.id.escaped()},
                    ${item.planId.escaped()}, 
                    ${item.pointPlanId.escaped()}, 
                    ${item.memberPointId.escaped()}, 
                    ${item.memberId.escaped()}, 
                    ${item.subjectFqn.escaped()}, 
                    ${item.point.escaped()},
                    ${item.fromStatus.escaped()}, 
                    ${item.gainStatementId.escaped()}, 
                    ${item.created.plus(i++, ChronoUnit.MILLIS).escaped()}, 
                    ${item.memberPointId.escaped()}
                    ),
                """.trimIndent()
            }
            sql.trim().dropLast(1)
        }

        super.execute(sql, mapOf())
    }

    fun deleteByIds(deleteFPIds: List<String>, accountTypeId: Long) {
        if (deleteFPIds.isEmpty()) {
            return
        }
        val bodyParams = HashMap<String, Any?>()
        val paramsItem = HashMap<String, Any?>()
        paramsItem["\$in"] = deleteFPIds
        bodyParams["id"] = paramsItem
        deleteByFilter(JsonUtils.toJson(bodyParams),replacePattern = accountTypeId.toString())
    }
}

@NotTransactionSafe
@Component
class MemberPointLogService: MemberPointDmBaseService<MemberPointLog>() {
    private val log = LogManager.getLogger(MemberPointLogService::class.java)
    override fun log(): Logger = log

    /** 根据最新会员积分账户信息，构建积分总账变更记录 */
    fun save(memberPoint: MemberPoint,accountTypeId: Long, newPoint: BigDecimal = memberPoint.point, createdTime: ZonedDateTime = ZonedDateTime.now()) {
        val memberPointLog = MemberPointLog()
        BeanUtils.copyProperties(memberPoint, memberPointLog)
        memberPointLog.apply {
            this.id = null
            this.point = newPoint
            this.created = createdTime
        }
        ApplicationContextHolder.getBean(MemberPointLogService::class.java).saveOrUpdate(memberPointLog,replacePattern = accountTypeId.toString())
    }


}



@NotTransactionSafe
@Component
class MemberPointValidStatementService: MemberPointDmBaseService<MemberPointValidStatement>() {
    private val log = LogManager.getLogger(MemberPointValidStatementService::class.java)
    override fun log(): Logger = log


    override fun batchInsert(dataList: List<MemberPointValidStatement>, replacePattern: String?) {
        if (dataList.isEmpty()) return
        val sql = SQL.build {
            var sql = """
                INSERT INTO data.loyalty.member.account.ValidStatement$replacePattern
                 (`id`,`planId`,`pointPlanId`,`memberPointId`,`memberId`,`subjectFqn`,`point`,`gainStatementId`,`effectiveDate`,`overdueDate`,`fromStatus`,`modified`,`created`,`openTraceId`,`memberPoint`,`gainStatement`,`shopId`,`kzzd1`,`kzzd2`,`kzzd3`,`channel`)
                VALUES 
            """.trimIndent()
            var i = 0L
            for (item in dataList) {
                sql += """
                    (
                    ${item.id.escaped()},
                    ${item.planId.escaped()}, 
                    ${item.pointPlanId.escaped()}, 
                    ${item.memberPointId.escaped()}, 
                    ${item.memberId.escaped()}, 
                    ${item.subjectFqn.escaped()}, 
                    ${item.point.escaped()}, 
                    ${item.gainStatementId.escaped()}, 
                    ${item.effectiveDate.escaped()}, 
                    ${item.overdueDate.escaped()},
                    ${item.fromStatus.escaped()}, 
                    ${item.modified.escaped()}, 
                    ${item.created.plus(i++, ChronoUnit.MILLIS).escaped()}, 
                    ${item.openTraceId.escaped()}, 
                    ${item.memberPointId.escaped()}, 
                    ${item.gainStatementId.escaped()}, 
                    ${item.shopId.escaped()}, 
                    ${item.kzzd1.escaped()},
                    ${item.kzzd2.escaped()},
                    ${item.kzzd3.escaped()},
                    ${item.channel.escaped()}
                    ),
                """.trimIndent()
            }
            sql.trim().dropLast(1)
        }

        super.execute(sql, mapOf())
    }

    fun deleteBatchValidPoint(ids: Array<String>, accountTypeId:Long) {
        if (ids.isEmpty()) {
            return
        }
        val bodyParams = HashMap<String, Any?>()
        val paramsItem = HashMap<String, Any?>()
        paramsItem["\$in"] = ids
        bodyParams["id"] = paramsItem
        deleteByFilter(JsonUtils.toJson(bodyParams),replacePattern = accountTypeId.toString())
    }



    // 根据扣减优先级策略，查询可用扣减的有效积分条目
    fun findByPriorities(
        pointAccountTypeId: Long,
        memberPointId: String,
        deductionPriorities: List<DeductionPriority>,
        deductDateTime: ZonedDateTime = ZonedDateTime.now(),
        statusIn: List<PointStateEnum> = emptyList(),
        excludesGsIds: Set<String> = emptySet(),
        openTraceId: String? = null,
        gainStatementId: String? = null,
        validId: String? = null,
        limit: Int = 3000
    ): List<MemberPointValidStatement> {
        val orders = ArrayList<String>()
        for (priority in deductionPriorities) {
            when (priority.order) {
                DeductionOrderEnum.EARLIEST -> orders.add("${priority.name} ASC")
                DeductionOrderEnum.LATEST -> orders.add("${priority.name} DESC")
                else -> {
                    if (priority.equalValue != null) {
                        orders.add("${priority.name} = '${priority.equalValue}' DESC")
                    }
                }
            }
        }
        val params = LinkedHashMap<String, Any>()
        params["memberPointId"] = memberPointId
        var idCondition = ""
        var statusCondition = ""
        var gainStatementCondition = ""
        var openTraceIdCondition = ""
        var excludesGsIdsCondition = ""
        var overdueDateCondition = ""
        if (!validId.isNullOrEmpty()) {
            idCondition = " AND id = (:validId) "
            params["validId"] = validId
        } else {
            if (statusIn.isNotEmpty()) {
                statusCondition = " AND fromStatus IN (:statusIn) "
                params["statusIn"] = statusIn
            }

            overdueDateCondition = "AND overdueDate >= :overdueDate "
            params["overdueDate"] = ZonedDateTime.of(deductDateTime.shDate(), LocalTime.MIN, ZoneId.systemDefault()).utcStr()

            if (gainStatementId != null) {
                gainStatementCondition = "AND gainStatementId = :gainStatementId "
                params["gainStatementId"] = gainStatementId
            }
            if (openTraceId != null) {
                openTraceIdCondition = "AND openTraceId = :openTraceId "
                params["openTraceId"] = openTraceId
            }
            if (excludesGsIds.isNotEmpty()) {
                excludesGsIdsCondition = " AND gainStatementId NOT IN (:excludesGsIds) "
                params["excludesGsIds"] = excludesGsIds
            }
        }

        var orderCondition = ""
        if (orders.isNotEmpty()) {
            orderCondition = " ORDER BY ${orders.joinToString(separator = ",")} "
        }
        val sql = """
            SELECT ${getFields()} 
            FROM ${getModelFqn(pointAccountTypeId)} 
            WHERE 
                memberPointId = :memberPointId 
                $idCondition
                $statusCondition
                $overdueDateCondition
                $gainStatementCondition
                $openTraceIdCondition
                $excludesGsIdsCondition
                $orderCondition
            LIMIT $limit
        """.trimIndent()
        return executeList(sql, params)
    }

    // 根据扣减优先级策略，查询可用扣减的有效积分条目
    fun findByOpenTraceId(
        pointAccountTypeId: Long,
        memberPointId: String,
        openTraceId: String,
        fromStatusIn: List<PointStateEnum> = emptyList(),
        validId: String? = null,
        limit: Int = 3000
    ): List<MemberPointValidStatement> {
        val params = LinkedHashMap<String, Any>()
        params["memberPointId"] = memberPointId
        var traceCondition = ""
        var idCondition = ""
        var statusCondition = ""
        if (!validId.isNullOrEmpty()) {
            idCondition = " AND id = :validId "
            params["validId"] = validId
        } else {
            params["openTraceId"] = openTraceId
            traceCondition = " AND openTraceId = :openTraceId "
            if (fromStatusIn.isNotEmpty()) {
                statusCondition = " AND fromStatus IN (:fromStatusIn) "
                params["fromStatusIn"] = fromStatusIn
            }
        }

        val sql = """
            SELECT ${getFields()} 
            FROM ${getModelFqn(pointAccountTypeId)} 
            WHERE 
                memberPointId = :memberPointId 
                $traceCondition
                $idCondition
                $statusCondition
            LIMIT $limit
        """.trimIndent()
        return executeList(sql, params)
    }


    // 查询已过期的
    fun findExpiredPoints(pointAccountTypeId: Long, memberPointId: String, date: ZonedDateTime = ZonedDateTime.now(), limit: Int = 3000): List<MemberPointValidStatement> {
        val params = LinkedHashMap<String, Any>()
        params["memberPointId"] = memberPointId
        params["overdueDate"] = date.utcStr()
        params["fromStatusIn"] = listOf(PointStateEnum.START, PointStateEnum.VALID, PointStateEnum.FURTHER_VALID)
        val sql = """
            SELECT ${getFields()} 
            FROM ${getModelFqn(pointAccountTypeId)} 
            WHERE 
                memberPointId = :memberPointId
                AND overdueDate < :overdueDate
                AND fromStatus IN (:fromStatusIn)
            ORDER BY overdueDate 
            LIMIT $limit
        """.trimIndent()
        return executeList(sql, params)
    }




    fun findListByGainStatementId(accountTypeId: Long, gainStatementId: String): List<MemberPointValidStatement> {
        val params = HashMap<String, Any?>()
        params["gainStatementId"] = gainStatementId
        return findListByFilter(JsonUtils.toJson(params), replacePattern = accountTypeId.toString())
    }

    fun findListByGainStatementIds(accountTypeId: Long, gainStatementIds: List<String>): List<MemberPointValidStatement> {
        if (gainStatementIds.isEmpty()) return emptyList()
        val params = HashMap<String, Any?>()
        val paramsItem = HashMap<String, Any?>()
        paramsItem["\$nin"] = arrayOf(PointStateEnum.OPEN_FROZE.code,PointStateEnum.SPECIAL_FROZE.code)
        val paramsItem2 = HashMap<String, Any?>()
        paramsItem2["\$in"] = gainStatementIds
        params["fromStatus"] = paramsItem
        params["gainStatementId"] = paramsItem2
        return findListByFilter(JsonUtils.toJson(params), replacePattern = accountTypeId.toString())
    }




    /**只限特殊变更使用*/
    fun findById(id: String, pointPlanId: Long): MemberPointValidStatement? {
        return findById(id,replacePattern = pointPlanId.toString()).getIfPresent()
    }

    /**特殊变更: 根据账户Id，查询所有有效积分*/
    fun findPageValidStatementList(transferInfoList: List<String>,pointPlanId: Long, statusIn: List<String>, pageable: Pageable): Page<MemberPointValidStatement> {
        val params = HashMap<String, Any?>()
        if(transferInfoList.size ==1) {
            params["memberPointId"] = transferInfoList[0]
        } else {
            val paramsItem = HashMap<String, Any?>()
            paramsItem["\$in"] = transferInfoList
            params["memberPointId"] = paramsItem
        }
        val paramsItem = HashMap<String, Any?>()
        paramsItem["\$in"] = statusIn
        params["fromStatus"] = paramsItem

        // 排除过期有效明细
        val overdueDateList = ArrayList<HashMap<String,Any?>>()
        val paramFirst = HashMap<String, Any?>()
        paramFirst["overdueDate"] = mapOf<String,Any?>("\$ge" to DateUtils.zonedDateTime2Day0(ZonedDateTime.now()))
        val paramSecond = HashMap<String, Any?>()
        paramSecond["overdueDate"] = mapOf<String,Any?>("\$is_null" to true)
        overdueDateList.add(paramFirst)
        overdueDateList.add(paramSecond)
        params["\$or"] = overdueDateList
        return findPageByFilter(JsonUtils.toJson(params),"{\"created\": \"desc\"}",pageable,replacePattern = pointPlanId.toString())
    }

    fun findExtendPointList(request: ExtendMemberPointRequest): List<MemberPointValidStatement> {
        if (!request.pointStatusList.contains(PCStatus.VALID)) return emptyList()
        val filterList = mutableListOf<String>()
        filterList.add("""{"memberId": "${request.memberId}" }""")
        filterList.add("""{"pointPlanId": ${request.pointPlanId} }""")
        filterList.add("""{"fromStatus": {"${"$"}ne": "${PointStateEnum.SPECIAL_FROZE}" } }""")
        filterList.add("""{"overdueDate": {"${"$"}ge": ${JsonUtils.toJson(request.pointStartTime)} } }""")
        filterList.add("""{"overdueDate": {"${"$"}le": ${JsonUtils.toJson(request.pointEndTime)} } }""")
        val filter = """{"${"$"}and":[""" + StringUtils.join(filterList, ",") + "]}"
        return findListByFilter(filter, replacePattern = request.pointPlanId.toString())
    }
}

@NotTransactionSafe
@Component
class MemberPointGainStatementService: MemberPointDmBaseService<MemberPointGainStatement>() {
    private val log = LogManager.getLogger(MemberPointGainStatementService::class.java)
    override fun log(): Logger = log


    /** 根据计划、时机、traceId memberPointId 多笔积分获取 */
    fun findByTraceIdList(pointPlanId: Long, eventTypeId: Long, traceId: String, memberPointId: String): List<MemberPointGainStatement> {
        val params = HashMap<String, Any?>()
        params["pointPlanId"] = pointPlanId
        params["eventTypeId"] = eventTypeId
        params["traceId"] = traceId
        params["memberPointId"] = memberPointId
        return findListByFilter(JsonUtils.toJson(params), replacePattern = pointPlanId.toString())
    }


    fun findByMemberPointId(memberPointId: String,accountTypeId: Long, limit: Int = 3000): List<MemberPointGainStatement> {
        val params = HashMap<String, Any?>()
        params["memberPointId"] = memberPointId
        return findListByFilter(JsonUtils.toJson(params), replacePattern = accountTypeId.toString(), limit = limit)
    }


    fun findByIds(ids: List<String>, accountTypeId: Long): List<MemberPointGainStatement> {
        if (ids.isEmpty()) return emptyList()
        val sql = """
            SELECT ${getFields()} 
            FROM ${getModelFqn(accountTypeId)} 
            WHERE id in (:ids) 
        """.trimIndent()
        val params = HashMap<String, Any>()
        params["ids"] = ids
        return executeList(sql, params)
    }



    // 查询待生效的积分(包括冻结的
    fun findPagePendingStatements(transferInfoList: List<String>, pointPlanId: Long, pageable: Pageable): Page<MemberPointGainStatement> {
        if (transferInfoList.isEmpty()) return Page.empty(pageable)
        val params = HashMap<String, Any?>()
        params["effectiveDate"] = mapOf("\$ge" to ZonedDateTime.now())
        params["memberPointId"] = mapOf("\$in" to transferInfoList)
        params["point"] = mapOf("\$gt" to 0)
        params["status"] = mapOf("\$in" to listOf(FSMPointEvent.DELAY_SEND.name, FSMPointEvent.SPECIAL_FREEZE.name))
        return findPageByFilter(JsonUtils.toJson(params),"{\"created\": \"desc\"}",pageable,replacePattern = pointPlanId.toString())
    }


    // 查询应该生效的积分
    fun findToBeEffectivePoints(accountTypeId: Long, memberPointId: String, date: ZonedDateTime, limit: Int = 3000): List<MemberPointGainStatement> {
        if (memberPointId.isEmpty()) return emptyList()
        val sql = """
            SELECT ${getFields()} 
            FROM ${getModelFqn(accountTypeId)} 
            WHERE memberPointId = :memberPointId and status = :status and effectiveDate < :effectiveDate
            LIMIT $limit
        """.trimIndent()
        val params = HashMap<String, Any>()
        params["memberPointId"] = memberPointId
        params["status"] = FSMPointEvent.DELAY_SEND.name
        params["effectiveDate"] = date.utcStr()
        return executeList(sql, params)
    }


    fun findByGainStatementId(gainStatementId: String, accountTypeId:Long): MemberPointGainStatement? {
        val paramsItem = HashMap<String, Any?>()
        paramsItem["id"] = gainStatementId
        return findOneByFilter(JsonUtils.toJson(paramsItem), accountTypeId.toString()).getIfPresent()
    }

    fun updateStatusByIds(accountTypeId: Long, ids: List<String>, status: FSMPointEvent) {
        val params = HashMap<String, Any?>()
        params["ids"] = ids
        params["status"] = status.name
        val sql = """
            UPDATE ${getModelFqn(accountTypeId)} 
            SET status = :status 
            WHERE id in (:ids)
        """.trimIndent()
        execute(sql, params)
    }


    override fun batchInsert(dataList: List<MemberPointGainStatement>, replacePattern: String?) {
        if (dataList.isEmpty()) return
        val sql = SQL.build {
            var sql = """
                INSERT INTO data.loyalty.member.account.GainStatement$replacePattern  
                    (
                        `id`, `planId`, `pointPlanId`, `memberPointId`, `memberId`, `subjectFqn`, `point`, `status`, 
                        `eventTypeId`, `traceId`, `effectiveDate`, `overdueDate`, `modified`, `created`, `ruleGroupId`, 
                        `occurrenceTs`, `ruleId`, `memberPoint`
                    ) 
                    VALUES 
                """.trimIndent()
            var i = 0L
            for (item in dataList) {
                sql += """
                    (
                    ${item.id.escaped()},
                    ${item.planId.escaped()}, 
                    ${item.pointPlanId.escaped()}, 
                    ${item.memberPointId.escaped()}, 
                    ${item.memberId.escaped()}, 
                    ${item.subjectFqn.escaped()}, 
                    ${item.point.escaped()}, 
                    ${item.status.escaped()}, 
                    ${item.eventTypeId.escaped()}, 
                    ${item.traceId.escaped()}, 
                    ${item.effectiveDate.escaped()}, 
                    ${item.overdueDate.escaped()},
                    ${item.modified.escaped()}, 
                    ${item.created.plus(i++, ChronoUnit.MILLIS).escaped()}, 
                    ${item.ruleGroupId.escaped()}, 
                    ${item.occurrenceTs.escaped()}, 
                    ${item.ruleId.escaped()}, 
                    ${item.memberPointId.escaped()}
                    ),
                """.trimIndent()
            }
            sql.trim().dropLast(1)
        }
        super.execute(sql, mapOf())
    }
}

@NotTransactionSafe
@Component
class MemberPointFrozenStatementService: MemberPointDmBaseService<MemberPointFrozenStatement>() {
    private val log = LogManager.getLogger(MemberPointFrozenStatementService::class.java)
    override fun log(): Logger = log

    fun findFrozenStatementByFrozenId(frozenId: String,accountTypeId: Long, fromType: FromTypeEnum? = null): List<MemberPointFrozenStatement> {
        val params = HashMap<String, Any?>()
        params["frozenId"] = frozenId
        if(fromType != null) {
            params["fromType"] = FromTypeEnum.NEGATIVE
        }
        return findListByFilter(JsonUtils.toJson(params), replacePattern = accountTypeId.toString())
    }

    fun deleteByFrozenId(frozenId: String,accountTypeId: Long) {
        val params = HashMap<String, Any?>()
        params["frozenId"] = frozenId
        deleteByFilter(JsonUtils.toJson(params),replacePattern = accountTypeId.toString())
    }



    /**
     * 查询会员冻结来源是负积分的明细记录
     */
    fun findNegativeByMemberPointId(memberPointId: String,accountTypeId:Long): List<MemberPointFrozenStatement> {
        val params = HashMap<String, Any?>()
        params["memberPointId"] = memberPointId
        params["fromType"] = FromTypeEnum.NEGATIVE
        return findListByFilter(JsonUtils.toJson(params), replacePattern = accountTypeId.toString())
    }




    fun findByFrozenIds(accountTypeId: Long, frozenIds: List<String>): List<MemberPointFrozenStatement> {
        if (frozenIds.isEmpty()) return emptyList()
        val sql = """
            SELECT ${getFields()} 
            FROM ${getModelFqn(accountTypeId)} 
            WHERE frozenId in (:frozenIds)
            LIMIT 10000
        """.trimIndent()
        val params = HashMap<String, Any>()
        params["frozenIds"] = frozenIds
        return executeList(sql, params)
    }

    override fun batchInsert(dataList: List<MemberPointFrozenStatement>, replacePattern: String?) {
        if (dataList.isEmpty()) return
        val sql = SQL.build {
            var sql = """
                INSERT INTO data.loyalty.member.account.FrozenStatement$replacePattern
                (
                 `id`,`planId`,`pointPlanId`,`memberPointId`,`memberId`,`subjectFqn`,
                 `point`,`gainStatementId`,`effectiveDate`,`overdueDate`,`fromStatus`,
                 `frozenId`,`fromStatementId`,`fromType`,`modified`,`created`, 
                 `memberFrozenPoint`, `memberPoint`, `gainStatement`,`data`
                 )
                VALUES 
            """.trimIndent()
            var i = 0L
            for (item in dataList) {
                sql += """
                    (
                    ${item.id.escaped()},
                    ${item.planId.escaped()}, 
                    ${item.pointPlanId.escaped()}, 
                    ${item.memberPointId.escaped()}, 
                    ${item.memberId.escaped()}, 
                    ${item.subjectFqn.escaped()}, 
                    ${item.point.escaped()}, 
                    ${item.gainStatementId.escaped()}, 
                    ${item.effectiveDate.escaped()}, 
                    ${item.overdueDate.escaped()},
                    ${item.fromStatus.escaped()}, 
                    ${item.frozenId.escaped()}, 
                    ${item.fromStatementId.escaped()}, 
                    ${item.fromType.escaped()}, 
                    ${item.modified.escaped()}, 
                    ${item.created.plus(i++, ChronoUnit.MILLIS).escaped()},
                    ${item.frozenId.escaped()}, 
                    ${item.memberPointId.escaped()}, 
                    ${item.gainStatementId.escaped()},
                    ${item.data.escaped()}
                    ),
                """.trimIndent()
            }
            sql.trim().dropLast(1)
        }

        super.execute(sql, mapOf())
    }

    fun deleteByIds(deleteFSIds: List<String>, accountTypeId: Long) {
        if (deleteFSIds.isEmpty()) {
            return
        }
        val bodyParams = HashMap<String, Any?>()
        val paramsItem = HashMap<String, Any?>()
        paramsItem["\$in"] = deleteFSIds
        bodyParams["id"] = paramsItem
        deleteByFilter(JsonUtils.toJson(bodyParams),replacePattern = accountTypeId.toString())
    }
}

@NotTransactionSafe
@Component
class MemberPointRecordService: MemberPointDmBaseService<MemberPointRecord>() {
    private val log = LogManager.getLogger(MemberPointRecordService::class.java)
    override fun log(): Logger = log


    fun findByTraceIdList(memberPointId: String, traceId: String,accountTypeId: Long, limit: Int = 3000): List<MemberPointRecord> {
        val params = HashMap<String, Any?>()
        params["traceId"] = traceId
        params["memberPointId"] = memberPointId
        return findListByFilter(JsonUtils.toJson(params), replacePattern = accountTypeId.toString(), limit = limit)
    }


    fun findByMemberIdAndTraceIdList(accountTypeId: Long, memberId: String, traceId: String, limit: Int = 3000): List<MemberPointRecord> {
        val params = HashMap<String, Any?>()
        params["traceId"] = traceId
        params["memberId"] = memberId
        return findListByFilter(JsonUtils.toJson(params), replacePattern = accountTypeId.toString(), limit = limit)
    }


    override fun batchInsert(dataList: List<MemberPointRecord>, replacePattern: String?) {
        if (dataList.isEmpty()) return
        val sql = SQL.build {
            var sql = """
                INSERT INTO data.loyalty.member.account.PointRecord$replacePattern
                 (`id`,`actionId`,`actionName`,`actionNodeId`,`actionNodeName`,`changeMode`,`changePoint`,`channel`,`created`,`desc`,`effectiveDate`,`eventTypeName`,`extralInfo`,`key`,`KZZD1`,`KZZD2`,`KZZD3`,`memberId`,`memberPoint`,`memberPointId`,`modified`,`operator`,`operatorId`,`overdueDate`,`planId`,`planName`,`point`,`pointPlanId`,`pointPlanName`,`recordDetail`,`recordSourceDetail`,`recordType`,`ruleGroup`,`ruleId`,`ruleName`,`shopId`,`status`,`subjectFqn`,`totalPoint`,`traceId`)
                VALUES 
            """.trimIndent()
            var i = 0L
            for (item in dataList) {
                val overdueDate = if(item.overdueDate?.isEqual(ConstantValue.LONG_TERM_OVERDUE_DATE) == true) null else item.overdueDate
                sql += """
                    (
                    ${item.id.escaped()},
                    ${item.actionId.escaped()},
                    ${item.actionName.escaped()},
                    ${item.actionNodeId.escaped()},
                    ${item.actionNodeName.escaped()},
                    ${item.changeMode.escaped()},
                    ${item.changePoint.escaped()},
                    ${item.channel.escaped()},
                    ${item.created.plus(i++, ChronoUnit.MILLIS).escaped()},
                    ${item.desc.escaped()},
                    ${item.effectiveDate.escaped()},
                    ${item.eventTypeName.escaped()},
                    ${item.extralInfo.escaped()},
                    ${item.key.escaped()},
                    ${item.KZZD1.escaped()},
                    ${item.KZZD2.escaped()},
                    ${item.KZZD3.escaped()},
                    ${item.memberId.escaped()},
                    ${item.memberPointId.escaped()},
                    ${item.memberPointId.escaped()},
                    ${item.modified.escaped()},
                    ${item.operator.escaped()},
                    ${item.operatorId.escaped()},
                    ${overdueDate.escaped()},
                    ${item.planId.escaped()},
                    ${item.planName.escaped()},
                    ${item.point.escaped()},
                    ${item.pointPlanId.escaped()},
                    ${item.pointPlanName.escaped()},
                    ${item.recordDetail.escaped()},
                    ${item.recordSourceDetail.escaped()},
                    ${item.recordType.escaped()},
                    ${item.ruleGroup.escaped()},
                    ${item.ruleId.escaped()},
                    ${item.ruleName.escaped()},
                    ${item.shopId.escaped()},
                    ${item.status.escaped()},
                    ${item.subjectFqn.escaped()},
                    ${item.totalPoint.escaped()},
                    ${item.traceId.escaped()}
                    ),
                """.trimIndent()
            }
            sql.trim().dropLast(1)
        }
        super.execute(sql, mapOf())
    }

}

@NotTransactionSafe
@Component
class PointExpireRemindRecordService: MemberPointDmBaseService<PointExpireRemindRecord>() {
    private val log = LogManager.getLogger(PointExpireRemindRecordService::class.java)
    override fun log(): Logger = log

    @DbNodeSelector(DbNodeSelector.Hint.AUTO)
    fun findByMemberIdsPage(memberIds: List<String>, accountTypeId: Long, pageable: Pageable, remindId: Long? = null): Page<PointExpireRemindRecord> {
        val bodyParams = HashMap<String, Any?>()
        val paramsItem = HashMap<String, Any?>()
        paramsItem["\$in"] = memberIds
        bodyParams["memberId"] = paramsItem
        if (remindId != null) {
            bodyParams["remindId"] = remindId
        }
        return findPageByFilter(JsonUtils.toJson(bodyParams),"{\"created\": \"desc\"}", pageable, accountTypeId.toString())
    }

}

@NotTransactionSafe
@Component
class PointDelayRemindRecordService: MemberPointDmBaseService<PointDelayRemindRecord>() {
    private val log = LogManager.getLogger(PointDelayRemindRecordService::class.java)
    override fun log(): Logger = log

    @DbNodeSelector(DbNodeSelector.Hint.AUTO)
    fun findByMemberIdsPage(memberIds: List<String>, accountTypeId: Long, pageable: Pageable, remindId: Long? = null): Page<PointDelayRemindRecord> {
        val bodyParams = HashMap<String, Any?>()
        val paramsItem = HashMap<String, Any?>()
        paramsItem["\$in"] = memberIds
        bodyParams["memberId"] = paramsItem
        if (remindId != null) {
            bodyParams["remindId"] = remindId
        }
        return findPageByFilter(JsonUtils.toJson(bodyParams),"{\"created\": \"desc\"}", pageable, accountTypeId.toString())
    }

}

@NotTransactionSafe
@Component
class PointRecordItemService: MemberPointDmBaseService<PointRecordItem>() {
    private val log = LogManager.getLogger(PointRecordItemService::class.java)
    override fun log(): Logger = log


    override fun save(t: PointRecordItem, replacePattern: String?): PointRecordItem {
        if (t.point < BigDecimal.ZERO && t.status != FSMPointEvent.EXPIRE.name) {
            t.effectiveDate = null
            t.overdueDate = null
        }
        return super.save(t, replacePattern)
    }


    override fun batchInsert(dataList: List<PointRecordItem>, replacePattern: String?) {
        if (dataList.isEmpty()) return
        val sql = SQL.build {
            var sql = """
                INSERT INTO data.loyalty.member.account.PointRecordItem$replacePattern
                 (`id`,`planId`,`status`,`sort`,`pointPlanId`,`traceId`,`memberId`,`point`,`created`,`effectiveDate`,`overdueDate`,`backId`,`parentBackId`,`recordId`,`member`)
                VALUES 
            """.trimIndent()
            var sort = 0L
            for (item in dataList) {
                if (item.created == null) {
                    item.created = ZonedDateTime.now()
                }
                sql += """
                    (
                    ${item.id.escaped()},
                    ${item.planId.escaped()}, 
                    ${item.status.escaped()}, 
                    ${sort++}, 
                    ${item.pointPlanId.escaped()}, 
                    ${item.traceId.escaped()}, 
                    ${item.memberId.escaped()}, 
                    ${item.point.escaped()}, 
                    ${item.created!!.plus(sort, ChronoUnit.MILLIS).escaped()}, 
                    ${item.effectiveDate.escaped()}, 
                    ${item.overdueDate.escaped()},
                    ${item.backId.escaped()},
                    ${item.parentBackId.escaped()},
                    ${item.recordId.escaped()},
                    ${item.memberId.escaped()}
                    ),
                """.trimIndent()
            }
            sql.trim().dropLast(1)
        }

        super.execute(sql, mapOf())
    }


}