package com.shuyun.loyalty.service.infrastructure.epassport

import org.springframework.context.EmbeddedValueResolverAware
import org.springframework.core.annotation.AnnotationUtils
import org.springframework.stereotype.Controller
import org.springframework.util.StringValueResolver
import org.springframework.web.accept.ContentNegotiationManager
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.RequestMappingInfo
import org.springframework.web.servlet.mvc.method.RequestMappingInfo.BuilderConfiguration
import org.springframework.web.servlet.mvc.method.RequestMappingInfoHandlerMapping
import org.springframework.web.util.pattern.PathPatternParser
import java.lang.reflect.Method


open class RequestMappingTool : RequestMappingInfoHandlerMapping(), EmbeddedValueResolverAware {
    private val contentNegotiationManager = ContentNegotiationManager()
    private val fileExtensions: MutableList<String?> = ArrayList()

    private val builderConfig = BuilderConfiguration().apply {
        setContentNegotiationManager(<EMAIL>)
        patternParser = PathPatternParser()
    }

    private fun buildInfo(
        patterns: Array<String>,
        methods: Array<RequestMethod>,
        params: Array<String>,
        headers: Array<String>,
        consumes: Array<String>,
        produces: Array<String>
    ): RequestMappingInfo {
        val builder = RequestMappingInfo
            .paths(*patterns)
            .methods(*methods)
            .params(*params)
            .headers(*headers)
            .consumes(*consumes)
            .produces(*produces)
            .options(builderConfig)
        return builder.build()
    }


    private fun createRequestMappingInfo(
        patterns: Array<String>,
        methods: Array<RequestMethod>,
        params: Array<String>,
        headers: Array<String>,
        consumes: Array<String>,
        produces: Array<String>
    ): RequestMappingInfo = buildInfo(patterns, methods, params, headers, consumes, produces)

    private var embeddedValueResolver: StringValueResolver? = null
    override fun setEmbeddedValueResolver(resolver: StringValueResolver) {
        embeddedValueResolver = resolver
    }

    override fun afterPropertiesSet() {
        val useRegisteredSuffixPatternMatch = false
        if (useRegisteredSuffixPatternMatch) {
            fileExtensions.addAll(contentNegotiationManager.allFileExtensions)
        }
        super.afterPropertiesSet()
    }

    override fun isHandler(beanType: Class<*>): Boolean {
        return AnnotationUtils.findAnnotation(beanType, Controller::class.java) != null
                || AnnotationUtils.findAnnotation(beanType, RequestMapping::class.java) != null
    }

    public override fun getMappingForMethod(method: Method, handlerType: Class<*>): RequestMappingInfo? {
        var methodInfo: RequestMappingInfo? = null
        for (annotation in method.declaredAnnotations) {
            when (annotation) {
                is RequestMapping -> methodInfo = createRequestMappingInfo(
                    resolveEmbeddedValuesInPatterns(annotation.value),
                    annotation.method,
                    annotation.params,
                    annotation.headers,
                    annotation.consumes,
                    annotation.produces
                )

                is GetMapping -> methodInfo = createRequestMappingInfo(
                    resolveEmbeddedValuesInPatterns(annotation.value),
                    arrayOf(RequestMethod.GET),
                    annotation.params,
                    annotation.headers,
                    annotation.consumes,
                    annotation.produces
                )

                is PostMapping -> methodInfo = createRequestMappingInfo(
                    resolveEmbeddedValuesInPatterns(annotation.value),
                    arrayOf(RequestMethod.POST),
                    annotation.params,
                    annotation.headers,
                    annotation.consumes,
                    annotation.produces
                )

                is PutMapping -> methodInfo = createRequestMappingInfo(
                    resolveEmbeddedValuesInPatterns(annotation.value),
                    arrayOf(RequestMethod.PUT),
                    annotation.params,
                    annotation.headers,
                    annotation.consumes,
                    annotation.produces
                )

                is PatchMapping -> methodInfo = createRequestMappingInfo(
                    resolveEmbeddedValuesInPatterns(annotation.value),
                    arrayOf(RequestMethod.PATCH),
                    annotation.params,
                    annotation.headers,
                    annotation.consumes,
                    annotation.produces
                )

                is DeleteMapping -> methodInfo = createRequestMappingInfo(
                    resolveEmbeddedValuesInPatterns(annotation.value),
                    arrayOf(RequestMethod.DELETE),
                    annotation.params,
                    annotation.headers,
                    annotation.consumes,
                    annotation.produces
                )
            }
        }

        if (methodInfo != null) {
            val typeAnnotation = AnnotationUtils.findAnnotation(handlerType, RequestMapping::class.java)
            if (typeAnnotation != null) {
                val typeInfo = createRequestMappingInfo(
                    resolveEmbeddedValuesInPatterns(typeAnnotation.value),
                    typeAnnotation.method,
                    typeAnnotation.params,
                    typeAnnotation.headers,
                    typeAnnotation.consumes,
                    typeAnnotation.produces
                )
                methodInfo = typeInfo.combine(methodInfo)
            }
        }
        return methodInfo
    }


    private fun resolveEmbeddedValuesInPatterns(patterns: Array<String>): Array<String> {
        return if (embeddedValueResolver == null) {
            patterns
        } else {
            val resolvedPatterns = ArrayList<String>()
            for (i in patterns.indices) {
                resolvedPatterns.add(embeddedValueResolver!!.resolveStringValue(patterns[i])!!)
            }
            resolvedPatterns.toTypedArray()
        }
    }
}