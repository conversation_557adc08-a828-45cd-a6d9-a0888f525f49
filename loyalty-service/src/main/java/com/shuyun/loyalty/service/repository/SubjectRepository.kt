package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.model.Subject
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.util.converter.ZonedDateTime2StringConverter
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component
import java.time.ZonedDateTime
import java.util.*

@Component
class SubjectRepository : DataModelCarryRepository<Subject>() {

    private val log = LogManager.getLogger(SubjectRepository::class.java)
    override fun log(): Logger = log

    fun findPageByPlanVersionIdAndDisabled(planVersionIdList: List<Long>, disabled: Boolean): List<Subject> {
        if(planVersionIdList.isEmpty()) return arrayListOf()
        val params = HashMap<String, Any?>()
        params["disabled"] = disabled
        val list = ArrayList<HashMap<String,Any?>>()
        if(planVersionIdList.size ==1) {
            params["planVersionId"] = planVersionIdList[0]
        } else {
            planVersionIdList.forEach {
                val paramsItem = HashMap<String, Any?>()
                paramsItem["planVersionId"] = it
                list.add(paramsItem)
            }
            params["\$or"] = list
        }
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findByPlanVersionIdAndDisabled(planVersionId: Long, disabled: Boolean, ids:List<Long>? = null): List<Subject> {
        val params = HashMap<String, Any?>()
        params["planVersionId"] = planVersionId
        params["disabled"] = disabled
        if(!ids.isNullOrEmpty()) {
            val idParam = HashMap<String, Any?>()
            idParam["\$in"] = ids
            params["id"] = idParam
        }
        return findListByFilter(JsonUtils.toJson(params))
    }

    fun findByPlanVersionId(planVersionId: Long): List<Subject> {
        val params = HashMap<String, Any?>()
        params["planVersionId"] = planVersionId
        return findListByFilter(JsonUtils.toJson(params))
    }


    fun findByIdAndDate(id:Long, date: ZonedDateTime): Optional<Subject> {
        val sql = "select s.name,s.planVersionId,s.dataType,s.id,s.status,s.sort,s.versionId " +
                "from data.loyalty.manager.subject s " +
                "inner join data.loyalty.manager.plan p on s.planVersionId = p.versionId " +
                "where s.disabled=0 and p.disabled=0 and s.id = :id and (p.status='PUBLISHED' OR p.status = 'FILED') " +
                "and s.status = 'ENABLED' and p.publishedTime<=:date and (p.filedTime>=:date or p.filedTime is null) order by s.versionId desc"
        val params = HashMap<String, Any>()
        params["id"] = id
        params["date"] = ZonedDateTime2StringConverter().convert(date)!!
        return executeOne(sql, params)
    }

    fun findByVersionId(versionId: Long): Optional<Subject> {
        val params = HashMap<String, Any?>()
        params["versionId"] = versionId
        return findOneByFilter(JsonUtils.toJson(params))
    }
}

