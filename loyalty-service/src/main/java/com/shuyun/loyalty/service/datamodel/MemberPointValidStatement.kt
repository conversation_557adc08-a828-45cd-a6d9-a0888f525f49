package com.shuyun.loyalty.service.datamodel

import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.annotation.FqnVariableModel
import com.shuyun.loyalty.service.service.MemberPointValidStatementService
import com.shuyun.pip.ApplicationContextHolder
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import java.math.BigDecimal
import java.time.ZonedDateTime
import javax.persistence.Column
import javax.persistence.JoinColumn
import javax.persistence.ManyToOne
import javax.persistence.Table

/**
 * 有效积分账户明细记录。
 * 当预扣时会使用该记录明细，当取消时，需要按这个进行归还。
 * 消费时，根据该账户进行消费。
 * 会员有效积分明细，历史获取、使用部分之后的扣除剩余等。
 *
 * 1、积分发放时，会增加积分获取记录，该记录的id将作为该次发放积分内部流转转源的id。不管是在有效积分明细，锁定明细、冻结明细中，都需要带着该信息
 * 2、当退单时，是最麻烦的，需要根据事件信息，分析出历史原单获取记录ID。然后使用该id追查记录做后续一系列动作
 * 3、内部流转均需要携带gainId。只在外部是，先分析历史获取记录，再根据获取记录追查所有明细
 */
@DataServiceModel
@FqnVariableModel
@Table(name = "data.loyalty.member.account.ValidStatement{*}")
open class MemberPointValidStatement : MemberPointStatement() {

    @Column
    var openTraceId: String? = null


    // 渠道
    @Column
    var channel: String? = null

    // 店铺
    @Column
    var shopId: String? = null

    // 扩展字段1
    @Column
    var kzzd1: String? = null

    // 扩展字段1
    @Column
    var kzzd2: String? = null

    // 扩展字段3
    @Column
    var kzzd3: String? = null

    @ManyToOne
    @JoinColumn(name = "gainStatementId")
    @Suppress("unused")
    private var gainStatement: MemberPointGainStatement? = null

    // 临时变量
    @Transient
    var backId: String? = null

    @Transient
    var subPoints: BigDecimal = BigDecimal.ZERO

    open fun saveOrUpdate(accountTypeId: Long) = ApplicationContextHolder.getBean(MemberPointValidStatementService::class.java).saveOrUpdate(this, replacePattern = accountTypeId.toString())
    open fun delete(accountTypeId: Long) = ApplicationContextHolder.getBean(MemberPointValidStatementService::class.java).delete(id!!, replacePattern = accountTypeId.toString())

    open fun save(accountTypeId: Long) = ApplicationContextHolder.getBean(MemberPointValidStatementService::class.java).save(this, replacePattern = accountTypeId.toString())

    /**特殊变更：根据账户Id，查询所有有效积分*/
    fun findPageValidStatementList(transferInfoList: List<String>,pointPlanId: Long, pageable: Pageable): Page<MemberPointValidStatement> {
        val status = listOf(PointStateEnum.SPECIAL_FROZE.code,PointStateEnum.START.code,PointStateEnum.VALID.code,PointStateEnum.FURTHER_VALID.code)
        return ApplicationContextHolder.getBean(MemberPointValidStatementService::class.java).findPageValidStatementList(transferInfoList,pointPlanId, status, pageable)
    }

    // 复制
    fun copy(id: String? = null, point: BigDecimal? = null, status: PointStateEnum? = null , openTraceId: String? = null, newOverdueDate: ZonedDateTime? = null): MemberPointValidStatement {
        val statement = MemberPointValidStatement()
        statement.id = id
        statement.pointPlanId = pointPlanId
        statement.point = point ?: this.point
        statement.memberPointId = memberPointId
        statement.subjectFqn = subjectFqn
        statement.channel = channel
        statement.shopId = shopId
        statement.kzzd1 = kzzd1
        statement.kzzd2 = kzzd2
        statement.kzzd3 = kzzd3
        statement.memberId = memberId
        statement.planId = planId
        statement.fromStatus = status ?: this.fromStatus
        statement.overdueDate = newOverdueDate ?: overdueDate
        statement.effectiveDate = effectiveDate
        statement.created = created
        statement.modified = ZonedDateTime.now()
        statement.gainStatementId = gainStatementId
        statement.openTraceId = openTraceId
        statement.backId = backId
        statement.subPoints = subPoints
        return statement
    }

}