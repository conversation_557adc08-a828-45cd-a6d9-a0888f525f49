package com.shuyun.loyalty.service.transfer.points

import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.PCStatus
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.service.datamodel.*
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.model.PointDeductRuleOperate
import com.shuyun.loyalty.service.util.sendNotify
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.ZonedDateTime
import java.util.*
import kotlin.jvm.optionals.getOrNull

// 积分作废
@Service
class MemberPointInvalidTransfer : MemberPointBaseTransfer() {



    fun invalidFrozen(lp: LoyaltyPoints, gs: MemberPointGainStatement, frozenPoint: MemberFrozenPoint) {

        // 作废已生效积分
        if (lp.deductRule!!.quotaType == PointDeductRuleOperate.QuotaTypeEnum.ZERO) {
            val zeroRecord = initPointRecord(
                lp, Uuid.uuid, FSMPointEvent.ABOLISH,
                points = BigDecimal.ZERO,
                changePoints = BigDecimal.ZERO,
                totalPoints = lp.member.point,
                pcStatus = PCStatus.FROZEN_ABOLISH
            ).apply {
                this.recordDetail = "积分作废规则配额为0"
            }
            zeroRecord.insert(lp.hierarchy.id)
            listOf(zeroRecord).sendNotify()
            return
        }

        gs.status = FSMPointEvent.ABOLISH
        gs.modified = ZonedDateTime.now()
        gs.saveOrUpdate(lp.hierarchy.id)

        // 暂时更新成负值，防止重复作废
        frozenPoint.point = -frozenPoint.point.abs()
        frozenPoint.saveOrUpdate(lp.hierarchy.id)

        lp.afterTotalPoints = lp.member.memberPoint!!.point

        // 保存
        val journalKey = lp.attr.uniqueId + "-" + gs.id
        lp.buildJournal(PointAction.INVALID_FROZEN, journalKey).save()
    }


    fun invalidSend(lp: LoyaltyPoints, gs: MemberPointGainStatement) {

        // 作废已生效积分
        if (lp.deductRule!!.quotaType == PointDeductRuleOperate.QuotaTypeEnum.ZERO) {
            val zeroRecord = initPointRecord(
                lp, Uuid.uuid, FSMPointEvent.ABOLISH,
                points = BigDecimal.ZERO,
                changePoints = BigDecimal.ZERO,
                totalPoints = lp.member.point,
                pcStatus = if (gs.status == FSMPointEvent.DELAY_SEND) PCStatus.DELAY_ABOLISH else PCStatus.ABOLISH
            ).apply {
                this.recordDetail = "积分作废规则配额为0"
            }
            zeroRecord.insert(lp.hierarchy.id)
            listOf(zeroRecord).sendNotify()
            return
        }

        // 保存
        val journal = invalidNotMatched(
            lp,
            gs,
            lp.pointRuleGroup,
            lp.deductRule,
            lp.attr.desc,
            PointAction.INVALID_SEND
        )

        gs.status = FSMPointEvent.ABOLISH
        gs.modified = ZonedDateTime.now()
        gs.saveOrUpdate(lp.hierarchy.id)

        // 保存总积分变更日志
        lp.member.memberPoint!!.update()
        lp.member.memberPoint!!.saveLog()

        journal.save()
    }


    fun transfer(lp: LoyaltyPoints, action: PointAction) {
        when (action) {
            PointAction.INVALID_SEND -> {
                val gainStatementId = lp.attr.businessId!!
                val records = ArrayList<MemberPointRecord>()
                val items = ArrayList<PointRecordItem>()
                val segments = ArrayList<MemberPointSegment>()
                val gs =
                    gainStatementService.findById(gainStatementId, lp.hierarchy.id.toString()).getOrNull() ?: return
                if (lp.attr.pendingPoints == true) {
                    val record = initPointRecord(
                        lp, Uuid.uuid, FSMPointEvent.ABOLISH,
                        points = lp.pointValue,
                        changePoints = -lp.afterPoints,
                        totalPoints = lp.afterTotalPoints,
                        pcStatus = PCStatus.DELAY_ABOLISH
                    ).apply {
                        recordDetail = "事件作废待生效积分"
                    }
                    val item = initRecordItem(
                        lp,
                        Uuid.uuid,
                        record.id!!, BigDecimal.ZERO, FSMPointEvent.ABOLISH, parentBackId = gs.id
                    )
                    items.add(item)
                    records.add(record)
                } else {
                    // 作废已生效的积分
                    val record = initPointRecord(
                        lp, Uuid.uuid, FSMPointEvent.ABOLISH,
                        points = lp.afterPoints,
                        changePoints = -lp.afterPoints,
                        totalPoints = lp.afterTotalPoints,
                        pcStatus = PCStatus.ABOLISH
                    ).apply {
                        recordDetail = "事件作废已生效积分"
                    }

                    // 查询大于扣减时间的所有积分块
                    val segment1 =
                        segmentService.findDateAfter(lp.hierarchy.id, lp.member.id, lp.date.shDate()).filter {
                            it.expireDate!!.isAfter(lp.member.leftSegmentDate) && it.point >= BigDecimal.ZERO
                        }
                    segments.addAll(segment1)

                    // 顺序扣减
                    // 优先作废当前原单对应的有效积分
                    val fetch = vssFetchFunc(gainStatementId)
                    val items1 = sequentiallyDeduct(
                        lp,
                        lp.pointValue,
                        fetch,
                        segments,
                        lp.hierarchy.negativeStrategy!!,
                        record.id!!,
                        FSMPointEvent.ABOLISH,
                        FSMPointEvent.DEDUCT,
                        FSMPointEvent.ABOLISH,
                        primaryGSId = gainStatementId
                    )
                    items.addAll(items1)
                    records.add(record)
                }
                batchUpdateRecordAndSegment(lp.hierarchy.id, records, items, segments)
            }

            PointAction.INVALID_FROZEN -> {
                val gainStatementId = lp.attr.businessId!!
                val fp = frozenPointService.findFrozenPointByGainStatementId(gainStatementId, lp.hierarchy.id) ?: return

                val recordId = Uuid.uuid
                val record = initPointRecord(
                    lp, recordId, FSMPointEvent.ABOLISH,
                    points = lp.pointValue,
                    changePoints = BigDecimal.ZERO,
                    totalPoints = lp.afterTotalPoints,
                    pcStatus = PCStatus.FROZEN_ABOLISH
                ).apply {
                    recordDetail = "事件作废冻结积分"
                }

                val items = ArrayList<PointRecordItem>()

                val fss = frozenStatementService.findFrozenStatementByFrozenId(fp.id!!, lp.hierarchy.id)
                val fssIds = fss.map { it.id!! }
                // 作废待生效的冻结积分
                if (fp.fromStatus == FSMPointEvent.DELAY_SEND) {
                    val item = initRecordItem(
                        lp,
                        Uuid.uuid,
                        recordId, BigDecimal.ZERO, FSMPointEvent.ABOLISH, parentBackId = fp.id
                    )
                    items.add(item)
                } else {
                    fss.forEach {
                        val item = initRecordItem(
                            lp,
                            Uuid.uuid,
                            recordId, BigDecimal.ZERO, FSMPointEvent.ABOLISH, parentBackId = it.id
                        )
                        items.add(item)
                    }
                }
                fp.delete(lp.hierarchy.id)
                frozenStatementService.deleteByIds(fssIds, lp.hierarchy.id)
                batchUpdateRecordAndSegment(lp.hierarchy.id, listOf(record), items, listOf())
            }

            PointAction.INVALID_ALL -> {
                val gss = gainStatementService.findByMemberPointId(lp.member.id, lp.hierarchy.id, limit = 10000)
                val gssQueue: Queue<MemberPointGainStatement> = LinkedList(gss)
                val records = ArrayList<MemberPointRecord>()
                val items = ArrayList<PointRecordItem>()
                val vssIds = ArrayList<String>()
                var p = lp.member.point

                while (true) {
                    val gs = gssQueue.poll() ?: break
                    if (gs.status == FSMPointEvent.DELAY_SEND) {
                        val record = initPointRecord(
                            lp, Uuid.uuid, FSMPointEvent.ABOLISH,
                            points = gs.point,
                            changePoints = BigDecimal.ZERO,
                            totalPoints = p,
                            pcStatus = PCStatus.DELAY_ABOLISH
                        ).apply {
                            recordDetail = "作废全部-待生效积分"
                        }
                        val item = initRecordItem(
                            lp,
                            Uuid.uuid,
                            record.id!!, BigDecimal.ZERO, FSMPointEvent.ABOLISH, parentBackId = gs.id
                        )
                        items.add(item)
                        records.add(record)
                    } else if (gs.status == FSMPointEvent.SEND) {
                        // 作废已生效的积分
                        val record = initPointRecord(
                            lp, Uuid.uuid, FSMPointEvent.ABOLISH,
                            points = BigDecimal.ZERO,
                            changePoints = BigDecimal.ZERO,
                            totalPoints = BigDecimal.ZERO,
                            pcStatus = PCStatus.ABOLISH
                        ).apply {
                            recordDetail = "作废全部-已生效积分"
                        }

                        val vss = validService.findListByGainStatementId(lp.hierarchy.id, gs.id!!)
                        var recordPoint = BigDecimal.ZERO
                        for (vs in vss) {
                            if (vs.fromStatus == PointStateEnum.SPECIAL_FROZE || vs.fromStatus == PointStateEnum.OPEN_FROZE) {
                                val item = initRecordItem(
                                    lp,
                                    Uuid.uuid,
                                    record.id!!, BigDecimal.ZERO, FSMPointEvent.ABOLISH, parentBackId = vs.id
                                )
                                items.add(item)
                            } else {
                                val item = initRecordItem(
                                    lp,
                                    Uuid.uuid,
                                    record.id!!, -vs.point, FSMPointEvent.ABOLISH, parentBackId = vs.id
                                )
                                items.add(item)
                                p -= vs.point
                                recordPoint += vs.point
                            }
                            vssIds.add(vs.id!!)
                        }

                        record.point = recordPoint
                        record.changePoint = -recordPoint
                        record.totalPoint = p
                        records.add(record)
                    } else if (gs.status == FSMPointEvent.FREEZE) {
                        val fp = frozenPointService.findFrozenPointByGainStatementId(gs.id!!, lp.hierarchy.id) ?: return
                        val recordId = Uuid.uuid
                        val record = initPointRecord(
                            lp, recordId, FSMPointEvent.ABOLISH,
                            points = BigDecimal.ZERO,
                            changePoints = BigDecimal.ZERO,
                            totalPoints = BigDecimal.ZERO,
                            pcStatus = PCStatus.FROZEN_ABOLISH
                        ).apply {
                            recordDetail = "作废全部-已冻结积分"
                        }
                        val fss = frozenStatementService.findFrozenStatementByFrozenId(fp.id!!, lp.hierarchy.id)
                        val fssIds = fss.map { it.id!! }
                        var recordPoint = BigDecimal.ZERO
                        // 作废待生效的冻结积分
                        if (fp.fromStatus == FSMPointEvent.DELAY_SEND) {
                            val item = initRecordItem(
                                lp,
                                Uuid.uuid,
                                recordId, BigDecimal.ZERO, FSMPointEvent.ABOLISH, parentBackId = fp.id
                            )
                            items.add(item)
                            record.recordDetail = "作废全部-已冻结未生效积分"
                            recordPoint = fp.point.abs()
                        } else {
                            fss.forEach {
                                val item = initRecordItem(
                                    lp,
                                    Uuid.uuid,
                                    recordId, BigDecimal.ZERO, FSMPointEvent.ABOLISH, parentBackId = it.id
                                )
                                items.add(item)
                                recordPoint += it.point.abs()
                            }
                            record.recordDetail = "作废全部-已冻结已生效积分"
                        }
                        record.point = recordPoint
                        record.changePoint = BigDecimal.ZERO
                        record.totalPoint = p
                        records.add(record)
                        fp.delete(lp.hierarchy.id)
                        frozenStatementService.deleteByIds(fssIds, lp.hierarchy.id)
                    } else {
                        val vss = validService.findListByGainStatementId(lp.hierarchy.id, gs.id!!)
                        if (vss.isNotEmpty()) {
                            gs.status = FSMPointEvent.SEND
                            gssQueue.add(gs)
                            continue
                        } else {
                            val fp = frozenPointService.findFrozenPointByGainStatementId(gs.id!!, lp.hierarchy.id)
                            if (fp != null) {
                                gs.status = FSMPointEvent.FREEZE
                                gssQueue.add(gs)
                                continue
                            }
                        }
                    }
                    val set = setOf(
                        FSMPointEvent.SEND,
                        FSMPointEvent.DELAY_SEND,
                        FSMPointEvent.FREEZE,
                        FSMPointEvent.SPECIAL_FREEZE,
                        FSMPointEvent.OPEN_FREEZE
                    )
                    if (gs.status in set) {
                        gs.status = FSMPointEvent.DEDUCT
                        gs.modified = ZonedDateTime.now()
                        gs.saveOrUpdate(lp.hierarchy.id)
                    }
                }
                if (p > BigDecimal.ZERO) {
                    // 可能永远也不会执行到这里
                    val record = initPointRecord(
                        lp, Uuid.uuid, FSMPointEvent.ABOLISH,
                        points = p,
                        changePoints = -p,
                        totalPoints = BigDecimal.ZERO,
                        pcStatus = PCStatus.ABOLISH
                    ).apply {
                        recordDetail = "作废全部-积分"
                    }
                    val item = initRecordItem(
                        lp,
                        Uuid.uuid,
                        record.id!!, -p, FSMPointEvent.ABOLISH, parentBackId = "-"
                    )
                    items.add(item)
                    records.add(record)
                }

                validService.deleteBatchValidPoint(vssIds.toTypedArray(), lp.hierarchy.id)
                batchUpdateRecordAndSegment(lp.hierarchy.id, records, items, listOf())
            }

            else -> return
        }
    }
}