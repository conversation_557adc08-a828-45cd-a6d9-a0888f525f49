package com.shuyun.loyalty.service.repository

import com.shuyun.loyalty.service.datamodel.SegmentMigration
import org.apache.logging.log4j.LogManager
import org.apache.logging.log4j.Logger
import org.springframework.stereotype.Component

@Component
class SegmentMigrationRepository: DataModelRepository<SegmentMigration>() {
    private val log = LogManager.getLogger(SegmentMigrationRepository::class.java)
    override fun log(): Logger = this.log

}