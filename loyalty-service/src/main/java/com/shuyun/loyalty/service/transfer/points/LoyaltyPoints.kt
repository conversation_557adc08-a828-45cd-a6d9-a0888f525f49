package com.shuyun.loyalty.service.transfer.points

import com.fasterxml.jackson.annotation.JsonIgnore
import com.github.f4b6a3.tsid.Tsid
import com.shuyun.loyalty.entity.bo.SendLimitRuleBO
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.entity.enums.NegativeStrategyEnum
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.service.datamodel.MemberPoint
import com.shuyun.loyalty.service.datamodel.MemberPointCalculateTask
import com.shuyun.loyalty.service.datamodel.PointAction
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.extension.adjustToSHTimeEnd
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.meta.TypeEnum
import com.shuyun.loyalty.service.model.DeductionPriority
import com.shuyun.loyalty.service.model.ForbiddenOperation
import com.shuyun.loyalty.service.model.Plan
import com.shuyun.loyalty.service.model.PointDeductRuleOperate
import com.shuyun.loyalty.service.service.LoyaltyPrograms
import com.shuyun.loyalty.service.service.SpecialListService
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.VisitorInfoUtil
import com.shuyun.pip.ApplicationContextHolder
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.concurrent.atomic.AtomicInteger


data class LoyaltyPoints(
    val plan: PointPlan,
    val subject: PointSubject,
    val hierarchy: PointHierarchy,
    val member: PointMember,
    var date: ZonedDateTime,
    var attr: PointAttr,
    var type: LoyaltyRequestType,
    @JsonIgnore var forbiddenConfigs: List<PointForbiddenConfig>? = null,
    var event: PointEvent? = null,
    @JsonIgnore var rawEvent: Event? = null,
    var eventType: PointEventType? = null,
    var pointRuleGroup: PointGroup? = null,
    var sendRule: PointIssuanceRule? = null,
    var deductRule: PointDeductionRule? = null,

    // 输入积分
    var pointValue: BigDecimal = BigDecimal.ZERO,
    // 过滤后的积分
    var afterPoints: BigDecimal = BigDecimal.ZERO,
    // 到账后的剩余积分
    var afterTotalPoints: BigDecimal = member.point,
    var counter: AtomicInteger = AtomicInteger(0),
    var out: OutNotification? = null
) {

    companion object {

        fun of(
            memberPoint: MemberPoint,
            type: LoyaltyRequestType,
            point: BigDecimal,
            changeMode: String,
            channel: String,
            traceId: String,
            uniqueId: String,
            date: ZonedDateTime = ZonedDateTime.now().plusSeconds(1),
            shopId: String? = null,
            kzzd1: String? = null,
            kzzd2: String? = null,
            kzzd3: String? = null,
            desc: String? = null,
            checkSpecialList: Boolean = true,
        ): LoyaltyPoints {
            val plan = LoyaltyPrograms.findPlanByAccountTypeId(
                memberPoint.pointPlanId!!,
                date
            ) ?: throw LoyaltyException(LoyaltyExceptionCode.PLAN_NOT_FOUND, "- hierarchyId: ${memberPoint.pointPlanId}")
            return of(
                plan,
                memberPoint,
                type,
                point,
                changeMode,
                channel,
                traceId,
                uniqueId,
                date,
                shopId,
                kzzd1,
                kzzd2,
                kzzd3,
                desc,
                checkSpecialList
            )
        }

        fun of(
            plan: Plan,
            memberPoint: MemberPoint,
            type: LoyaltyRequestType,
            point: BigDecimal,
            changeMode: String,
            channel: String,
            traceId: String,
            uniqueId: String,
            date: ZonedDateTime = ZonedDateTime.now().plusSeconds(1),
            shopId: String? = null,
            kzzd1: String? = null,
            kzzd2: String? = null,
            kzzd3: String? = null,
            desc: String? = null,
            checkSpecialList: Boolean = true,
        ): LoyaltyPoints {

            val subject = plan.subjectList!!.first()
            val hierarchy = subject.pointAccountTypeList!!.first()

            val lp = LoyaltyPoints(
                plan = PointPlan(plan.id!!, plan.name!!, plan.sendLimitRuleSwitch),
                subject = PointSubject(subject.id!!, subject.name!!, subject.dataType!!),
                hierarchy = PointHierarchy(
                    hierarchy.id!!,
                    hierarchy.name!!,
                    hierarchy.precision!!,
                    hierarchy.rounding!!,
                    hierarchy.unit!!,
                    topLimit = hierarchy.topLimit,
                    singleTopLimit = hierarchy.singleTopLimit,
                    limitRules = if (!hierarchy.sendLimitRule.isNullOrEmpty()) Json.parse<List<PointLimitRule>>(hierarchy.sendLimitRule!!) else null,
                    deductionPriorities = hierarchy.priorityDeduction?.let { Json.parse<List<DeductionPriority>>(it) },
                    negativeStrategy = hierarchy.negativeStrategy!!,
                ),
                member = PointMember(
                    memberPoint.id!!,
                    memberPoint.memberId,
                    memberPoint.point,
                    memberPoint = memberPoint
                ),
                date = date,
                forbiddenConfigs = if (checkSpecialList) ApplicationContextHolder.getBean(SpecialListService::class.java)
                    .find(subject.id!!, hierarchy.id!!,TypeEnum.POINT) else null,
                attr = PointAttr(
                    traceId = traceId,
                    uniqueId = uniqueId,
                    changeMode = changeMode,
                    channel = channel,
                    operatorId = VisitorInfoUtil.userId.get() ?: ConstantValue.DEFAULT_OPERATOR,
                    operator = VisitorInfoUtil.username.get() ?: ConstantValue.DEFAULT_OPERATOR,
                    shopId = shopId,
                    kzzd1 = kzzd1,
                    kzzd2 = kzzd2,
                    kzzd3 = kzzd3,
                    desc = desc
                ),
                type = type,
                pointValue = BigDecimal(point.toString()).setScale(hierarchy.precision!!, hierarchy.rounding),
                afterPoints = BigDecimal(point.toString()).setScale(hierarchy.precision!!, hierarchy.rounding)
            )

            return lp
        }

    }

    @JsonIgnore
    fun getDateAndIncr(): ZonedDateTime {
        return date.plus(counter.getAndIncrement().toLong(), ChronoUnit.MILLIS)
    }

    fun buildJournal(action: PointAction, uniqueId: String): MemberPointCalculateTask {
        // 如果有值重置生效时间和过期时间
        attr.effectiveDate = attr.effectiveDate ?: date
        attr.overdueDate = attr.overdueDate?.adjustToSHTimeEnd()
        if (action == PointAction.SEND
            || action == PointAction.REVERSE_SEND
            || action == PointAction.EFFECT
            ) {
            this.hierarchy.deductionPriorities = null
            this.hierarchy.negativeStrategy = null
        } else {
            if (action != PointAction.RECALCULATE && action != PointAction.UNFREEZE_RECALCULATE) {
                this.hierarchy.topLimit = null
                this.hierarchy.singleTopLimit = null
                this.hierarchy.limitRules = null
            }
        }
        if (pointValue < BigDecimal.ZERO && attr.overrideHistoryPoint != true) {
            throw LoyaltyException(LoyaltyExceptionCode.POINT_LESS_THAN_ZERO)
        }
        counter.incrementAndGet()
        val journal = MemberPointCalculateTask().also {
            it.id = Tsid.fast().toLowerCase()
            it.hierarchyId = hierarchy.id
            it.traceId = attr.traceId
            it.uniqueId = uniqueId
            it.memberId = member.memberId
            it.memberPointId = member.id
            it.action = action
            it.data = Json.toJson(this)
        }
        return journal
    }

}

data class OutNotification(
    val requestId: String,
    val type : PointAction,
    val memberId: String,
    val accountTypeId: Long,
    val expectPoints : BigDecimal,
    val actualPoints : BigDecimal,
)


data class PointPlan(val id: Long, val name: String, val enableSendLimit: Boolean?)
data class PointSubject(val id: Long, val name: String, val dataType: String)
data class PointHierarchy(
    val id: Long,
    val name: String,
    var precision: Int,
    val rounding: RoundingMode,
    var unit: String,
    var topLimit: Long? = null,
    var singleTopLimit: Long? = null, // *********
    var limitRules: List<PointLimitRule>? = null,
    var sendLimitResults: List<PointLimitResult>? = null,
    var deductionPriorities: List<DeductionPriority>? = null,
    var negativeStrategy: NegativeStrategyEnum? = null,
) {
    // 积分精度舍入
    fun scale(origin: BigDecimal): BigDecimal {
        return BigDecimal(origin.toString()).setScale(precision, rounding)
    }
}

data class PointMember(
    val id: String,
    var memberId: String,
    var point: BigDecimal,
    // 积分变动的最左边的那条过期时间
    var leftSegmentDate: LocalDate? = null,
    @JsonIgnore var memberPoint: MemberPoint? = null,
)

// 当前只有会员级别的上限控制只有积分值限制，没有次数限制，所以这里直接给默认积分类型
data class PointLimitRule(
    val cycle: Cycle,
    val value: Long,
    val type: SendLimitRuleBO.SendLimitRuleType = SendLimitRuleBO.SendLimitRuleType.POINT
) {
    enum class Cycle(val desc: String) {
        DAY("每天"),
        WEEK("每周"),
        MONTH("每月"),
        QUARTER("每季度"),
        YEAR("每年"),
        MAX("最大")
    }
}
enum class PointLimitRuleType(val desc: String) {
    NONE("-"),
    SINGLE("单笔"),
    TOTAL("累计"),
    TIME_BASED_RULE_POINT_LIMIT("基于时间维度的积分值限制"),
    TIME_BASED_RULE_COUNT_LIMIT("基于时间维度的次数限制"),
    SEND_RULE_GROUP_POINT_LIMIT("基于规则组维度的积分值限制"),
    SEND_RULE_GROUP_COUNT_LIMIT("基于规则组维度的次数限制"),
    EVENT_TYPE_POINT_LIMIT("基于事件维度的积分值限制"),
    EVENT_TYPE_COUNT_LIMIT("基于事件维度的次数限制"),
}
data class PointLimitResult(
    // 拦截之前的剩余总计
    var beforeTotalPoints: BigDecimal,
    // 拦截之前发放的积分
    var beforePoints: BigDecimal,
    // 拦截之后发放的积分
    var afterPoints: BigDecimal,

    var limitRuleType: PointLimitRuleType? = null,
    var topLimit: BigDecimal? = null,
    var singleTopLimit: BigDecimal? = null,
    var timeBasedRule: Any? = null,
    // 限制点
    var sendLimitCalc: Any? = null,
    var desc: String? = null
)


data class PointGroup(val id: Long, val name: String, val limit: Boolean = false)
data class PointIssuanceRule(val id: Long, val name: String)
data class PointDeductionRule(val id: Long, val name: String, var quotaType: PointDeductRuleOperate.QuotaTypeEnum)
data class PointEventType(
    val id: Long,
    val name: String,
    var occurrenceTs: Long,
    val eventOperation: EventOperationEnum,
    val relativePointEventType: PointEventType? = null,
    val sendCycleTimePath: String? = null  // 重算必填
)

data class PointForbiddenConfig(
    var columnPath: String?,
    var specialListGroupIds: String? = null,
    var forbiddens: Map<ForbiddenPort,List<ForbiddenOperation>> = emptyMap()
) {
    companion object
}

data class PointAttr(
    var traceId: String,
    var uniqueId: String,
    var changeMode: String,
    var channel: String,
    var operatorId: String = ConstantValue.DEFAULT_OPERATOR,
    var operator: String = ConstantValue.DEFAULT_OPERATOR,
    var effectiveDate: ZonedDateTime? = null,
    var overdueDate: ZonedDateTime? = null,
    var shopId: String? = null,
    var desc: String? = null,
    var recordDetail: String? = null,
    var kzzd1: String? = null,
    var kzzd2: String? = null,
    var kzzd3: String? = null,
    var actionId: String? = null,
    var actionName: String? = null,
    var actionNodeId: String? = null,
    var actionNodeName: String? = null,
    var businessId: String? = null,
    var backId: String? = null,
    var pendingPoints: Boolean? = null,
    var abolish: Boolean? = null,
    var sign: String? = null,   // 积分重算时标记加还是减(+/-)
    var beforeGsStatus: String? = null,
    var autoFillShopId: Boolean? = null,
    var migrationType: String? = null,  // 积分迁移的类型：SEND,  DEDUCT, FREEZE, UNFREEZE
    var overrideHistoryPoint: Boolean? = null,  // 积分迁移时是否覆盖历史积分
    var reverseType: String? = null,  // 对哪种类型执行反向操作 SEND, DEDUCT,USE_FREEZE
    var sourceId: String? = null,  // 用于反向操作
)

data class PointEvent(val fqn: String, val key: String, val occurrenceTs: Long, val detectionTs: Long)
enum class LoyaltyRequestType { EVENT, API, TX_API, MANUAL, AUTO }

