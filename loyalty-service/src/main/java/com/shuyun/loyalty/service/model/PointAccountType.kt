package com.shuyun.loyalty.service.model

import com.shuyun.loyalty.entity.api.constants.PublishStatusEnum
import com.shuyun.loyalty.entity.enums.NegativeStrategyEnum
import com.shuyun.loyalty.service.annotation.DataServiceModel
import com.shuyun.loyalty.service.transfer.points.PointAttr
import com.shuyun.loyalty.service.util.ModelInitUtil.copyPropertiesIgnoreNull
import com.shuyun.pip.component.json.JsonUtils
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.math.RoundingMode
import javax.persistence.*

/**
 * 账户配置
 */
@Schema(title = "账户类型")
@Table(name = "data.loyalty.manager.pointAccountType")
@DataServiceModel
data class PointAccountType(
    @Schema(title = "名称", type = "String")
    @Column
    var name: String? = null,

    @Schema(title = "单位", type = "String")
    @Column
    var unit: String? = null,

    @Schema(title = "主体ID", type = "String")
    @Column
    var subjectVersionId: Long? = null,

    @Schema(title = "账户ID", type = "String")
    @Column
    var id: Long? = null,

    @Schema(title = "保留小数位数", type = "String")
    @Column(name = "\"precision\"", nullable = false)
    var precision: Int? = null,

    @Schema(title = "舍入策略", type = "String")
    @Enumerated(EnumType.STRING)
    @Column
    var rounding: RoundingMode? = null, // 舍入策略

    @Schema(title = "异常扣除设置", type = "String")
    @Enumerated(EnumType.STRING)
    @Column
    var negativeStrategy: NegativeStrategyEnum? = null,

    @Schema(title = "可用积分上限", type = "Long")
    @Column
    var topLimit: Long? = null,

    @Schema(title = "描述", type = "String")
    @Column
    var description: String? = null,

    @Schema(title = "前端排序号", type = "Int")
    @Column
    var sort: Int? = null,

    @Schema(title = "执行顺序,越大越先执行", type = "Int")
    @Column
    var executeOrder: Int? = null,

    @Schema(title = "状态", type = "String")
    @Column
    @Enumerated(EnumType.STRING)
    var status: PublishStatusEnum? = null,

    @Schema(title = "单笔积分上限", type = "Long")
    @Column
    var singleTopLimit: Long? = null,

    @Schema(title = "累计发放上限", type = "String")
    @Column
    var sendLimitRule: String? = null,

    // PointDeductRule.PriorityDeductionEnum
    @Schema(title = " 优先扣除", type = "String")
    @Column
    var priorityDeduction: String? = null,

    @Schema(title = "过期配置", type = "object")
    @Transient
    var remindConfigList: List<RemindConfig>? = null,

    @Schema(title = "审批流开关", type = "String")
    @Column
    var processSwitch: Boolean? = null,

    @Schema(title = "审批流ID", type = "String")
    @Column
    var processId: String? = null,

    @Transient
    @Schema(title = "历史是否开启过审批流", type = "Boolean")
    var processOpen: Boolean? = null,

    ) : BaseDataServiceModel() {
    override fun copyToOldOne(old: BaseDataServiceModel, backup: Boolean) {
        old as PointAccountType
        if (backup) {
            //已发布
            if (PublishStatusEnum.DRAFT == old.status) {
                status = PublishStatusEnum.DRAFT
            } else {
                if (!arrayOf(PublishStatusEnum.PUBLISHED, PublishStatusEnum.FILED).contains(status)) {
                    throw IllegalArgumentException("已发布的计划的非草稿状态的账户类型，状态只能为发布或者归档")
                } else if (PublishStatusEnum.FILED == old.status && PublishStatusEnum.PUBLISHED == status) {
                    throw IllegalArgumentException("已发布的计划的归档的账户类型，状态只能为归档")
                }
            }
        } else {
            //未发布
            status = PublishStatusEnum.DRAFT
        }
        subjectVersionId = null
        copyPropertiesIgnoreNull(this, old)
        old.topLimit = this.topLimit
        old.singleTopLimit = this.singleTopLimit
        old.priorityDeduction = this.priorityDeduction
    }

    /**获取舍入后的值*/
    fun calculateRoundingResult(origin: BigDecimal): BigDecimal {
        val originActual = BigDecimal(origin.toString())
        return originActual.setScale(precision!!, rounding)
    }

}

enum class DeductionOrderEnum { EARLIEST, LATEST, EQUAL }
data class DeductionPriority(val name: String, val order: DeductionOrderEnum, var next: String? = null, var equalValue: Any? = null)
data class DeductionPriority2(val name: String, val order: DeductionOrderEnum, var next: DeductionPriority2? = null, var equalValue: Any? = null)

val PointAccountType.deductionPriorities: List<DeductionPriority> get() {
    if (this.priorityDeduction.isNullOrEmpty()) {
        return listOf()
    }
    return try {
        JsonUtils.parse2List(this.priorityDeduction, DeductionPriority::class.java)
    } catch (_: Exception) {
        emptyList()
    }
}


fun List<DeductionPriority>.inject(attr: PointAttr): List<DeductionPriority> {
    for (priority in this) {
        attr.shopId?.let {
            if (priority.name == "shopId" && priority.order == DeductionOrderEnum.EQUAL) {
                priority.equalValue = it
            }
        }
        attr.kzzd1?.let {
            if (priority.name == "kzzd1" && priority.order == DeductionOrderEnum.EQUAL) {
                priority.equalValue = it
            }
        }
        attr.kzzd2?.let {
            if (priority.name == "kzzd2" && priority.order == DeductionOrderEnum.EQUAL) {
                priority.equalValue = it
            }
        }
        attr.kzzd3?.let {
            if (priority.name == "kzzd3" && priority.order == DeductionOrderEnum.EQUAL) {
                priority.equalValue = it
            }
        }
    }
    return this
}