package com.shuyun.loyalty.service.util

import com.shuyun.loyalty.entity.bo.SendLimitRuleBO
import com.shuyun.loyalty.service.meta.LimitCycleEnum
import com.shuyun.pip.util.converter.Date2LongConverter
import com.shuyun.pip.util.converter.Long2ZonedDateTimeConverter
import com.shuyun.pip.util.converter.String2ZonedDateTimeConverter
import org.apache.logging.log4j.LogManager
import java.text.SimpleDateFormat
import java.time.*
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import java.util.*

object DateUtils {

    private val logger = LogManager.getLogger(DateUtils::class.java)


    fun formatMinutesCacheKey(dateTime: ZonedDateTime): ZonedDateTime {
        return dateTime.truncatedTo(ChronoUnit.MINUTES)
    }


    fun date2DateTime(date: Date) = Long2ZonedDateTimeConverter().convert(Date2LongConverter().convert(date)!!)!!


    fun zonedDateTime2Day0(zonedDateTime: ZonedDateTime): ZonedDateTime =
        zonedDateTime.withHour(0).withMinute(0).withSecond(0).withNano(0)


    /**
     *
     */
    fun getCalendar(date: String): Calendar? {
        try {
            val convert = String2ZonedDateTimeConverter().convert(date)
            return GregorianCalendar.from(convert)
        }catch (e :Exception) {
            logger.warn("日期格式错误: {} ", date, e)
        }
        return null
    }


    private fun dateToLocalDate(date: String, zoneId: ZoneId? = null): LocalDate? {
        val z = zoneId ?: ZoneId.systemDefault()
        val c =  runCatching {
            dateToZonedDatetime(date)
        }.getOrElse {
            val t = try {
                val sdf = when {
                    date.indexOf('-') != -1 -> SimpleDateFormat("yyyy-MM-dd")
                    date.indexOf('/') != -1 -> SimpleDateFormat("yyyy/MM/dd")
                    date.indexOf('年') != -1 -> SimpleDateFormat("yyyy年MM月dd日")
                    else -> return null
                }
                ZonedDateTime.ofInstant(sdf.parse(date).toInstant(), z)
            } catch (_: Exception) {
                null
            }
            t
        }
        return c?.withZoneSameInstant(z)?.toLocalDate()
    }


    fun dateToZonedDatetime(date: String): ZonedDateTime? {
        if (date.isEmpty()) return null
        return runCatching {
            ZonedDateTime.parse(date).withZoneSameInstant(ZoneId.systemDefault())
        }.getOrElse {
            try {
                ZonedDateTime.of(LocalDateTime.parse(date), ZoneId.systemDefault())
            } catch (_: Throwable) {
                dateToLocalDate(date)?.let { ZonedDateTime.of(it, LocalTime.MIN, ZoneId.systemDefault()) }
            }
        }
    }

    fun stringToLocalDate(date: String): LocalDate? {
        return dateToLocalDate(date, ZoneId.systemDefault())
    }

    fun dateToCalendar(date: String): Calendar? {
        try {
            val sdf = if (date.indexOf('-') != -1) {
                SimpleDateFormat("yyyy-MM-dd")
            } else if (date.indexOf('/') != -1) {
                SimpleDateFormat("yyyy/MM/dd")
            } else if (date.indexOf('年') != -1) {
                SimpleDateFormat("yyyy年MM月dd日")
            } else {
                return null
            }
            val calender = Calendar.getInstance()
            calender.time = sdf.parse(date)
            return calender
        } catch (e: Exception) {
            logger.warn("日期格式错误: {} ", date, e)
        }
        return null
    }

    /**
     * 时机存在多笔规则时,生效时间自动加1
     */
    fun getCalendarAndIncrement(date: String): Calendar? {
        try {
            val convert = String2ZonedDateTimeConverter().convert(date)?.withNano(0)
            val calendar = GregorianCalendar.from(convert)
            calendar.timeInMillis = calendar.timeInMillis.plus(CountThreadUtils.increment())
            return calendar
        }catch (e :Exception) {
            logger.warn("日期格式错误: {} ", date, e)
        }
        return null
    }



    fun getStartDateByType(now: LocalDate, type: SendLimitRuleBO.SendLimitRuleCycle): String {
        val date = when(type) {
            SendLimitRuleBO.SendLimitRuleCycle.DAY -> now
            SendLimitRuleBO.SendLimitRuleCycle.WEEK -> LocalDateTime.of(now.minusDays(now.dayOfWeek.value - 1L), LocalTime.MIN).toLocalDate()
            SendLimitRuleBO.SendLimitRuleCycle.MONTH -> LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN).toLocalDate()
            SendLimitRuleBO.SendLimitRuleCycle.QUARTER -> LocalDate.of(now.year,now.month.firstMonthOfQuarter(),1)
            SendLimitRuleBO.SendLimitRuleCycle.YEAR -> LocalDate.of(now.year, Month.JANUARY, 1)
            SendLimitRuleBO.SendLimitRuleCycle.MAX -> "3000-12-31"
        }
        return date.toString().replace("-","")
    }

    fun getStartDateByType(now: LocalDate, type: LimitCycleEnum?): LocalDate {
        return when(type) {
            LimitCycleEnum.DAY -> now
            LimitCycleEnum.WEEK -> LocalDateTime.of(now.minusDays(now.dayOfWeek.value - 1L), LocalTime.MIN).toLocalDate()
            LimitCycleEnum.MONTH -> LocalDateTime.of(LocalDate.now().with(TemporalAdjusters.firstDayOfMonth()), LocalTime.MIN).toLocalDate()
            else -> throw IllegalStateException("不支持type")
        }
    }

    fun dateToLocalDateTime(date: Date): LocalDateTime {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault())
    }

    fun dateToZonedDateTime(date: Date): ZonedDateTime {
        return ZonedDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault())
    }

    fun parse(date: String, format: String): Date {
        return SimpleDateFormat(format).parse(date)
    }

}