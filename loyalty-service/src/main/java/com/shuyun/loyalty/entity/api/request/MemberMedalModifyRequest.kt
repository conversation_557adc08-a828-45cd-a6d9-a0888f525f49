package com.shuyun.loyalty.entity.api.request

import com.shuyun.loyalty.entity.api.constants.MedalChangeMode
import io.swagger.v3.oas.annotations.media.Schema
import javax.validation.constraints.NotEmpty
import javax.validation.constraints.NotNull

@Schema(title = "MemberMedalModifyRequest")
class MemberMedalModifyRequest: MemberMedalModifyBaseStruct() {

    @Schema(title = "渠道类型", type = "String")
    @field:NotEmpty(message = "渠道类型不能为空")
    var channelType: String? = null

    @Schema(title = "追溯ID", type = "String")
    var triggerId: String? = null

    @Schema(title = "变更方式", type = "String")
    @field:NotNull(message = "变更方式不能为空")
    var changeWayType: MedalChangeMode? = null

}