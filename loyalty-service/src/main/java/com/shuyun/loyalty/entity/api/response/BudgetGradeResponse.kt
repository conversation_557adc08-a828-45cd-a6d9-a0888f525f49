package com.shuyun.loyalty.entity.api.response

import io.swagger.v3.oas.annotations.media.Schema

@Schema(title = "BudgetGradeResponse")
class BudgetGradeResponse {

    @Schema(title = "等级规则数据", type = "Object")
    var gradeRuleList: ArrayList<BudgetGradeRuleResponse> = ArrayList()

    @Schema(title = "目标等级ID", type = "String")
    var targetGradeId:String? = null

    @Schema(title = "BudgetGradeRuleResponse")
    class BudgetGradeRuleResponse {

        @Schema(title = "等级规则类型", type = "String")
        var type: String? = null

        @Schema(title = "等级规则详情", type = "Object")
        var gradeRule: BudgetGradeRuleDetailResponse? = null
    }

    @Schema(title = "BudgetGradeRuleDetailResponse")
    class BudgetGradeRuleDetailResponse {

        @Schema(title = "等级ID", type = "String")
        var gradeId: String? = null

        @Schema(title = "等级名称", type = "String")
        var gradeName:String? = null

        @Schema(title = "规则ID", type = "String")
        var ruleId:String? = null

        @Schema(title = "规则名称", type = "String")
        var ruleName:String? = null

        @Schema(title = "规则组ID", type = "String")
        var ruleGroupId:String? = null

        @Schema(title = "规则组名称", type = "String")
        var ruleGroupName:String? = null

        var filterGroupList:ArrayList<BudgetGradeRuleFilterGroupResponse> = ArrayList()

        @Schema(title = "结果", type = "String")
        var result: Any? = null

        @Schema(title = "条件表达式json", type = "List")
        var expressionIdentResponseList: List<ExpressionIdentResponse>? = null

        @Schema(title = "条件表达式displayInfoJson", type = "String")
        var expressionDisplayInfo: String? = null
    }

    @Schema(title = "BudgetGradeRuleFilterGroupResponse")
    class BudgetGradeRuleFilterGroupResponse {

        @Schema(title = "表达式", type = "String")
        var filterList:ArrayList<BudgetGradeRuleFilterResponse> = ArrayList()

    }

    @Schema(title = "BudgetGradeRuleFilterResponse")
    class BudgetGradeRuleFilterResponse {

        @Schema(title = "表达式", type = "String")
        var expressionObj:String?=null

        @Schema(title = "属性", type = "String")
        var property: BudgetGradeRulePropertyResponse?=null

        @Schema(title = "目标值", type = "String")
        var value:Any?=null

        @Schema(title = "比较符", type = "String")
        var comparisonOperators:String?=null

        @Schema(title = "当前值", type = "String")
        var currentValue:Any?=null

        @Schema(title = "当前值详情(选择器有值)", type = "String")
        var valueDetail:Any? = null

    }

    @Schema(title = "BudgetGradeRulePropertyResponse")
    class BudgetGradeRulePropertyResponse {

        @Schema(title = "属性名称", type = "String")
        var propertyName:String?=null

        @Schema(title = "属性ID", type = "String")
        var propertyId:String?=null

    }
}

