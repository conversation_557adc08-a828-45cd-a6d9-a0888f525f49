package com.shuyun.loyalty.entity.api.request

import com.fasterxml.jackson.annotation.JsonProperty
import com.shuyun.loyalty.entity.api.constants.ChangeMode
import io.swagger.v3.oas.annotations.Hidden
import io.swagger.v3.oas.annotations.media.Schema
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotNull

@Schema(title = "InvalidatingMemberPointRequest")
class InvalidatingMemberPointRequest {

    @Schema(title = "计划ID", type = "Long")
    @field:NotNull(message = "计划ID不能为空")
    var planId: Long? = null

    @Schema(title = "积分账户ID列表", type = "List")
    @field:NotNull(message = "积分账户ID列表不能为空")
    var pointPlanIds: List<Long>? = null

    @Schema(title = "会员ID", type = "String")
    @field:NotBlank(message = "会员ID不能为空")
    lateinit var memberId: String

    @Schema(description = "业务唯一ID")
    @field:NotBlank(message = "业务唯一ID不能为空")
    lateinit var uniqueId: String

    @Schema(title = "渠道类型", type = "String")
    @field:NotBlank(message = "渠道类型不能为空")
    lateinit var channelType: String

    @Schema(title = "备注", type = "String")
    var desc: String? = null

    @Hidden
    @Schema(title = "变更方式-接口变更", type = "String")
    val changeMode = ChangeMode.INTERFACE

    @Schema(title = "店铺ID", type = "String")
    var shopId: String? = null

    @Schema(title = "扩展字段1 注意:字母全部大写", type = "string")
    @set:JsonProperty("KZZD1")
    @get:JsonProperty("KZZD1")
    var KZZD1: String? = null

    @Schema(title = "扩展字段2 注意:字母全部大写", type = "string")
    @set:JsonProperty("KZZD2")
    @get:JsonProperty("KZZD2")
    var KZZD2: String? = null

    @Schema(title = "扩展字段3 注意:字母全部大写", type = "string")
    @set:JsonProperty("KZZD3")
    @get:JsonProperty("KZZD3")
    var KZZD3: String? = null

}