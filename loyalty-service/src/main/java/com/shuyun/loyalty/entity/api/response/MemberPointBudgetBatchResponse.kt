package com.shuyun.loyalty.entity.api.response

import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import javax.validation.constraints.NotNull

@Schema(title = "MemberPointBudgetBatchResponse")
open class MemberPointBudgetBatchResponse {
    @Schema(title = "业务ID", type = "String")
    @field:NotNull(message = "业务ID")
    var key: String? = null

    @Schema(title = "主体列表", type = "Object")
    var planList: ArrayList<Plan> = ArrayList()

    @Schema(title = "积分", type = "number")
    var point: BigDecimal = BigDecimal.ZERO

    @Schema(title = "BudgetPlan")
    class Plan {
        @Schema(title = "名称", type = "String")
        var name: String? = null

        @Schema(title = "计划ID", type = "String")
        var id: Long? = null

        @Schema(title = "主体列表", type = "Object")
        var subjectList: ArrayList<Subject> = ArrayList()
    }

    @Schema(title = "BudgetSubject")
    class Subject {
        @Schema(title = "名称", type = "String")
        var name: String? = null

        @Schema(title = "主体ID", type = "String")
        var id: Long? = null

        @Schema(title = "积分账号列表", type = "Object")
        var pointAccountList: ArrayList<PointAccount> = ArrayList()
    }

    @Schema(title = "BudgetPointAccount")
    class PointAccount {
        @Schema(title = "名称", type = "String")
        var name: String? = null

        @Schema(title = "积分账号ID", type = "String")
        var id: Long? = null

        @Schema(title = "匹配规则组", type = "Object")
        var ruleGroupList: ArrayList<PointRuleGroup> = ArrayList()

        @Schema(title = "积分", type = "number")
        var point: BigDecimal = BigDecimal.ZERO
    }

    @Schema(title = "BudgetPointRuleGroup")
    class PointRuleGroup {
        @Schema(title = "名称", type = "String")
        var name: String? = null

        @Schema(title = "规则ID", type = "String")
        var id: String? = null

        @Schema(title = "匹配规则", type = "Object")
        var ruleList: ArrayList<PointRule> = ArrayList()
    }

    @Schema(title = "BudgetPointRule")
    class PointRule {
        @Schema(title = "名称", type = "String")
        var name: String? = null

        @Schema(title = "规则ID", type = "String")
        var id: String? = null

        @Schema(title = "积分", type = "number")
        var point: BigDecimal = BigDecimal.ZERO
    }
}











