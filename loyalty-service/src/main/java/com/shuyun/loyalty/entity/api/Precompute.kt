package com.shuyun.loyalty.entity.api

import com.fasterxml.jackson.annotation.JsonIgnore
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.meta.EventOccasionEnum
import com.shuyun.loyalty.service.meta.TypeEnum
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.loyalty.service.model.Plan
import com.shuyun.loyalty.service.model.Subject
import io.swagger.v3.oas.annotations.Hidden
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.ZonedDateTime


@Schema(description = "积分扣减")
data class PointDeduction(
    @Schema(description = "积分账户类型Id")
    var accountTypeId: Long,
    @Schema(description = "期望扣减积分值")
    var point: BigDecimal,
    @Schema(description = "渠道类型")
    var channelType: String,
    @Schema(description = "扣减key，用于唯一标识一次扣减，后续撤销操作需要携带此key")
    var key: String? = null,
    @Schema(description = "店铺Id")
    var shopId: String? = null,
    @Schema(description = "备注")
    var desc: String? = null,
    @Schema(description = "扩展字段1")
    var kzzd1: String? = null,
    @Schema(description = "扩展字段2")
    var kzzd2: String? = null,
    @Schema(description = "扩展字段3")
    var kzzd3: String? = null,
    var actionName: String? = null,
    var actionId: String? = null,
    var actionNodeId: String? = null,
    var actionNodeName: String? = null,
)

@Schema(description = "事件")
data class EventExample(
    @Schema(description = "事件FQN")
    val fqn: String,
    @Schema(description = "事件Key")
    val key: String,
    @Schema(description = "发生时间")
    val occurrenceTs: Long,
    @Schema(description = "检测时间")
    val detectionTs: Long,
    @Schema(description = "其他字段1")
    val field1: String? = null,
    @Schema(description = "其他字段2")
    val field2: String? = null,
    @Schema(description = "其他字段3")
    val field3: String? = null,
    @Schema(description = "其他字段", example = "...")
    val xyz: String? = null,
)

@Schema(description = "积分等级规则试算")
data class PrecomputeRequest(
//    @Schema(description = "主体标识")
//    val subjectUid: String,
    @Schema(description = "积分等级类型")
    val accountTypeIds: Map<TypeEnum, List<Long>>,
    @Schema(description = "事件列表", oneOf = [EventExample::class])
    val event: Event,
    @Schema(description = "扣减积分")
    val deductions: List<PointDeduction>? = null,
    @Schema(description = "上一次试算结果")
    val lastResponse: PrecomputeResponse? = null,
    @Hidden
    val checkPointsQuotaAndBlockList: Boolean = false,
)


interface PrecomputeResult

@Schema(description = "积分试算结果")
data class PrecomputeRulePoint(
    @Schema(description = "类型", allowableValues = ["POINT"], example = "POINT")
    val type: TypeEnum,
    @Schema(description = "时机ID")
    val eventTypeId: Long,
    @Schema(description = "时机名称")
    val eventTypeName: String,
    @Schema(description = "时机FQN")
    val eventFQN: String,
    @Schema(description = "积分账户类型ID")
    val accountTypeId: Long,
    @Schema(description = "积分账户类型名称")
    val accountTypeName: String,
    @Schema(description = "原单追溯ID")
    val traceId: String,
    @Schema(description = "唯一ID")
    val uniqueId: String,
    @Schema(description = "积分规则组ID")
    val ruleGroupId: Long,
    @Schema(description = "积分规则组名称")
    val ruleGroupName: String,
    @Schema(description = "积分规则ID")
    val ruleId: Long,
    @Schema(description = "积分规则名称")
    val ruleName: String,
    @Schema(description = "积分值")
    val points: BigDecimal,
    @Schema(description = "生效时间")
    val effectiveDate: ZonedDateTime,
    @Schema(description = "过期时间")
    val overdueDate: ZonedDateTime?,
    @Schema(description = "描述")
    val desc: String,
    @Schema(description = "渠道")
    val channel: String? = null,
    @Schema(description = "店铺ID")
    val shopId: String? = null,
    @Schema(description = "扩展字段1")
    val kzzd1: String? = null,
    @Schema(description = "扩展字段2")
    val kzzd2: String? = null,
    @Schema(description = "扩展字段3")
    val kzzd3: String? = null,
    @JsonIgnore
    val sort: Int,
) : PrecomputeResult

@Schema(description = "等级试算结果")
data class PrecomputeRuleGrade(
    @Schema(description = "类型", allowableValues = ["GRADE"], example = "GRADE")
    val type: TypeEnum,
    @Schema(description = "时机ID")
    val eventTypeId: Long,
    @Schema(description = "时机名称")
    val eventTypeName: String,
    @Schema(description = "时机FQN")
    val eventFQN: String,
    @Schema(description = "等级体系ID")
    val gradeHierarchyId: Long,
    @Schema(description = "等级体系名称", example = "等级体系名称A")
    val gradeHierarchyName: String,
    @Schema(description = "原单追溯ID")
    val traceId: String,
    @Schema(description = "唯一ID")
    val uniqueId: String,
    @Schema(description = "等级规则组ID")
    val ruleGroupId: Long,
    @Schema(description = "积分规则组名称")
    val ruleGroupName: String,
    @Schema(description = "等级规则ID")
    val ruleId: Long,
    @Schema(description = "等级规则名称")
    val ruleName: String,
    @Schema(description = "等级ID")
    val gradeDefinitionId: Long,
    @Schema(description = "等级名称", example = "最强王者")
    val gradeDefinitionName: String,
    @Schema(description = "等级排序")
    val gradeDefinitionIdSort: Map<Long, Int>,
    @Schema(description = "过期时间")
    val overdueDate: ZonedDateTime?,
    @Schema(description = "描述")
    val desc: String,
    @Schema(description = "渠道", example = "TAOBAO")
    val channel: String? = null,
    @Schema(description = "店铺ID")
    val shopId: String? = null,
    @Schema(description = "扩展字段1")
    val kzzd1: String? = null,
    @Schema(description = "扩展字段2")
    val kzzd2: String? = null,
    @Schema(description = "扩展字段3")
    val kzzd3: String? = null,
) : PrecomputeResult


@Schema(description = "试算结果")
data class PrecomputeResponse(
    @Schema(description = "计划ID")
    val planId: Long,
    @Schema(description = "计划名称")
    val planName: String,
    @Schema(description = "计划主体ID")
    val subjectId: Long,
    @Schema(description = "计划主体名称")
    val subjectName: String,
    @Schema(description = "计划主体FQN")
    val subjectFqn: String,
    @Schema(description = "主体标识")
    val subjectUid: String,
    @Schema(anyOf = [PrecomputeRulePoint::class, PrecomputeRuleGrade::class], exampleClasses = [PrecomputeRulePoint::class, PrecomputeRuleGrade::class])
    val contents: List<Any>,
    @Schema(description = "请求的扣减参数")
    val deductions: List<PointDeduction>? = null,
    @Schema(description = "前一次试算结果")
    val lastResponse: PrecomputeResponse? = null,
    @Schema(description = "试算时间")
    val dateTime: ZonedDateTime? = null,
    @Schema(description = "哈希值")
    var hash: String? = null
)

data class PrecomputeEvent(val plan: Plan, val subject: Subject, val eventType: EventType, val event: Event, val req: PrecomputeRequest, val date: ZonedDateTime)
class PrecomputeTask(val executeOrder: Int, val accountTypeId: Long, val eoe: EventOccasionEnum, val func: () -> List<PrecomputeResult>)
data class PrecomputeTuple6<A, B, C, D, E, F>(
    val first:  A,
    val second: B,
    val third:  C,
    val fourth: D,
    val fifth:  E,
    val sixth:  F
)