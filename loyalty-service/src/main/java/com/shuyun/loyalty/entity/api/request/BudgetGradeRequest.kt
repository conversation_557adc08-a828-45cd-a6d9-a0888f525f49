package com.shuyun.loyalty.entity.api.request

import com.shuyun.loyalty.entity.api.constants.GradeRecordType
import io.swagger.v3.oas.annotations.media.Schema
import javax.validation.constraints.NotEmpty
import javax.validation.constraints.NotNull

@Schema(title = "BudgetGradeRequest")
class BudgetGradeRequest {

    @Schema(title = "会员id", type = "String")
    @field:NotEmpty(message = "会员ID不能为空")
    var memberId: String?=null

    @Schema(title = "等级规则类型", type = "String")
    @field:NotNull(message = "等级规则类型不能为空")
    var gradeRuleTypes: List<GradeRecordType>? = null

    @Schema(title = "等级体系ID", type = "String")
    @field:NotNull(message = "等级体系ID")
    var gradeHierarchyId: Long? = null

    @Schema(title = "目标等级id", type = "String")
    @field:NotNull(message = "目标等级id不能为空")
    var targetGradeIds: List<String>?=null
}