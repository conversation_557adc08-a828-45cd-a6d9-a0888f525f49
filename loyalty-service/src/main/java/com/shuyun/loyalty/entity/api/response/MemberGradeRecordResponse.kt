package com.shuyun.loyalty.entity.api.response

import com.shuyun.loyalty.entity.api.constants.GradeRecordType
import io.swagger.v3.oas.annotations.media.Schema
import java.time.ZonedDateTime


@Schema(title = "MemberGradeRecordResponse")
class MemberGradeRecordResponse {

    @Schema(title = "计划Id")
    var planId: Long? = null

    @Schema(title = "计划名称")
    var planName: String? = null

    @Schema(title = "主体Id")
    var subjectId: Long? = null

    @Schema(title = "主体名称")
    var subjectName: String? = null

    @Schema(title = "主体FQN")
    var subjectFqn: String? = null

    @Schema(title = "等级体系Id")
    var gradeHierarchyId: Long? = null

    @Schema(title = "等级体系名称")
    var gradeHierarchyName: String? = null

    @Schema(title = "会员ID")
    var memberId: String? = null

    @Schema(title = "变更前等级Id")
    var originalGradeId: Long? = null

    @Schema(title = "变更前等级名称")
    var originalGradeName: String? = null

    @Schema(title = "变更前生效时间")
    var originalEffectDate: ZonedDateTime? = null

    @Schema(title = "变更前过期时间")
    var originalOverdueDate: ZonedDateTime? = null

    @Schema(title = "变更后等级Id")
    var currentGradeId: Long? = null

    @Schema(title = "变更后等级名称")
    var currentGradeName: String? = null

    @Schema(title = "变更后生效时间")
    var currentEffectDate: ZonedDateTime? = null

    @Schema(title = "变更后过期时间")
    var currentOverdueDate: ZonedDateTime? = null

    @Schema(title = "变更类型")
    var recordType: GradeRecordType? = null

    @Schema(title = "描述")
    var description: String? = null

    @Schema(title = "变更时间")
    var created: ZonedDateTime? = null

    var id: String? = null

    var changeWay: String? = null

}

