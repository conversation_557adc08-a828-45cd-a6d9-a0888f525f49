package com.shuyun.loyalty.entity.dto


interface MemberMessage
data class MemberPointMessage(val pointAccountTypeId: Long, val memberId: String, val memberPointId: String) : MemberMessage
data class MemberPointExpireMessage(val pointAccountTypeId: Long, val memberId: String, val memberPointId: String): MemberMessage
data class MemberPointEffectMessage(val pointAccountTypeId: Long, val memberId: String, val memberPointId: String): MemberMessage


