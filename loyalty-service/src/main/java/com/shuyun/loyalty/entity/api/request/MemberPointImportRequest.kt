package com.shuyun.loyalty.entity.api.request

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.Hidden
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.ZonedDateTime
import javax.validation.constraints.NotNull

@Schema(title = "MemberPointImportRequest")
class MemberPointImportRequest {

    @field:NotNull(message = "会员ID")
    @Schema(title = "会员ID", type = "string")
    var memberId: String? = null

    @field:NotNull(message = "积分变更类型")
    @Schema(title = "积分变更类型", type = "string")
    var action: Type? = null

    @Schema(title = "积分生效时间", type = "string")
    @field:NotNull(message = "积分生效时间")
    var effectiveDate: ZonedDateTime? = null

    @Schema(title = "积分过期时间", type = "string")
    var overdueDate: ZonedDateTime? = null


    @Schema(title = "积分值", type = "number")
    var point: BigDecimal? = null

    @Schema(title = "备注信息", type = "string")
    var description: String? = null

    @field:NotNull(message = "积分生成时间")
    @Schema(title = "积分生成时间", type = "string")
    var createdDate: ZonedDateTime? = null

    @field:NotNull(message = "幂等键")
    @Schema(title = "幂等键", type = "string")
    var key: String? = null


    @Schema(title = "渠道", type = "string", required = false)
    var channelType: String? = null

    @Schema(title = "ShopId", type = "string")
    var shopId: String? = null

    @Schema(title = "扩展字段1 注意:字母全部大写", type = "string", required = false)
    @set:JsonProperty("KZZD1")
    @get:JsonProperty("KZZD1")
    var KZZD1: String? = null

    @Schema(title = "扩展字段2 注意:字母全部大写", type = "string", required = false)
    @set:JsonProperty("KZZD2")
    @get:JsonProperty("KZZD2")
    var KZZD2: String? = null

    @Schema(title = "扩展字段3 注意:字母全部大写", type = "string", required = false)
    @set:JsonProperty("KZZD3")
    @get:JsonProperty("KZZD3")
    var KZZD3: String? = null

    @Hidden
    var myId: String? = null

    @Hidden
    var overrideHistory: Boolean? = null

    enum class Type {
        SEND,
        DEDUCT,
    }
}

