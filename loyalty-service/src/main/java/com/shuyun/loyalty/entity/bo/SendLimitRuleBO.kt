package com.shuyun.loyalty.entity.bo

import java.math.BigDecimal
import java.time.ZonedDateTime

/**
 *
 */
open class SendLimitRuleBO {
    var value: Long? = null
    var type: SendLimitRuleType? = null
    var cycle: SendLimitRuleCycle? = null

    enum class SendLimitRuleType {
        POINT, COUNT, ACCOUNT_SINGLE_POINT, ACCOUNT_ACCUMULATIVE_POINT
    }

    enum class SendLimitRuleCycle {
        DAY, WEEK, MONTH, QUARTER, YEAR, MAX
    }
}

open class SendLimitRuleCalcBO : SendLimitRuleBO() {
    var afterCount: Long? = null
    var beforeCount: Long? = null

    // 拦截前应发积分
    var beforePoint: BigDecimal? = null

    // 拦截后发放积分
    var afterPoint: BigDecimal? = null
    var result: Boolean = false
}

//拦截详情
class SendLimitRuleResultDetail {
    // 拦截规则
    var limitRuleCalcList: ArrayList<SendLimitRuleCalcBO>? = null

    // 拦截id
    var limitId: Long? = null

    // 逻辑关系
    var logic: String? = null

    // 关联ID
    var refId: String? = null

    // 拦截维度
    var type: String? = null

    // 拦截维度
    var limitRuleType: String? = null

    // 限制点
    var sendLimitCalc: Any? = null
}

//拦截结果
class SendLimitRuleCalcResult {
    // 规则计算表达式
    var sendLimitRuleResultDetail: ArrayList<SendLimitRuleResultDetail> = ArrayList()

    // 未拦截
    var result: Boolean = false

    // 计算时间
    var now: ZonedDateTime? = null

    // 实发积分
    var realPoint: BigDecimal = BigDecimal.ZERO

    // 应发积分
    var point: BigDecimal = BigDecimal.ZERO

    // 拦截上限开关
    var open = false
}