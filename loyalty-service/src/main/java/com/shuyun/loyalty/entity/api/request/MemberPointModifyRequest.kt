package com.shuyun.loyalty.entity.api.request

import com.fasterxml.jackson.annotation.JsonProperty
import com.shuyun.loyalty.entity.api.constants.OpenFSMPointEvent
import com.shuyun.loyalty.entity.dto.MemberPointDeductRequest
import com.shuyun.loyalty.entity.dto.MemberPointSendRequest
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.ZonedDateTime
import javax.validation.constraints.NotEmpty
import javax.validation.constraints.NotNull

@Schema(title = "MemberPointModifyRequest")
class MemberPointModifyRequest {

    @Schema(title = "积分账户ID", type = "Long")
    @field:NotNull(message = "积分账户ID不能为空")
    var pointAccountId: Long? = null

    @Schema(title = "会员ID", type = "String")
    @field:NotEmpty(message = "会员ID不能为空")
    var memberId: String? = null

    @Schema(title = "触发动作", type = "object")
    @field:NotNull(message = "触发动作不能为空")
    var recordType: OpenFSMPointEvent? = null

    @Schema(title = "积分值", type = "Long")
    @field:NotNull(message = "积分值不能为空")
    var point: BigDecimal? = null

    @Schema(title = "过期时间，永不过期或者扣减时不用传", type = "Long")
    var overdueDate: ZonedDateTime? = null

    @Schema(title = "desc描述", type = "String")
    var desc: String? = null

    @Schema(title = "渠道类型", type = "string")
    @field:NotEmpty(message = "渠道类型不能为空")
    var channelType: String? = null

    @Schema(title = "积分生效日期", type = "string")
    var effectDate: ZonedDateTime? = null

    @Schema(title = "追溯ID", type = "String")
    @field:NotEmpty(message = "追溯ID不能为空")
    var triggerId: String? = null

    @Schema(title = "活动ID", type = "String")
    var actionId: String? = null

    @Schema(title = "活动名称", type = "String")
    var actionName: String? = null

    @Schema(title = "发放节点id", type = "String")
    var actionNodeId: String? = null

    @Schema(title = "发放节点名称", type = "String")
    var actionNodeName: String? = null

    @Schema(title = "变更方式", type = "Long")
    var changeMode: String? = null

    @Schema(title = "店铺ID", type = "Long")
    var shopId: String? = null

    @Schema(title = "扩展字段1 注意:字母全部大写", type = "string")
    @set:JsonProperty("KZZD1")
    @get:JsonProperty("KZZD1")
    var KZZD1: String? = null

    @Schema(title = "扩展字段2 注意:字母全部大写", type = "string")
    @set:JsonProperty("KZZD2")
    @get:JsonProperty("KZZD2")
    var KZZD2: String? = null

    @Schema(title = "扩展字段3 注意:字母全部大写", type = "string")
    @set:JsonProperty("KZZD3")
    @get:JsonProperty("KZZD3")
    var KZZD3: String? = null


    fun buildSendRequest(): MemberPointSendRequest {
        val request = MemberPointSendRequest()
        request.point =  point!!
        request.effectiveDate = effectDate
        request.overdueDate = overdueDate
        request.pointAccountId = pointAccountId!!
        request.desc = desc
        request.actionName = actionName
        request.actionId = actionId
        request.actionNodeId = actionNodeId
        request.actionNodeName = actionNodeName
        request.changeMode = changeMode
        request.businessId = triggerId!!
        request.triggerId = triggerId!!
        request.channelType = channelType!!
        request.memberId = memberId!!
        request.shopId = shopId
        request.KZZD1 = KZZD1
        request.KZZD2 = KZZD2
        request.KZZD3 = KZZD3

        return request
    }


    fun buildDeductRequest(): MemberPointDeductRequest {
        val request = MemberPointDeductRequest()
        request.point =  point!!
        request.pointAccountId = pointAccountId!!
        request.desc = desc
        request.actionName = actionName
        request.actionId = actionId
        request.actionNodeId = actionNodeId
        request.actionNodeName = actionNodeName
        request.changeMode = changeMode
        request.businessId =  triggerId!!
        request.triggerId = triggerId!!
        request.channelType = channelType!!
        request.memberId = memberId!!
        request.shopId = shopId
        request.KZZD1 = KZZD1
        request.KZZD2 = KZZD2
        request.KZZD3 = KZZD3
        return request
    }
}




