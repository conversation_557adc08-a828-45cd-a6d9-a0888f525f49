package com.shuyun.loyalty.entity.api.request

import io.swagger.v3.oas.annotations.media.Schema
import javax.validation.constraints.NotNull

@Schema(title = "MedalHierarchyAndDefinditRequest")
class MedalHierarchyAndDefinditRequest {

    @Schema(title = "勋章ID", type = "Long")
    @field:NotNull(message = "勋章ID不能为空")
    var medalDefinitionId: Long? = null

    @Schema(title = "勋章体系ID", type = "Long")
    @field:NotNull(message = "勋章体系ID")
    var medalHierarchyId: Long? = null


}