package com.shuyun.loyalty.entity.api.response

import io.swagger.v3.oas.annotations.media.Schema
import java.time.ZonedDateTime

@Schema(title = "MemberMedalResponse")
class MemberMedalResponse {

    @Schema(title = "计划ID", type = "Long")
    var planId: Long? = null

    @Schema(title = "计划名称", type = "String")
    var planName: String? = null

    @Schema(title = "主体ID", type = "Long")
    var subjectId: Long? = null

    @Schema(title = "主体名称", type = "String")
    var subjectName: String? = null

    @Schema(title = "主体fqn", type = "String")
    var subjectFqn: String? = null

    @Schema(title = "勋章体系ID", type = "Long")
    var medalHierarchyId: Long? = null

    @Schema(title = "勋章体系名称", type = "String")
    var medalHierarchyName: String? = null

    @Schema(title = "会员ID", type = "String")
    var memberId: String? = null

    @Schema(title = "勋章ID", type = "Long")
    var medalDefinitionId: Long? = null

    @Schema(title = "勋章名称", type = "String")
    var medalDefinitionName: String? = null

    @Schema(title = "生效时间", type = "String")
    var effectDate: ZonedDateTime? = null

    @Schema(title = "过期时间", type = "String")
    var overdueDate: ZonedDateTime? = null

}