package com.shuyun.loyalty.entity.api.request

import io.swagger.v3.oas.annotations.media.Schema
import java.time.ZonedDateTime
import javax.validation.constraints.NotEmpty
import javax.validation.constraints.NotNull

@Schema(title = "MemberGradeModifyBaseStruct")
open class MemberGradeModifyBaseStruct {

    @Schema(title = "等级体系ID", type = "Long")
    @field:NotNull(message = "等级体系ID不能为空")
    var gradeHierarchyId: Long? = null

    @Schema(title = "会员ID", type = "String")
    @field:NotEmpty(message = "会员ID不能为空")
    var memberId: String? = null

    @Schema(title = "目标等级", type = "Long")
    var gradeDefinitionId: Long? = null

    @Schema(title = "过期时间，永不过期或者扣减时不用传", type = "Long")
    var overdueDate: ZonedDateTime? = null

    @Schema(title = "备注", type = "string")
    @field:NotEmpty(message = "备注不能为空")
    var description: String? = null

    @Schema(title = "是否使用之前等级的生效时间，默认不使用", type = "Boolean")
    var useOriginalEffectTime: Boolean? = null

}