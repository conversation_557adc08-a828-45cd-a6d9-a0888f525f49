package com.shuyun.loyalty.entity.dto

import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.ZonedDateTime

/**
 * 积分发放
 */
@Schema(description = "积分发放")
class MemberPointSendRequest : MemberPointRequest() {

    @Schema(description = "要操作的积分")
    var point: BigDecimal = BigDecimal.ZERO

    @Schema(description = "积分生效时间")
    var effectiveDate: ZonedDateTime? = null

    @Schema(description = "过期时间，永不过期或者扣减时不用传")
    var overdueDate: ZonedDateTime? = null

    var tx: Boolean? = null

    var triggerId: String? = null

    override fun validation() {
        super.validation()
        if (effectiveDate != null && overdueDate != null && overdueDate!! < effectiveDate) throw IllegalArgumentException(
            "积分失效时间必须大于积分生效时间"
        )
        require(overdueDate == null || overdueDate!! > ZonedDateTime.now()) { "积分失效时间必须大于当前时间" }
    }
}