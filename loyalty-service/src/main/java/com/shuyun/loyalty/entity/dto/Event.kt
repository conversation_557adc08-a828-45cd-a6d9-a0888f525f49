package com.shuyun.loyalty.entity.dto

import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime

/**
 * 异步处理事件定义
 */
data class Event<T>(
    /** 默认情况下为memberId，通过分区保证顺序（但不完全保证） */
    val key: String,
    /** 事件类型 */
    val type: EventType,
    val data: T,
    /** 总积分 **/
    var totalPoint: BigDecimal? = null,
    /** 当前扣减日期 **/
    var realOverdueDate: LocalDate? = null,
    /** facade查询积分块时间, 解决0点时差问题,如查询积分块日期19号,stream查询validStatement是20号**/
    var occurrenceDate: LocalDate? = null,
    val occurrenceTs: LocalDateTime = LocalDateTime.now(),
    // 积分块零点问题生成的唯一ID
    var uniqueId: String? = null,
    // 发放上限拦截后的规则
    var sendLimitRuleStr: String? = null,
    var expectPoint: BigDecimal? = null,
    var oldTotalPoint: BigDecimal? = null,
    // 实际处理时间
    val actualProcessingDateTime: ZonedDateTime? = null,
) {
    enum class EventType(val desc: String) {
        DELAY_SEND("延迟发放"),
        SEND("积分发放"),
        DEDUCT("积分扣减"),
        FROZEN("积分冻结"),
        UNFROZEN("解冻"),
        FROZEN_DEDUCT("冻结消耗"),
        INIT("积分账户初始化")
    }
}
