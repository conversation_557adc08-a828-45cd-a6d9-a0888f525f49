{"fqn": "dm.at.UserA", "owner": "tester", "description": "SomeUser", "type": "Object", "enums": [], "custom": true, "embedded": false, "visible": true, "mutable": true, "extensible": false, "insertable": true, "updatable": true, "deletable": true, "autoMapping": true, "hints": [], "fields": [{"name": "id", "tag": "id", "constraint": {"nullable": false, "unique": false, "stringId": false, "generatedValue": false, "integerLength": 8, "stringLength": 32}, "custom": true, "mutable": true, "visible": true, "filterable": true, "insertable": true, "updatable": true, "fieldType": {"owner": "System", "fqn": "system.lang.Id", "type": "Id"}, "createTime": "2018-08-14 02:32:14", "updateTime": "2018-08-14 02:32:14"}, {"name": "ageA", "constraint": {"nullable": true, "unique": false, "stringId": false, "generatedValue": false, "integerLength": 8}, "custom": true, "mutable": true, "visible": true, "filterable": true, "insertable": true, "updatable": true, "fieldType": {"owner": "System", "fqn": "system.lang.Integer", "type": "Integer"}, "createTime": "2018-08-14 02:32:14", "updateTime": "2018-08-14 02:32:14"}, {"name": "arrayFieldA", "constraint": {"nullable": true, "unique": false, "stringId": false, "generatedValue": false}, "custom": true, "mutable": true, "visible": true, "filterable": true, "insertable": true, "updatable": true, "refType": "OneToMany", "refField": {"name": "left"}, "fieldType": {"owner": "tester", "fqn": "dm.at.UserA$ArrayFieldA$A", "type": "Array", "elementType": {"owner": "System", "fqn": "system.lang.String", "type": "String"}}, "createTime": "2018-08-14 02:32:14", "updateTime": "2018-08-14 02:32:14"}, {"name": "birthdayA", "constraint": {"nullable": true, "unique": false, "stringId": false, "generatedValue": false}, "custom": true, "mutable": true, "visible": true, "filterable": true, "insertable": true, "updatable": true, "fieldType": {"owner": "System", "fqn": "system.lang.Date", "type": "Date"}, "createTime": "2018-08-14 02:32:14", "updateTime": "2018-08-14 02:32:14"}, {"name": "depositA", "constraint": {"nullable": true, "unique": false, "stringId": false, "generatedValue": false, "integerLength": 18, "fractionalLength": 3, "stringLength": 64}, "custom": true, "mutable": true, "visible": true, "filterable": true, "insertable": true, "updatable": true, "fieldType": {"owner": "System", "fqn": "system.lang.Currency", "type": "<PERSON><PERSON><PERSON><PERSON>"}, "createTime": "2018-08-14 02:32:14", "updateTime": "2018-08-14 02:32:14"}, {"name": "enumFieldA", "constraint": {"nullable": true, "unique": false, "stringId": false, "generatedValue": false, "stringLength": 32}, "custom": true, "mutable": true, "visible": true, "filterable": true, "insertable": true, "updatable": true, "fieldType": {"owner": "tester", "fqn": "dm.at.UserA$EnumFieldA$E", "visible": false, "custom": true, "deletable": true, "description": "数据类型dm.at.UserA中字段enumFieldA的类型", "type": "Enum", "insertable": true, "enums": [{"text": "Value0"}, {"text": "Value1"}, {"text": "Value2"}], "mutable": true, "updatable": true, "embedded": true, "extensible": false, "autoMapping": true}, "createTime": "2018-08-14 02:32:14", "updateTime": "2018-08-14 02:32:14"}, {"name": "luckyTimeA", "constraint": {"nullable": true, "unique": false, "stringId": false, "generatedValue": false}, "custom": true, "mutable": true, "visible": true, "filterable": true, "insertable": true, "updatable": true, "fieldType": {"owner": "System", "fqn": "system.lang.Time", "type": "Time"}, "createTime": "2018-08-14 02:32:14", "updateTime": "2018-08-14 02:32:14"}, {"name": "marriedA", "constraint": {"nullable": true, "unique": false, "stringId": false, "generatedValue": false}, "custom": true, "mutable": true, "visible": true, "filterable": true, "insertable": true, "updatable": true, "fieldType": {"owner": "System", "fqn": "system.lang.<PERSON>", "type": "Boolean"}, "createTime": "2018-08-14 02:32:14", "updateTime": "2018-08-14 02:32:14"}, {"name": "nameA", "constraint": {"nullable": true, "unique": false, "stringId": false, "generatedValue": false, "stringLength": 255}, "custom": true, "mutable": true, "visible": true, "filterable": true, "insertable": true, "updatable": true, "fieldType": {"owner": "System", "fqn": "system.lang.String", "type": "String"}, "createTime": "2018-08-14 02:32:14", "updateTime": "2018-08-14 02:32:14"}, {"name": "pointsA", "constraint": {"nullable": true, "unique": false, "stringId": false, "generatedValue": false, "integerLength": 18, "fractionalLength": 3}, "custom": true, "mutable": true, "visible": true, "filterable": true, "insertable": true, "updatable": true, "fieldType": {"owner": "System", "fqn": "system.lang.Number", "type": "Number"}, "createTime": "2018-08-14 02:32:14", "updateTime": "2018-08-14 02:32:14"}, {"name": "registerTimeA", "constraint": {"nullable": true, "unique": false, "stringId": false, "generatedValue": false}, "custom": true, "mutable": true, "visible": true, "filterable": true, "insertable": true, "updatable": true, "fieldType": {"owner": "System", "fqn": "system.lang.DateTime", "type": "DateTime"}, "createTime": "2018-08-14 02:32:14", "updateTime": "2018-08-14 02:32:14"}], "createTime": "2018-08-14 02:32:14", "updateTime": "2018-08-14 02:32:15"}