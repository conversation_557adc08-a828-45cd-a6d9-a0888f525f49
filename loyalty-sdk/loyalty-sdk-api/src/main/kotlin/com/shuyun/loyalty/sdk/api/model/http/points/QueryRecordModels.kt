package com.shuyun.loyalty.sdk.api.model.http.points

import com.fasterxml.jackson.annotation.JsonAlias
import java.math.BigDecimal
import java.time.ZonedDateTime

// 分页查询会员积分记录
// GET /loyalty-facade/v1/point:record:page  Page<MemberPointRecordResponse>
// GET /loyalty-facade/v1/point:record:list  List<MemberPointRecordResponse>
class MemberPointRecordResponse {
    var id: String? = null
    var planId: Long? = null
    var planName: String? = null
    var subjectId: Long? = null
    var subjectName: String? = null
    var subjectFqn: String? = null
    var pointAccountTypeId: Long? = null
    var pointAccountTypeName: String? = null
    var memberId: String? = null
    var point: BigDecimal? = null
    var recordType: String? = null
    var effectiveDate: ZonedDateTime? = null
    var overdueDate: ZonedDateTime? = null
    var desc: String? = null
    var totalPoint: BigDecimal? = null
    var created: ZonedDateTime? = null
    var changeMode: String? = null
    var traceId: String? = null
    var key: String? = null
    var channelType: String? = null
    var shopId: String? = null
    @JsonAlias("kzzd1", "KZZD1") var kzzd1: String? = null
    @JsonAlias("kzzd2", "KZZD2") var kzzd2: String? = null
    @JsonAlias("kzzd3", "KZZD3") var kzzd3: String? = null
    var actionId: String? = null
    var actionName: String? = null
    var actionNodeId: String? = null
    var actionNodeName: String? = null
    var status: String? = null
    var ruleId: Long? = null
    var ruleName: String? = null
    var eventTypeName: String? = null
    var signedPoint: BigDecimal? = null
}


// 查询会员积分记录条数
// GET /loyalty-facade/v1/point:record:count
class MemberPointRecordCountResponse {
    var totalElements: Long? = null
}


// 查询单笔会员积分记录详情
// GET /loyalty-facade/v1/point:record:detail
class MemberPointRecordDetailResponse {
    var recordType: String? = null
    var recordSourceDetail: String? = null
    var effectiveDate: ZonedDateTime? = null
    var overdueDate: ZonedDateTime? = null
    var extralInfo: String? = null
    var recordDetail: String? = null
    var desc: String? = null
    var operator: String? = null
    var operatorId: String? = null
    var channel: String? = null
    var modified: ZonedDateTime? = null
    var key: String? = null
    var ruleGroup: String? = null
    var changeMode: String? = null
    var planName: String? = null
    var pointPlanName: String? = null
    var traceId: String? = null
    var totalPoint: BigDecimal? = BigDecimal.ZERO
    var memberPointId: String? = null
    var planId: Long? = null
    var pointPlanId: Long? = null
    var memberId: String? = null
    var point: BigDecimal? = BigDecimal.ZERO
    var subjectFqn: String? = null
    var created: ZonedDateTime = ZonedDateTime.now()
}


// 查询积分明细记录
// GET /loyalty-facade/v1/point:record:item
class MemberPointRecordItemResponse {
    var planId: Long? = null
    var pointPlanId: Long? = null
    var traceId: String? = null
    var memberId: String? = null
    var point: BigDecimal = BigDecimal.ZERO
    var created: ZonedDateTime? = null
    var effectiveDate: ZonedDateTime? = null
    var overdueDate: ZonedDateTime? = null
    var sort: Long? = null
    var status: String? = null
}