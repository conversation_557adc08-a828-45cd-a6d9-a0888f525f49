package com.shuyun.loyalty.sdk.api.model.http.points

import com.fasterxml.jackson.annotation.JsonAlias
import java.math.BigDecimal
import java.time.ZonedDateTime

// 批量变更会员积分
// POST /loyalty-facade/v1/point:batch:modify
class MemberPointBatchModifyRequest {
    var pointAccountId: Long? = null
    var recordType: String? = null
    var triggerId: String? = null
    var openPointBatchItems: List<MemberPointBatchModifyRequestItem>? = null
}


class MemberPointBatchModifyRequestItem {
    var memberId: String? = null
    var point: BigDecimal? = null
    var overdueDate: ZonedDateTime? = null
    var desc: String? = null
    var channelType: String? = null
    var effectDate: ZonedDateTime? = null
    var actionId: String? = null
    var actionName: String? = null
    var actionNodeId: String? = null
    var actionNodeName: String? = null
    var changeMode: String? = null
    var shopId: String? = null
    @JsonAlias("kzzd1", "KZZD1") var kzzd1: String? = null
    @JsonAlias("kzzd2", "KZZD2") var kzzd2: String? = null
    @JsonAlias("kzzd3", "KZZD3") var kzzd3: String? = null
}


// 查询批量变更积分结果
// GET /loyalty-facade/v1/point:batch:log
class MemberPointBatchModifyResponse {
    var openType: String? = null
    var triggerId: String? = null
    var processStatus: String? = null
    var totalNumber: Int? = null
    var failNumber: Int? = null
    var successNumber: Int? = null
    var memberPointModifyBatchItemResponse: List<MemberPointBatchModifyResponseItem>? = null
}


class MemberPointBatchModifyResponseItem {
    var memberId: String? = null
    var responseErrorData: String? = null
}