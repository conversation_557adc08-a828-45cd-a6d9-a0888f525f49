package com.shuyun.loyalty.sdk.api.model.http.points

import com.fasterxml.jackson.annotation.JsonAlias
import java.math.BigDecimal

// 撤销会员积分
// POST /loyalty-facade/v1/point:revert
class MemberPointRevertRequest {
    var pointAccountId: Long? = null
    var memberId: String? = null
    var recordType: ReversePointEvent? = null
    var point: BigDecimal? = null
    var channelType: String? = null
    var changeMode: String? = null
    var desc: String? = null
    var uniqueId: String? = null
    var tradeId: String? = null
    var shopId: String? = null
    @JsonAlias("kzzd1","KZZD1")  var kzzd1: String? = null
    @JsonAlias("kzzd2","KZZD2")  var kzzd2: String? = null
    @JsonAlias("kzzd3","KZZD3")  var kzzd3: String? = null
    var actionId: String? = null
    var actionName: String? = null
    var actionNodeId: String? = null
    var actionNodeName: String? = null
}

enum class ReversePointEvent(val desc: String){
    SEND("立即发放"),
    DEDUCT("扣减"),
    USE_FREEZE("冻结消耗")
}

class MemberPointRevertResponse {
    var requestId: String? = null
}