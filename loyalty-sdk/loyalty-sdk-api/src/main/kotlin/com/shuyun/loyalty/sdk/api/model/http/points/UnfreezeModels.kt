package com.shuyun.loyalty.sdk.api.model.http.points

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnore
import java.math.BigDecimal


// 积分解冻
// POST /loyalty-facade/v1/point:unfrozen
class MemberPointUnfreezeRequest {
    var pointAccountId: Long? = null
    var memberId: String? = null
    var channelType: String? = null
    var changeMode: String? = null
    var businessId: String? = null
    var lockWaitTime: Long? = null
    var shopId: String? = null
    var desc: String? = null
    var actionName: String? = null
    var actionId: String? = null
    var actionNodeId: String? = null
    var actionNodeName: String? = null
    @JsonAlias("kzzd1","KZZD1") var kzzd1: String? = null
    @JsonAlias("kzzd2","KZZD2")  var kzzd2: String? = null
    @JsonAlias("kzzd3","KZZD3")  var kzzd3: String? = null
    var autoFillShopId: Boolean? = false
}


class MemberPointUnfreezeResponse {
    @JsonIgnore var memberPointId: String? = null
    var point: BigDecimal? = null
    var type: String? = "OPEN_UNFREEZE"
    var requestId: String? = null
}