@file:JvmMultifileClass

package com.shuyun.loyalty.sdk.api.model.http.grade

import java.time.ZonedDateTime

// 等级初始化为最低等级
// POST /loyalty-facade/v1/grade/init
class MemberGradeInitRequest {
    var gradeHierarchyId: Long? = null
    var memberId: String? = null
    var overdueDate: ZonedDateTime? = null
    var description: String? = null
    var channelType: String? = null
    var triggerId: String? = null
    var changeWayType: String? = null
}


// 等级变更
// POST /loyalty-facade/v1/grade/modify
class MemberGradeModifyRequest {
    var gradeHierarchyId: Long? = null
    var memberId: String? = null
    var gradeDefinitionId: Long? = null
    var overdueDate: ZonedDateTime? = null
    var description: String? = null
    var useOriginalEffectTime: Boolean? = null
    var channelType: String? = null
    var triggerId: String? = null
    var changeWayType: String? = null
}


// 批量等级变更到最高或者最低
// POST /loyalty-facade/v1/grade/modify:batch
class MemberGradeBatchModifyRequest {
    var gradeHierarchyIds: List<Long>? = null
    var memberId: String? = null
    var channelType: String? = null
    var triggerId: String? = null
    var changeWayType: String? = null
    var gradeModifyType: GradeBatchModifyType? = null
    var overdueDate: ZonedDateTime? = null
    var description: String? = null
}


enum class GradeBatchModifyType {
    LOWEST_GRADE,
    HIGHEST_GRADE,
    NO_GRADE
}