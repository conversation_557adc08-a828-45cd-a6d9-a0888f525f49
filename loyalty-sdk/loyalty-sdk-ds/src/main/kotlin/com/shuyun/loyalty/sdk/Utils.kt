package com.shuyun.loyalty.sdk

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.core.json.JsonReadFeature
import com.fasterxml.jackson.databind.*
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.convertValue
import com.fasterxml.jackson.module.kotlin.readValue
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.format.DateTimeFormatterBuilder
import java.time.temporal.ChronoField
import java.util.*


object Json {

    val objectMapper: ObjectMapper = ObjectMapper()

    init {
        setDefault(objectMapper)
    }

    fun setDefault(objectMapper: ObjectMapper) {
        objectMapper.findAndRegisterModules()
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
        objectMapper.setTimeZone(TimeZone.getDefault())
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL)
        objectMapper.enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN)
        objectMapper.enable(JsonParser.Feature.ALLOW_SINGLE_QUOTES)
        objectMapper.enable(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature())
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        objectMapper.registerModule(JavaTimeModule())
        objectMapper.registerModule(SimpleModule().apply {
            addSerializer(MyZonedDateTimeSerializer())
            addSerializer(MyLocalDateTimeSerializer())
            addSerializer(MyLocalTimeSerializer())
            //addSerializer(MyEscapedStringSerializer())
        })
        objectMapper.registerKotlinModule()
    }


    fun <T> toJson(t: T): String = objectMapper.writeValueAsString(t)

    @Suppress("unused")
    fun <T> toJsonPretty(t: T): String = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(t)

    inline fun <reified T> parse(json: String): T = objectMapper.readValue<T>(json)

    inline fun <reified T> convert(from: Any): T = objectMapper.convertValue<T>(from)

    inline fun <reified T> copy(from: T): T = parse(toJson(from))

    private class MyZonedDateTimeSerializer : JsonSerializer<ZonedDateTime>() {
        // 日志格式的毫秒强制格式化为三位数
        private var dateTimeFormatter: DateTimeFormatter = DateTimeFormatterBuilder().appendPattern("yyyy-MM-dd'T'HH:mm:ss")
            .appendFraction(ChronoField.MILLI_OF_SECOND, 3, 3, true)
            .appendOffsetId()
            .toFormatter()

        override fun handledType(): Class<ZonedDateTime> = ZonedDateTime::class.java

        override fun serialize(zonedDateTime: ZonedDateTime, jsonGenerator: JsonGenerator, serializerProvider: SerializerProvider) {
            val format = dateTimeFormatter.format(zonedDateTime)
            jsonGenerator.writeString(format)
        }
    }


    private class MyLocalDateTimeSerializer : JsonSerializer<LocalDateTime>() {
        // 日志格式的毫秒强制格式化为三位数
        private var dateTimeFormatter: DateTimeFormatter = DateTimeFormatterBuilder().appendPattern("yyyy-MM-dd'T'HH:mm:ss")
            .appendFraction(ChronoField.MILLI_OF_SECOND, 3, 3, true)
            .toFormatter()

        override fun handledType(): Class<LocalDateTime> = LocalDateTime::class.java

        override fun serialize(localDateTime: LocalDateTime, jsonGenerator: JsonGenerator, serializerProvider: SerializerProvider) {
            val format = dateTimeFormatter.format(localDateTime)
            jsonGenerator.writeString(format)
        }
    }


    private class MyLocalTimeSerializer : JsonSerializer<LocalTime>() {
        // 日志格式的毫秒强制格式化为三位数
        private var dateTimeFormatter: DateTimeFormatter = DateTimeFormatterBuilder().appendPattern("HH:mm:ss")
            .appendFraction(ChronoField.MILLI_OF_SECOND, 3, 3, true)
            .toFormatter()

        override fun handledType(): Class<LocalTime> = LocalTime::class.java

        override fun serialize(localTime: LocalTime, jsonGenerator: JsonGenerator, serializerProvider: SerializerProvider) {
            val format = dateTimeFormatter.format(localTime)
            jsonGenerator.writeString(format)
        }
    }
}


object Property {
    fun getSysOrEnv(key: String): String? = System.getProperty(key) ?: System.getenv(key)
    fun getSysOrEnv(key: String, default: String) = getSysOrEnv(key) ?: default
    fun getSysOrEnv(key: String, default: Int) = getSysOrEnv(key)?.toInt() ?: default
    fun getSysOrEnv(key: String, default: Boolean) = getSysOrEnv(key)?.toBoolean() ?: default
    fun getSysOrEnv(key: String, default: Long) = getSysOrEnv(key)?.toLong() ?: default
}


object Uuid {
    val uuid: String get()  {
        val uuid = UUID.randomUUID()
        val str = uuid.toString()
        return str.replace("-", "")
    }
}
