package com.shuyun.loyalty.sdk

import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

fun ZonedDateTime.localDate(): LocalDate {
    return this.withZoneSameInstant(ZoneId.of("Asia/Shanghai")).toLocalDate()
}


fun ZonedDateTime?.utcStr(): String? {
    if (this == null) return null
    return DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").format(this.withZoneSameInstant(ZoneOffset.UTC))
}

fun ZonedDateTime.toEpochMilli(): Long {
    return this.toInstant().toEpochMilli()
}


fun ZonedDateTime.shDate(): LocalDate {
    return this.withZoneSameInstant(ZoneId.of("Asia/Shanghai")).toLocalDate()
}

