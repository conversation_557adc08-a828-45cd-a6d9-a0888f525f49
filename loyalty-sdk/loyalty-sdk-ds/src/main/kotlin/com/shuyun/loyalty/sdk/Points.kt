package com.shuyun.loyalty.sdk

import com.shuyun.dm.api.enums.DataModelStatus
import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.loyalty.sdk.Dataapi.sdk
import com.shuyun.loyalty.sdk.MetadataApi.getModel
import com.shuyun.loyalty.sdk.Vers.isHigherLoyaltyVersion
import org.slf4j.LoggerFactory
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters

object Plans {

    data class APlan(val id: Long, val planVersionId: Long, val name: String, val subjects: List<ASubject>)
    data class ASubject(val id: Long, val name: String, val fqn: String, val subjectEventTypes: List<ASubjectEventType>, val pointAccountTypes: List<APointAccountType>, val gradeHierarchies: List<AGradeHierarchy>, val medalHierarchies: List<AMedalHierarchy>)
    data class ASubjectEventType(val versionId: Long, val id: Long, val name: String, val eventStream: String, val originalOrderPath: String?, val referencePath: String?, var subjectFqn: String? = null)
    data class APointAccountType(val id: Long, val name: String, val negativeStrategy: String, val sendLimitRule: String?)
    data class AGradeHierarchy(val id: Long, val name: String, val gradeDefinitions: List<AGradeDefinition>)
    data class AGradeDefinition(val id: Long, val name: String, val sort: Int)
    data class AMedalHierarchy(val id: Long, val name: String, val medalDefinitions: List<AMedalDefinition>)
    data class AMedalDefinition(val id: Long, val name: String, val sort: Int)
    data class ADeductionPriority(val name: String, val order: String, var next: String? = null, var equalValue: Any? = null)

    @Suppress("UNCHECKED_CAST")
    fun findPlans(excludePoint: Boolean = false, excludeGrade: Boolean = false, excludeMedal: Boolean = false, excludeEvent: Boolean = false): List<APlan> {
        return sdk.use { conn ->
            val planSql = """
                SELECT id, versionId, name, sendLimitRuleSwitch
                FROM data.loyalty.manager.plan 
                WHERE disabled = 0 AND status='PUBLISHED'
            """.trimIndent()
            val response = conn.execute(planSql, mapOf())
            val plans = response.data.map { d ->
                val row = d as Map<String, Any?>
                val planVersionId = row["versionId"].toString().toLong()
                val subjectSql = """
                    SELECT id, versionId, name, dataType as fqn ,mergeFqn ,hasMerge,status
                    FROM data.loyalty.manager.subject 
                    WHERE planVersionId = :planVersionId 
                    AND disabled = 0 
                    AND status = 'ENABLED'
                """.trimIndent()
                val subjectResponse = conn.execute(subjectSql, mapOf("planVersionId" to planVersionId))
                val subjects = subjectResponse.data.map { s ->
                    val row = s as Map<String, Any?>
                    val subjectVersionId = row["versionId"].toString().toLong()
                    val eventTypes = if (!excludeEvent) {
                        val eventTypeSql = """
                            SELECT `versionId`,`id`, `name`,`eventStream`,`operation`,`operationGrade`,`operationMedal`,`status`, originalOrderPath, referencePath
                            FROM data.loyalty.manager.eventType 
                            WHERE subjectVersionId = :subjectVersionId
                            AND disabled = 0 
                            AND status = 'ENABLED'
                        """.trimIndent()
                        val params = mapOf("subjectVersionId" to subjectVersionId)
                        val eventTypeResponse = conn.execute(eventTypeSql, params)
                        val eventTypes = eventTypeResponse.data.map { e ->
                            val row = e as Map<String, Any?>
                            ASubjectEventType(
                                id = row["id"].toString().toLong(),
                                versionId = row["versionId"].toString().toLong(),
                                name = row["name"] as String,
                                eventStream = row["eventStream"] as String,
                                originalOrderPath = row["originalOrderPath"] as String?,
                                referencePath = row["referencePath"] as String?
                            )
                        }
                        eventTypes
                    } else {
                        emptyList()
                    }

                    val accountTypes = if (!excludePoint) {
                        val pointAccountTypeSql = """
                            SELECT 
                                id, name, priorityDeduction, negativeStrategy, 
                                topLimit, singleTopLimit, sendLimitRule,
                                precision, unit, rounding,updateTime
                            FROM data.loyalty.manager.pointAccountType 
                            WHERE subjectVersionId = :subjectVersionId  
                            AND disabled = 0 
                            AND status = 'PUBLISHED'
                        """.trimIndent()
                        val pointAccountTypeResponse = conn.execute(pointAccountTypeSql, mapOf("subjectVersionId" to subjectVersionId))
                        val defaultDeductionPriorities = listOf(
                            ADeductionPriority("overdueDate", "EARLIEST", "effectiveDate"),
                            ADeductionPriority("effectiveDate", "EARLIEST")
                        )
                        val accountTypes = pointAccountTypeResponse.data.map { a ->
                            val row = a as Map<String, Any?>
//                            var priorityDeduction = row["priorityDeduction"] as String?
//                            if (priorityDeduction.isNullOrBlank()) {
//                                priorityDeduction = Json.toJson(defaultDeductionPriorities)
//                            }
                            APointAccountType(
                                id = row["id"].toString().toLong(),
                                name = row["name"] as String,
                                negativeStrategy = row["negativeStrategy"] as String,
                                sendLimitRule = (row["sendLimitRule"] as String?)?.let { it.ifBlank { null } },
                            )
                        }
                        accountTypes
                    } else {
                        emptyList()
                    }


                    val gradeHierarchies = if (!excludeGrade) {
                        val gradeHierarchySql = """
                            SELECT versionId, id, name 
                            FROM data.loyalty.manager.gradeHierarchy 
                            WHERE subjectVersionId = :subjectVersionId 
                            AND disabled = false 
                            AND status = 'PUBLISHED' 
                        """.trimIndent()
                        val gradeHierarchyResponse = conn.execute(gradeHierarchySql, mapOf("subjectVersionId" to subjectVersionId))
                        val gradeHierarchies = gradeHierarchyResponse.data.map { g ->
                            val row = g as Map<String, Any?>
                            val gradeDefinitionSql = """
                                SELECT id, name, sort 
                                FROM data.loyalty.manager.gradeDefinition 
                                WHERE gradeHierarchyVersionId = :gradeHierarchyVersionId 
                                AND disabled = false 
                                ORDER BY sort
                            """.trimIndent()
                            val gradeDefinitionResponse = conn.execute(gradeDefinitionSql, mapOf("gradeHierarchyVersionId" to row["versionId"]))
                            val gradeDefinitions = gradeDefinitionResponse.data.map { gd ->
                                val gdRow = gd as Map<String, Any?>
                                AGradeDefinition(
                                    id = gdRow["id"].toString().toLong(),
                                    name = gdRow["name"] as String,
                                    sort = gdRow["sort"].toString().toInt()
                                )
                            }
                            AGradeHierarchy(
                                id = row["id"].toString().toLong(),
                                name = row["name"] as String,
                                gradeDefinitions = gradeDefinitions
                            )
                        }
                        gradeHierarchies
                    } else {
                        emptyList()
                    }


                    val medalHierarchies = if (!excludeMedal) {
                        val medalHierarchySql = """
                            SELECT versionId, id, name 
                            FROM data.loyalty.manager.medalHierarchy 
                            WHERE subjectVersionId = :subjectVersionId 
                            AND disabled = false 
                            AND status = 'PUBLISHED' 
                        """.trimIndent()
                        val medalHierarchyResponse = conn.execute(medalHierarchySql, mapOf("subjectVersionId" to subjectVersionId))
                        val medalHierarchies = medalHierarchyResponse.data.map { m ->
                            val row = m as Map<String, Any?>
                            val medalDefinitionSql = """
                                SELECT id, name, sort 
                                FROM data.loyalty.manager.medalDefinition 
                                WHERE medalHierarchyVersionId = :medalHierarchyVersionId 
                                AND disabled = false
                                ORDER BY sort
                            """.trimIndent()
                            val medalDefinitionResponse = conn.execute(medalDefinitionSql, mapOf("medalHierarchyVersionId" to row["versionId"]))
                            val medalDefinitions = medalDefinitionResponse.data.map { md ->
                                val mdRow = md as Map<String, Any?>
                                AMedalDefinition(
                                    id = mdRow["id"].toString().toLong(),
                                    name = mdRow["name"] as String,
                                    sort = mdRow["sort"].toString().toInt()
                                )
                            }
                            AMedalHierarchy(
                                id = row["id"].toString().toLong(),
                                name = row["name"] as String,
                                medalDefinitions = medalDefinitions,
                            )
                        }
                        medalHierarchies
                    } else {
                        emptyList()
                    }

                    ASubject(
                        id = row["id"].toString().toLong(),
                        name = row["name"] as String,
                        fqn = row["fqn"] as String,
                        subjectEventTypes = eventTypes,
                        pointAccountTypes = accountTypes,
                        gradeHierarchies = gradeHierarchies,
                        medalHierarchies = medalHierarchies
                    )
                }

                APlan(
                    id = row["id"].toString().toLong(),
                    planVersionId = row["versionId"].toString().toLong(),
                    name = (row["name"] as? String) ?: "",
                    subjects = subjects
                )
            }
            plans
        }
    }
}


object Limits {

    private const val POINT = "POINT"
    private const val COUNT = "COUNT"

    private val LocalDate.str: String get() = format(DateTimeFormatter.ofPattern("yyyyMMdd"))

    data class UnitDate(
        val max: LocalDate,
        val year: LocalDate,
        val quarter: LocalDate,
        val month: LocalDate,
        val week: LocalDate,
        val day: LocalDate
    ) {
        operator fun get(unit: LimitTimeUnit): String {
            return when (unit) {
                LimitTimeUnit.MAX -> max.str
                LimitTimeUnit.YEAR -> year.str
                LimitTimeUnit.QUARTER -> quarter.str
                LimitTimeUnit.MONTH -> month.str
                LimitTimeUnit.WEEK -> week.str
                LimitTimeUnit.DAY -> day.str
            }
        }
        operator fun get(unit: String): String {
            return get(LimitTimeUnit.valueOf(unit))
        }
    }

    class LimitValue: LinkedHashMap<String, Number>() {
        override fun toString(): String {
            return Json.toJson(this)
        }
    }


    fun getUnitFirstDate(d: LocalDate): UnitDate {
        // 每个单位所在的第一天日期
        val max = LocalDate.of(3000, 12, 31)
        val year = LocalDate.of(d.year, 1, 1)
        val quarter = LocalDate.of(d.year, d.month.firstMonthOfQuarter(), 1)
        val month = LocalDate.of(d.year, d.month, 1)
        val week = d.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
        val day = LocalDate.of(d.year, d.month, d.dayOfMonth)
        return UnitDate(max, year, quarter, month, week, day)
    }


    fun buildLimitValue(date: ZonedDateTime = ZonedDateTime.now(), initPoints: BigDecimal = BigDecimal.ZERO, initCount: Int = 0): LimitValue {
        val ufd = getUnitFirstDate(date.withZoneSameInstant(ZoneId.of("Asia/Shanghai")).toLocalDate())
        val map = LimitValue()
        for (unit in LimitTimeUnit.entries) {
            map["${ufd[unit]}|${unit}|${POINT}"] = initPoints
            map["${ufd[unit]}|${unit}|${COUNT}"] = initCount
        }
        return map
    }


    private fun newLimitValue(oldLimitValue: LimitValue): LimitValue {
        val newLimitValue = buildLimitValue(ZonedDateTime.now())
        for (key in newLimitValue.keys) {
            oldLimitValue[key]?.let { newLimitValue[key] = it }
        }
        return newLimitValue
    }


    fun incrementAndGet(limitValue: String, points: BigDecimal, count: Int = 1, date: ZonedDateTime = ZonedDateTime.now()): LimitValue {
        val oldLimitValue = Json.parse<LimitValue>(limitValue)
        val newLimitValue = newLimitValue(oldLimitValue)
        val keys = buildLimitValue(date).keys
        for (k in keys) {
            val isPoint = k.endsWith(POINT)
            if (isPoint) {
                newLimitValue[k]?.let { newLimitValue[k] = (it.toString().toBigDecimal()) + points }
            } else {
                newLimitValue[k]?.let { newLimitValue[k] = (it.toInt()) + count }
            }
        }
        return newLimitValue
    }


    fun decrementAndGet(limitValue: String, points: BigDecimal, count: Int = 1, date: ZonedDateTime = ZonedDateTime.now()): LimitValue {
        val oldLimitValue = Json.parse<LimitValue>(limitValue)
        val newLimitValue = newLimitValue(oldLimitValue)
        val keys = buildLimitValue(date).keys
        for (k in keys) {
            val isPoint = k.endsWith(POINT)
            if (isPoint) {
                newLimitValue[k]?.let {
                    val newPoints = (it.toString().toBigDecimal()) - points
                    newLimitValue[k] = if (newPoints < BigDecimal.ZERO) BigDecimal.ZERO else newPoints
                }
            } else {
                newLimitValue[k]?.let {
                    val newCount = (it.toInt()) - count
                    newLimitValue[k] = if (newCount < 0) 0 else newCount
                }
            }
        }
        return newLimitValue
    }

    enum class LimitTimeUnit(val desc: String) {
        MAX("最大"),
        YEAR("每年"),
        QUARTER("每季度"),
        MONTH("每月"),
        WEEK("每周"),
        DAY("每天"),
        ;
    }
}


object RiskCtrlC {

    private const val CHECKLIST_SQL = "SELECT id FROM data.prctvmkt.common.Checklist WHERE TYPE = 'BLACK' AND CUSTOMERNO = :customerno AND SUBJECTID = :subjectId"
    private const val CHECKLIST_NORMAL_SQL = "$CHECKLIST_SQL LIMIT 1"
    private const val CHECKLIST_GROUP_SQL = "$CHECKLIST_SQL AND TAGS INCLUDE ANY (:tags) LIMIT 1"


    fun existBlack(customerno: String, fqn: String, groupIds: List<String>? = null, sdk: (sql: String, params: Map<String, Any>) -> List<Map<String, Any?>>): Boolean {
        val params: MutableMap<String, Any> = mutableMapOf("customerno" to customerno, "subjectId" to fqn)
        return if (groupIds.isNullOrEmpty()) {
            sdk(CHECKLIST_NORMAL_SQL, params).isNotEmpty()
        } else {
            params["tags"] = groupIds
            sdk(CHECKLIST_GROUP_SQL, params).isNotEmpty()
        }
    }
}


object Segments {


    private val logger = LoggerFactory.getLogger(Segments::class.java)


    private val LONG_TERM_OVERDUE_DATE: ZonedDateTime = ZonedDateTime.parse("3000-12-12T00:00:00.000Z")
    private val NEW_OVERDUE_DATE: ZonedDateTime = ZonedDateTime.parse("${LocalDate.now().year + Property.getSysOrEnv("new.vs.add.year", 1)}-12-31T23:59:59.999+08:00")


    /**
     * 重建积分块数据 reference: 0-以当前会员积分总账为准，1-以有效积分记录为准
     */
    fun rebuildSegment(sdk: DataapiWebSocketSdk, pointAccountId: Long, memberId: String, reference: Int, now: ZonedDateTime = ZonedDateTime.now()): BigDecimal {
        val memberPoints = selectMemberPoints(sdk, pointAccountId, memberId)
        if (memberPoints.size > 1) {
            throw IllegalStateException("同一个会员ID(${memberId})在(${pointAccountId})中存在多个账户")
        }

        val memberPoint = memberPoints.firstOrNull() ?: return BigDecimal.ZERO

        logger.info("重建积分块数据 memberId: {} point: {}", memberPoint.memberId, memberPoint.point)

        deletePointSegmentByMemberIds(sdk, memberPoint.pointPlanId, listOf(memberId))

        adjustRecords(sdk, memberPoint, reference)

        memberPoint.openSegmentFlag = true

        val segments = mutableListOf<BasicSegment>()
        if (memberPoint.point > BigDecimal.ZERO) {
            val validPoints = queryValidPoints(sdk, memberPoint.pointPlanId, memberPoint.memberId)
            if (memberPoint.point > validPoints.sumOf { it.point } && reference == 0) {
                val p1 = queryValidPoints(sdk, memberPoint.pointPlanId, memberPoint.memberId, overdueDate = ZonedDateTime.now().minusYears(5)).sumOf { it.point }
                if (memberPoint.point.compareTo(p1) != 0) {
                    segments.add(BasicSegment(Uuid.uuid, memberPoint.id, memberPoint.point - validPoints.sumOf { it.point }, NEW_OVERDUE_DATE.shDate()))
                }
            }
            for (v in validPoints) {
                val date = v.overdueDate?.shDate() ?: LONG_TERM_OVERDUE_DATE.shDate()
                var last: BasicSegment? = null
                for (segment in segments) {
                    if (segment.expireDate.isAfter(date)) {
                        continue
                    }
                    segment.point += v.point
                    last = segment
                }
                if (last == null) {
                    segments.add(BasicSegment(Uuid.uuid, memberPoint.id, v.point, date))
                } else {
                    if (!last.expireDate.isEqual(date)) {
                        segments.add(BasicSegment(Uuid.uuid, memberPoint.id, v.point, date))
                    }
                }
            }
        }
        if (memberPoint.point < BigDecimal.ZERO) {
            segments.add(BasicSegment(Uuid.uuid, memberPoint.id, memberPoint.point, LONG_TERM_OVERDUE_DATE.shDate()))
        }

        batchInsertPointSegment(sdk, memberPoint, segments)
        updateMemberPoint(sdk, memberPoint, now)
        return memberPoint.point
    }


    private fun adjustRecords(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, reference: Int) {
        val beforePoints = memberPoint.point
        val negativeRecords = queryNegativePoints(sdk, memberPoint.pointPlanId, memberPoint.memberId)
        val validRecords = queryValidPoints(sdk, memberPoint.pointPlanId, memberPoint.memberId)

        val sumValidPoints = validRecords.sumOf { x -> x.point }
        val sumNegativePoints = negativeRecords.sumOf { x -> x.point }
        val sumPoints = if (reference == 0) beforePoints else (sumValidPoints - sumNegativePoints)
        if (sumPoints > BigDecimal.ZERO) {
            deleteNegativePoints(sdk, memberPoint.pointPlanId, memberPoint.memberId)
            if (sumPoints < sumValidPoints) {
                var p = sumValidPoints - sumPoints
                for (validPoint in validRecords) {
                    if (validPoint.point > p) {
                        validPoint.point -= p
                        updateValidPointsById(sdk, memberPoint.pointPlanId, validPoint.id, validPoint.point)
                        p = BigDecimal.ZERO
                    } else {
                        deleteValidPointsById(sdk, memberPoint.pointPlanId, validPoint.id)
                        p -= validPoint.point
                        validPoint.point = BigDecimal.ZERO
                    }
                    if (p <= BigDecimal.ZERO) {
                        break
                    }
                }
            }
            if (sumPoints > sumValidPoints) {
                insertPointValidStatements(sdk, memberPoint, sumPoints - sumValidPoints)
            }
        } else if (sumPoints < BigDecimal.ZERO) {
            deleteValidPoints(sdk, memberPoint.pointPlanId, memberPoint.memberId)
            if (sumPoints.abs() < sumNegativePoints) {
                var p = sumNegativePoints - sumPoints.abs()
                for (negativePoint in negativeRecords) {
                    if (negativePoint.point > p) {
                        negativePoint.point -= p
                        updateNegativePointsById(sdk, memberPoint.pointPlanId, negativePoint.id, negativePoint.point)
                        p = BigDecimal.ZERO
                    } else {
                        deleteNegativePointsById(sdk, memberPoint.pointPlanId, negativePoint.id)
                        p -= negativePoint.point
                        negativePoint.point = BigDecimal.ZERO
                    }
                    if (p <= BigDecimal.ZERO) {
                        break
                    }
                }
            }
            if (sumPoints.abs() > sumNegativePoints) {
                val p = sumPoints.abs() - sumNegativePoints
                insertNegativePoints(sdk, memberPoint, p, Uuid.uuid)
            }
        } else {
            deleteNegativePoints(sdk, memberPoint.pointPlanId, memberPoint.memberId)
            deleteValidPoints(sdk, memberPoint.pointPlanId, memberPoint.memberId)
        }

        memberPoint.point = sumPoints
    }


    private data class MemberPoint(
        var id: String,
        var planId: Long,
        var pointPlanId: Long,
        var memberId: String,
        var subjectFqn: String,
        var point: BigDecimal,
        var version: Long,
        var openSegmentFlag: Boolean
    )

    private fun selectMemberPoints(
        sdk: DataapiWebSocketSdk,
        pointAccountId: Long,
        memberId: String
    ): List<MemberPoint> {
        val sql = """
            SELECT id,planId,pointPlanId,memberId,subjectFqn,point,version,openSegmentFlag
            FROM data.loyalty.member.account.point$pointAccountId
            WHERE memberId = :memberId
        """.trimIndent()
        val contents = sdk.execute(sql, mapOf("memberId" to memberId)).data
        return Json.convert(contents)
    }


    private fun deletePointSegmentByMemberIds(sdk: DataapiWebSocketSdk, pointPlanId: Long, memberIds: List<String>) {
        if (memberIds.isEmpty()) {
            return
        }
        val sql = """
            DELETE FROM data.loyalty.member.account.PointSegment${pointPlanId} 
            WHERE memberId in (:memberIds)
        """.trimIndent()
        val params = mapOf("memberIds" to memberIds)
        sdk.execute(sql, params)
    }


    private data class ValidPointRecord(
        var id: String,
        var planId: Long,
        var pointPlanId: Long,
        var memberPointId: String,
        var memberId: String,
        var subjectFqn: String,
        var effectiveDate: ZonedDateTime,
        var overdueDate: ZonedDateTime?,
        var point: BigDecimal,
        var gainStatementId: String,
        var fromStatus: String,
        var openTraceId: String?,
        var modified: ZonedDateTime?,
        var created: ZonedDateTime?
    )

    private fun queryValidPoints(
        sdk: DataapiWebSocketSdk,
        hierarchyId: Long,
        memberId: String,
        overdueDate: ZonedDateTime = ZonedDateTime.now()
    ): List<ValidPointRecord> {
        val pageSize = 3000
        var number = 0
        val map = HashMap<String, ValidPointRecord>()
        while (true) {
            val sql = """
                select `id`,`planId`,`pointPlanId`,`memberPointId`,`memberId`,`subjectFqn`,`point`,`gainStatementId`,`effectiveDate`,`overdueDate`,`fromStatus`,`modified`,`created`,`openTraceId`
                from data.loyalty.member.account.ValidStatement$hierarchyId
                where memberId = :memberId and fromStatus not in (:fromStatus) and (overdueDate > :overdueDate or overdueDate is null)
                order by id
                LIMIT $pageSize OFFSET ${number * pageSize}
            """.trimIndent()

            val params = mapOf(
                "memberId" to memberId,
                "fromStatus" to listOf("OPEN_FROZE", "SPECIAL_FROZE"),
                "overdueDate" to overdueDate.utcStr()
            )

            val data = sdk.execute(sql, params).data
            val records: List<ValidPointRecord> = Json.convert(data)
            for (record in records) {
                map[record.id] = record
            }
            if (records.isEmpty() || records.size < pageSize) {
                break
            }
            number++
        }
        return map.values.toList().sortedBy { it.overdueDate ?: LONG_TERM_OVERDUE_DATE }

    }

    private fun batchInsertPointSegment(
        sdk: DataapiWebSocketSdk,
        memberPoint: MemberPoint,
        segments: List<BasicSegment>
    ) {
        if (segments.isEmpty()) return
        var sql =
            "INSERT INTO data.loyalty.member.account.PointSegment${memberPoint.pointPlanId} (id, planId, pointPlanId, memberPointId,memberId, subjectFqn, point, expireDate, modified, created, memberPoint) VALUES "
        var i = 0L
        val now = ZonedDateTime.now()
        for (item in segments) {
            val created = now.plus(i++, ChronoUnit.MILLIS).utcStr()
            val expireDate = if (isHigherLoyaltyVersion) "'${item.expireDate}'" else {
                if (item.expireDate == LONG_TERM_OVERDUE_DATE.shDate()) "NULL" else "'${item.expireDate}'"
            }
            sql += """
                (
                '${item.id}', ${memberPoint.planId}, ${memberPoint.pointPlanId}, '${memberPoint.id}', 
                '${memberPoint.memberId}', '${memberPoint.subjectFqn}', ${item.point}, ${expireDate}, 
                '${created}', '${created}', '${memberPoint.id}'
                ),
            """.trimIndent()
        }
        sql = sql.trim().dropLast(1)
        sdk.execute(sql, emptyMap())
    }


    private fun updateMemberPoint(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, now: ZonedDateTime = ZonedDateTime.now()) {
        val sql = """
            UPDATE data.loyalty.member.account.point${memberPoint.pointPlanId} 
            SET 
                point = :point, 
                version = :newVersion, 
                openSegmentFlag = :openSegmentFlag, 
                modified = :modified 
            WHERE id = :id
        """.trimIndent()
        val param = mapOf(
            "point" to memberPoint.point,
            "newVersion" to memberPoint.version + 1,
            "version" to memberPoint.version,
            "openSegmentFlag" to memberPoint.openSegmentFlag,
            "modified" to now,
            "id" to memberPoint.id
        )
        sdk.execute(sql, param)
        memberPoint.version += 1
    }


    private data class NegativePointRecord(
        var id: String,
        var point: BigDecimal,
    )

    private fun queryNegativePoints(
        sdk: DataapiWebSocketSdk,
        pointAccountId: Long,
        memberId: String
    ): List<NegativePointRecord> {
        val sql = """
            select id, point from data.loyalty.member.account.NegativeStatement$pointAccountId
            where memberId = :memberId
            order by created asc
            limit 10000
        """.trimIndent()
        val params = mapOf("memberId" to memberId)
        val rows = sdk.execute(sql, params).data
        return Json.convert(rows)
    }


    private fun insertNegativePoints(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, point: BigDecimal, recordId: String) {
        val sql = """
            INSERT INTO data.loyalty.member.account.NegativeStatement${memberPoint.pointPlanId}
            (id,planId,pointPlanId,memberPointId,memberId,subjectFqn,point,recordId,modified,created,memberPoint)
            VALUES (
              :id,:planId,:pointPlanId,:memberPointId,:memberId,:subjectFqn,:point,:recordId,:modified,:created,:memberPoint  
            )
        """.trimIndent()
        val params = mapOf(
            "id" to Uuid.uuid,
            "planId" to memberPoint.planId,
            "pointPlanId" to memberPoint.pointPlanId,
            "memberPointId" to memberPoint.id,
            "memberId" to memberPoint.memberId,
            "subjectFqn" to memberPoint.subjectFqn,
            "point" to point,
            "recordId" to recordId,
            "modified" to ZonedDateTime.now(),
            "created" to ZonedDateTime.now(),
            "memberPoint" to memberPoint.id
        )
        sdk.execute(sql, params)
    }


    private fun deleteNegativePoints(sdk: DataapiWebSocketSdk, pointAccountId: Long, memberId: String) {
        val sql = """
            delete from data.loyalty.member.account.NegativeStatement$pointAccountId
            where memberId = :memberId
        """.trimIndent()
        val params = mapOf("memberId" to memberId)
        sdk.execute(sql, params)
    }


    private fun updateValidPointsById(
        sdk: DataapiWebSocketSdk,
        hierarchyId: Long,
        id: String,
        newPoints: BigDecimal
    ) {
        val sql = """
            update data.loyalty.member.account.ValidStatement$hierarchyId
            set point = :newPoints
            where id = :id
        """.trimIndent()
        val params = mapOf(
            "newPoints" to newPoints,
            "id" to id
        )
        sdk.execute(sql, params)
    }


    private fun deleteValidPointsById(
        sdk: DataapiWebSocketSdk,
        hierarchyId: Long,
        id: String
    ) {
        val sql = """
            delete from data.loyalty.member.account.ValidStatement$hierarchyId
            where id = :id
        """.trimIndent()
        val params = mapOf(
            "id" to id
        )
        sdk.execute(sql, params)
    }

    private fun deleteValidPoints(sdk: DataapiWebSocketSdk, hierarchyId: Long, memberId: String) {
        val sql = """
            delete from data.loyalty.member.account.ValidStatement$hierarchyId
            where memberId = :memberId
        """.trimIndent()
        val params = mapOf("memberId" to memberId)
        sdk.execute(sql, params)
    }

    private fun updateNegativePointsById(
        sdk: DataapiWebSocketSdk,
        pointAccountId: Long,
        id: String,
        newPoints: BigDecimal
    ) {
        val sql = """
            update data.loyalty.member.account.NegativeStatement$pointAccountId
            set point = :newPoints
            where id = :id
        """.trimIndent()
        val params = mapOf(
            "newPoints" to newPoints,
            "id" to id
        )
        sdk.execute(sql, params)
    }


    private fun deleteNegativePointsById(
        sdk: DataapiWebSocketSdk,
        pointAccountId: Long,
        id: String
    ) {
        val sql = """
            delete from data.loyalty.member.account.NegativeStatement$pointAccountId
            where id = :id
        """.trimIndent()
        val params = mapOf(
            "id" to id
        )
        sdk.execute(sql, params)
    }


    private fun insertPointValidStatements(sdk: DataapiWebSocketSdk, memberPoint: MemberPoint, point: BigDecimal) {
        sdk.insert(
            "data.loyalty.member.account.ValidStatement${memberPoint.pointPlanId}",
            mapOf(
                "id" to Uuid.uuid,
                "planId" to memberPoint.planId,
                "pointPlanId" to memberPoint.pointPlanId,
                "memberPointId" to memberPoint.id,
                "memberId" to memberPoint.memberId,
                "subjectFqn" to memberPoint.subjectFqn,
                "point" to point,
                "gainStatementId" to "-",
                "effectiveDate" to ZonedDateTime.now(),
                "overdueDate" to NEW_OVERDUE_DATE,
                "fromStatus" to "VALID",
                "modified" to ZonedDateTime.now(),
                "created" to ZonedDateTime.now(),
                "gainStatement" to mapOf("id" to "-"),
                "memberPoint" to mapOf("id" to memberPoint.id)
            ), false, false
        )
    }

    private data class BasicSegment(
        val id: String,
        val memberPointId: String,
        var point: BigDecimal,
        var expireDate: LocalDate
    )

}


object Vers {

    val isHigherLoyaltyVersion by lazy {
        higherVersion()
    }

    private fun higherVersion(): Boolean {
        val plans = Plans.findPlans(excludeEvent = true, excludeGrade = true, excludeMedal = true)
        val ids = ArrayList<Long>()
        for (p in plans) {
            for (s in p.subjects) {
                for (pointAccountType in s.pointAccountTypes) {
                    ids.add(pointAccountType.id)
                }
            }
        }
        var f = false
        for (id in ids) {
            val fqn = "data.loyalty.member.account.Point${id}"
            val model = getModel(fqn) ?: continue
            if (model.status != DataModelStatus.COMMITTED) {
                continue
            }
            val modelJ = getModel("data.loyalty.member.PointCalculateJournal${id}") ?: continue
            if (modelJ.status == DataModelStatus.COMMITTED) {
                f = true
            }
            break
        }
        return f
    }
}