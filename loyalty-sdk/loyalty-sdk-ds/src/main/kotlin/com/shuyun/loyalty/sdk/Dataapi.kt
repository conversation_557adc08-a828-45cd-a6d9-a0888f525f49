package com.shuyun.loyalty.sdk

import com.shuyun.dm.api.domain.DataModel
import com.shuyun.dm.api.metadata.request.GetModelRequest
import com.shuyun.dm.api.vo.FetchStartRequest
import com.shuyun.dm.dataapi.sdk.client.DataapiWebSocketSdk
import com.shuyun.dm.metadata.sdk.MetadataSdkFactory
import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.sdk.ds.DataapiPoolFactory
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.withContext
import org.slf4j.LoggerFactory
import java.util.function.Function

object Dataapi {

    val sdk: DataapiWebSocketSdk get() {
        return DataapiPoolFactory.createHttpSdk().asDataapiWebSocketSdk().also {
            it.open()
        }
    }


    fun fetch(sql: String, params: Map<String, Any?> = emptyMap(), size: Int = 1000, block: (List<Map<String, Any?>>) -> Unit) {
        DataapiPoolFactory.createHttpSdk().fetch(FetchStartRequest(sql, params, size)).use { res ->
            while (true) {
                val response = res.next()
                if(!response.isSuccess) break
                val data = response?.data?.toList()
                if (data.isNullOrEmpty()) break
                block(data)
            }
        }
    }

    fun <T> withTrans(func: (DataapiWebSocketSdk) -> T): T {
        return sdk.use { x ->
            try {
                x.transactionBegin()
                val res = func(x)
                x.transactionCommit()
                res
            } catch (e: Throwable) {
                x.transactionRollback()
                throw e
            }
        }
    }

    suspend fun <T> withTransaction(func: suspend (DataapiWebSocketSdk) -> T) = withContext(Dispatchers.IO + MDCContext()) {
        sdk.use { x ->
            try {
                x.transactionBegin()
                val res = func(x)
                x.transactionCommit()
                res
            } catch (e: Throwable) {
                x.transactionRollback()
                throw e
            }
        }
    }


    suspend fun <T> noneTransaction(func: Function<DataapiWebSocketSdk, T>) = withContext(Dispatchers.IO + MDCContext()) {
        sdk.use { socketSdk ->
            func.apply(socketSdk)
        }
    }

}


object MetadataApi {

    private val logger = LoggerFactory.getLogger(MetadataApi::class.java)

    fun getModel(fqn: String): DataModel? {
        try {
            val model = MetadataSdkFactory.createMetadataHttpSdk().getModel(GetModelRequest().apply {
                this.fqn = fqn
                this.allRef = false
            })
            return model
        } catch (e: Exception) {
            if (e is SdkException) {
                if (e.code?.toInt() == 404 && e.error_code == "151147") return null
            }
            logger.warn("获取模型异常 fqn: {} msg: {}", fqn, e.message)
            return null
        }
    }
}