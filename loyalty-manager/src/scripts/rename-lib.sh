#!/bin/bash

# === 检查参数 ===
if [ -z "$1" ]; then
  echo "Usage: $0 <path_to_jar_file>"
  exit 1
fi
JAR_PATH=$1
if [ ! -f "$JAR_PATH" ]; then
  echo "Error: File $JAR_PATH does not exist."
  exit 1
fi
if [[ "$JAR_PATH" != *.jar ]]; then
  echo "Error: File $JAR_PATH is not a JAR file."
  exit 1
fi

# === 准备变量 ===
JAR_FILE=$(basename "$JAR_PATH")
JAR_DIR=$(dirname "$JAR_PATH")
RENAME_DIR=_rename_output_jar_
ORDER_FILE=zip-order.txt
MAP_FILE=zip-map.txt

# === 准备工作区 ===
cd "$JAR_DIR"
rm -rf "$RENAME_DIR"
mkdir "$RENAME_DIR"
cd "$RENAME_DIR"
cp "../$JAR_FILE" .
jar xf "$JAR_FILE"
rm -rf META-INF/maven

# === 初始化打包顺序文件 ===
> $MAP_FILE
echo "META-INF/" >> $MAP_FILE
if [ -d "BOOT-INF" ]; then
  echo "BOOT-INF/classes/" >> $MAP_FILE
fi
find . -maxdepth 1 -type d ! -name 'BOOT-INF' ! -name 'META-INF' ! -name '.' -exec basename {} \; | awk '{print $0"/"}' >> $MAP_FILE

# === 获取原始 lib 顺序 ===
if [ -d "BOOT-INF" ]; then
  unzip -l -qq "$JAR_FILE" | awk '{print $4}' | grep "^BOOT-INF/lib/.*\.jar$" > "$ORDER_FILE"
  while read -r line; do
    original_file=$(basename "$line")
    original_dir=$(dirname "$line")
    random_name=$(echo "${original_file%.jar}" | sed -e 's/[-.]/_/g')
    new_name="com_shuyun_kylin_lib_${random_name}.jar"

    echo "Renamed $original_file to $new_name"
    echo "$original_dir/$new_name" >> $MAP_FILE
    mv "$line" "$original_dir/$new_name"
  done < "$ORDER_FILE"
fi

# === 使用 jar cf 按顺序打包 ===
echo "Creating new JAR file..."
jar c0fm "${JAR_FILE%.jar}_obfuscated.jar" META-INF/MANIFEST.MF @$MAP_FILE

# === 清理 ===
mv -f "${JAR_FILE%.jar}_obfuscated.jar" "../$JAR_FILE"
cd ../
rm -rf "$RENAME_DIR"
echo "重新打包完成"