{"scriptVersion": "202109051151121_add_segment_margation", "scriptOperation": "CREATE_MODEL", "description": "积分块迁移临时表", "scriptContent": {"model": {"fqn": "data.loyalty.manager.segment.margation", "owner": "loyalty", "description": "积分块迁移临时表", "type": "Object", "fields": [{"name": "pointPlanId", "description": "积分帐号", "fieldType": {"fqn": "system.lang.Integer"}, "constraint": {"integerLength": 10}}, {"name": "pointAccountId", "description": "积分账号ID", "fieldType": {"fqn": "system.lang.String"}, "indexed": true}, {"name": "point", "description": "积分", "fieldType": {"type": "Number"}, "constraint": {"integerLength": 10, "fractionalLength": 7}}, {"name": "validPoint", "description": "有效明细积分", "fieldType": {"type": "Number"}, "constraint": {"integerLength": 10, "fractionalLength": 7}}, {"name": "created", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.DateTime"}, "description": "创建时间", "title": "创建时间"}, {"name": "updated", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.DateTime"}, "description": "更新时间", "title": "更新时间"}]}}}