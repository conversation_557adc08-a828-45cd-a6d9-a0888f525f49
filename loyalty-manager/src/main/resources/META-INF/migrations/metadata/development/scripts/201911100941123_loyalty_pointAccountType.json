[{"scriptVersion": "201911100941129_loyalty_pointAccountType", "scriptOperation": "CREATE_MODEL", "description": "忠诚度积分账号", "scriptContent": {"model": {"fqn": "data.loyalty.manager.pointAccountType", "owner": "pointAccountType", "custom": true, "type": "Object", "description": "积分账号", "title": "积分账号", "fields": [{"name": "versionId", "custom": true, "fieldType": {"fqn": "system.lang.Id"}, "description": "主键", "title": "主键"}, {"name": "id", "custom": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "id", "title": "id"}, {"name": "subjectVersionId", "custom": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "subjectVersionId", "title": "subjectVersionId"}, {"name": "status", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "状态", "title": "状态"}, {"name": "name", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "账户名称", "title": "账户名称"}, {"name": "unit", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "单位", "title": "单位"}, {"name": "precision", "custom": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "精度", "title": "精度"}, {"name": "rounding", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "舍入策略", "title": "舍入策略"}, {"name": "negativeStrategy", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "异常扣除设置", "title": "异常扣除设置"}, {"name": "topLimit", "custom": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "积分上限", "title": "积分上限"}, {"name": "description", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "描述", "title": "描述"}, {"name": "sort", "custom": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "前端排序", "title": "前端排序"}, {"name": "disabled", "custom": true, "fieldType": {"fqn": "system.lang.<PERSON>"}, "description": "是否已删除", "title": "是否已删除"}, {"name": "creatorId", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "创建人id", "title": "创建人id"}, {"name": "<PERSON><PERSON><PERSON>", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "创建人", "title": "创建人"}, {"name": "createTime", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.DateTime"}, "description": "创建时间", "title": "创建时间"}, {"name": "updaterId", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "更新人id", "title": "更新人id"}, {"name": "updaterName", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "更新人", "title": "更新人"}, {"name": "updateTime", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.DateTime"}, "description": "更新时间", "title": "更新时间"}, {"name": "executeOrder", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "执行顺序", "title": "执行顺序"}, {"name": "singleTopLimit", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "singleTopLimit", "title": "singleTopLimit"}, {"name": "priorityDeduction", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "priorityDeduction", "title": "priorityDeduction"}]}}}]