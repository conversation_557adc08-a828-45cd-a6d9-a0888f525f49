[{"scriptVersion": "20220811110511000_add_mergeRecord", "scriptOperation": "CREATE_FIELD", "description": "mergeRecord", "scriptContent": {"fqn": "data.loyalty.manager.mergeRecord", "fieldName": "medalStatus", "field": {"name": "medalStatus", "custom": true, "indexed": false, "fieldType": {"fqn": "system.lang.String"}, "description": "勋章状态", "title": "勋章状态"}}}, {"scriptVersion": "20220811110511001_add_mergeRecord", "scriptOperation": "CREATE_FIELD", "description": "mergeRecord", "scriptContent": {"fqn": "data.loyalty.manager.mergeRecord", "fieldName": "medalReason", "field": {"name": "medalReason", "custom": true, "indexed": false, "fieldType": {"fqn": "system.lang.String"}, "description": "勋章原因", "title": "勋章原因"}}}, {"scriptVersion": "20220811110511002_add_mergeRecord", "scriptOperation": "CREATE_FIELD", "description": "mergeRecord", "scriptContent": {"fqn": "data.loyalty.manager.mergeRecord", "fieldName": "medalHierarchyId", "field": {"name": "medalHierarchyId", "custom": true, "indexed": false, "fieldType": {"fqn": "system.lang.Integer"}, "description": "勋章体系ID", "title": "勋章体系ID"}}}]