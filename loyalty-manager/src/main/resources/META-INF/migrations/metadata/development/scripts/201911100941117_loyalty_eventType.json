[{"scriptVersion": "201911100941120_loyalty_eventType", "scriptOperation": "CREATE_MODEL", "description": "忠诚度时机", "scriptContent": {"model": {"fqn": "data.loyalty.manager.eventType", "owner": "eventType", "custom": true, "type": "Object", "description": "时机", "title": "时机", "fields": [{"name": "versionId", "custom": true, "fieldType": {"fqn": "system.lang.Id"}, "description": "主键", "title": "主键"}, {"name": "id", "custom": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "id", "title": "id"}, {"name": "subjectVersionId", "custom": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "subjectVersionId", "title": "subjectVersionId"}, {"name": "status", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "状态", "title": "状态"}, {"name": "name", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "主体名称", "title": "主体名称"}, {"name": "eventStream", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "eventStream", "title": "eventStream"}, {"name": "disabled", "custom": true, "fieldType": {"fqn": "system.lang.<PERSON>"}, "description": "是否已删除", "title": "是否已删除"}, {"name": "creatorId", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "创建人id", "title": "创建人id"}, {"name": "<PERSON><PERSON><PERSON>", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "创建人", "title": "创建人"}, {"name": "createTime", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.DateTime"}, "description": "创建时间", "title": "创建时间"}, {"name": "updaterId", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "更新人id", "title": "更新人id"}, {"name": "updaterName", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "更新人", "title": "更新人"}, {"name": "updateTime", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.DateTime"}, "description": "更新时间", "title": "更新时间"}, {"name": "referencePath", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "主体ID关联路径", "title": "主体ID关联路径"}, {"name": "sort", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "sort", "title": "sort"}, {"name": "operation", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "触发操作", "title": "触发操作"}, {"name": "originalOrderPath", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "原单路径", "title": "原单路径"}, {"name": "relatedEventTypeIds", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "关联时机ID", "title": "关联时机ID"}, {"name": "occasionString", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "适用场景", "title": "适用场景"}, {"name": "operationGrade", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "操作等级", "title": "操作等级"}]}}}]