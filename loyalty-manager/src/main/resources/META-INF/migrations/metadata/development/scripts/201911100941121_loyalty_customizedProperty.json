[{"scriptVersion": "201911100941131_loyalty_customizedProperty", "scriptOperation": "CREATE_MODEL", "description": "忠诚度属性表", "scriptContent": {"model": {"fqn": "data.loyalty.manager.customizedProperty", "owner": "customizedProperty", "custom": true, "type": "Object", "description": "属性表", "title": "属性表", "fields": [{"name": "versionId", "custom": true, "fieldType": {"fqn": "system.lang.Id"}, "description": "主键", "title": "主键"}, {"name": "id", "custom": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "id", "title": "id"}, {"name": "belongerVersionId", "custom": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "belongerVersionId", "title": "belongerVersionId"}, {"name": "status", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "状态", "title": "状态"}, {"name": "name", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "属性名称", "title": "属性名称"}, {"name": "dataType", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "数据类型", "title": "数据类型"}, {"name": "expression", "custom": true, "constraint": {"stringLength": 30000}, "fieldType": {"fqn": "system.lang.String"}, "description": "属性值", "title": "属性值"}, {"name": "comparatorString", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "constraint": {"stringLength": 30000}, "description": "比较符", "title": "比较符"}, {"name": "belongerType", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "属性所有者", "title": "属性所有者"}, {"name": "widgetType", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "组件样式", "title": "组件样式"}, {"name": "ruleConfigString", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "constraint": {"stringLength": 200000}, "description": "属性设置", "title": "属性设置"}, {"name": "disabled", "custom": true, "fieldType": {"fqn": "system.lang.<PERSON>"}, "description": "是否已删除", "title": "是否已删除"}, {"name": "creatorId", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "创建人id", "title": "创建人id"}, {"name": "<PERSON><PERSON><PERSON>", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "创建人", "title": "创建人"}, {"name": "createTime", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.DateTime"}, "description": "创建时间", "title": "创建时间"}, {"name": "updaterId", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "更新人id", "title": "更新人id"}, {"name": "updaterName", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "更新人", "title": "更新人"}, {"name": "updateTime", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.DateTime"}, "description": "更新时间", "title": "更新时间"}, {"name": "sort", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "排序", "title": "排序"}, {"name": "defaultValue", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "默认值", "title": "默认值"}, {"name": "dsl", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "constraint": {"stringLength": 200000}, "description": "dsl", "title": "dsl"}]}}}]