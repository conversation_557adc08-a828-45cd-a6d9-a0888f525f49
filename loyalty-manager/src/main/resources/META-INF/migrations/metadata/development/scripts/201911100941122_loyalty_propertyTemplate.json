[{"scriptVersion": "201911100941128_loyalty_propertyTemplate", "scriptOperation": "CREATE_MODEL", "description": "忠诚度属性模版", "scriptContent": {"model": {"fqn": "data.loyalty.manager.propertyTemplate", "owner": "propertyTemplate", "custom": true, "type": "Object", "description": "属性模版", "title": "属性模版", "fields": [{"name": "versionId", "custom": true, "fieldType": {"fqn": "system.lang.Id"}, "description": "主键", "title": "主键"}, {"name": "id", "custom": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "id", "title": "id"}, {"name": "belongerVersionId", "custom": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "belongerVersionId", "title": "belongerVersionId"}, {"name": "status", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "状态", "title": "状态"}, {"name": "name", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "属性模板名称", "title": "属性模板名称"}, {"name": "templateDetail", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "constraint": {"stringLength": 200000}, "description": "属性模板名称", "title": "属性模板名称"}, {"name": "disabled", "custom": true, "fieldType": {"fqn": "system.lang.<PERSON>"}, "description": "是否已删除", "title": "是否已删除"}, {"name": "creatorId", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "创建人id", "title": "创建人id"}, {"name": "<PERSON><PERSON><PERSON>", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "创建人", "title": "创建人"}, {"name": "createTime", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.DateTime"}, "description": "创建时间", "title": "创建时间"}, {"name": "updaterId", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "更新人id", "title": "更新人id"}, {"name": "updaterName", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "更新人", "title": "更新人"}, {"name": "updateTime", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.DateTime"}, "description": "更新时间", "title": "更新时间"}, {"name": "sort", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "排序", "title": "排序"}, {"name": "dataType", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.String"}, "description": "dateType", "title": "dateType"}, {"name": "belongerId", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.Integer"}, "description": "singleTopLimit", "title": "singleTopLimit"}, {"name": "belongerType", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "description": "belongerType", "title": "belongerType"}]}}}]