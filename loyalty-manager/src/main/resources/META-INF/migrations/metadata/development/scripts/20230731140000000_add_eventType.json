[{"scriptVersion": "20230731140000000_add_eventType", "scriptOperation": "CREATE_FIELD", "description": "忠诚度时机", "scriptContent": {"fqn": "data.loyalty.manager.eventType", "fieldName": "matchingTimePathForGradeRule", "field": {"name": "matchingTimePathForGradeRule", "custom": true, "indexed": false, "fieldType": {"fqn": "system.lang.String"}, "description": "等级规则匹配时间路径", "title": "等级规则匹配时间路径"}}}, {"scriptVersion": "20230731140000001_add_eventType", "scriptOperation": "CREATE_FIELD", "description": "忠诚度时机", "scriptContent": {"fqn": "data.loyalty.manager.eventType", "fieldName": "matchingTimePathForPointsRule", "field": {"name": "matchingTimePathForPointsRule", "custom": true, "indexed": false, "fieldType": {"fqn": "system.lang.String"}, "description": "积分规则匹配时间路径", "title": "积分规则匹配时间路径"}}}, {"scriptVersion": "20230731140000002_add_eventType", "scriptOperation": "CREATE_FIELD", "description": "忠诚度时机", "scriptContent": {"fqn": "data.loyalty.manager.eventType", "fieldName": "matchingTimePathForMedalRule", "field": {"name": "matchingTimePathForMedalRule", "custom": true, "indexed": false, "fieldType": {"fqn": "system.lang.String"}, "description": "勋章规则匹配时间路径", "title": "勋章规则匹配时间路径"}}}, {"scriptVersion": "20230731140000003_add_eventType", "scriptOperation": "CREATE_FIELD", "description": "忠诚度时机", "scriptContent": {"fqn": "data.loyalty.manager.eventType", "fieldName": "delayedEventStrategy", "field": {"name": "delayedEventStrategy", "custom": true, "indexed": false, "constraint": {"nullable": false, "defaultValue": "NONE"}, "fieldType": {"fqn": "system.lang.String"}, "description": "延迟时机处理策略", "title": "延迟时机处理策略"}}}, {"scriptVersion": "20230731140000004_add_eventType", "scriptOperation": "CREATE_FIELD", "description": "忠诚度时机", "scriptContent": {"fqn": "data.loyalty.manager.eventType", "fieldName": "delayedEventPath", "field": {"name": "delayedEventPath", "custom": true, "indexed": false, "fieldType": {"fqn": "system.lang.String"}, "description": "延迟时机参照字段路径", "title": "延迟时机参照字段路径"}}}, {"scriptVersion": "20230731140000005_add_eventType", "scriptOperation": "CREATE_FIELD", "description": "忠诚度时机", "scriptContent": {"fqn": "data.loyalty.manager.eventType", "fieldName": "discardDelayedEventAfter", "field": {"name": "discardDelayedEventAfter", "custom": true, "indexed": false, "fieldType": {"fqn": "system.lang.String"}, "description": "时机事件延迟多久不再参与计算", "title": "时机事件延迟时间上限"}}}]