[{"scriptVersion": "202108041853042_loyalty_alert", "scriptOperation": "CREATE_MODEL", "description": "忠诚度告警事件", "scriptContent": {"model": {"fqn": "data.loyalty.manager.alert", "owner": "alert", "custom": true, "type": "Object", "description": "忠诚度告警事件", "title": "忠诚度告警事件", "fields": [{"name": "id", "custom": true, "fieldType": {"fqn": "system.lang.Id"}, "description": "主键", "title": "主键"}, {"name": "bizType", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "indexed": true, "description": "业务类型", "title": "业务类型"}, {"name": "data", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "constraint": {"stringLength": 200000}, "description": "原始内容", "title": "原始内容"}, {"name": "content", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "constraint": {"stringLength": 200000}, "description": "扩展内容", "title": "扩展内容"}, {"name": "errorMsg", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "constraint": {"stringLength": 200000}, "description": "告警错误详情", "title": "告警错误详情"}, {"name": "errorCode", "custom": true, "fieldType": {"fqn": "system.lang.String"}, "constraint": {"stringLength": 20}, "description": "告警错误码", "title": "告警错误码"}, {"name": "created", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.DateTime"}, "description": "创建时间", "title": "创建时间"}, {"name": "modified", "custom": true, "indexed": true, "fieldType": {"fqn": "system.lang.DateTime"}, "description": "更新时间", "title": "更新时间"}]}}}]