[{"fqn": "data.loyalty.member.hierarchy.MedalRecord{id}", "owner": "loyalty", "description": "主体勋章变更记录", "title": "主体勋章变更记录", "type": "Object", "useMode": "HTAP", "fields": [{"name": "planId", "description": "计划ID", "title": "计划ID", "fieldType": {"type": "Integer"}}, {"name": "planName", "description": "计划名称", "title": "计划名称", "fieldType": {"type": "String"}}, {"name": "medalHierarchyId", "description": "勋章体系ID", "title": "勋章体系ID", "fieldType": {"type": "Integer"}, "constraint": {"integerLength": 10}, "indexed": true}, {"name": "medalHierarchyName", "description": "勋章体系名称", "title": "勋章体系名称", "fieldType": {"type": "String"}}, {"name": "medalDefinitionId", "description": "勋章ID", "title": "勋章ID", "fieldType": {"type": "Integer"}, "constraint": {"integerLength": 10}, "indexed": true}, {"name": "medalDefinitionName", "description": "勋章体系名称", "title": "勋章体系名称", "fieldType": {"type": "String"}}, {"name": "currentMedalDefinitionIds", "description": "当前拥有勋章ID 以英文逗号隔开", "title": "当前拥有勋章ID", "fieldType": {"type": "String"}, "constraint": {"integerLength": 512}}, {"name": "memberMedalId", "description": "会员对应勋章模型中的记录的ID", "title": "勋章模型会员id", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}, "indexed": true}, {"name": "memberId", "description": "会员ID", "title": "会员ID", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}, "indexed": true}, {"name": "originalEffectDate", "description": "变更前勋章生效日期", "title": "变更前勋章生效日期", "fieldType": {"type": "DateTime"}}, {"name": "originalOverdueDate", "description": "变更前勋章过期日期", "title": "变更前勋章过期日期", "fieldType": {"type": "DateTime"}}, {"name": "currentEffectDate", "description": "变更后勋章生效日期", "title": "变更后勋章生效日期", "fieldType": {"type": "DateTime"}}, {"name": "currentOverdueDate", "description": "变更后勋章过期日期", "title": "变更后勋章过期日期", "fieldType": {"type": "DateTime"}}, {"name": "recordType", "description": "OBTAIN 获取\nKEEP 保持\nRECYCLE 回收", "title": "变更类型", "fieldType": {"fqn": "data.loyalty.member.enum.MedalType"}, "indexed": true}, {"name": "changeWay", "description": "INTERFACE 接口变更\nOBTAIN 发放规则\nKEEP 保持规则\nMANUAL 手动操作", "title": "变更方式", "fieldType": {"type": "String"}}, {"name": "triggerId", "description": "内部事件ID，外部追溯ID", "title": "追溯ID", "fieldType": {"type": "String"}}, {"name": "traceId", "description": "源单ID", "title": "源单ID", "fieldType": {"type": "String"}}, {"name": "recordSourceDetail", "description": "recordSourceDetail", "title": "备注详情", "fieldType": {"type": "String"}}, {"name": "operator", "description": "操作人", "title": "操作人", "fieldType": {"type": "String"}}, {"name": "subjectFqn", "description": "归属主体FQN", "title": "归属主体FQN", "fieldType": {"type": "String"}}, {"name": "description", "description": "备注说明", "title": "备注说明", "fieldType": {"type": "String"}}, {"name": "recordDetail", "description": "recordDetail", "title": "此字段为研发自用，实施勿动", "fieldType": {"type": "String"}}, {"name": "created", "description": "创建时间", "title": "创建时间", "fieldType": {"type": "DateTime"}, "indexed": true}, {"name": "channel", "description": "变更渠道", "title": "变更渠道", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}, "indexed": true}, {"name": "ruleId", "description": "积分规则ID", "title": "积分规则ID", "fieldType": {"type": "Integer"}}, {"name": "eventTypeId", "description": "时机ID", "title": "时机ID", "fieldType": {"type": "Integer"}}, {"name": "eventTypeName", "description": "时机名称", "title": "时机名称", "fieldType": {"type": "String"}}, {"name": "ruleName", "description": "规则名称", "title": "规则名称", "fieldType": {"type": "String"}}, {"name": "key", "description": "事件流key", "title": "事件流key", "fieldType": {"type": "String"}}, {"name": "eventFqn", "description": "事件流fqn", "title": "事件流fqn", "fieldType": {"type": "String"}}, {"name": "KZZD1", "description": "扩展字段1", "title": "扩展字段1", "fieldType": {"type": "String"}, "indexed": true}, {"name": "KZZD2", "description": "扩展字段2", "title": "扩展字段2", "fieldType": {"type": "String"}, "indexed": true}, {"name": "KZZD3", "description": "扩展字段3", "title": "扩展字段3", "fieldType": {"type": "String"}, "indexed": true}]}]