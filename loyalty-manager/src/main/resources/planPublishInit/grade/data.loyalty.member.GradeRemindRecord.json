[{"fqn": "data.loyalty.member.hierarchy.GradeExpireRemindRecord{id}", "owner": "loyalty", "description": "等级过期提醒记录", "title": "等级过期提醒记录", "type": "Object", "subscribed": true, "destSubscribedFqn": "event.loyalty.member.hierarchy.GradeExpireRemindRecord{id}.raw", "fields": [{"name": "planId", "description": "计划ID", "title": "计划ID", "fieldType": {"type": "Integer"}}, {"name": "gradeHierarchyId", "description": "等级体系ID", "title": "等级体系ID", "fieldType": {"type": "Integer"}, "constraint": {"integerLength": 10}, "indexed": true}, {"name": "gradeHierarchyName", "description": "等级体系名称", "title": "等级体系名称", "fieldType": {"type": "String"}}, {"name": "memberId", "description": "会员ID", "title": "会员ID", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}, "indexed": true}, {"name": "currentGradeId", "description": "变更后等级ID", "title": "变更后等级ID", "fieldType": {"type": "Integer"}, "constraint": {"integerLength": 10}, "indexed": true}, {"name": "currentGradeName", "description": "变更后等级名称", "title": "变更后等级名称", "fieldType": {"type": "String"}}, {"name": "currentEffectDate", "description": "变更后等级生效日期", "title": "变更后等级生效日期", "fieldType": {"type": "DateTime"}}, {"name": "currentOverdueDate", "description": "变更后等级过期日期", "title": "变更后等级过期日期", "fieldType": {"type": "DateTime"}}, {"name": "subjectFqn", "description": "归属主体FQN", "title": "归属主体FQN", "fieldType": {"type": "String"}}, {"name": "desc", "description": "备注说明", "title": "备注说明", "fieldType": {"type": "String"}}, {"name": "created", "description": "创建时间", "title": "创建时间", "fieldType": {"type": "DateTime"}, "indexed": true}, {"name": "remindId", "description": "配置ID", "title": "配置ID", "fieldType": {"type": "Integer"}, "indexed": true}, {"name": "name", "description": "配置名称", "title": "配置名称", "fieldType": {"type": "String"}}]}]