[{"fqn": "data.loyalty.member.hierarchy.GradeRecord{id}", "owner": "loyalty", "description": "主体等级变更记录", "title": "主体等级变更记录", "type": "Object", "useMode": "HTAP", "subscribed": true, "destSubscribedFqn": "event.loyalty.member.hierarchy.GradeRecord{id}.raw", "fields": [{"name": "planId", "description": "计划ID", "title": "计划ID", "fieldType": {"type": "Integer"}}, {"name": "planName", "description": "计划名称", "title": "计划名称", "fieldType": {"type": "String"}}, {"name": "memberGradeId", "description": "会员对应等级模型中的记录的ID", "title": "等级模型会员id", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}, "indexed": true}, {"name": "gradeHierarchyId", "description": "等级体系ID", "title": "等级体系ID", "fieldType": {"type": "Integer"}, "constraint": {"integerLength": 10}, "indexed": true}, {"name": "gradeHierarchyName", "description": "等级体系名称", "title": "等级体系名称", "fieldType": {"type": "String"}}, {"name": "memberId", "description": "会员ID", "title": "会员ID", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}, "indexed": true}, {"name": "originalGradeId", "description": "变更前等级ID", "title": "变更前等级ID", "fieldType": {"type": "Integer"}, "constraint": {"integerLength": 10}, "indexed": true}, {"name": "originalGradeName", "description": "变更前等级名称", "title": "变更前等级名称", "fieldType": {"type": "String"}}, {"name": "originalEffectDate", "description": "变更前等级生效日期", "title": "变更前等级生效日期", "fieldType": {"type": "DateTime"}}, {"name": "originalOverdueDate", "description": "变更前等级过期日期", "title": "变更前等级过期日期", "fieldType": {"type": "DateTime"}}, {"name": "currentGradeId", "description": "变更后等级ID", "title": "变更后等级ID", "fieldType": {"type": "Integer"}, "constraint": {"integerLength": 10}, "indexed": true}, {"name": "currentGradeName", "description": "变更后等级名称", "title": "变更后等级名称", "fieldType": {"type": "String"}}, {"name": "currentEffectDate", "description": "变更后等级生效日期", "title": "变更后等级生效日期", "fieldType": {"type": "DateTime"}}, {"name": "currentOverdueDate", "description": "变更后等级过期日期", "title": "变更后等级过期日期", "fieldType": {"type": "DateTime"}}, {"name": "recordType", "description": "UPGRADE 升级\nDEGRADE 降级\nHOLD_BACK_GRADE 保级\nGRADE_RECALCULATE 重算", "title": "变更类型", "fieldType": {"fqn": "data.loyalty.member.enum.GradeType"}, "indexed": true}, {"name": "changeWay", "description": "AUTO_CALC 系统计算\nMANUAL 业务端变更\nINTERFACE 接口变更\nINTERFACE_BATCH 接口批量变更", "title": "变更方式", "fieldType": {"type": "String"}}, {"name": "triggerId", "description": "内部事件ID，外部追溯ID", "title": "追溯ID", "fieldType": {"type": "String"}}, {"name": "traceId", "description": "源单ID", "title": "源单ID", "fieldType": {"type": "String"}}, {"name": "recordSourceDetail", "description": "recordSourceDetail", "title": "备注详情", "fieldType": {"type": "String"}}, {"name": "operator", "description": "操作人", "title": "操作人", "fieldType": {"type": "String"}}, {"name": "subjectFqn", "description": "归属主体FQN", "title": "归属主体FQN", "fieldType": {"type": "String"}}, {"name": "description", "description": "备注说明", "title": "备注说明", "fieldType": {"type": "String"}}, {"name": "extraInfo", "description": "extraInfo", "title": "额外信息", "fieldType": {"type": "String"}}, {"name": "recordDetail", "description": "recordDetail", "title": "此字段为研发自用，实施勿动", "fieldType": {"type": "String"}}, {"name": "created", "description": "创建时间", "title": "创建时间", "fieldType": {"type": "DateTime"}, "indexed": true}, {"name": "channel", "description": "变更渠道", "title": "变更渠道", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}, "indexed": true}, {"name": "eventTypeName", "description": "时机名称", "title": "时机名称", "fieldType": {"type": "String"}}, {"name": "ruleId", "description": "规则ID", "title": "规则ID", "fieldType": {"type": "Integer"}}, {"name": "ruleName", "description": "规则名称", "title": "规则名称", "fieldType": {"type": "String"}}]}]