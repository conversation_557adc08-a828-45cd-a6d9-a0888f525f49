[{"fqn": "data.loyalty.member.hierarchy.Grade{id}", "owner": "loyalty", "description": "主体等级", "title": "主体等级", "type": "Object", "useMode": "HTAP", "subscribed": true, "destSubscribedFqn": "event.loyalty.member.hierarchy.Grade{id}.raw", "fields": [{"name": "planId", "description": "计划ID", "title": "计划ID", "fieldType": {"type": "Integer"}}, {"name": "gradeHierarchyId", "description": "等级体系ID", "title": "等级体系ID", "fieldType": {"type": "Integer"}, "constraint": {"integerLength": 10}, "indexed": true}, {"name": "version", "description": "版本号", "title": "版本号", "fieldType": {"type": "Integer"}}, {"name": "memberId", "description": "会员全网id", "title": "会员全网id", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}, "indexed": true}, {"name": "currentGradeDefinitionId", "description": "当前等级id", "title": "当前等级id", "fieldType": {"type": "Integer"}, "constraint": {"integerLength": 10}, "indexed": true}, {"name": "effectDate", "description": "有效期开始时间", "title": "有效期开始时间", "fieldType": {"type": "DateTime"}}, {"name": "overdueDate", "description": "有效期结束时间", "title": "有效期结束时间", "fieldType": {"type": "DateTime"}, "indexed": true}, {"name": "subjectFqn", "description": "归属性主体", "title": "归属性主体", "fieldType": {"type": "String"}}, {"name": "created", "description": "创建时间", "title": "创建时间", "fieldType": {"type": "DateTime"}}, {"name": "planName", "description": "计划名称", "title": "计划名称", "fieldType": {"type": "String"}}, {"name": "gradeHierarchyName", "description": "等级体系名称", "title": "等级体系名称", "fieldType": {"type": "String"}}, {"name": "currentGradeName", "description": "等级名称", "title": "等级名称", "fieldType": {"type": "String"}}]}]