{"fqn": "data.loyalty.member.account.PointDelayRemindRecord{id}", "title": "PointDelayRemindRecord{id}", "eventType": "COMPLEX", "description": "PointDelayRemindRecord{id}", "dslContext": [{"dsl": "get(\"event.objectEventType\").contains(\"INSERT\")", "isFilter": true}, {"dsl": "$.keys().map{k,v -> put(v.replace(\"data.\", \"\"), get(v))}", "isFilter": false}], "originEventFqn": "event.loyalty.member.account.PointDelayRemindRecord{id}", "properties": [{"name": "id", "title": "id", "description": "id", "dataType": {"type": "String"}, "isRequired": true, "isGlobal": true}, {"name": "occurrenceDt", "title": "occurrenceDt", "description": "occurrenceDt", "dataType": {"type": "DateTime"}, "isRequired": true, "isGlobal": true}, {"name": "detectionDt", "title": "detectionDt", "description": "detectionDt", "dataType": {"type": "DateTime"}, "isRequired": true, "isGlobal": true}, {"name": "objectId", "title": "objectId", "description": "objectId", "dataType": {"type": "String"}, "isRequired": true, "isGlobal": true}, {"name": "subjectId", "title": "subjectId", "description": "subjectId", "dataType": {"type": "Integer"}, "isRequired": false, "isGlobal": false}, {"name": "pointPlanId", "title": "pointPlanId", "description": "pointPlanId", "dataType": {"type": "Integer"}, "isRequired": false, "isGlobal": false}, {"name": "pointPlanName", "title": "pointPlanName", "description": "pointPlanName", "dataType": {"type": "String"}, "isRequired": false, "isGlobal": false}, {"name": "planId", "title": "planId", "description": "planId", "dataType": {"type": "Integer"}, "isRequired": false, "isGlobal": false}, {"name": "created", "title": "created", "description": "created", "dataType": {"type": "DateTime"}, "isRequired": false, "isGlobal": false}, {"name": "name", "title": "name", "description": "name", "dataType": {"type": "String"}, "isRequired": false, "isGlobal": false}, {"name": "desc", "title": "desc", "description": "desc", "dataType": {"type": "String"}, "isRequired": false, "isGlobal": false}, {"name": "remindId", "title": "remindId", "description": "remindId", "dataType": {"type": "Integer"}, "isRequired": false, "isGlobal": false}, {"name": "subjectFqn", "title": "subjectFqn", "description": "subjectFqn", "dataType": {"type": "String"}, "isRequired": false, "isGlobal": false}, {"name": "point", "title": "point", "description": "point", "dataType": {"type": "Decimal"}, "isRequired": false, "isGlobal": false}, {"name": "effectiveDate", "title": "effectiveDate", "description": "effectiveDate", "dataType": {"type": "DateTime"}, "isRequired": false, "isGlobal": false}, {"name": "overdueDate", "title": "overdueDate", "description": "overdueDate", "dataType": {"type": "DateTime"}, "isRequired": false, "isGlobal": false}, {"name": "memberId", "title": "memberId", "description": "memberId", "dataType": {"type": "Reference", "dataModelFqn": "{dataModelFqn}"}, "isRequired": false, "isGlobal": false}, {"name": "memberPoint", "title": "memberPoint", "description": "memberPoint", "dataType": {"type": "Reference", "dataModelFqn": "data.loyalty.member.account.Point{id}"}, "isRequired": false, "isGlobal": false}]}