[{"fqn": "data.loyalty.member.account.PointRecordItem{id}", "owner": "loyalty", "description": "积分明细记录", "title": "积分明细记录", "type": "Object", "useMode": "HTAP", "subscribed": true, "destSubscribedFqn": "event.loyalty.member.account.PointRecordItem{id}.raw", "fields": [{"name": "planId", "description": "计划id", "title": "计划id", "fieldType": {"type": "Integer"}}, {"name": "status", "description": "状态", "title": "状态", "fieldType": {"fqn": "data.loyalty.member.enum.RecordType"}}, {"name": "sort", "description": "排序", "title": "排序", "fieldType": {"type": "Integer"}}, {"name": "pointPlanId", "description": "积分账户id", "title": "积分账户id", "fieldType": {"type": "Integer"}, "constraint": {"integerLength": 10}}, {"name": "traceId", "description": "业务唯一号", "title": "业务唯一号", "fieldType": {"type": "String"}, "constraint": {"integerLength": 255}, "indexed": true}, {"name": "memberId", "description": "会员全网id", "title": "会员全网id", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}, "indexed": true}, {"name": "point", "description": "当前可用有效积分值", "title": "当前可用有效积分值", "fieldType": {"type": "Number"}, "constraint": {"integerLength": 10, "fractionalLength": 7}}, {"name": "created", "description": "创建时间", "title": "创建时间", "fieldType": {"type": "DateTime"}}, {"name": "effectiveDate", "description": "积分开始有效期", "title": "积分开始有效期", "fieldType": {"type": "DateTime"}}, {"name": "overdueDate", "description": "积分结束有效期", "title": "积分结束有效期", "fieldType": {"type": "DateTime"}}, {"name": "backId", "description": "追溯ID,追溯使用", "title": "追溯ID", "fieldType": {"type": "String"}, "constraint": {"integerLength": 64}, "indexed": true}, {"name": "parentBackId", "description": "追溯ID父ID,追溯使用", "title": "追溯ID父ID", "fieldType": {"type": "String"}, "constraint": {"integerLength": 64}, "indexed": true}, {"name": "recordId", "description": "积分记录表主键", "title": "积分记录表主键", "fieldType": {"type": "String"}, "constraint": {"integerLength": 64}, "indexed": true}]}]