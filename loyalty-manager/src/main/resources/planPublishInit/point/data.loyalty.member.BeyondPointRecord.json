[{"fqn": "data.loyalty.member.account.BeyondPointRecord{id}", "owner": "loyalty", "description": "积分超额记录表", "title": "积分超额记录表", "type": "Object", "useMode": "HTAP", "subscribed": true, "destSubscribedFqn": "event.loyalty.member.account.BeyondPointRecord{id}.raw", "fields": [{"name": "planId", "description": "计划id", "title": "计划id", "fieldType": {"type": "Integer"}}, {"name": "pointPlanId", "description": "积分账户id", "title": "积分账户id", "fieldType": {"type": "Integer"}, "constraint": {"integerLength": 10}}, {"name": "memberPointId", "description": "会员积分账户id", "title": "会员积分账户id", "fieldType": {"type": "String"}, "indexed": true}, {"name": "memberId", "description": "会员全网id", "title": "会员全网id", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}, "indexed": true}, {"name": "pointRecordId", "description": "变更记录ID", "title": "变更记录ID", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}, "indexed": true}, {"name": "subjectFqn", "description": "归属性主体", "title": "归属性主体", "fieldType": {"type": "String"}}, {"name": "diffPoint", "description": "差值", "title": "差值 ", "fieldType": {"type": "Number"}, "constraint": {"integerLength": 10, "fractionalLength": 7}}, {"name": "point", "description": "真实积分值", "title": "真实积分值", "fieldType": {"type": "Number"}, "constraint": {"integerLength": 10, "fractionalLength": 7}}, {"name": "changePoint", "description": "变动积分", "title": "变动积分", "fieldType": {"type": "Number"}, "constraint": {"integerLength": 10, "fractionalLength": 7}}, {"name": "totalPoint", "description": "当前总积分值", "title": "当前总积分值", "fieldType": {"type": "Number"}, "constraint": {"integerLength": 10, "fractionalLength": 7}}, {"name": "recordType", "description": " SEND 立即发放 \n DELAY_SEND 延迟发放 \n EXPIRE 过期 \n FREEZE 冻结 \n UNFREEZE 取消冻结 \n DEDUCT 扣减 \n ABOLISH 作废 \n TIMER 定时 \n RECALCULATE 废弃重算 \n SPECIAL_DEDUCT 特殊扣除 \n SPECIAL_FREEZE 特殊冻结 \n SPECIAL_UNFREEZE 特殊解冻 \n SPECIAL_ABOLISH 特殊废弃 \n MANUAL_ABOLISH 手动废弃 \n REVERSE_SEND 反向发放 \n REVERSE_DEDUCT 反向扣减\n", "title": "触发动作", "fieldType": {"fqn": "data.loyalty.member.enum.RecordType"}, "constraint": {"stringLength": 20}, "indexed": true}, {"name": "status", "description": "DELAY 待生效 \n  DELAY_FROZEN 待生效已冻结 \n  DELAY_ABOLISH 待生效已作废 \n  EXPIRE 已生效已过期 \n  VALID 已生效 \n  FROZEN 已生效已冻结 \n  USED 已生效已使用 \n  ABOLISH 已生效已作废 \n  FROZEN_ABOLISH 已生效已冻结已作废\n", "title": "状态", "fieldType": {"fqn": "data.loyalty.member.enum.PcStatus"}}, {"name": "type", "description": "DEDUCT \n SEND", "title": "数据类型", "fieldType": {"type": "String"}, "constraint": {"stringLength": 20}}, {"name": "recordSourceDetail", "description": "业务来源描述信息", "title": "业务来源描述信息", "fieldType": {"type": "String"}}, {"name": "effectiveDate", "description": "积分生效时间，如果是锁定、冻结则没有有效期", "title": "积分生效时间", "fieldType": {"type": "DateTime"}}, {"name": "overdueDate", "description": "积分过期时间，如果是锁定、冻结则没有有效期", "title": "积分过期时间", "fieldType": {"type": "DateTime"}}, {"name": "extralInfo", "description": "操作人、操作原记录id，订单id，规则id，规则组id，参与规则组条件、参与规则计算数据，interfaceSource：接口调用来源", "title": "备注其他信息", "fieldType": {"type": "String"}}, {"name": "recordDetail", "description": "此字段为研发自用，实施勿动", "title": "变更信息", "fieldType": {"type": "String"}}, {"name": "desc", "description": "recordDetail为系统自动根据规则生成的明细信息。而desc为可接口，或特殊场景添加", "title": "备注", "fieldType": {"type": "String"}}, {"name": "operator", "description": "操作人，默认系统", "title": "操作人", "fieldType": {"type": "String"}}, {"name": "operatorId", "description": "操作人用户ID", "title": "操作人ID", "fieldType": {"type": "String"}}, {"name": "modified", "description": "数据最后一次修改时间", "title": "修改时间", "fieldType": {"type": "DateTime"}, "indexed": true}, {"name": "created", "description": "创建时间", "title": "创建时间", "fieldType": {"type": "DateTime"}}, {"name": "channel", "description": "请在channel_type枚举中维护好渠道映射关系，接口会走此表后进行存储，事件的来源固定存为loyalty", "title": "数据来源", "fieldType": {"fqn": "data.loyalty.member.enum.ChannelType"}}, {"name": "key", "description": "事件流key", "title": "事件流key", "fieldType": {"type": "String"}}, {"name": "ruleGroup", "description": "积分规则组", "title": "积分规则组", "fieldType": {"type": "String"}}, {"name": "ruleName", "description": "积分规则名称", "title": "积分规则名称", "fieldType": {"type": "String"}}, {"name": "ruleId", "description": "积分规则ID", "title": "积分规则ID", "fieldType": {"type": "Integer"}}, {"name": "changeMode", "description": "此字段关联change_mode表数据，默认的字段值为：AUTO_CALC 系统计算、MANUAL 手动变更、INTERFACE 接口变更、INTERFACE_BATCH 接口批量变更、EBM 事件营销、ACTIVE_MARKETING 主动营销", "title": "变更方式", "fieldType": {"fqn": "data.loyalty.member.enum.ChangeMode"}, "constraint": {"stringLength": 50}}, {"name": "planName", "description": "计划名称", "title": "计划名称", "fieldType": {"type": "String"}}, {"name": "pointPlanName", "description": "积分账户名称", "title": "积分账户名称", "fieldType": {"type": "String"}}, {"name": "traceId", "description": "原单ID", "title": "原单ID", "fieldType": {"type": "String"}}, {"name": "eventTypeName", "description": "时机名称", "title": "时机名称", "fieldType": {"type": "String"}}, {"name": "actionName", "description": "此字段用来存储触发积分变动的活动名称，历史数据默认为空", "title": "活动名称", "fieldType": {"type": "String"}}, {"name": "shopId", "description": "店铺ID", "title": "店铺ID", "fieldType": {"type": "String"}, "indexed": true}, {"name": "KZZD1", "description": "扩展字段1", "title": "扩展字段1", "fieldType": {"type": "String"}}, {"name": "KZZD2", "description": "扩展字段2", "title": "扩展字段2", "fieldType": {"type": "String"}}, {"name": "KZZD3", "description": "扩展字段3", "title": "扩展字段3", "fieldType": {"type": "String"}}]}]