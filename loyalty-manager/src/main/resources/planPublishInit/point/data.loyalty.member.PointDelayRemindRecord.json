[{"fqn": "data.loyalty.member.account.PointDelayRemindRecord{id}", "owner": "loyalty", "description": "积分延迟提醒", "title": "积分延迟提醒", "type": "Object", "subscribed": true, "destSubscribedFqn": "event.loyalty.member.account.PointDelayRemindRecord{id}.raw", "fields": [{"name": "memberId", "description": "会员ID", "title": "会员ID", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}, "indexed": true}, {"name": "pointPlanId", "description": "积分账户id", "title": "积分账户id", "fieldType": {"type": "Integer"}}, {"name": "pointPlanName", "description": "积分账户名称", "title": "积分账户名称", "fieldType": {"type": "String"}, "indexed": true}, {"name": "effectiveDate", "description": "积分开始有效期", "title": "积分开始有效期", "fieldType": {"type": "DateTime"}}, {"name": "overdueDate", "description": "积分结束有效期", "title": "积分结束有效期", "fieldType": {"type": "DateTime"}}, {"name": "subjectId", "description": "主体ID", "title": "主体ID", "fieldType": {"type": "Integer"}}, {"name": "point", "description": "提醒积分值", "title": "提醒积分值", "fieldType": {"type": "Number"}, "constraint": {"integerLength": 10, "fractionalLength": 7}}, {"name": "planId", "description": "计划ID", "title": "计划ID", "fieldType": {"type": "Integer"}}, {"name": "created", "description": "创建时间", "title": "创建时间", "fieldType": {"type": "DateTime"}}, {"name": "name", "description": "提醒配置名称", "title": "提醒配置名称", "fieldType": {"type": "String"}}, {"name": "desc", "description": "备注", "title": "备注", "fieldType": {"type": "String"}}, {"name": "remindId", "description": "配置ID", "title": "配置ID", "fieldType": {"type": "Integer"}, "indexed": true}, {"name": "subjectFqn", "description": "主体", "title": "主体", "fieldType": {"type": "String"}}]}]