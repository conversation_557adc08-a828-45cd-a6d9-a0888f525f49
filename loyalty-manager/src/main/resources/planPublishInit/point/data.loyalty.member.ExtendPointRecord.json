[{"fqn": "data.loyalty.member.account.ExtendPointRecord{id}", "owner": "loyalty", "description": "延长会员积分记录", "title": "延长会员积分记录", "type": "Object", "useMode": "HTAP", "fields": [{"name": "planId", "description": "计划ID", "title": "计划ID", "fieldType": {"type": "Integer"}}, {"name": "pointPlanId", "description": "积分账户ID", "title": "积分账户ID", "fieldType": {"type": "Integer"}}, {"name": "memberPointId", "description": "会员积分账户ID", "title": "会员积分账户ID", "fieldType": {"type": "String"}, "indexed": true}, {"name": "memberId", "description": "会员全网id", "title": "会员全网id", "fieldType": {"type": "String"}}, {"name": "subjectFqn", "description": "归属主体", "title": "归属主体", "fieldType": {"type": "String"}}, {"name": "point", "description": "延长积分值", "title": "延长积分值", "fieldType": {"type": "Number"}, "constraint": {"integerLength": 10, "fractionalLength": 7}}, {"name": "gainStatementId", "description": "非空，积分获取记录ID，所有积分账户内部流转均需要该属性", "title": "原单模型ID", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}}, {"name": "effectDate", "description": "积分生效时间", "title": "积分生效时间", "fieldType": {"type": "DateTime"}}, {"name": "overdueDate", "description": "积分失效时间", "title": "积分失效时间", "fieldType": {"type": "DateTime"}}, {"name": "originOverdueDate", "description": "原积分失效时间", "title": "原积分失效时间", "fieldType": {"type": "DateTime"}}, {"name": "status", "description": "积分状态,已生效-VALID、待生效-DELAY", "title": "积分状态", "fieldType": {"type": "String"}}, {"name": "desc", "description": "备注", "title": "备注", "fieldType": {"type": "String"}}, {"name": "channel", "description": "请在channel_type枚举中维护好渠道映射关系，接口会走此表后进行存储，事件的来源固定存为loyalty", "title": "数据来源", "fieldType": {"fqn": "data.loyalty.member.enum.ChannelType"}}, {"name": "openTraceId", "description": "对外接口使用的原单标示", "title": "对外唯一标示(类似原单标示)", "fieldType": {"type": "String"}}, {"name": "actionId", "description": "活动ID", "title": "活动ID", "fieldType": {"type": "String"}}, {"name": "actionName", "description": "活动名称", "title": "活动名称", "fieldType": {"type": "String"}}, {"name": "shopId", "description": "店铺ID", "title": "店铺ID", "fieldType": {"type": "String"}}, {"name": "KZZD1", "description": "扩展字段1", "title": "扩展字段1", "fieldType": {"type": "String"}}, {"name": "KZZD2", "description": "扩展字段2", "title": "扩展字段2", "fieldType": {"type": "String"}}, {"name": "KZZD3", "description": "扩展字段3", "title": "扩展字段3", "fieldType": {"type": "String"}}, {"name": "operator", "description": "操作人，默认系统", "title": "操作人", "fieldType": {"type": "String"}}, {"name": "operatorId", "description": "操作人用户ID", "title": "操作人ID", "fieldType": {"type": "String"}}, {"name": "modified", "description": "数据最后一次修改时间", "title": "修改时间", "fieldType": {"type": "DateTime"}}, {"name": "created", "description": "创建时间", "title": "创建时间", "fieldType": {"type": "DateTime"}}]}]