[{"fqn": "data.loyalty.member.account.PointSegment{id}", "owner": "loyalty", "description": "会员积分块", "title": "会员积分块", "type": "Object", "useMode": "HTAP", "fields": [{"name": "planId", "description": "计划id", "title": "计划id", "fieldType": {"type": "Integer"}}, {"name": "pointPlanId", "description": "积分账户id", "title": "积分账户id", "fieldType": {"type": "Integer"}, "constraint": {"integerLength": 10}}, {"name": "memberPointId", "description": "会员积分账户id", "title": "会员积分账户id", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}, "indexed": true}, {"name": "memberId", "description": "会员全网id", "title": "会员全网id", "fieldType": {"type": "String"}, "constraint": {"stringLength": 100}, "indexed": true}, {"name": "subjectFqn", "description": "归属性主体", "title": "归属性主体", "fieldType": {"type": "String"}}, {"name": "point", "description": "积分值", "title": "积分值", "fieldType": {"type": "Number"}, "constraint": {"integerLength": 10, "fractionalLength": 7}}, {"name": "expireDate", "description": "过期日期", "title": "过期日期", "fieldType": {"type": "Date"}}, {"name": "modified", "description": "数据最后一次修改时间", "title": "修改时间", "fieldType": {"type": "DateTime"}}, {"name": "created", "description": "created", "title": "创建时间", "fieldType": {"type": "DateTime"}}]}]