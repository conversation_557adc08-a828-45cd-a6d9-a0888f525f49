server:
  port: 8080
  servlet:
    context-path: /loyalty-manager/v1

spring:
  application:
    name: loyalty-manager
  datasource:
    url: ${database.url}
    username: ${database.username}
    password: ${database.password}
    driverClassName: ${database.driverClass}
  profiles:
    active: schedule, pip-common, pip-feign
  servlet:
    multipart:
      max-file-size: ${spring.multipart.file.size:500MB}
      max-request-size: ${spring.multipart.request.size:1500MB}
  aop:
    proxy-target-class: true

  cloud:
    stream:
      kafka:
        binder:
          brokers: ${kafka.brokers:127.0.0.1:9092}
          configuration:
            max.incremental.fetch.session.cache.slots: ${max.incremental.fetch.session.cache.slots:10000}
            auto.offset.reset: latest
          auto-add-partitions: true
          auto-create-topics: true
          min-partition-count: ${kafka.min.partition.num:60}
          required-acks: ${kafka.binder.requiredAcks:1}
          producer-properties:
            send.buffer.bytes: ${kafka.producer.sendBufferBytes:1048576}
            batch.size: ${kafka.producer.batchSize:327680}
            linger.ms: ${kafka.linger.ms:1}
          replication-factor: ${kafka.replication.factor:1}
      bindings:
        #等级变更通知
        GRADE_NOTIFY_OUTPUT:
          destination: LOYALTY_GRADE_NOTIFY_${system.environment}
          contentType: application/json
          producer:
            partition-key-expression: headers['memberId']
        #积分变更通知
        POINT_NOTIFY_OUTPUT:
          destination: LOYALTY_POINT_NOTIFY_${system.environment}
          contentType: application/json
          producer:
            partition-key-expression: headers['memberId']
        #变更记录通知
        RECORD_NOTIFY_OUTPUT:
          destination: LOYALTY_RECORD_NOTIFY_${system.environment}
          contentType: application/json
          producer:
            partition-key-expression: headers['memberId']
        #计划变更通知
        PLAN_NOTIFY_OUTPUT:
          destination: LOYALTY_PLAN_NOTIFY_${system.environment}
          contentType: application/json
        #用于不可以积压，需要及时处理的消息，它们在资源分配上会有所不同
        GREEN_CHANNEL_OUTPUT:
          destination: LOYALTY_GREEN_CHANNEL_${system.environment}
          contentType: application/json
        #积分变更
        POINT_MODIFY_OUTPUT:
          destination: POINT_MODIFY_CHANNEL_${system.environment}
          contentType: application/json
          producer:
            partition-key-expression: headers['memberId']
        #等级变更
        GRADE_MODIFY_OUTPUT:
          destination: GRADE_MODIFY_CHANNEL_${system.environment}
          contentType: application/json
        #勋章变更
        MEDAL_MODIFY_OUTPUT:
          destination: MEDAL_MODIFY_CHANNEL_${system.environment}
          contentType: application/json
        #审计日志
        AUDIT_LOG_OUTPUT:
          destination: RECORD_LOG_CHANNEL_${system.environment}
          contentType: application/json
        LOYALTY_HTTP_REQUEST_ASYNC_PROCESS_OUTPUT:
          destination: ${system.environment}_LOYALTY_HTTP_REQUEST_ASYNC_PROCESS
          contentType: application/json
          producer:
            partition-key-expression: headers['memberId']
      binders:
        bind:
          type: kafka

  redis:
    host: ${redis.host}
    port: ${redis.port}
    password: ${redis.password}
    lettuce:
      pool:
        max-active: ${lettuce.pool.max-active:16}
        max-idle: ${lettuce.pool.max-idle:16}
        min-idle: ${lettuce.pool.min-idle:1}
        max-wait: ${lettuce.pool.max-wait:-1ms}
        time-between-eviction-runs: 30000ms

management:
  endpoints:
    web:
      base-path: /system
      exposure:
        include: health,info,metrics,prometheus,loggers
  endpoint:
    health.show-details: always
