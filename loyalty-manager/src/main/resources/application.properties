#禁用配置中心默认将配置配置上传功能
spectrum.disableAutoUploadProperties=true

#jpa
spring.datasource.druid.initial-size=5
spring.datasource.druid.max-active=100
spring.datasource.druid.validation-query=select 1
spring.datasource.druid.test-on-borrow=true
spring.datasource.druid.test-on-return=true
spring.datasource.druid.test-while-idle=true
spring.jpa.hibernate.ddl-auto=none
spring.jpa.open-in-view=false
spring.data.redis.repositories.enabled=false

epassport.register.token=eyJhbGciOiJIUzI1NiIsInR5cCI6Imp3dCJ9.eyJzc28iOmZhbHNlLCJqdGkiOiI3NzkyZjg1MWI3NDY0YThmOTM4OTQ2OTkxZGU0Y2RhZiIsImV4cCI6MCwiaWF0IjoxNTQ0MTYwNzk2LCJpc3MiOiJlcGFzc3BvcnQiLCJhdWQiOiJsb3lhbHR5NCIsInR5cCI6IlJlZ2lzdGVyIiwib3NpIjoiMmIwMDU5ZTJlOWM4NGNlOGJiMWFkNzgwYzAxNTc3NzkiLCJpc1NTTyI6ZmFsc2V9.udcOpZdtcseZHihhVY5avEAwNtiTVQzi4CFuzLkiw8Q

#druid
druid.stat.mergeSql=true
druid.stat.logSlowSql=true
druid.stat.slowSqlMillis=1000

swagger.base-package=com.shuyun.loyalty.manager.resource


spring.messages.basename=i18n/messages

#ribbon
ribbon.ReadTimeout=120000

#事件服务
event.service.party.name=loyalty_manager
event.service.party.title=忠诚度
event.service.party.description=忠诚度
event.service.party.createBy=loyalty_manager

#异常码
exception.error.response.module=loyalty
exception.error.response.module.code=06

logging.format=${system.logging.format:[%d{yyyy-MM-dd HH:mm:ss.SSS}] [%-5level] [%class{0}:%M:%L] [%t] [%X{loyalty_trace_id}] [-[%msg]-] %n}
logging.file.path=${system.logging.dir:/var/log}