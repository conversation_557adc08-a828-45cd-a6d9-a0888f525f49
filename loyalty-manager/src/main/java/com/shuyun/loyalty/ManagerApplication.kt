package com.shuyun.loyalty

import com.shuyun.dm.dataapi.sdk.DataapiSdkFactory
import com.shuyun.fx.configuration.EnableFx
import com.shuyun.loyalty.manager.kafka.sink.KafkaSource
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import org.apache.logging.log4j.LogManager
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration
import org.springframework.cloud.openfeign.EnableFeignClients
import org.springframework.cloud.stream.annotation.EnableBinding
import org.springframework.context.annotation.EnableAspectJAutoProxy
import org.springframework.data.jpa.repository.config.EnableJpaRepositories

@EnableFx
@SpringBootApplication(exclude = [FlywayAutoConfiguration::class])
@EnableJpaRepositories
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableBinding(KafkaSource::class)
@EnableFeignClients(basePackages = ["com.shuyun.loyalty.manager.resource.open"])
class ManagerApplication : BaseApplication(ManagerApplication::class.java)

private val logger = LogManager.getLogger(ManagerApplication::class.java)

fun main(args: Array<String>) {
    DataapiSdkFactory.userContextSupplier(UserContextThreadSafe.getUserContextThreadSafeSupplier())
    logger.info("服务启动中...")
    ManagerApplication().run(*args)
    logger.info("服务启动完成！")
}