package com.shuyun.loyalty.manager.service

import com.google.common.eventbus.AllowConcurrentEvents
import com.google.common.eventbus.Subscribe
import com.shuyun.loyalty.manager.kafka.sink.KafkaSource
import com.shuyun.loyalty.service.datamodel.MemberGradeRecord
import com.shuyun.loyalty.service.datamodel.MemberPointRecord
import com.shuyun.loyalty.service.event.notify.*
import com.shuyun.loyalty.service.model.DefaultSubscriber
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.messaging.MessageChannel
import org.springframework.messaging.support.MessageBuilder
import org.springframework.stereotype.Component

@Component
@Suppress("unused")
class LoyaltyManagerDefaultSubscriber : DefaultSubscriber {

    @Autowired
    private lateinit var kafkaSource: KafkaSource

    @Subscribe
    @AllowConcurrentEvents
    fun subscribe(event : MemberGradeRecord){
        try {
            val message = GradeModifiedNotify().initByRecord(event)
            notify(kafkaSource.gradeNotifyOutput(), message)
            notify(kafkaSource.recordNotifyOutput(), RecordNotify().initByRecord(event))
        } catch (e: Throwable) {
            throw e
        }

    }

    @Subscribe
    @AllowConcurrentEvents
    fun subscribe(event : MemberPointRecord){
        try {
            val message = PointModifiedNotify().initByRecord(event)
            notify(kafkaSource.pointNotifyOutput(), message)
            notify(kafkaSource.recordNotifyOutput(), RecordNotify().initByRecord(event))
        } catch (e: Throwable) {
            throw e

        }
    }

    @Subscribe
    @AllowConcurrentEvents
    fun subscribe(event : PublishedPlanChanged){
        try {
            val message = PlanModifiedNotify().initByPlan(event.currentPlan,event.originalPlan)
            notify(kafkaSource.planNotifyOutput(), message)
        } catch (e: Throwable) {
            throw e

        }
    }

    fun notify(messageChannel: MessageChannel, message: LoyaltyNotify) {
        val msg = MessageBuilder.withPayload(message)
            .setHeader("memberId", message.partitionKey() ?: "-")
            .build()
        messageChannel.send(msg)
    }
}