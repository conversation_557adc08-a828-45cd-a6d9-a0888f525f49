package com.shuyun.loyalty.manager.service

import com.shuyun.loyalty.manager.util.Constants.keyId
import com.shuyun.loyalty.manager.util.ListCompareUtil
import com.shuyun.loyalty.manager.util.WAY_TO_HANDLE_DATA
import com.shuyun.loyalty.manager.vo.MedalRuleGroupVo
import com.shuyun.loyalty.sdk.localDate
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.exception.MedalException
import com.shuyun.loyalty.service.meta.MedalRuleGroupTypeEnum
import com.shuyun.loyalty.service.meta.RuleGroupStatusEnum
import com.shuyun.loyalty.service.meta.TimeTypeEnum.ABSOLUTE_TIME
import com.shuyun.loyalty.service.meta.TimeTypeEnum.RELATIVE_TIME
import com.shuyun.loyalty.service.model.MedalRule
import com.shuyun.loyalty.service.model.MedalRuleGroup
import com.shuyun.loyalty.service.service.I18nModelService
import com.shuyun.loyalty.service.service.MedalRuleGroupBaseService
import com.shuyun.pip.ApplicationContextHolder
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.ExampleMatcher
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.ZonedDateTime

@Service
class MedalRuleGroupService {

    @Autowired
    private lateinit var medalRuleGroupBaseService: MedalRuleGroupBaseService

    @Autowired
    private lateinit var medalRuleService: MedalRuleService

    @Autowired
    private lateinit var i18nModelService: I18nModelService

    fun listMedalRuleGroup(medalHierarchyId: Long, medalDefinitionId: Long, type: MedalRuleGroupTypeEnum,
                           name: String?, status: RuleGroupStatusEnum?): List<MedalRuleGroupVo> {
        val condition = MedalRuleGroup().apply {
            this.medalHierarchyId = medalHierarchyId
            this.medalDefinitionId = medalDefinitionId
            this.groupType = type
            this.name = name
        }
        val matcher = ExampleMatcher.matching().withIgnoreNullValues()
        val sort = Sort.by(Sort.Direction.DESC, "createTime")
        val groups = medalRuleGroupBaseService.findAllByExample(condition, matcher, sort)
            .filter { it.status == status }
            .map {
                MedalRuleGroupVo().initVoFromModel(it)
            }
        for (group in groups) {
            group._i18nPayload = i18nModelService.findI18nPayload(MedalRuleGroup.I18N_GROUP__NAME, group.id)
        }
        return groups
    }

    fun listMedalRuleGroupView(medalHierarchyId: Long, name: String?, statuses: String?): List<MedalRuleGroupVo> {
        val statusSet = if (statuses.isNullOrBlank())
            setOf(RuleGroupStatusEnum.RUNNING, RuleGroupStatusEnum.WAIT_EFFECT)
        else
            statuses.split(',').map { RuleGroupStatusEnum.valueOf(it) }.toSet()

        val list = if (name.isNullOrBlank())
            medalRuleGroupBaseService.findByMedalHierarchyIdOrderByCreateTimeDesc(medalHierarchyId).filter {
                statusSet.contains(it.status)
            }
        else
            medalRuleGroupBaseService.findByMedalHierarchyIdAndNameOrderByCreateTimeDesc(medalHierarchyId, name).filter {
                statusSet.contains(it.status)
            }

        val groups = list.map { MedalRuleGroupVo().initVoFromModel(it) }
        for (group in groups) {
            group._i18nPayload = i18nModelService.findI18nPayload(MedalRuleGroup.I18N_GROUP__NAME, group.id)
        }
        return groups
    }


    fun findById(id: Long): MedalRuleGroupVo {
        val group = MedalRuleGroupVo().initVoFromModel(medalRuleGroupBaseService.findById(id))
        group._i18nPayload = i18nModelService.findI18nPayload(MedalRuleGroup.I18N_GROUP__NAME, group.id)
        return group
    }

    fun findByMedalDefinitionId(medalDefinitionId: Long): List<MedalRuleGroupVo> {
        val groups = medalRuleGroupBaseService.findByMedalDefinition(medalDefinitionId).map {
            MedalRuleGroupVo().initVoFromModel(it)
        }
        for (group in groups) {
            group._i18nPayload = i18nModelService.findI18nPayload(MedalRuleGroup.I18N_GROUP__NAME, group.id)
        }
        return groups
    }

    fun saveOrUpdate(ruleGroupVoList: List<MedalRuleGroupVo>) {
        checkMedalRuleGroupVoList(ruleGroupVoList)
        val definitionId = ruleGroupVoList[0].medalDefinitionId!!

        val oldRuleGroupList = medalRuleGroupBaseService.findByMedalDefinition(definitionId)

        var sort = 0
        val newRuleGroupList = ruleGroupVoList.map {
            val medalRuleGroup = it.initModelFromVo()
            medalRuleGroup.sort = sort++
            medalRuleGroup
        }

        ListCompareUtil.initModifyDataByCompareList(newRuleGroupList, oldRuleGroupList, true, keyId)
            .forEach { (ruleGroup, wayToHandle) ->
                ruleGroup as MedalRuleGroup
                ruleGroupVoList.forEach {
                    val condition = MedalRuleGroup().apply {
                        this.medalHierarchyId = it.medalHierarchyId
                        this.medalDefinitionId = it.medalDefinitionId
                        this.groupType = it.groupType
                        this.name = it.name
                        this.disabled = false
                    }
                    val matcher = ExampleMatcher.matching().withIgnoreNullValues()
                    val list = medalRuleGroupBaseService.findAllByExample(condition, matcher).filter { group ->
                        wayToHandle == WAY_TO_HANDLE_DATA.TO_ADD || group.id != it.id
                    }
                    if (list.isNotEmpty() && wayToHandle != WAY_TO_HANDLE_DATA.TO_DELETE)
                        throw LoyaltyException(LoyaltyExceptionCode.MEDAL_GROUP_REPEATED)
                }
                val self = ApplicationContextHolder.getBean(MedalRuleGroupService::class.java)
                when (wayToHandle) {
                    WAY_TO_HANDLE_DATA.TO_ADD -> {
                        self.insert(ruleGroup)
                        i18nModelService.saveOrUpdate(MedalRuleGroup.I18N_GROUP__NAME, ruleGroup.id!!, ruleGroup._i18nPayload)
                    }
                    WAY_TO_HANDLE_DATA.TO_UPDATE -> {
                        self.update(ruleGroup)
                        i18nModelService.saveOrUpdate(MedalRuleGroup.I18N_GROUP__NAME, ruleGroup.id!!, ruleGroup._i18nPayload)
                    }
                    WAY_TO_HANDLE_DATA.TO_DELETE -> { /* no delete to do */ }
                }
        }

    }

    @Transactional(rollbackFor = [Exception::class])
    fun insert(ruleGroup: MedalRuleGroup) {
        medalRuleGroupBaseService.insert(ruleGroup)
        medalRuleService.insert(ruleGroup)
    }

    @Transactional(rollbackFor = [Exception::class])
    fun update(ruleGroup: MedalRuleGroup) {
        medalRuleGroupBaseService.update(ruleGroup)

        val oldRuleList = medalRuleGroupBaseService.findById(ruleGroup.id!!).ruleList
        ListCompareUtil.initModifyDataByCompareList(ruleGroup.ruleList, oldRuleList!!, true, keyId)
            .forEach { (rule, wayToHandle) ->
                rule as MedalRule
                when (wayToHandle) {
                    WAY_TO_HANDLE_DATA.TO_ADD -> medalRuleService.insert(rule)
                    WAY_TO_HANDLE_DATA.TO_UPDATE -> medalRuleService.update(rule)
                    WAY_TO_HANDLE_DATA.TO_DELETE -> {/* no delete to do */}
                }
            }
    }

    fun delete(id: Long) {
        medalRuleGroupBaseService.delete(id)
        i18nModelService.delete(MedalRuleGroup.I18N_GROUP__NAME, id)
    }

    fun file(id: Long) {
        medalRuleGroupBaseService.file(id)
    }

    private fun checkMedalRuleGroupVoList(medalRuleGroupVoList: List<MedalRuleGroupVo>) {
        val nameList = medalRuleGroupVoList.filter { !it.name.isNullOrEmpty() }.map { it.name!! }
        if (nameList.distinct().size != nameList.size)
            throw MedalException(LoyaltyExceptionCode.MEDAL_GROUP_REPEATED)

        val medalDefinitionIdList = medalRuleGroupVoList.map { it.medalDefinitionId }
        if (medalDefinitionIdList.distinct().size > 1)
            throw LoyaltyException(LoyaltyExceptionCode.ONELY_SAVE_ONE)
        if (medalDefinitionIdList[0] == 0L)
            throw LoyaltyException(LoyaltyExceptionCode.MEDAL_GROUP_NULL)

        medalRuleGroupVoList.forEach { group ->
            if (null == group.startTime)  group.startTime = ZonedDateTime.now()
            group.ruleList?.map {
                if (it.endTimePropertyId != null && it.endTimePropertyId != -1L) return@map
                when(listOf(it.endYearType, it.endMonthType, it.endDayType)) {
                    listOf(ABSOLUTE_TIME, ABSOLUTE_TIME, ABSOLUTE_TIME) -> {
                        if (it.endYearTime != null) {
                            if (it.endMonthTime == null || it.endDayTime == null)
                                throw LoyaltyException(LoyaltyExceptionCode.MEDAL_TIME_ERROR)
                            val toLocalDate = group.startTime!!.localDate()
                            if (LocalDate.of(it.endYearTime!!, it.endMonthTime!!, it.endDayTime!!) <= toLocalDate)
                                throw LoyaltyException(LoyaltyExceptionCode.MEDAL_TIME_ERROR)
                        }
                    }
                    listOf(RELATIVE_TIME, ABSOLUTE_TIME, ABSOLUTE_TIME) -> {}
                    listOf(RELATIVE_TIME, RELATIVE_TIME, ABSOLUTE_TIME) -> {}
                    listOf(RELATIVE_TIME, RELATIVE_TIME, RELATIVE_TIME) -> {}
                    else -> { throw LoyaltyException(LoyaltyExceptionCode.MEDAL_TIME_ERROR) }
                }
            }
        }
    }

}