package com.shuyun.loyalty.manager.resource

import com.shuyun.epassport.sdk.register.RequiresPermissions
import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.sdk.Uuid
import io.swagger.v3.oas.annotations.media.Schema
import okhttp3.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.apache.commons.codec.digest.DigestUtils
import org.apache.logging.log4j.LogManager
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.io.IOException
import java.time.Duration
import java.util.*
import java.util.concurrent.CompletableFuture


@RestController
@RequestMapping("/")
class ChatResource {

    private val logger = LogManager.getLogger(ChatResource::class.java)

    private val client = OkHttpClient.Builder()
        .connectTimeout(Duration.ofSeconds(30))
        .build()

    private val address get() = Property.getSysOrEnv("system.boss.api.address")
    private val apiKey get() = Property.getSysOrEnv("system.boss.api.key")
    private val apiSecret get() = Property.getSysOrEnv("system.boss.api.secret")

    enum class ChatPurpose { EXPLAIN, EXPR }

    @Schema(title = "ChatRequest")
    data class ChatRequest(
        @Schema(title = "主体模型FQN")
        val fqn: String,
        @Schema(title = "推理类型")
        val chatPurpose: ChatPurpose,
        @Schema(title = "属性描述")
        val prompt: String
    )

    @Schema(title = "ChatResponse")
    data class ChatResponse(
        @Schema(title = "主体模型FQN")
        val fqn: String,
        @Schema(title = "推理类型")
        val chatPurpose: ChatPurpose,
        @Schema(title = "业务ID")
        val bizId: String,
        @Schema(title = "推理结果")
        val content: String = "",
    )


    @Schema(title = "AcceptRequest")
    data class AcceptRequest(
        @Schema(title = "业务ID")
        val bizId: String,
        @Schema(title = "主体模型FQN")
        val fqn: String,
        @Schema(title = "推理类型")
        val chatPurpose: ChatPurpose,
        @Schema(title = "原属性描述")
        val prompt: String,
        @Schema(title = "推理结果")
        val response: String,
        @Schema(title = "调整后的属性描述")
        val userPrompt: String,
        @Schema(title = "调整后的推理结果")
        val userResponse: String,
        @Schema(title = "评估")
        val evaluation: String = ""
    )


    @PostMapping("/chat")
    @RequiresPermissions(allowAuthenticated = true)
    fun chat(@RequestBody request: ChatRequest): CompletableFuture<ChatResponse> {
        val future = CompletableFuture<ChatResponse>()
        doChat(request, future)
        return future
    }



    @PostMapping("/accept")
    @RequiresPermissions(allowAuthenticated = true)
    fun accept(@RequestBody request: AcceptRequest) {
        doAccept(request)
    }


    private fun doChat(req: ChatRequest, result: CompletableFuture<ChatResponse>) {
        val bizId = Uuid.uuid
        val timestamp = System.currentTimeMillis()
        val apiSign = sign(mapOf(
            "x_api_key" to apiKey!!,
            "x_api_timestamp" to timestamp.toString()
        ), apiSecret!!)

        val url = "${address}/order-management/v1/entp/api/loyalty/chat?x_api_key=$apiKey&x_api_timestamp=$timestamp&x_api_sign=$apiSign"

        val json = """
            {
                "chatPurpose": "${req.chatPurpose}",
                "tenantCode": "${GlobalContext.defTenantId()}",
                "bizId": "$bizId",
                "params": { "品牌": "${req.fqn}" },
                "prompt": "${req.prompt}"
            }
        """.trimIndent()
        logger.debug("doChat: $json")
        val body = json.toRequestBody("application/json".toMediaTypeOrNull())

        val request = Request.Builder()
            .url(url)
            //.headers(headers)
            .post(body)
            .build()

        client.newCall(request).enqueue(object : Callback {
            val empty = ChatResponse(req.fqn,req.chatPurpose,bizId)
            override fun onFailure(call: Call, e: IOException) {
                logger.warn("doChat failed", e)
                result.complete(ChatResponse(req.fqn,req.chatPurpose,bizId))
            }
            override fun onResponse(call: Call, response: Response) {
                response.use {
                    if (response.isSuccessful) {
                        response.body?.let {
                            result.complete(empty.copy(content = it.string()))
                        } ?: result.complete(empty)
                    } else {
                        val error = response.body?.string() ?: "failed"
                        logger.warn("doChat failed: $error")
                        result.complete(empty)
                    }
                }
            }
        })
    }


    private fun doAccept(req: AcceptRequest) {
        val timestamp = System.currentTimeMillis()
        val apiSign = sign(mapOf(
            "x_api_key" to apiKey!!,
            "x_api_timestamp" to timestamp.toString()
        ), apiSecret!!)

        val url = "${address}/order-management/v1/entp/api/loyalty/annotation-data/create?x_api_key=$apiKey&x_api_timestamp=$timestamp&x_api_sign=$apiSign"
        val params = when (req.chatPurpose) {
            ChatPurpose.EXPR -> """{ "品牌": "${req.fqn}" }"""
            ChatPurpose.EXPLAIN -> ""
        }
        val json = """
            {
                "tenantCode": "${GlobalContext.defTenantId()}",
                "bizId": "${req.bizId}",
                "chatPurpose": "${req.chatPurpose}",
                "params": "$params",
                "prompt": "${req.prompt}",
                "response": "${req.response}",
                "userPrompt": "${req.userPrompt}",
                "userResponse": "${req.userResponse}",
                "evaluation": "${req.evaluation}"
            }
        """.trimIndent()
        logger.debug("doAccept: $json")
        val body = json.toRequestBody("application/json".toMediaTypeOrNull())

        val request = Request.Builder()
            .url(url)
            .post(body)
            .build()

        client.newCall(request).enqueue(object : Callback {
            override fun onFailure(call: Call, e: IOException) {
                logger.warn("doAccept failed", e)
            }
            override fun onResponse(call: Call, response: Response) {
                response.use {
                    if (!response.isSuccessful) {
                        val error = response.body?.string() ?: "failed"
                        logger.warn("doAccept failed: $error")
                    }
                }
            }
        })
    }


    private fun sign(parameters: Map<String, String> , secret: String): String {
        val keys = parameters.keys.toTypedArray().also { Arrays.sort(it) }
        val buffer = StringBuilder().append(secret)
        keys.forEach { buffer.append(it).append(parameters[it]) }
        buffer.append(secret)
        return DigestUtils.md5Hex(buffer.toString())
    }
}