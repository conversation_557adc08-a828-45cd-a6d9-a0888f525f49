package com.shuyun.loyalty.manager.service

import com.fasterxml.jackson.databind.node.ArrayNode
import com.pip.mybatisplus.toolkit.MetadataFactory
import com.shuyun.dm.api.domain.DataModel
import com.shuyun.dm.api.domain.ModelRelation
import com.shuyun.dm.metadata.sdk.api.MetadataHttpApi
import com.shuyun.dm.metadata.sdk.client.DefaultMetadataHttpSdk
import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.loyalty.service.repository.*
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import org.apache.commons.io.IOUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.Resource
import org.springframework.core.io.support.PathMatchingResourcePatternResolver
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import retrofit2.Response
import java.io.InputStream
import java.lang.reflect.InvocationTargetException

@Service
class ModelInitService  {

    private val logger = LogManager.getLogger(ModelInitService::class.java)

    @Autowired
    private lateinit var pointRuleGroupRepository: PointRuleGroupRepository
    @Autowired
    private lateinit var pointSendRuleRepository: PointSendRuleRepository
    @Autowired
    private lateinit var pointDeductRuleRepository: PointDeductRuleRepository
    @Autowired
    private lateinit var pointDeductRuleOperateRepository: PointDeductRuleOperateRepository
    @Autowired
    private lateinit var gradeRuleGroupRepository: PointDeductRuleOperateRepository
    @Autowired
    private lateinit var gradeRuleRepository: GradeRuleRepository

    @Transactional
    fun deleteGroup(eventTypeList: List<EventType>){
        val pointGroupList  = pointRuleGroupRepository.findAll()
        pointGroupList.filter {group -> eventTypeList.map { it.id }.contains(group.eventTypeId) }.forEach {
            pointRuleGroupRepository.deleteById(it.id!!)
            pointSendRuleRepository.deleteByRuleGroupId(it.id!!)
            pointDeductRuleRepository.deleteByRuleGroupId(it.id!!)
            pointDeductRuleOperateRepository.deleteByRuleGroupId(it.id!!)
        }

        val gradeGroupList = gradeRuleGroupRepository.findAll()
        gradeGroupList.filter {group -> eventTypeList.map { it.id }.contains(group.eventTypeId) }.forEach {
            gradeRuleGroupRepository.deleteById(it.id!!)
            gradeRuleRepository.deleteByGroupId(it.id!!)
        }
    }

    fun buildPlanModelTask(modelResourceList: Array<Resource>, id: String, subjectFqn: String): List<ModelTask> {
        return modelResourceList.distinctBy { it.filename!!.lowercase() }.map {
            ModelTask(it.filename!!, id) {
                modelInit(it.inputStream, id)
            }
        }
    }


    fun buildModelReference(referenceResourceList: Array<Resource>, id: String, subjectFqn: String): Map<String, List<ModelRelation>> {
        val result = HashMap<String, MutableList<ModelRelation>>()
        referenceResourceList.distinctBy { it.filename!!.lowercase() }.forEach {
            val relations = buildModelReference(it.inputStream, id, subjectFqn)
            relations.forEach { (fqn, relations) ->
                if (result[fqn] == null) {
                    result[fqn] = ArrayList()
                }
                result[fqn]!!.addAll(relations)
            }
        }
        return result
    }


    private fun modelInit(input: InputStream, idSplit: String) {
        var id = idSplit
        if (id.indexOf("&|^") > -1) id = idSplit.split("&|^")[0]
        val jsonArray = parseInputToJsonArray(input, id)
        jsonArray.forEach {
            val dataModel = JsonUtils.convert(it, DataModel::class.java)
            dataModel.fields.forEach { field ->
                if (field.fieldType.type == com.shuyun.dm.api.domain.ModelType.Number) {
                    field.constraint.fractionalLength = idSplit.split("&|^")[1].toInt()
                }
            }
            try {
                MetadataFactory.sdk().createModel(dataModel)
            } catch (e: SdkException) {
                if (409 != e.code?.toInt()) {
                    throw e
                } else {
                    logger.info("模型已经存在： ${dataModel.name}")
                }
            }
        }
    }


    fun addRelation(fqn: String, relation: ModelRelation, retryCount: Int = 0) {
        val sdk = MetadataFactory.sdk() as DefaultMetadataHttpSdk
        val cls = DefaultMetadataHttpSdk::class.java
        val field = cls.getDeclaredField("httpApi")
        val method = cls.getDeclaredMethod("getBody", Response::class.java)
        try {
            method.isAccessible = true
            field.isAccessible = true
            val httpApi = field.get(sdk) as MetadataHttpApi
            val response = httpApi.addRelation(fqn, autoCommit = false, relation).execute()
            method.invoke(sdk, response)
        } catch (e: Exception) {
            if (e is InvocationTargetException) {
                val te = e.targetException
                if (te is SdkException) {
                    when {
                        409 == te.code?.toInt() -> {
                            logger.info("关系字段已经存在：${fqn} ${te.message}")
                        }
                        te.error_code == "151013" -> {
                            // 模型尚未提交
                            UserContextThreadSafe.setMetadataRefScanSkipped(true)
                            sdk.updateModel(fqn, DataModel().apply { this.fqn = fqn })
                            if (retryCount < 20) {
                                addRelation(fqn, relation, retryCount + 1)
                            } else {
                                throw LoyaltyException(LoyaltyExceptionCode.UNKNOWN_EXCEPTION, te)
                            }
                        }
                        else -> {
                            logger.error("添加模型关系失败 fqn: {} relation: {}", fqn, JsonUtils.toJson(relation))
                            throw LoyaltyException(LoyaltyExceptionCode.UNKNOWN_EXCEPTION, te)
                        }
                    }
                } else {
                    throw te
                }
            }
            else throw e
        } finally {
            method.isAccessible = false
            field.isAccessible = false
        }
    }


    //新的建立模型之间对应关系的方式
    private fun buildModelReference(input: InputStream, idSplit: String, subjectFqn: String): Map<String, List<ModelRelation>> {
        var id = idSplit
        if(id.indexOf("&|^") > -1) id = idSplit.split("&|^")[0]
        val jsonArray = parseInputToJsonArray(input, id, subjectFqn)
        val relations = HashMap<String, MutableList<ModelRelation>>()
        jsonArray.forEach {
            val fqn = it.get("fqn").asText()
            val data = it.get("relation")
            val relation = JsonUtils.convert(data, ModelRelation::class.java)
            if (relations[fqn] == null) {
                relations[fqn] = ArrayList()
            }
            relation.mainModelFqn = fqn
            relations[fqn]!!.add(relation)
        }
        return relations
    }


    //新的建立模型之间对应关系的方式
    private fun deleteReference(input : InputStream, id : String, subjectFqn:String){
        val jsonArray = parseInputToJsonArray(input, id, subjectFqn)
        jsonArray.forEach {
            val fqn = it.get("fqn").asText()
            val data = it.get("relation")
            val relation = JsonUtils.convert(data, ModelRelation::class.java)
            try {
                MetadataFactory.sdk().deleteRelation(fqn, relation.mainModelField.name)
            } catch (e: SdkException) {
                logger.error("删除引用失败，fqn:$fqn,field:${relation.mainModelField.name}",e)
            }
        }
    }

    fun planModelDelete(id: Long, modelType: ModelType, subjectFqn: String) {
        val references =
            PathMatchingResourcePatternResolver().getResources("classpath:planPublishInit/${modelType.type}/reference/*.json")
        references.forEach {
            try {
                deleteReference(it.inputStream, id.toString(), subjectFqn)
            } catch (e: Exception) {
                logger.warn("删除引用失败，文件:${it.filename}",e)
            }
        }
        val modelList =
            PathMatchingResourcePatternResolver().getResources("classpath:planPublishInit/${modelType.type}/*.json")
        modelList.forEach {
            try {
                modelDelete(it.inputStream, id.toString())
            } catch (e: Exception) {
                logger.warn("删除模型失败，文件:${it.filename}",e)
            }
        }
    }

    private fun modelDelete(input : InputStream, id : String){
        val jsonArray = parseInputToJsonArray(input, id)
        jsonArray.forEach {
            val dataModel = JsonUtils.convert(it, DataModel::class.java)
            try {
                MetadataFactory.sdk().deleteModel(dataModel.fqn)
            } catch (e: Exception) {
                logger.error("删除模型失败，fqn:${dataModel.fqn}",e)
            }
        }
    }


    private fun parseInputToJsonArray(input: InputStream, id: String, subjectFqn: String? = null): ArrayNode {
        var text = IOUtils.toString(input, "UTF-8")
        text = text.replace("{id}", id)
        subjectFqn?.let { text = text.replace("{subjectFqn}", it) }
        return JsonUtils.parse(text, ArrayNode::class.java)
    }

}

enum class ModelType(val type : String){
    GRADE("grade"),
    POINT("point"),
    MEDAL("medal")
}
