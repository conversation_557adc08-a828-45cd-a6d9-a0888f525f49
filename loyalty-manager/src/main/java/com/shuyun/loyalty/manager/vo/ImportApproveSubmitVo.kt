package com.shuyun.loyalty.manager.vo

import io.swagger.v3.oas.annotations.media.Schema
import javax.validation.constraints.NotNull

@Schema(title = "ManualApproveSubmitVo")
data class ImportApproveSubmitVo(
    @Schema(title = "导入积分、等级、勋章信息", type = "Long")
    @field:NotNull(message = "导入积分、等级、勋章信息")
    var importPointOrGradeVo: ImportPointOrGradeVo? = null,

    @Schema(title = "审批流实例id", type = "Long")
    var instanceId: Long? = null

)