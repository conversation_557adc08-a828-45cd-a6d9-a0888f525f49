package com.shuyun.loyalty.manager.service

import com.shuyun.epassport.sdk.register.RegisterClientException
import com.shuyun.loyalty.service.infrastructure.epassport.Epassport
import com.shuyun.loyalty.service.infrastructure.epassport.RegisterPathUtil
import com.shuyun.loyalty.service.infrastructure.epassport.RequestMappingTool
import com.shuyun.loyalty.service.util.RedisConstantValue
import com.shuyun.loyalty.service.util.RedisUtils
import com.shuyun.pip.ApplicationContextHolder
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationContext
import org.springframework.stereotype.Service
import org.yaml.snakeyaml.Yaml
import java.io.BufferedReader
import java.io.InputStreamReader
import java.nio.charset.StandardCharsets

@Service
class MedalConfigService {

    @Autowired
    private lateinit var applicationContext: ApplicationContext

    @Autowired
    private lateinit var passport: Epassport

    @Autowired
    private lateinit var requestMappingTool: RequestMappingTool

    private val redisUtils by lazy {  ApplicationContextHolder.getBean(RedisUtils::class.java) }

    fun subscribeMedal() {
        val activated = redisUtils.getString(RedisConstantValue.MEDAL_ACTIVATED_KEY)
        if (true.toString() != activated) {
            registerClient(true)
            redisUtils.setString(RedisConstantValue.MEDAL_ACTIVATED_KEY, true.toString())
        }
    }

    fun unsubscribeMedal() {
        val activated = redisUtils.getString(RedisConstantValue.MEDAL_ACTIVATED_KEY)
        if (true.toString() == activated) {
            registerClient(false)
            redisUtils.setString(RedisConstantValue.MEDAL_ACTIVATED_KEY, false.toString())
        }
    }

    private fun registerClient(withMedal: Boolean) {
        val fileName = if (withMedal) "register_medal.yml" else "register.yml"
        val resource = Thread.currentThread().getContextClassLoader()
            .getResourceAsStream(fileName) ?: throw RegisterClientException("register.yml文件不存在!")
        val reader = BufferedReader(InputStreamReader(resource, StandardCharsets.UTF_8))
        val config = Yaml().loadAs(reader, com.shuyun.epassport.sdk.register.Configuration::class.java)
        val rules = RegisterPathUtil.getRegisterRuleList(applicationContext, requestMappingTool)
        passport.register(rules, config)
    }

}