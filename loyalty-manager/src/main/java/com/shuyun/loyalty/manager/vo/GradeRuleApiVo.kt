package com.shuyun.loyalty.manager.vo

import com.shuyun.loyalty.service.model.GradeRule
import io.swagger.v3.oas.annotations.media.Schema

@Schema(title = "GradeRuleApiVo")
data class GradeRuleApiVo(
    @Schema(title = "前端展示表达式时需要的辅助信息，格式前端自定", type = "String")
    var displayInfo: String? = null,
    @Schema(title = "后端加工后的表达式", type = "String")
    var expressionTranslated: String? = null,
    @Schema(title = "条件表达式存放变量", type = "String")
    var expressionVariable: String? = null,
    @Schema(title = "变更的等级ID", type = "String")
    var degradeId: Long? = null

) {
    fun build(gradeRuleList:List<GradeRule>?):List<GradeRuleApiVo>{
        val gradeRules = mutableListOf<GradeRuleApiVo>()
        gradeRuleList?.forEach{
            val grade = GradeRuleApiVo()
            grade.displayInfo = it.displayInfo
            grade.expressionVariable = it.expressionVariable
            grade.expressionTranslated = it.expressionTranslated
            grade.degradeId = it.degradeId
            gradeRules.add(grade)
        }
        return gradeRules
    }
}