package com.shuyun.loyalty.manager.resource

import com.shuyun.epassport.sdk.register.RequiresPermissions
import com.shuyun.loyalty.service.infrastructure.es.EventClient
import com.shuyun.loyalty.service.model.EventStreamMetas
import com.shuyun.loyalty.service.service.EventStreamMetasBaseService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/event/stream/metas")
@RequiresPermissions(allowAuthenticated = true)
class EventStreamMetasResource {

    @Autowired
    private lateinit var eventStreamMetasBaseService: EventStreamMetasBaseService

    @Operation(summary = "获取事件流元数据", tags = ["事件流API"])
    @GetMapping("/{fqn}")
    @RequiresPermissions(value = ["loyalty4.setting.plan.view", "loyalty4.manage.point.rule.edit", "loyalty4.manage.grade.rule.edit", "loyalty4.manage.medal.rule.edit"])
    fun getEventStreamMetas(@Parameter(description = "fqn") @PathVariable("fqn") fqn: String):EventStreamMetas{
        return eventStreamMetasBaseService.findByFqn(fqn)
    }


    @Operation(summary = "获取事件流元数据列表", tags = ["事件流API"])
    @GetMapping("/list")
    @RequiresPermissions(value = ["loyalty4.setting.plan.view", "loyalty4.manage.point.rule.edit", "loyalty4.manage.grade.rule.edit", "loyalty4.manage.medal.rule.edit"])
    fun getEventStreamMetasList(): List<EventStreamMetas> {
        return eventStreamMetasBaseService.findAll()
    }


    @Operation(summary = "通过事件模型获取事件流元数据", tags = ["事件流API"])
    @GetMapping("/es/{esFqn}")
    @RequiresPermissions(value = ["loyalty4.setting.plan.view", "loyalty4.manage.point.rule.edit", "loyalty4.manage.grade.rule.edit", "loyalty4.manage.medal.rule.edit"])
    fun getEventStreamMetasByEsFqn(@PathVariable esFqn: String, @RequestParam("subjectFqn") subjectFqn: String): EventStreamMetas? {
        return eventStreamMetasBaseService.findESStreamMetas(esFqn, subjectFqn)
    }


    @Operation(summary = "通过主体获取事件流元数据列表", tags = ["事件流API"])
    @GetMapping("/es/list")
    @RequiresPermissions(value = ["loyalty4.setting.plan.view", "loyalty4.manage.point.rule.edit", "loyalty4.manage.grade.rule.edit", "loyalty4.manage.medal.rule.edit"])
    fun getEventStreamMetasBySubjectFqn(@RequestParam("subjectFqn") subjectFqn: String): List<EventStreamMetas> {
        return EventClient.findESStreamMetasBySubjectFQN(subjectFqn)
    }

}