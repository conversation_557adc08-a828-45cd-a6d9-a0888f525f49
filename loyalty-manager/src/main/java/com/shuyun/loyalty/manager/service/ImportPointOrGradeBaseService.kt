package com.shuyun.loyalty.manager.service

import com.shuyun.loyalty.manager.repository.ImportPointOrGrade
import com.shuyun.loyalty.manager.repository.ImportPointOrGradeRepository
import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.meta.ImportStatusEnum
import com.shuyun.loyalty.service.meta.OperationType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Example
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import java.util.*
import kotlin.jvm.optionals.getOrNull


@Service
class ImportPointOrGradeBaseService {
    @Autowired
    private lateinit var importPointOrGradeRepository: ImportPointOrGradeRepository

    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    fun save(importPoint: ImportPointOrGrade): ImportPointOrGrade {
        return importPointOrGradeRepository.save(importPoint)
    }

    fun update(importPoint: ImportPointOrGrade) {
        val o = findById(importPoint.id!!).getOrNull() ?: return
        if (o.status != ImportStatusEnum.RUNNING) {
            importPoint.status = o.status
        }
        importPointOrGradeRepository.save(importPoint.apply { this.updateTime = Date() })
    }

    fun findImportPointOrGradePageByExample(
        example: Example<ImportPointOrGrade>,
        pageable: Pageable
    ): Page<ImportPointOrGrade> {
        return importPointOrGradeRepository.findAll(example, pageable)
    }

    fun findById(id: Long): Optional<ImportPointOrGrade> {
        return importPointOrGradeRepository.findById(id)
    }

}