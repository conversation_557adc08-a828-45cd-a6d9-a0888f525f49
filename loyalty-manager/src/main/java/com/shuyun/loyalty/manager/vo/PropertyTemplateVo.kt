package com.shuyun.loyalty.manager.vo

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.service.meta.DataTypeVoEnum
import com.shuyun.loyalty.service.model.I18nPayload
import com.shuyun.loyalty.service.model.PropertyTemplate
import com.shuyun.loyalty.service.util.ModelInitUtil.copyPropertiesIgnoreNull
import io.swagger.v3.oas.annotations.media.Schema
import javax.persistence.EnumType
import javax.persistence.Enumerated
import javax.validation.constraints.NotBlank


/**
 * 属性
 */
@Schema(title = "PropertyTemplateVo")
data class PropertyTemplateVo(

    @Schema(title = "前端排序号", type = "Int")
    var sort: Int? = null,

    @Schema(title = "名称", type = "String")
    @field:NotBlank(message = "模板名称不能为空")
    var name: String?=null,

    @Schema(title = "模板定义", type = "String")
    @field:NotBlank(message = "模板定义不能为空")
    var templateDetail: String?=null,

    @Schema(title = "属性模板ID", type = "String")
    var id: Long? = null,

    @Schema(title = "状态", type = "String")
    @Enumerated(EnumType.STRING)
    var status: EnableStatusEnum?=null,

    @Schema(title = "数据服务对应数据类型", type = "String")
    @Enumerated(EnumType.STRING)
    var dataType: DataTypeVoEnum? = null,

    @Schema(title = "属性模板所有者ID", type = "String")
    var belongerId: Long? = null

): I18nPayload(){
    /**根据vo初始化model*/
    fun initPropertyTemplateFromVo(propertyTemplate: PropertyTemplate) {
        copyPropertiesIgnoreNull(this, propertyTemplate)
    }
}