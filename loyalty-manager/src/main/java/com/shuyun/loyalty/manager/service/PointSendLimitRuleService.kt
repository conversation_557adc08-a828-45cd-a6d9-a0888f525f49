package com.shuyun.loyalty.manager.service

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.manager.audit.AuditLogUtil
import com.shuyun.loyalty.manager.audit.enums.BehaviorEnum
import com.shuyun.loyalty.manager.audit.enums.ModuleEnum
import com.shuyun.loyalty.manager.vo.PointSendLimitRuleVo
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.PointSendLimitType
import com.shuyun.loyalty.service.model.PointSendLimitRule
import com.shuyun.loyalty.service.service.PointSendLimitRuleBaseService
import com.shuyun.loyalty.service.util.ConstantValue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class PointSendLimitRuleService {

    @Autowired
    private lateinit var pointSendLimitRuleBaseService: PointSendLimitRuleBaseService

    /**
     * 1. 查询上限规则是否存在
     * 2. 验证规则组是否存在
     */
    @Transactional(rollbackFor = [Exception::class])
    fun saveOrUpdateRule(ruleVo: PointSendLimitRuleVo): PointSendLimitRule {
        val pointSendLimitRule: PointSendLimitRule?
        if (ruleVo.id != null) {
            pointSendLimitRule = pointSendLimitRuleBaseService.findByIdAndDisabled(ruleVo.id!!, ConstantValue.LOGIC_DELETE_NOT).orElseThrow { throw LoyaltyException(LoyaltyExceptionCode.POINT_SEND_LIMIT_NOT_FOUND) }
            val oldStatus = pointSendLimitRule.status
            ruleVo.initFromVo(pointSendLimitRule)
            pointSendLimitRuleBaseService.updatePointLimitRule(pointSendLimitRule)
            val newStatus = pointSendLimitRule.status
            // 默认编辑条件
            var behavior = BehaviorEnum.UPDATE_SEND_LIMIT_CONDITION
            var annObject = "条件"
            if (oldStatus != newStatus) {
                annObject = if (newStatus == EnableStatusEnum.ENABLED) "启用" else "关闭"
                behavior = BehaviorEnum.UPDATE_SEND_LIMIT_RULE
                AuditLogUtil.sendMessage(
                    ModuleEnum.POINT_SEND_LIMIT_RULE,
                    behavior,
                    "",
                    pointSendLimitRule.id.toString(),
                    annObject
                )
            } else {
                AuditLogUtil.sendMessage(
                    ModuleEnum.POINT_SEND_LIMIT_RULE,
                    behavior,
                    "",
                    pointSendLimitRule.id.toString(),
                    annObject
                )
            }
        } else {
            pointSendLimitRule = PointSendLimitRule()
            ruleVo.initFromVo(pointSendLimitRule)
            pointSendLimitRuleBaseService.insertPointLimitRule(pointSendLimitRule)
            AuditLogUtil.sendMessage(
                ModuleEnum.POINT_SEND_LIMIT_RULE,
                BehaviorEnum.CREATE_SEND_LIMIT_RULE,
                pointSendLimitRule.id.toString(),
                "",
                "规则"
            )
        }
        return pointSendLimitRule!!
    }

    @Transactional(rollbackFor = [Exception::class])
    fun deleteLimitRule(limitRuleId: Long) {
        val pointSendLimitRule =
            pointSendLimitRuleBaseService.findByIdAndDisabled(limitRuleId, ConstantValue.LOGIC_DELETE_NOT)
                .orElseThrow { throw LoyaltyException(LoyaltyExceptionCode.POINT_SEND_LIMIT_NOT_FOUND) }
        pointSendLimitRule.disabled = true
        pointSendLimitRuleBaseService.deletePointLimitRule(pointSendLimitRule)
        AuditLogUtil.sendMessage(
            ModuleEnum.POINT_SEND_LIMIT_RULE,
            BehaviorEnum.DELETE_SEND_LIMIT_RULE,
            oldValue = limitRuleId.toString(),
            annObject = "规则"
        )
    }

    fun findLimitRuleList(pointAccountTypeId: Long, type: PointSendLimitType, groupId: Long? = null): List<PointSendLimitRule> {
        val list = pointSendLimitRuleBaseService.findLimitRuleList(pointAccountTypeId, type, groupId)
        when (type) {
            PointSendLimitType.RULE_GROUP -> {
                AuditLogUtil.sendMessage(
                    ModuleEnum.POINT_SEND_LIMIT_RULE,
                    BehaviorEnum.FIND_SEND_LIMIT_RULE,
                    annObject = "规则组维度"
                )
            }

            PointSendLimitType.EVENT -> {
                AuditLogUtil.sendMessage(
                    ModuleEnum.POINT_SEND_LIMIT_RULE,
                    BehaviorEnum.FIND_SEND_LIMIT_RULE,
                    annObject = "事件维度"
                )
            }

            else -> {}
        }
        return list
    }
}