package com.shuyun.loyalty.manager.audit

import com.shuyun.lite.util.Common
import com.shuyun.loyalty.manager.kafka.sink.KafkaSource
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.messaging.support.MessageBuilder
import org.springframework.stereotype.Service
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.ExecutorService
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit

/**
 * @Auther: quan.yuan
 */
@Service
class SendService {

    private val logger = LoggerFactory.getLogger(SendService::class.java)

    private val executor: ExecutorService = ThreadPoolExecutor(
        20,
        100,
        60L,
        TimeUnit.SECONDS,
        ArrayBlockingQueue(Common.getSysOrEnv("system.operate.log.queue.size", "100000").toInt()),
        ThreadPoolExecutor.DiscardPolicy()
    )

    @Autowired
    private lateinit var kafkaSource: KafkaSource


    fun sendMessage(messages: Map<String, Any?>) {
        executor.execute {
            try {
                kafkaSource.auditLogOutput().send(MessageBuilder.withPayload(messages).build())
            } catch (e: Exception) {
                logger.error("操作记录数据报错提交kafka异常", e)
            }
        }
    }
}