package com.shuyun.loyalty.manager.service

import com.pip.mybatisplus.pools.DmPoolFactory
import com.pip.shuyun.pool.transaction.DmTransaction
import com.shuyun.loyalty.entity.api.request.ExtendMemberPointRequest
import com.shuyun.loyalty.entity.api.request.MemberPointBudgetBatchRequest
import com.shuyun.loyalty.entity.api.response.MemberPointBudgetBatchResponse
import com.shuyun.loyalty.sdk.Segments
import com.shuyun.loyalty.service.calculate.BudgetMemberPointCalculate
import com.shuyun.loyalty.service.datamodel.ExtendPointRecord
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.event.convertMatchTimeToDate
import com.shuyun.loyalty.service.event.findEventValueByPath
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.infrastructure.eventStreamMeta.LocalEventMetasRepository
import com.shuyun.loyalty.service.message.point.PointAccountEventMessage
import com.shuyun.loyalty.service.meta.EventOccasionEnum
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.model.ExecuteOrder
import com.shuyun.loyalty.service.model.PointAccountExecuteOrder
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.loyalty.service.service.*
import com.shuyun.loyalty.service.util.ConstantValue.LONG_TERM_OVERDUE_DATE
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.ZonedDateTime
import java.util.*
import java.util.concurrent.ConcurrentHashMap

@Service
class OpenMemberPointService {

    @Autowired
    private lateinit var memberPointService: MemberPointService

    @Autowired
    private lateinit var memberPointValidStatementService: MemberPointValidStatementService

    @Autowired
    private lateinit var memberPointGainStatementService: MemberPointGainStatementService


    @Autowired
    private lateinit var budgetMemberPointCalculate: BudgetMemberPointCalculate

    @Autowired
    private lateinit var metasRepository: LocalEventMetasRepository

    @Autowired
    private lateinit var pointRuleGroupService: PointRuleGroupBaseService

    @Autowired
    private lateinit var pointSendRuleService: PointSendRuleBaseService

    private val logger = LogManager.getLogger(OpenMemberPointService::class.java)

    @DmTransaction
    fun extend(request: ExtendMemberPointRequest) {
        if (request.pointStartTime.isAfter(request.pointEndTime))
            throw LoyaltyException(LoyaltyExceptionCode.POINT_EXTEND_NULL)
        if (request.extendTime == null && request.extendDay == null)
            throw LoyaltyException(LoyaltyExceptionCode.POINT_EXTEND_NULL)

        val idempotentService = ApplicationContextHolder.getBean(IdempotentService::class.java)
        idempotentService.idempotent(request.businessId)
        val memberPoint = memberPointService.getByMemberId(request.pointPlanId!!, request.memberId)
        if (memberPoint == null) {
            logger.warn("会员积分账户不存在 memberId:{}, pointPlanId:{}", request.memberId, request.pointPlanId)
            throw LoyaltyException(LoyaltyExceptionCode.POINT_NOT_FOUND)
        }
        extendPoint(request)
        DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
            val points = Segments.rebuildSegment(
                sdk,
                pointAccountId = memberPoint.pointPlanId!!,
                memberId = memberPoint.memberId,
                reference = 1
            )
            memberPoint.point = points
        }
    }


    @DmTransaction
    fun migrateMemberPointSegment(pointAccountId: Long, memberId: String) {
        DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
            Segments.rebuildSegment(
                sdk,
                pointAccountId = pointAccountId,
                memberId = memberId,
                reference = 0
            )
        }
    }



    /**
     * 延长用户有效积分和待生效积分
     */
    private fun extendPoint(request: ExtendMemberPointRequest) {
        val pointPlanId = request.pointPlanId!!
        memberPointValidStatementService.findExtendPointList(request).forEach {
            if (it.overdueDate.toLocalDate().isEqual(LONG_TERM_OVERDUE_DATE.toLocalDate())) return@forEach

            val newOverdueDate = request.extendTime ?: it.overdueDate.plusDays(request.extendDay!!)

            if (it.overdueDate.toLocalDate().isAfter(newOverdueDate.toLocalDate())) {
                throw LoyaltyException(LoyaltyExceptionCode.POINT_EXTEND_TIME_ERROR)
            }

            ExtendPointRecord.fromValid(request, it, newOverdueDate).saveOrUpdate(pointPlanId)

            it.overdueDate = newOverdueDate
            it.modified = ZonedDateTime.now()
            it.saveOrUpdate(pointPlanId)

            memberPointGainStatementService.findByGainStatementId(it.gainStatementId, it.pointPlanId!!)?.apply {
                this.modified = ZonedDateTime.now()
                this.overdueDate = newOverdueDate
            }?.saveOrUpdate(pointPlanId)
        }
    }

    /**
     * 预算接口
     */
    fun budget(budget: MemberPointBudgetBatchRequest): List<MemberPointBudgetBatchResponse> {
        val response = ArrayList<MemberPointBudgetBatchResponse>()
        budget.event?.forEach { e ->
            val event = JsonUtils.parse(e, Event::class.java)
            val plans = LoyaltyPrograms.findPlansByFqn(event.getFqn(), event.getOccurrenceZonedDateTime())
            if (plans.isEmpty()) {
                logger.warn("积分预算-处理结束 通过预算事件FQN({})和时间发生时间({})没有匹配到任何忠诚度方案", event.getFqn(), event.getOccurrenceZonedDateTime())
                return@forEach
            }

            val memberPointBudgetResponse = MemberPointBudgetBatchResponse()
            memberPointBudgetResponse.key = event.getKey()

            for (plan in plans) {
                val executors = ArrayList<ExecuteOrder>()
                plan.subjectList?.forEach { subject ->
                    subject.eventTypeList?.filter { it.eventStream == event.getFqn() }?.forEach { eventType ->
                        if (eventType.occasion?.contains(EventOccasionEnum.CALC_POINT) == true) {
                            subject.pointAccountTypeList?.forEach pat@ { accountType ->

                                val matchingTimePath = eventType.matchingTimePathForPointsRule ?: Event.OCCURRENCE_TS
                                val matchingTimeValue = event.findEventValueByPath(matchingTimePath)
                                if (matchingTimeValue.isNullOrEmpty()) {
                                    logger.warn("积分试算-不合法的积分消息 不存在的属性值({}) event: {} ", matchingTimePath, event)
                                    return@pat
                                }
                                val startDate = event.convertMatchTimeToDate(matchingTimePath, matchingTimeValue)
                                if (startDate == null) {
                                    logger.warn("积分试算-不合法的消息 不存在的属性值格式不正确(path: {} value: {})", matchingTimePath, matchingTimeValue)
                                    return@pat
                                }

                                val pointRuleGroups = pointRuleGroupService.findPointRuleGroup(
                                    plan.id!!,
                                    accountType.id!!,
                                    eventType.id!!,
                                    eventType.operation!!,
                                    startDate
                                )
                                val groups = ArrayList<PointRuleGroup>()
                                pointRuleGroups.forEach group@ { group ->
                                    when (group.scoreType) {
                                        EventOperationEnum.SEND -> {
                                            group.sendRuleList = pointSendRuleService.findByRuleGroupIdAndDisabled(group.id!!)
                                            if (group.sendRuleList.isNullOrEmpty()) return@group
                                        }
                                        else -> return@group
                                    }
                                    groups.add(group)
                                }

                                if (groups.isEmpty()) {
                                    return@pat
                                }

                                eventType.eventStreamMetas = metasRepository.findById(event.getFqn())
                                val executeOrder = PointAccountExecuteOrder(
                                    plan,
                                    subject,
                                    accountType,
                                    eventType,
                                    startDate,
                                    accountType.executeOrder!!
                                )
                                executors.add(executeOrder)
                                logger.info(
                                    "积分预算方案匹配：方案名称: {} 主体名称: {} 时机名称: {} 时机FQN: {} 顺序号: {}",
                                    plan.name,
                                    subject.name,
                                    eventType.name,
                                    eventType.eventStream,
                                    accountType.executeOrder,
                                )
                                eventType.pointRuleGroups[accountType.id!!] = groups
                            }
                        }
                    }
                }

                // 计算顺序排序
                executors.sortBy { x -> -x.order }

                val budgetPlan = MemberPointBudgetBatchResponse.Plan().apply {
                    this.id = plan.id
                    this.name = plan.name
                }
                val subjectMap = HashMap<Long, MemberPointBudgetBatchResponse.Subject>()
                val now = ZonedDateTime.now()
                executors.forEach eo@ { executor ->
                    val eo = executor as PointAccountExecuteOrder
                    val clone = event.clone()
                    val message = buildPointEventMessage(eo, clone, eo.matchingTime)
                    message.setEvent(clone)
                    val list = budgetMemberPointCalculate.memberPointSend(message, now)
                    if(list.isEmpty()) {
                        return@eo
                    }
                    this.buildBudgetResponse(list, memberPointBudgetResponse, eo, subjectMap, budgetPlan)
                }
                memberPointBudgetResponse.planList.add(budgetPlan)
            }

            response.add(memberPointBudgetResponse)

        }

        response.forEach {
            it.planList.removeIf { plan -> plan.subjectList.isEmpty() }
        }
        return response
    }

    /**
     * 创建预算响应值
     */
    private fun buildBudgetResponse(list: ArrayList<Map<String, String?>>, memberPointBudgetResponse: MemberPointBudgetBatchResponse, eo: PointAccountExecuteOrder,
                                    subjectMap: HashMap<Long, MemberPointBudgetBatchResponse.Subject>, budgetPlan: MemberPointBudgetBatchResponse.Plan) {
        val ruleGroupMap = ConcurrentHashMap<String, MemberPointBudgetBatchResponse.PointRuleGroup>()
        var point = BigDecimal.ZERO
        val pointAccountType = eo.pointAccountType
        // 拼装规则组与规则
        list.forEach { map ->
            val ruleGroup = ruleGroupMap.getOrPut(map["ruleGroupId"].toString()) {
                MemberPointBudgetBatchResponse.PointRuleGroup().apply {
                    this.id = map["ruleGroupId"].toString()
                    this.name = map["ruleGroupName"].toString()
                }
            }
            ruleGroup.ruleList.add(MemberPointBudgetBatchResponse.PointRule().apply {
                this.id = map["ruleId"].toString()
                this.name = map["ruleName"].toString()
                this.point = pointAccountType.calculateRoundingResult(map["pointValue"].toString().toBigDecimal())
                if(pointAccountType.singleTopLimit != null && pointAccountType.singleTopLimit!!.toBigDecimal() < this.point) {
                    this.point = pointAccountType.singleTopLimit!!.toBigDecimal()
                }
                memberPointBudgetResponse.point = memberPointBudgetResponse.point.plus(this.point)
                point += this.point
            })
        }
        // 设置积分账号
        val budgetPointAccount = MemberPointBudgetBatchResponse.PointAccount().apply {
            this.id = eo.pointAccountType.id
            this.name = eo.pointAccountType.name
            this.point = point
        }
        budgetPointAccount.ruleGroupList = ArrayList(ruleGroupMap.values)

        val budgetSubject = subjectMap.getOrPut(eo.subject.id!!) {
            // 设置主体
            val budgetSubject = MemberPointBudgetBatchResponse.Subject().apply {
                this.id = eo.subject.id!!
                this.name = eo.subject.name!!
            }
            budgetPlan.subjectList.add(budgetSubject)
            budgetSubject
        }
        budgetSubject.pointAccountList.add(budgetPointAccount)
    }

    private fun buildPointEventMessage(exec: PointAccountExecuteOrder, event: Event, matchingTime: Date) = PointAccountEventMessage(exec, event, matchingTime)


}