package com.shuyun.loyalty.manager.migration

import org.springframework.boot.actuate.health.AbstractHealthIndicator
import org.springframework.boot.actuate.health.Health
import org.springframework.boot.info.BuildProperties
import org.springframework.stereotype.Component

@Component
class MigrationHealthIndicator(val buildProperties: BuildProperties?) : AbstractHealthIndicator() {


    override fun doHealthCheck(builder: Health.Builder?) {
        val bd = if (MigrationRunListener.MIGRATION_IS_OK) {
            builder!!.up()
        } else {
            builder!!.down()
        }
        if (buildProperties != null) {
            bd.withDetail("version", buildProperties?.version)
        }
    }
}