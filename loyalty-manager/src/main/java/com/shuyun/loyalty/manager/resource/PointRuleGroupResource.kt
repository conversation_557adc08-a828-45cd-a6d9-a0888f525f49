package com.shuyun.loyalty.manager.resource

import com.shuyun.epassport.sdk.register.RequiresPermissions
import com.shuyun.loyalty.manager.service.PointRuleGroupService
import com.shuyun.loyalty.manager.vo.PointRuleGroupVo
import com.shuyun.loyalty.manager.vo.UpdatePointRuleGroupProcessVo
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.meta.ProcessActionTypeEnum
import com.shuyun.loyalty.service.meta.RuleGroupStatusEnum
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.util.ObjectUtils
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import javax.validation.Valid

@RestController
@RequestMapping("/pointRuleGroup")
@RequiresPermissions(allowAuthenticated = true)
class PointRuleGroupResource {

    private val logger = LogManager.getLogger(PointRuleGroupResource::class.java)

    @Autowired
    private lateinit var pointRuleGroupService: PointRuleGroupService

    @Operation(summary = "新建/编辑积分规则组", tags = ["积分规则组 API"])
    @PostMapping("/addRuleGroup")
    @RequiresPermissions(value = ["loyalty4.manage.point.rule.edit"])
    fun saveOrUpdateRuleGroup(@Valid @RequestBody pointRuleGroupVo: PointRuleGroupVo):Map<String,Long>{
        logger.debug("新建/编辑积分规则组 参数:{}", { JsonUtils.toJson(pointRuleGroupVo) })
        var oldName: String? = null
        if (!ObjectUtils.isEmpty(pointRuleGroupVo.id)) {
            oldName = pointRuleGroupService.findByIdAndDisabled(pointRuleGroupVo.id!!)
        }
        val pointRuleGroup = pointRuleGroupService.saveOrUpdateRuleGroup(pointRuleGroupVo, oldName)
        return mapOf("id" to pointRuleGroup.id!!)
    }

    @Operation(summary = "获取积分规则组", tags = ["积分规则组 API"])
    @GetMapping("/{scoreType}/{planId}/{pointAccountTypeId}")
    @RequiresPermissions(value = ["loyalty4.manage.point.rule.view","loyalty4.manage.point.rule.edit","loyalty4.manage.point.rule.file"])
    fun findPointRuleGroupList(@Parameter(description = "计划ID") @PathVariable("planId")planId:Long,
                               @Parameter(description = "账户ID") @PathVariable("pointAccountTypeId")pointAccountTypeId:Long,
                               @Parameter(description = "规则组ID") @RequestParam("groupId", required = false) groupId: Long?,
                               @Parameter(description = "规则组名称") @RequestParam("groupName", required = false) groupName: String?,
                               @Parameter(description = "规则状态") @RequestParam("groupStatus", required = false) groupStatus: List<RuleGroupStatusEnum>?,
                               @Parameter(description = "积分类型") @PathVariable("scoreType") scoreType: EventOperationEnum,
                               @Parameter(description = "审批动作类型") @RequestParam("processActionTypes", required = false) processActionTypes: List<ProcessActionTypeEnum>?): List<PointRuleGroup> {
        logger.debug("获取积分规则组 参数: planId={}, pointAccountTypeId={}, scoreType={}, groupId={}, groupName={}, groupStatus={} ,processActionTypes={}",
            planId,pointAccountTypeId,scoreType,groupId,groupName,groupStatus, processActionTypes)

        return pointRuleGroupService.findPointRuleGroupList(planId,pointAccountTypeId,scoreType,groupId,groupName,groupStatus,processActionTypes)
    }


    @Operation(summary = "获取单个积分规则组", tags = ["积分规则组 API"])
    @GetMapping("/{groupId}")
    @RequiresPermissions(value = ["loyalty4.manage.point.rule.view","loyalty4.manage.point.rule.edit","loyalty4.manage.point.rule.file"])
    fun findPointRuleGroup(@Parameter(description = "规则组ID") @PathVariable("groupId") groupId: Long): PointRuleGroup {
        return pointRuleGroupService.findGroupById(groupId)
    }


    @Operation(summary = "积分规则组归档", tags = ["积分规则组 API"])
    @PostMapping("/file/{ruleGroupId}")
    @RequiresPermissions(value = ["loyalty4.manage.point.rule.file"])
    fun upPointRuleGroupStatus(@Parameter(description = "规则组ID") @PathVariable("ruleGroupId")ruleGroupId:Long){
        logger.debug("积分规则组归档 参数: ruleGroupId: {}",ruleGroupId)
        pointRuleGroupService.upPointRuleGroupStatus(ruleGroupId)
    }

    @Operation(summary = "删除积分规则组", tags = ["积分规则组 API"])
    @DeleteMapping("/{ruleGroupId}")
    @RequiresPermissions(value = ["loyalty4.manage.point.rule.edit"])
    fun deletePointRuleGroup(@Parameter(description = "规则组ID") @PathVariable("ruleGroupId") ruleGroupId: Long){
        logger.debug("删除积分规则组 参数: ruleGroupId: {}", ruleGroupId)
        pointRuleGroupService.deletePointRuleGroup(ruleGroupId)
    }

    @Operation(summary = "更新审批", tags = ["积分规则组 API"])
    @PostMapping("/updateRuleGroupProcess")
    @RequiresPermissions(value = ["loyalty4.manage.point.rule.edit"])
    fun updateRuleGroupProcess(@Valid @RequestBody request: UpdatePointRuleGroupProcessVo){
        logger.debug("更新审批 参数:{}", {JsonUtils.toJson(request)})
        pointRuleGroupService.updateRuleGroupProcess(request)
    }

}