package com.shuyun.loyalty.manager.service

import com.shuyun.eawf.enums.ActionEnum
import com.shuyun.eawf.sdk.ActinstSdk
import com.shuyun.eawf.sdk.ProcessInstanceQuerySdk
import com.shuyun.eawf.sdk.ProcessSdk
import com.shuyun.eawf.vo.Actinst
import com.shuyun.loyalty.entity.enums.ProcessStatusEnum
import com.shuyun.loyalty.manager.resource.PointRuleGroupResource
import com.shuyun.loyalty.manager.service.audit.AuditPointRuleGroupService
import com.shuyun.loyalty.manager.vo.PointRuleGroupVo
import com.shuyun.loyalty.manager.vo.UpdatePointRuleGroupProcessVo
import com.shuyun.loyalty.service.calculate.params.CalcParams
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.meta.PointSendLimitType
import com.shuyun.loyalty.service.meta.ProcessActionTypeEnum
import com.shuyun.loyalty.service.meta.RuleGroupStatusEnum
import com.shuyun.loyalty.service.model.PointDeductRule
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.loyalty.service.model.PointSendRule
import com.shuyun.loyalty.service.repository.PropertyTemplateRepository
import com.shuyun.loyalty.service.service.*
import com.shuyun.loyalty.service.util.DateUtils
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.i18n.LocaleI18nContextHolder
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*
import kotlin.concurrent.thread

@Service
class PointRuleGroupService {

    private val logger = LogManager.getLogger(PointRuleGroupResource::class.java)

    @Autowired
    private lateinit var pointRuleGroupBaseService: PointRuleGroupBaseService

    @Autowired
    private lateinit var pointSendRuleService: PointSendRuleService

    @Autowired
    private lateinit var pointDeductRuleService: PointDeductRuleService

    @Autowired
    private lateinit var eventTypeBaseService: EventTypeBaseService

    @Autowired
    private lateinit var customizedPropertyBaseService: CustomizedPropertyBaseService

    @Autowired
    private lateinit var propertyTemplateRepository: PropertyTemplateRepository

    @Autowired
    private lateinit var pointSendLimitRuleBaseService: PointSendLimitRuleBaseService

    @Autowired
    private lateinit var auditPointRuleGroupService: AuditPointRuleGroupService

    @Autowired
    private lateinit var i18nModelService: I18nModelService

    @Transactional(rollbackFor = [Exception::class])
    fun saveOrUpdateRuleGroup(pointRuleGroupVo: PointRuleGroupVo, oldName: String?):PointRuleGroup {
        //判断规则组是否存在
        this.isGroupName(pointRuleGroupVo)
        var pointRuleGroup = PointRuleGroup()
        if (pointRuleGroupVo.id != null) pointRuleGroup = this.getGroupOptional(pointRuleGroupVo.id!!)
        //获取规则组判断状态后的实体
        pointRuleGroup = pointRuleGroupVo.getPointRuleGroupInsertOrUpdate(pointRuleGroup)
        logger.trace("获取积分规则组判断状态后的实体 {}", { JsonUtils.toJson(pointRuleGroup) })
        when {
            //规则组ID为空，创建规则组
            pointRuleGroup.id == null -> {
                ApplicationContextHolder.getBean(PointRuleGroupService::class.java).insertPointRuleGroup(pointRuleGroup)
            }
            //规则组状态为待生效，任意修改
            pointRuleGroup.groupStatus == RuleGroupStatusEnum.WAIT_EFFECT ||
                    pointRuleGroup.groupStatus == RuleGroupStatusEnum.DESIGN ||
                    pointRuleGroup.groupStatus == RuleGroupStatusEnum.REJECT -> {
                if(pointRuleGroup.groupStatus == RuleGroupStatusEnum.REJECT) {
                    pointRuleGroup.processStatus = ProcessStatusEnum.DESIGN
                    pointRuleGroup.processInstanceId = null
                }
                updatePointRuleGroup(pointRuleGroup, oldName!!)
            }
            //规则组开始时间<当前时间<规则组结束时间(运行中)
            pointRuleGroup.groupStatus == RuleGroupStatusEnum.RUNNING -> updatePointRuleGroupEffectiveTime(pointRuleGroup, oldName!!)
        }

        //保存国际化信息
        i18nModelService.saveOrUpdate(PointRuleGroup.I18N_GROUP__GROUP_NAME, pointRuleGroup.id!!, pointRuleGroupVo._i18nPayload)

        return pointRuleGroup
    }

    fun findByIdAndDisabled(id: Long) = pointRuleGroupBaseService.findByIdAndDisabled(id).get().groupName

    fun findPointRuleGroupList(planId: Long, pointAccountTypeId: Long, scoreType: EventOperationEnum, ruleGroupId: Long?, groupName: String?, groupStatus: List<RuleGroupStatusEnum>?,processActionTypes: List<ProcessActionTypeEnum>?): List<PointRuleGroup> {
        var ruleGroupList = auditPointRuleGroupService.findPointRuleGroup(CalcParams(pointAccountTypeId, scoreType, groupName, ruleGroupId, groupStatus))
        logger.debug("查询规则组数量:{} planId: {}", { ruleGroupList.size }, { planId })
        processActionTypes?.let {
            ruleGroupList = ruleGroupList.filter { it.processId != null && it.processInstanceId != null }
            logger.info("根据审批动作类型过滤,过滤掉无审批实例和审批ID的数据后剩余记录数量:{} ", { ruleGroupList.size })
        }
        //获取当前用户、审批实例ID对应的审批流操作权限 , 后期不改方案时想提升性能可以cache, 在审批流通知接口要evict
        val batchFindProcessMap = HashMap<Long/*流程ID*/,ArrayList<Long>/*实例ID*/>()
        // 查询我已审批、我的送审入参
        val batchFindProcessHisMap = HashMap<Long/*流程ID*/,ArrayList<Long>/*实例ID*/>()
        // 查询存在审批流ID ,不存在审批实例ID且状态为设计中的,(需要查询提交权限)
        val batchFindProcessSubmitList = HashSet<Long/*流程ID*/>()
        var openProcessFlag = false
        ruleGroupList.forEach {
            it.groupStatus = it.getRuleGroupStatus()
            if(it.processId?.isNotBlank() == true) { openProcessFlag = true }
            if(it.processId == null || (it.processInstanceId == null && it.groupStatus != RuleGroupStatusEnum.DESIGN)) {
                return@forEach
            }

            if(it.processInstanceId == null) {
                batchFindProcessSubmitList.add(it.processId!!.toLong())
                return@forEach
            }

            if (it.groupStatus == RuleGroupStatusEnum.REJECT)
                batchFindProcessSubmitList.add(it.processId!!.toLong())

            val processInstanceHisList = batchFindProcessHisMap.getOrPut(it.processId!!.toLong()) { ArrayList() }
            processInstanceHisList.add(it.processInstanceId!!.toLong())

            if(it.groupStatus == RuleGroupStatusEnum.DESIGN || it.groupStatus == RuleGroupStatusEnum.PROCESS || it.groupStatus == RuleGroupStatusEnum.REJECT) {
                val processInstanceList = batchFindProcessMap.getOrPut(it.processId!!.toLong()) { ArrayList() }
                processInstanceList.add(it.processInstanceId!!.toLong())
            }
        }
        val batchFindProcessMapResult = HashMap<Long/*流程ID*/,Map<Long/*实例ID*/, Set<ActionEnum>>>()
        logger.debug("请求我的操作权限入参:{}  ", { JsonUtils.toJson(batchFindProcessMap) })
        batchFindProcessMap.forEach {
            val batchFindMyApprovalResponse = ProcessInstanceQuerySdk.batchFindMyApproval(it.key, it.value)
            batchFindProcessMapResult[it.key] = batchFindMyApprovalResponse
        }
        logger.debug("请求我的操作权限出参:{}  ", { JsonUtils.toJson(batchFindProcessMapResult) })

        var batchFindProcessSubmitListResult = listOf<Long>()
        if(openProcessFlag) {
            // 查询审批流提交动作
            logger.debug("请求当前用户是否存在提交动作参数:{}  ", { JsonUtils.toJson(batchFindProcessSubmitList) })
            batchFindProcessSubmitListResult = ProcessSdk.list().map { it.id }
            logger.debug("请求当前用户是否存在提交动作结果:{}  ", { JsonUtils.toJson(batchFindProcessSubmitListResult) })
        }

        val withI18nPayload = LocaleI18nContextHolder.getWithAllI18n() ?: false

        //单规则组查询规则列表
        val chunk0 = ruleGroupList.chunked(30)
        for (list in chunk0) {
            val ts = ArrayList<Thread>()
            for (it in list) {
                val t = thread {
                    val customizedPropertyList = customizedPropertyBaseService.findSubjectCustomizedPropertyByAllDate(LocaleI18nContextHolder.getAcceptLanguage(), DateUtils.date2DateTime(it.createTime!!))
                    val propertyTemplateNameList = propertyTemplateRepository.findByVersionNameList(LocaleI18nContextHolder.getAcceptLanguage(), DateUtils.date2DateTime(it.createTime!!))
                    when (it.scoreType) {
                        EventOperationEnum.SEND -> it.sendRuleList = pointSendRuleService.findPointSendRuleList(customizedPropertyList, propertyTemplateNameList, it)
                        else -> it.deductRuleList = pointDeductRuleService.findPointDeductRuleList(customizedPropertyList, propertyTemplateNameList, it)
                    }
                    // 增加审批流操作权限
                    if(it.processId != null && it.processInstanceId != null) {
                        it.processAction = batchFindProcessMapResult[it.processId!!.toLong()]?.get(it.processInstanceId!!.toLong())
                    }
                    if(it.processId != null && (
                                (it.processInstanceId == null && it.groupStatus == RuleGroupStatusEnum.DESIGN ) ||
                                        (it.processInstanceId != null && it.groupStatus == RuleGroupStatusEnum.REJECT )  )
                        && batchFindProcessSubmitListResult.contains(it.processId!!.toLong())) {
                        it.processAction = it.processAction ?: HashSet()
                        val set = it.processAction!!.toMutableSet()
                        set.add(ActionEnum.SUBMIT)
                        it.processAction = set
                    }
                }
                ts.add(t)
            }
            ts.forEach { it.join() }
        }
        ruleGroupList = filterProcessActionStatus(processActionTypes, batchFindProcessHisMap, ruleGroupList)
        val chunk1 = ruleGroupList.chunked(30)
        for (list in chunk1) {
            val ts = ArrayList<Thread>()
            list.forEach {
                val t = thread {
                    try {
                        LocaleI18nContextHolder.setWithAllI18n(withI18nPayload)
                        it.eventTypeName = eventTypeBaseService.getEffectiveEventTypeName(it.eventTypeId!!,DateUtils.date2DateTime(it.createTime!!))
                        it._i18nPayload = i18nModelService.findI18nPayload(PointRuleGroup.I18N_GROUP__GROUP_NAME, it.id)
                        it.deductRuleList?.forEach { rule ->
                            rule._i18nPayload = i18nModelService.findI18nPayload(PointDeductRule.I18N_GROUP__RULE_NAME, rule.id)
                        }
                        it.sendRuleList?.forEach { rule ->
                            rule._i18nPayload = i18nModelService.findI18nPayload(PointSendRule.I18N_GROUP__RULE_NAME, rule.id)
                        }
                    } catch (e: Throwable) {
                        logger.warn("处理国际化错误${it.eventTypeId}, ${it.createTime}", e)
                    }
                }
                ts.add(t)
            }
            ts.forEach { it.join() }
        }
        return ruleGroupList
    }

    @Transactional(rollbackFor = [Exception::class])
    fun upPointRuleGroupStatus(ruleGroupId: Long) {
        val pointRuleGroup = this.getGroupOptional(ruleGroupId)
        //结束时间<当前时间(结束)
        if (pointRuleGroup.isRuleGroupEnd()) {
            pointRuleGroup.fileStatus = true
            auditPointRuleGroupService.updatePointRuleGroup(pointRuleGroup)
            when (pointRuleGroup.scoreType) {
                EventOperationEnum.SEND -> pointSendRuleService.updatePointSendRuleStatus(ruleGroupId)
                else -> pointDeductRuleService.updatePointDeductRuleStatus(ruleGroupId)
            }
        } else {
            logger.error("该积分规则组不允许归档 ruleGroupId: $ruleGroupId")
            throw LoyaltyException(LoyaltyExceptionCode.POINT_RULE_GROUP_NOT_OPERATION_FILE, "该积分规则组不允许归档")
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun deletePointRuleGroup(ruleGroupId: Long) {
        val ruleGroupOptional = pointRuleGroupBaseService.findByIdAndDisabled(ruleGroupId)
        //查询规则组是否存在
        val pointRuleGroup = if (ruleGroupOptional.isPresent) ruleGroupOptional.get() else {
            logger.error("该积分规则组不存在 ruleGroupId: {}", {JsonUtils.toJson(ruleGroupId)})
            throw LoyaltyException(LoyaltyExceptionCode.POINT_GROUP_NOT_FOUND, "该积分规则组不存在")
        }
        //规则组开始时间>当前时间(待生效)，可删除
        if (pointRuleGroup.isRuleGroupDesign() || pointRuleGroup.isRuleGroupReject() || pointRuleGroup.isRuleGroupWaitEffect() || pointRuleGroup.fileStatus == true) {
            auditPointRuleGroupService.deletePointRuleGroup(pointRuleGroup)
            i18nModelService.delete(PointRuleGroup.I18N_GROUP__GROUP_NAME, pointRuleGroup.id)
            // 删除包含此规则组ID的积分发放上限
            pointSendLimitRuleBaseService.updatePointLimitRuleByNlmsIds(pointRuleGroup.id!!.toString(), PointSendLimitType.RULE_GROUP)
        } else {
            logger.error("该积分规则组不允许删除 ruleGroupId: {}", {JsonUtils.toJson(ruleGroupId)})
            throw LoyaltyException(LoyaltyExceptionCode.NOT_OPERATION_DELETE_POINT_GROUP, "该规则组不允许删除")
        }
        //删除规则
        when (pointRuleGroup.scoreType) {
            EventOperationEnum.SEND -> {
                pointSendRuleService.deletePointSendRule(ruleGroupId)
                i18nModelService.delete(PointSendRule.I18N_GROUP__RULE_NAME, ruleGroupId)
            }
            else ->  {
                pointDeductRuleService.deletePointDeductRule(ruleGroupId)
                i18nModelService.delete(PointDeductRule.I18N_GROUP__RULE_NAME, ruleGroupId)
            }
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun updateRuleGroupProcess(request: UpdatePointRuleGroupProcessVo) {
        val pointRuleGroup = this.getGroupOptional(request.pointRuleGroupId!!)
        val oldProcessStatus = pointRuleGroup.processStatus
        pointRuleGroup.processInstanceId = request.processInstanceId
        pointRuleGroup.processStatus = request.processStatus
        pointRuleGroup.updateTime = Date()
        pointRuleGroup.processId = request.processId
        if(request.processStatus == ProcessStatusEnum.PASS && ProcessStatusEnum.PASS != oldProcessStatus) {
            logger.info("更细审批流通过时间, id :{} ", { pointRuleGroup.id })
            pointRuleGroup.processPassTime = Date()
        }
        auditPointRuleGroupService.updatePointRuleGroup(pointRuleGroup)
    }

    private fun getGroupOptional(id: Long): PointRuleGroup {
        val ruleGroupOptional = pointRuleGroupBaseService.findByIdAndDisabled(id)
        return if (ruleGroupOptional.isPresent) ruleGroupOptional.get() else {
            logger.error("该积分规则组不存在 ruleGroupId: {}", id)
            throw LoyaltyException(LoyaltyExceptionCode.POINT_GROUP_NOT_FOUND, "该积分规则组不存在")
        }
    }

    fun findGroupById(id: Long): PointRuleGroup {
        val pointRuleGroup = pointRuleGroupBaseService.findById(id)
            ?: throw LoyaltyException(LoyaltyExceptionCode.POINT_GROUP_NOT_FOUND, "该积分规则组不存在")
        val withI18nPayload = LocaleI18nContextHolder.getWithAllI18n() ?: false
        if (withI18nPayload) {
            pointRuleGroup._i18nPayload = i18nModelService.findI18nPayload(PointRuleGroup.I18N_GROUP__GROUP_NAME, pointRuleGroup.id)
        }
        return pointRuleGroup
    }

    /**判断规则组名称重复*/
    private fun isGroupName(pointRuleGroupVo: PointRuleGroupVo) {
        when (pointRuleGroupVo.id) {
            null -> {
                //同计划下，同账户，规则组不重复
                if (pointRuleGroupBaseService.countByGroupName(pointRuleGroupVo.planId!!,
                        pointRuleGroupVo.pointAccountTypeId!!, pointRuleGroupVo.groupName!!.trim()) > 0) {
                    logger.warn("该积分规则组名称已存在 groupName: ${pointRuleGroupVo.groupName}")
                    throw LoyaltyException(LoyaltyExceptionCode.POINT_GROUP_REPEATED, "该积分规则组名称已存在")
                }
            }
            else -> {
                if (pointRuleGroupBaseService.countByGroupName(pointRuleGroupVo.planId!!,
                        pointRuleGroupVo.pointAccountTypeId!!, pointRuleGroupVo.id!!, pointRuleGroupVo.groupName!!.trim()) > 0) {
                    logger.warn("该积分规则组名称已存在 groupName: ${pointRuleGroupVo.groupName}")
                    throw LoyaltyException(LoyaltyExceptionCode.POINT_GROUP_REPEATED, "该积分规则组名称已存在")
                }
            }
        }
    }


    fun insertPointRuleGroup(pointRuleGroup: PointRuleGroup) {
        pointRuleGroup.fileStatus = false
        auditPointRuleGroupService.insertPointRuleGroup(pointRuleGroup)
        when (pointRuleGroup.scoreType) {
            EventOperationEnum.SEND -> pointSendRuleService.insertPointSendRule(pointRuleGroup)
            else -> pointDeductRuleService.insertPointDeductRule(pointRuleGroup)
        }
    }

    private fun updatePointRuleGroup(pointRuleGroup: PointRuleGroup, oldName: String) {
        pointRuleGroup.fileStatus = false
        auditPointRuleGroupService.updatePointRuleGroup(pointRuleGroup, oldName)
        when (pointRuleGroup.scoreType) {
            EventOperationEnum.SEND -> pointSendRuleService.updatePointSendRule(pointRuleGroup)
            else -> pointDeductRuleService.updatePointDeductRule(pointRuleGroup)
        }
    }

    private fun updatePointRuleGroupEffectiveTime(pointRuleGroup: PointRuleGroup, oldName: String) {
        auditPointRuleGroupService.updatePointRuleGroup(pointRuleGroup, oldName)
    }

    /**
     * 过滤 处理待我审批、我已审批、我的送审
     */
    private fun filterProcessActionStatus(processActionTypes: List<ProcessActionTypeEnum>?,batchFindProcessMap: HashMap<Long, ArrayList<Long>>,
                                          ruleGroupList: List<PointRuleGroup>): List<PointRuleGroup> {
        var ruleGroups = ruleGroupList
        val result = ArrayList<PointRuleGroup>()
        // 处理待我审批、我已审批、我的送审 , 待我审批: 从审批权限中判断是否审批动作
        processActionTypes?.let { pat ->
            val findByMeMap = HashMap<Long/*流程ID*/, Map<Long/*实例ID*/, List<Actinst>/*记录*/>>()
            logger.debug("请求待我审批等状态入参:{}  ", { JsonUtils.toJson(batchFindProcessMap) })
            batchFindProcessMap.forEach {
                val processActinstList = ActinstSdk.findByMe(
                    it.key,
                    it.value,
                    listOf(ActionEnum.SUBMIT, ActionEnum.REJECT, ActionEnum.RECALL, ActionEnum.AGREE, ActionEnum.TERMINATION)
                )
                val map = processActinstList.groupBy { processActinst -> processActinst.instanceId }
                findByMeMap[it.key] = map
            }
            logger.debug("请求待我审批等状态出参:{}  ", { JsonUtils.toJson(findByMeMap) })

            ruleGroups.forEach {
                // 待我审批: 从审批权限中判断是否审批动作
                if (pat.contains(ProcessActionTypeEnum.TODO)  && !it.processAction.isNullOrEmpty()) {
                    if (it.processAction!!.contains(ActionEnum.AGREE) || it.processAction!!.contains(ActionEnum.REJECT)) {
                        result.add(it)
                        return@forEach
                    }
                }

                val actinstList = findByMeMap[it.processId.toString().toLong()]?.get(it.processInstanceId.toString().toLong())
                if (actinstList.isNullOrEmpty()) {
                    return@forEach
                }
                // 我已经审批
                if (pat.contains(ProcessActionTypeEnum.DONE)) {
                    val list =
                        actinstList.filter { it.status == ActionEnum.AGREE || it.status == ActionEnum.REJECT || it.status == ActionEnum.TERMINATION }
                    if (list.isNotEmpty()) {
                        result.add(it)
                        return@forEach
                    }
                }
                // 我的送审
                if (pat.contains(ProcessActionTypeEnum.SUBMIT_ME)) {
                    val list = actinstList.filter { it.status == ActionEnum.SUBMIT }
                    if (list.isNotEmpty()) {
                        result.add(it)
                        return@forEach
                    }
                }
            }
            logger.info("根据审批动作类型过滤,过滤动作类型后剩余记录数量:{} , 动作:{}",{ result.size },{ JsonUtils.toJson(pat) })
            ruleGroups = result
        }
        return ruleGroups
    }


}