package com.shuyun.loyalty.manager.resource.open

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.entity.api.response.*
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.util.MDCUtils
import com.shuyun.pip.exception.code.ServerCallException
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.extensions.Extension
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty
import io.swagger.v3.oas.annotations.media.Schema
import org.springframework.beans.BeanUtils
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import javax.annotation.Resource

@RestController
@RequestMapping("/open/plan")
class OpenPlanResource {

    @Resource
    private lateinit var loyaltyFacadeFeignApi:LoyaltyFacadeFeignApi

    @Deprecated("use /gradeHierarchy")
    @Operation(summary = "获取计划信息", tags = ["计划 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/detail")
    fun getPlanDetail(@Parameter(description = "计划ID",required = true) @RequestParam(value = "planId") planId : Long): PlanDetailResponse {
        val plan: Plan
        try {
            plan = loyaltyFacadeFeignApi.getPlan(MDCUtils.getTraceId(), planId)
        } catch (e: ServerCallException) {
            if (e.trackErrorCode == "061002") {
                throw LoyaltyException(LoyaltyExceptionCode.PLAN_NOT_FOUND, planId.toString())
            }
            throw e
        }
        val planDetailResponse = PlanDetailResponse()
        planDetailResponse.id = plan.id
        planDetailResponse.name = plan.name
        planDetailResponse.subjects = plan.subjects.map {sub ->
            val subject = SubjectDetailResponse()
            subject.id = sub.id
            subject.name = sub.name
            subject.mergeFqn = sub.mergeFqn
            subject.fqn = sub.fqn
            subject.hierarchies = sub.gradeHierarchies.map { gh ->
                val hierarchyDetailResponse = HierarchyDetailResponse()
                hierarchyDetailResponse.id = gh.id
                hierarchyDetailResponse.name = gh.name
                hierarchyDetailResponse.grades = gh.gradeDefinitions.map { gd ->
                    val gradeDetailResponse = GradeDetailResponse()
                    BeanUtils.copyProperties(gd, gradeDetailResponse)
                    gradeDetailResponse.sort = gd.sort.toInt()
                    gradeDetailResponse
                }
                hierarchyDetailResponse
            }
            subject.accounts = sub.pointAccountTypes.map { pa ->
                val ad = AccountDetailResponse()
                ad.id = pa.id
                ad.name = pa.name
                ad.unit = pa.unit
                ad
            }
            subject.eventTypes = sub.subjectEventTypes.map { se ->
                val eventTypeResponse = EventTypeResponse()
                eventTypeResponse.name = se.name
                eventTypeResponse.eventStream = se.eventStream
                eventTypeResponse.subjectVersionId = se.versionId
                eventTypeResponse.operation = se.operation
                eventTypeResponse.operationGrade = se.operationGrade
                eventTypeResponse.status = se.status
                eventTypeResponse
            }
            subject.status = EnableStatusEnum.valueOf(sub.status!!)
            subject.hasMerge =  sub.hasMerge
            subject
        }
        return planDetailResponse
    }


    @Operation(summary = "获取计划信息(积分/等级/勋章)", tags = ["计划 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/info")
    fun getPlan(@Parameter(description = "计划ID",required = true) @RequestParam(value = "planId") planId : Long): BasicPlan {
        val plan =  loyaltyFacadeFeignApi.getPlan(MDCUtils.getTraceId(), planId)
        return BasicPlan(
            id = plan.id,
            name = plan.name,
            subjectRes = plan.subjects.map {sub->
                Subject(
                    id = sub.id,
                    name = sub.name,
                    fqn = sub.fqn,
                    gradeHierarchies = sub.gradeHierarchies.map { gh ->
                        GradeHierarchy(
                            id = gh.id,
                            name = gh.name,
                            gradeDefinitions = gh.gradeDefinitions.map { gd ->
                               GradeDefinition(id = gd.id,
                                    name = gd.name,
                                    sort = gd.sort.toInt())
                            }
                        )
                    },
                    medalHierarchies = sub.medalHierarchies.map { mh ->
                        MedalHierarchy(
                            id = mh.id,
                            name = mh.name,
                            medalDefinitions = mh.medalDefinitions.map { md ->
                                MedalDefinition(
                                    id = md.id,
                                    name = md.name,
                                    sort = md.sort.toInt())
                            }
                        )
                    },
                    pointAccountTypes = sub.pointAccountTypes.map { pat ->
                        PointAccountType(
                            id = pat.id,
                            name = pat.name,
                            unit = pat.unit,
                            rounding = pat.rounding?.name
                        )
                    }
                )
            }
        )
    }


    @Operation(summary = "获取计划和等级信息", tags = ["计划 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/gradeHierarchy")
    fun gradeHierarchy(@Parameter(description = "计划ID", required = true) @RequestParam(value = "planId") planId: Long): BasicPlan {
        val plan = loyaltyFacadeFeignApi.getPlan(MDCUtils.getTraceId(), planId)
        return BasicPlan(
            id = plan.id,
            name = plan.name,
            subjectRes = plan.subjects.map {sub->
                Subject(
                    id = sub.id,
                    name = sub.name,
                    fqn = sub.fqn,
                    gradeHierarchies = sub.gradeHierarchies.map { gh ->
                        GradeHierarchy(
                            id = gh.id,
                            name = gh.name,
                            gradeDefinitions = gh.gradeDefinitions.map { gd ->
                                GradeDefinition(
                                    id = gd.id,
                                    name = gd.name,
                                    sort = gd.sort.toInt()
                                )
                            }
                        )
                    }
                )
            }
        )
    }


    @Schema(title = "计划")
    data class BasicPlan(
        @Schema(title = "计划ID")
        val id: Long,
        @Schema(title = "计划名称")
        val name: String,
        @Schema(title = "主体")
        val subjectRes: List<Subject>? = null,
    )

    @Schema(title = "主体")
    data class Subject(
        @Schema(title = "主体ID")
        val id: Long,
        @Schema(title = "主体名称")
        val name: String,
        @Schema(title = "主体数据模型")
        val fqn: String,
        @Schema(title = "等级体系")
        val gradeHierarchies: List<GradeHierarchy>? = null,
        @Schema(title = "勋章体系")
        val medalHierarchies: List<MedalHierarchy>? = null,
        @Schema(title = "积分账户")
        val pointAccountTypes: List<PointAccountType>? = null,

    )


    @Schema(title = "等级体系")
    data class GradeHierarchy(
        @Schema(title = "等级体系ID")
        val id: Long,
        @Schema(title = "等级体系名称")
        val name: String,
        @Schema(title = "等级定义")
        val gradeDefinitions: List<GradeDefinition>?,
    )

    @Schema(title = "等级定义")
    data class GradeDefinition(
        @Schema(title = "等级定义ID")
        val id: Long,
        @Schema(title = "等级定义名称")
        val name: String,
        @Schema(title = "排序号")
        val sort: Int,
    )

    data class MedalDefinition(
        @Schema(title = "等级定义ID")
        val id: Long,
        @Schema(title = "等级定义名称")
        val name: String,
        @Schema(title = "排序号")
        val sort: Int,
    )


    @Schema(title = "积分账户")
    data class PointAccountType(
        @Schema(title = "积分账户ID")
        val id: Long,
        @Schema(title = "积分账户名称")
        val name: String,
        @Schema(title = "单位")
        val unit: String?,
        @Schema(title = "小数舍入策略")
        val rounding: String?,
    )

    data class MedalHierarchy(
        @Schema(title = "勋章体系ID")
        val id: Long,
        @Schema(title = "勋章体系名称")
        val name: String,
        @Schema(title = "等级定义")
        val medalDefinitions: List<MedalDefinition>?,
    )
}