package com.shuyun.loyalty.manager.service.audit

import com.shuyun.loyalty.manager.audit.annotation.CreateLogAnnotation
import com.shuyun.loyalty.manager.audit.annotation.DeleteLogAnnotation
import com.shuyun.loyalty.manager.audit.annotation.QueryLogAnnotation
import com.shuyun.loyalty.manager.audit.annotation.UpdateLogAnnotation
import com.shuyun.loyalty.manager.audit.enums.BehaviorEnum
import com.shuyun.loyalty.manager.audit.enums.ModuleEnum
import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.calculate.params.CalcParams
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.loyalty.service.service.PointRuleGroupBaseService
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class AuditPointRuleGroupService {

    private val logger = LogManager.getLogger(AuditPointRuleGroupService::class.java)

    @Autowired
    private lateinit var pointRuleGroupBaseService: PointRuleGroupBaseService

    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    @CreateLogAnnotation(
        `object` = "积分规则名称",
        behaviorCode = BehaviorEnum.CREATE_RULE,
        module = ModuleEnum.POINT_RULE,
        fields = ["groupName"]
    )
    fun insertPointRuleGroup(pointRuleGroup: PointRuleGroup) {
        logger.debug("保存积分规则组{}", { JsonUtils.toJson(pointRuleGroup) })
        pointRuleGroup.groupName = pointRuleGroup.groupName!!.trim()
        pointRuleGroup.startTime?.let { it.time = (it.time / 1000) * 1000 }
        pointRuleGroup.endTime?.let { it.time = (it.time / 1000) * 1000 }
        pointRuleGroupBaseService.update(pointRuleGroup)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    @UpdateLogAnnotation(
        `object` = "积分规则名称",
        behaviorCode = BehaviorEnum.UPDATE,
        module = ModuleEnum.POINT_RULE,
        fields = ["groupName"],
        table = "point_rule_group"
    )
    fun updatePointRuleGroup(pointRuleGroup: PointRuleGroup, @Suppress("unused") oldName: String = "") {
        logger.debug("修改积分规则组{}", { JsonUtils.toJson(pointRuleGroup) })
        pointRuleGroup.groupName = pointRuleGroup.groupName!!.trim()
        pointRuleGroupBaseService.update(pointRuleGroup)
    }

    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    @DeleteLogAnnotation(
        `object` = "积分规则名称",
        behaviorCode = BehaviorEnum.DELETE,
        module = ModuleEnum.POINT_RULE,
        fields = ["groupName"]
    )
    fun deletePointRuleGroup(pointRuleGroup: PointRuleGroup) {
        pointRuleGroup.disabled = true
        logger.debug("删除积分规则组 查询参数groupId: {} ", pointRuleGroup.id)
        pointRuleGroupBaseService.update(pointRuleGroup)
    }


    @QueryLogAnnotation(behaviorCode = BehaviorEnum.QUERY, module = ModuleEnum.POINT_RULE)
    fun findPointRuleGroup(
        params: CalcParams
    ): List<PointRuleGroup> {
        return pointRuleGroupBaseService.findPointRuleGroup(params)
    }

}