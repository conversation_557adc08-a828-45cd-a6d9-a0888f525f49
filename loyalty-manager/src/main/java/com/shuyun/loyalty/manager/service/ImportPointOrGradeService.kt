package com.shuyun.loyalty.manager.service

import com.pip.shuyun.pool.transaction.DmTransaction
import com.pip.shuyun.pool.transaction.TransactionInfoHolder
import com.shuyun.easy.filestorage.FileClient
import com.shuyun.easy.filestorage.helper.UploadResponse
import com.shuyun.loyalty.entity.api.constants.ChangeMode
import com.shuyun.loyalty.entity.api.constants.MedalChangeMode
import com.shuyun.loyalty.entity.api.constants.RequestType
import com.shuyun.loyalty.entity.api.constants.TransferMedalTypeEnum
import com.shuyun.loyalty.entity.api.request.MemberGradeModifyRequest
import com.shuyun.loyalty.entity.api.request.MemberMedalModifyRequest
import com.shuyun.loyalty.entity.dto.MemberPointMessage
import com.shuyun.loyalty.entity.dto.MemberRequest
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.manager.config.uploadFile
import com.shuyun.loyalty.manager.repository.ImportPointOrGrade
import com.shuyun.loyalty.manager.service.VerifyResult.*
import com.shuyun.loyalty.manager.util.ImportPointOrGradeFuture
import com.shuyun.loyalty.manager.vo.ImportPointOrGradeVo
import com.shuyun.loyalty.service.exception.FileException
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.kafka.IPointSyncProducer
import com.shuyun.loyalty.service.meta.ImportStatusEnum
import com.shuyun.loyalty.service.meta.RepeatType
import com.shuyun.loyalty.service.meta.TypeEnum
import com.shuyun.loyalty.service.model.*
import com.shuyun.loyalty.service.service.ImportFileConfigBaseService
import com.shuyun.loyalty.service.service.LoyaltyPrograms
import com.shuyun.loyalty.service.service.MemberPointService
import com.shuyun.loyalty.service.service.TmpTableBaseService
import com.shuyun.loyalty.service.transfer.grade.MemberGradeTransferService
import com.shuyun.loyalty.service.transfer.medal.MemberMedalTransferService
import com.shuyun.loyalty.service.transfer.points.LoyaltyPoints
import com.shuyun.loyalty.service.transfer.points.LoyaltyRequestType
import com.shuyun.loyalty.service.transfer.points.MemberPointDeductTransfer
import com.shuyun.loyalty.service.transfer.points.MemberPointSendTransfer
import com.shuyun.loyalty.service.util.*
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.exception.AbstractPipException
import com.shuyun.pip.util.EncodingDetect
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.csv.CSVFormat
import org.apache.commons.csv.CSVParser
import org.apache.commons.io.IOUtils
import org.apache.commons.io.input.BOMInputStream
import org.apache.logging.log4j.LogManager
import org.apache.poi.hssf.util.HSSFColor
import org.apache.poi.ss.usermodel.*
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Example
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.InputStreamReader
import java.io.OutputStream
import java.math.BigDecimal
import java.net.URLEncoder
import java.nio.charset.StandardCharsets
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.time.*
import java.time.format.DateTimeFormatter
import java.util.*
import java.util.concurrent.atomic.AtomicReference
import javax.persistence.OptimisticLockException
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse
import kotlin.concurrent.withLock


@Service
class ImportPointOrGradeService {

    private val logger = LogManager.getLogger(ImportPointOrGradeService::class.java)

    @Autowired
    private lateinit var importPointOrGradeBaseService: ImportPointOrGradeBaseService

    @Autowired
    private lateinit var importFileConfigBaseService: ImportFileConfigBaseService

    @Autowired
    private lateinit var memberPointService: MemberPointService


    @Autowired
    private lateinit var memberPointSendTransfer: MemberPointSendTransfer

    @Autowired
    private lateinit var memberPointDeductTransfer: MemberPointDeductTransfer

    @Autowired
    private lateinit var memberGradeTransferService: MemberGradeTransferService

    @Autowired
    private lateinit var memberMedalTransferService: MemberMedalTransferService

    @Autowired
    private lateinit var tmpTableBaseService: TmpTableBaseService


    @Autowired(required = false)
    private lateinit var fileClient: FileClient

    /**导出错误信息*/
    fun exportPointError(id: Long, response: HttpServletResponse) {
        val optional = importPointOrGradeBaseService.findById(id)
        if (!optional.isPresent || optional.get().failFileName == null || optional.get().failFileName!!.isBlank()) {
            throw LoyaltyException(LoyaltyExceptionCode.UNKNOWN_EXCEPTION)
        }
        val file = try {
            fileClient.downloadToFile(optional.get().failGroupName, optional.get().failFileName)
        } catch (e: Exception) {
            throw FileException(LoyaltyExceptionCode.FILE_EXPORT_ERROR, e)
        }

        //获取导入配置
        val importFileConfig = ImportFileConfig().findSubjectIdAndTime(optional.get().subjectId!!, ZonedDateTime.now())

        val inputStream = file.inputStream()

        val importName = optional.get().importName

        val failRecordI18n = I18nUtil.transferMessage(LoyaltyExceptionCode.FAIL_RECORD).replace(" ", "_")

        val errorName = if (importName != null) {
            if (importName.indexOf(".") > -1) importName.split(".")[0] + "_${failRecordI18n}"
            else importName + "_${failRecordI18n}"
        } else failRecordI18n

        val out = response.outputStream
        out.use {
            response.setHeader("Content-Disposition", "attachment; filename=${URLEncoder.encode(errorName, "UTF-8")}.${importFileConfig.fileType}")
            when (importFileConfig.fileType) {
                "csv" -> response.contentType = "text/csv;charset=utf-8"
                "xlsx" -> response.contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
                else -> {}
            }
            IOUtils.copy(inputStream, it)
        }
    }

    fun findImportPointOrGradePage(
        number: Int?, size: Int?, planId: Long?, subjectId: Long?,
        accountOrHierarchyId: Long?
    ): Page<ImportPointOrGrade> {
        val condition = ImportPointOrGrade().apply {
            this.planId = planId
            this.subjectId = subjectId
            this.accountOrHierarchyId = accountOrHierarchyId
            this.importDesc = null
        }
        val example = Example.of(condition)
        val pageable = PageRequest.of(number!!, size!!, Sort.Direction.DESC, "id")
        val pageList = importPointOrGradeBaseService.findImportPointOrGradePageByExample(example, pageable)
        logger.trace("import point page list : {}", { JsonUtils.toJson(pageList) })
        return pageList
    }


    // 上传文件
    fun uploadFile(
        planId: Long,
        subjectId: Long,
        accountOrHierarchyId: Long,
        multipartFile: MultipartFile,
        type: TypeEnum
    ): Map<String, String> {
        //获取导入配置
        val importFileConfig = ImportFileConfig().findSubjectIdAndTime(subjectId, ZonedDateTime.now())
        val fileExtension = importFileConfig.getFileExtension(multipartFile.originalFilename!!)

        if (fileExtension != importFileConfig.fileType) {
            logger.warn("导入文件格式必须是{}格式", importFileConfig.fileType)
            throw LoyaltyException(LoyaltyExceptionCode.FILE_WRITE_ERROR)
        }

        var totalNumber = 0
        var failNumber = 0
        when (importFileConfig.fileType) {
            "csv" -> {
                val delimiter = if (importFileConfig.splitCode == "tab") "\\t" else importFileConfig.splitCode!!
                val lines = importFileConfig.buildExampleLines(type, delimiter)
                val reader = InputStreamReader(
                    BOMInputStream.builder().setInputStream(multipartFile.inputStream).get(),
                    StandardCharsets.UTF_8
                )
                val csvFormat = CSVFormat.DEFAULT.builder().setDelimiter(delimiter).setIgnoreEmptyLines(true).build()
                val parser = CSVParser(reader, csvFormat)
                try {
                    for (record in parser) {
                        totalNumber++
                        if (lines.first().size != record.size()) {
                            failNumber++
                        }
                    }
                } finally {
                    parser.close()
                    reader.close()
                }
            }

            "xlsx" -> {
                val inputStream = multipartFile.inputStream
                val workbook = XSSFWorkbook(inputStream)
                try {
                    val sheet = workbook.getSheetAt(0)
                    val rowIterator = sheet.iterator()
                    val lines = importFileConfig.buildExampleLines(type)
                    val columnSize = lines.first().size
                    while (rowIterator.hasNext()) {
                        val row = rowIterator.next()
                        val cellIterator = row.cellIterator()
                        var cellSize = 0
                        var isNotEmptyRow = false
                        while (cellIterator.hasNext()) {
                            val cell = cellIterator.next()
                            val isNotEmptyCell = cell != null
                            if (isNotEmptyCell) {
                                isNotEmptyRow = true
                                cellSize++
                            }
                        }
                        if (!isNotEmptyRow) {
                            continue
                        }
                        totalNumber++
                        if (columnSize != cellSize) {
                            failNumber++
                        }
                    }

                } finally {
                    inputStream.close()
                    workbook.close()
                }
            }

            else -> {}
        }

        try {
            val res = ApplicationContextHolder.getBean(FileClient::class.java).uploadFile(
                UUID.randomUUID().toString(),
                multipartFile.inputStream,
                multipartFile.size
            )
            logger.info("上传文件 planId: {}, accountOrHierarchyId: {}, fileName:{}, groupName:{}", planId, accountOrHierarchyId, res.fileName, res.groupName)
            return mapOf(
                "fileName" to res.fileName,
                "groupName" to res.groupName,
                "totalNumber" to (totalNumber - 1).toString(),
                "failNumber" to failNumber.toString()
            )
        } catch (_: Throwable) {
            return mapOf("ERROR" to I18nUtil.transferMessage(LoyaltyExceptionCode.FILE_UPLOAD_ERROR).replace(",", "."))
        }
    }


    /**导入积分和等级*/
    fun importPointOrGrade(importPointVo: ImportPointOrGradeVo, request: HttpServletRequest, type: TypeEnum) {
        val importFileConfig = importFileConfigBaseService.findEnableBySubjectId(importPointVo.subjectId!!)
        val transferApproveSetting = JsonUtils.parse(importFileConfig.transferApproveSetting, TransferApproveSettingVo::class.java)
        // 如果开启审批且包含当前类型禁止使用此接口
        if (transferApproveSetting.importApproveSwitch == true && transferApproveSetting.importApproveTypes?.contains(type) == true) {
            throw LoyaltyException(LoyaltyExceptionCode.REQUEST_EXCEPTION)
        }
        VisitorInfoUtil.userId.set(importPointVo.creatorId)
        VisitorInfoUtil.username.set(importPointVo.creatorName)
        val importPoint = importPointVo.buildImportPointOrGrade(ImportStatusEnum.RUNNING, importPointVo.importType!!)
            .apply {
                createTime = Date()
                updateTime = Date()
            }
        ApplicationContextHolder.getBean(ImportPointOrGradeBaseService::class.java).save(importPoint)
        ImportPointOrGradeFuture.importPointOrGrade(importPoint, request)
    }


    /**导入等级积分*/
    fun importPointOrGradeOrMedal(importPointOrGrade: ImportPointOrGrade, lineContinue: Long) {
        VisitorInfoUtil.userId.set(importPointOrGrade.creatorId)
        VisitorInfoUtil.username.set(importPointOrGrade.creatorName)
        ModelInitUtil.copyPropertiesIgnoreNull(importPointOrGrade, this)
        if (lineContinue > 0) {
            importPointOrGrade.importDesc = I18nUtil.transferMessage(
                LoyaltyExceptionCode.LINE_UPLOAD_INTERRUPT,
                importPointOrGrade.totalNumber!!.plus(1).toString()
            )
        }

        //下载文件
        val fileClient = ApplicationContextHolder.getBean(FileClient::class.java)
        val file = fileClient.downloadToFile(importPointOrGrade.groupName, importPointOrGrade.fileName)

        var index = 0
        val now = ZonedDateTime.now()
        val plan = when (importPointOrGrade.importType!!) {
            TypeEnum.POINT -> LoyaltyPrograms.findPlanByAccountTypeId(importPointOrGrade.accountOrHierarchyId!!, now)
            TypeEnum.GRADE -> LoyaltyPrograms.findPlanByGradeHierarchyId(importPointOrGrade.accountOrHierarchyId!!, now)
            TypeEnum.MEDAL -> LoyaltyPrograms.findPlanByMedalHierarchyId(importPointOrGrade.accountOrHierarchyId!!, now)
        } ?: throw LoyaltyException(LoyaltyExceptionCode.PLAN_NOT_FOUND, "- hierarchyId: ${importPointOrGrade.accountOrHierarchyId}")

        val importFileConfig = ImportFileConfig().findSubjectIdAndTime(importPointOrGrade.subjectId!!, ZonedDateTime.now())
        val fileExtension = importFileConfig.getFileExtension(importPointOrGrade.importName!!)

        val records = ArrayList<List<String>>()
        when (fileExtension) {
            "csv" -> {
                val delimiter = if (importFileConfig.splitCode == "tab") "\\t" else importFileConfig.splitCode!!
                val encode = EncodingDetect.getJavaEncode(file)
                val reader = InputStreamReader(BOMInputStream.builder().setInputStream(file.inputStream()).get(), encode)
                val csvFormat = CSVFormat.DEFAULT.builder().setDelimiter(delimiter).setIgnoreEmptyLines(true).build()
                val parser = CSVParser(reader, csvFormat)
                try {
                    for (record in parser) {
                        val row = ArrayList<String>()
                        for (column in record) {
                            row.add(column ?: "")
                        }
                        records.add(row)
                    }
                } finally {
                    parser.close()
                    reader.close()
                }
            }
            "xlsx" -> {
                val workbook = XSSFWorkbook(file.inputStream())
                workbook.use { book ->
                    val sheet = book.getSheetAt(0)
                    val rowIterator = sheet.iterator()
                    while (rowIterator.hasNext()) {
                        val row = rowIterator.next()
                        val cellIterator = row.cellIterator()
                        val line = ArrayList<String>()
                        while (cellIterator.hasNext()) {
                            val cell = cellIterator.next()
                            val value = when (cell.cellType) {
                                CellType.NUMERIC -> {
                                    if (DateUtil.isCellDateFormatted(cell)) {
                                        if (importPointOrGrade.importType == TypeEnum.MEDAL) {
                                            DateUtils.dateToLocalDateTime(cell.dateCellValue).format(DateTimeFormatter.ofPattern("yyyy/MM/dd"))
                                        } else {
                                            cell.localDateTimeCellValue.format(DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"))
                                        }
                                    } else {
                                        DecimalFormat("#").apply { maximumFractionDigits = 8 }.format(cell.numericCellValue)
                                    }
                                }
                                CellType.STRING -> cell.stringCellValue
                                CellType.BOOLEAN -> cell.booleanCellValue.toString()
                                else -> ""
                            }
                            line.add(value)
                        }
                        if (line.isNotEmpty() && !line.all { it.isBlank() }) {
                            records.add(line)
                        }
                    }
                }
            }
            else -> {}
        }

        val lines = importFileConfig.buildExampleLines(importPointOrGrade.importType!!)
        val headerSize = lines.first().size
        for (record in records) {
            if (index++ == 0) {
                // 跳过标题行
                continue
            }
            if (lineContinue > 0) {
                if (lineContinue >= index) {
                    logger.info("跳过已经处理过的行 行号：{}", index)
                    continue
                }
            }
            if (importPointOrGrade.status != ImportStatusEnum.RUNNING) {
                break
            }
                    val memberIdRef = AtomicReference<String>()
            var lineError: String? = null
            try {
                when (val result = verifyLine(plan, importFileConfig, importPointOrGrade.importType!!, record, headerSize, index)) {
                    is ErrorResult -> {
                        lineError = result.error
                        importPointOrGrade.failNumber = (importPointOrGrade.failNumber ?: 0) + 1
                    }
                    else -> {
                        transfer(importPointOrGrade.accountOrHierarchyId!!, importPointOrGrade.fileName!!, result, memberIdRef)
                        importPointOrGrade.successNumber = (importPointOrGrade.successNumber ?: 0) + 1
                    }
                }
            } catch (e: Throwable) {
                val id = memberIdRef.get() ?: ""
                val error = when {
                    e.message?.isNotEmpty() == true -> "$id ${e.message}"
                    e.cause != null -> "$id ${e.cause?.message}"
                    else -> "$id ${
                        I18nUtil.transferMessage(
                            LoyaltyExceptionCode.LINE_UPLOAD_INTERRUPT,
                            index.toString()
                        )
                    } "
                }
                lineError = error
                importPointOrGrade.importDesc = error
                importPointOrGrade.failNumber = (importPointOrGrade.failNumber ?: 0) + 1
                if (e !is OptimisticLockException && e !is AbstractPipException) {
                    logger.warn("导入失败", e)
                    importPointOrGrade.status = ImportStatusEnum.FAIL
                }
            }
            importPointOrGrade.totalNumber = (importPointOrGrade.totalNumber ?: 0) + 1
            if (lineError != null) {
                val tmpLines = record + lineError
                runCatching {
                    val tmpTable = TmpTable()
                    tmpTable.tmpText = tmpLines.joinToString(separator = "$$")
                    tmpTable.fileId = importPointOrGrade.id
                    tmpTableBaseService.save(tmpTable)
                }
            }
            if (importPointOrGrade.status == ImportStatusEnum.RUNNING) {
                if (index == records.size) {
                    importPointOrGrade.status = ImportStatusEnum.SUCCESS
                }
            }
            if (importPointOrGrade.status == ImportStatusEnum.FAIL || importPointOrGrade.status == ImportStatusEnum.SUCCESS) {
                uploadErrorFile(importPointOrGrade, lines.first(), fileExtension)?.also {
                    importPointOrGrade.failGroupName = it.groupName
                    importPointOrGrade.failFileName = it.fileName
                }
            }
            importPointOrGrade.update()
        }
        logger.info("导入{}完成", importPointOrGrade.importName)
    }


    private fun uploadErrorFile(importPointOrGrade: ImportPointOrGrade, headerLine: List<String>, fileExtension: String): UploadResponse? {
        val errLine0 = headerLine + I18nUtil.transferMessage(LoyaltyExceptionCode.ERROR_INFO)
        val errorLines = ArrayList<List<String>>().also { it.add(errLine0) }

        val list = tmpTableBaseService.findByFileId(importPointOrGrade.id!!)
        for (tmpTable in list) {
            if (tmpTable.tmpText.isNullOrBlank()) continue
            val tmpLines = tmpTable.tmpText!!.split("$$").toMutableList()
            if (tmpLines.size < errLine0.size) {
                val errorMessage = tmpLines.last()
                tmpLines.removeLast()
                tmpLines.addAll(Collections.nCopies(errLine0.size - tmpLines.size - 1, ""))// 补上缺失的列
                tmpLines.add(errorMessage)
            }
            errorLines.add(tmpLines)
        }
        if (errorLines.size > 1) {
            val svc = ApplicationContextHolder.getBean(ImportPointOrGradeService::class.java)
            val output = ByteArrayOutputStream()
            svc.writeFile(output, errorLines, fileExtension)
            val input = ByteArrayInputStream(output.toByteArray())
            return try {
                val response = ApplicationContextHolder.getBean(FileClient::class.java).uploadFile(
                    UUID.randomUUID().toString(),
                    input,
                    input.available().toLong()
                )
                tmpTableBaseService.deleteByFileId(importPointOrGrade.id!!)
                response
            } catch (e: Exception) {
                importPointOrGrade.importDesc += (" " + I18nUtil.transferMessage(LoyaltyExceptionCode.SAVE_FILE_ERROR))
                logger.warn("保存错误文件", e)
                null
            } finally {
                output.close()
                input.close()
            }
        }
        return null
    }


    private fun transfer(accountOrHierarchyId: Long, fileName: String, verifiedResult: VerifyResult, currentMemberId: AtomicReference<String>) {
        val svc = ApplicationContextHolder.getBean(ImportPointOrGradeService::class.java)
        when (verifiedResult) {
            is VerifiedPointResult -> {
                for (memberId in verifiedResult.memberIds) {
                    currentMemberId.set(memberId)
                    val lock = ApplicationContextHolder.getBean(Locker::class.java).getLock(
                        String.format(
                            MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT,
                            accountOrHierarchyId,
                            memberId
                        )
                    )
                    try {
                        lock.lock()
                        svc.transferPoint(fileName, memberId, verifiedResult)
                    } finally {
                        lock.unlock()
                    }
                }
            }
            is VerifiedGradeResult -> {
                for (memberId in verifiedResult.memberIds) {
                    currentMemberId.set(memberId)
                    svc.transferGrade(fileName, memberId, verifiedResult)
                }
            }
            is VerifiedMedalResult -> {
                for (memberId in verifiedResult.memberIds) {
                    currentMemberId.set(memberId)
                    svc.transferMedal(fileName, memberId, verifiedResult)
                }
            }

            else -> {}
        }
    }


    /**导出文件*/
    fun exportTemplate(planId: Long, subjectId: Long, type: TypeEnum, response: HttpServletResponse) {
        val plan = LoyaltyPrograms.findPlanById(planId) ?: throw LoyaltyException(
            LoyaltyExceptionCode.PLAN_NOT_FOUND,
            planId.toString()
        )
        val subject = plan.subjectList?.find { it.id == subjectId }
            ?: throw LoyaltyException(LoyaltyExceptionCode.SUBJECT_ID_NOT_FOUND)

        //获取导入配置
        val importFileConfig = ImportFileConfig().findSubjectIdAndTime(subject.id!!, ZonedDateTime.now())

        response.outputStream.use {
            val encodeToName = URLEncoder.encode(
                "${plan.name}_${subject.name}_${type}_${SimpleDateFormat("yyyyMMdd").format(Date())}".replace(
                    " ",
                    ""
                ), "UTF-8"
            )
            response.setHeader(
                "Content-Disposition",
                "attachment; filename=${encodeToName}.${importFileConfig.fileType}"
            )
            when (importFileConfig.fileType) {
                "csv" -> {
                    val delimiter = if (importFileConfig.splitCode == "tab") "\\t" else importFileConfig.splitCode!!
                    val lines = importFileConfig.buildExampleLines(type, delimiter, withBOM = true)
                    response.contentType = "text/csv;charset=utf-8"
                    writeFile(it, lines, importFileConfig.fileType!!, delimiter)
                }

                "xlsx" -> {
                    response.contentType =
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"
                    val lines = importFileConfig.buildExampleLines(type)
                    writeFile(it, lines, importFileConfig.fileType!!)
                }

                else -> {}
            }
        }
    }


    fun writeFile(outputStream: OutputStream, lines: List<List<String>>, fileType: String, csvDelimiter: String = ",") {
        when (fileType) {
            "csv" -> {
                val csvFormat = CSVFormat.DEFAULT.builder().setDelimiter(csvDelimiter).build()
                csvFormat.print(outputStream.writer()).apply {
                    lines.forEach { line -> printRecord(*line.toTypedArray()) }
                    close(true)
                }
            }

            "xlsx" -> {
                val workbook = XSSFWorkbook()
                val workSheet = workbook.createSheet()
                val cellStyle = workbook.createCellStyle().apply {
                    setFont(workbook.createFont().apply { color = HSSFColor.HSSFColorPredefined.BLACK.index })
                    fillForegroundColor = IndexedColors.WHITE.getIndex()
                    fillPattern = FillPatternType.SOLID_FOREGROUND
                    borderBottom = BorderStyle.THIN
                    borderLeft = BorderStyle.THIN
                    borderRight = BorderStyle.THIN
                    borderTop = BorderStyle.THIN
                }

                val titleStyle = cellStyle.copy().apply {
                    val font = workbook.createFont().apply { bold = true; color = HSSFColor.HSSFColorPredefined.BLACK.index }
                    setFont(font)
                }
                lines.forEachIndexed { index, columns ->
                    val row = workSheet.createRow(index)
                    columns.forEachIndexed { i, column ->
                        val cell = row.createCell(i)
                        cell.setCellValue(column)
                        if (index == 0) {
                            cell.cellStyle = titleStyle
                        } else {
                            cell.cellStyle = cellStyle
                        }
                    }
                }
                lines.first().forEachIndexed { index, _ -> workSheet.autoSizeColumn(index) }
                workbook.write(outputStream)
                workbook.close()
            }

            else -> {}
        }
    }


    @DmTransaction
    fun transferPoint(
        fileName: String,
        memberId: String,
        vpr: VerifiedPointResult
    ) {
        val memberPoint = memberPointService.getOrCreate(vpr.pointAccountId, memberId)
        val lp = LoyaltyPoints.of(
            memberPoint = memberPoint,
            type = LoyaltyRequestType.MANUAL,
            point = vpr.point.abs(),
            changeMode = ChangeMode.MANUAL.name,
            channel = ConstantValue.DEFAULT_CHANNEL_TYPE,
            traceId = fileName,
            uniqueId = DigestUtils.md5Hex(fileName + memberId) + "-" + vpr.lineNumber,
            shopId = vpr.shopId,
            kzzd1 = vpr.kzzd1,
            kzzd2 = vpr.kzzd2,
            kzzd3 = vpr.kzzd3,
            desc = vpr.desc
        ).apply {
            attr.recordDetail = "导入积分-FileName:${fileName.substringAfter('/')}"
        }

        if (vpr.point >= BigDecimal.ZERO) {
            lp.apply {
                attr.effectiveDate = vpr.effectiveDate
                attr.overdueDate = vpr.overdueDate
            }
            val error = memberPointSendTransfer.send(lp, lp.afterPoints, ForbiddenPort.MANUAL, lp.attr.uniqueId)
            if (error != null) {
                logger.info("通过文件导入发放积分 memberId: {} fileName: {} {}", memberId, fileName, error)
            }
        } else {
            memberPointDeductTransfer.deduct(
                lp,
                ForbiddenPort.MANUAL,
                ForbiddenOperation.POINT_DEDUCT_BY_USE,
                lp.attr.uniqueId
            )
        }
        TransactionInfoHolder.afterCommit {
            ApplicationContextHolder.getBean(IPointSyncProducer::class.java)
                .send(
                    MemberPointMessage(
                        lp.hierarchy.id,
                        lp.member.memberId,
                        lp.member.id
                    )
                )
        }
    }


    fun transferGrade(
        fileName: String,
        memberId: String,
        vgr: VerifiedGradeResult
    ) {
        val req = MemberGradeModifyRequest().apply {
            this.gradeHierarchyId = vgr.gradeHierarchyId
            this.channelType = ConstantValue.DEFAULT_CHANNEL_TYPE
            this.gradeDefinitionId = vgr.targetGradeDefinitionId
            this.changeWayType = ChangeMode.MANUAL
            this.description = vgr.desc
            this.memberId = memberId //会员ID
            this.triggerId = fileName
            this.overdueDate = vgr.overdueDate
        }
        memberGradeTransferService.modifyMemberGrade(req, RequestType.SYNC, ForbiddenPort.MANUAL)
    }


    fun transferMedal(
        fileName: String,
        memberId: String,
        vmr: VerifiedMedalResult
    ) {
        val request = MemberMedalModifyRequest().apply {
            this.medalHierarchyId = vmr.medalHierarchyId
            this.medalDefinitionId = vmr.targetMedalDefinitionId
            this.channelType = ConstantValue.DEFAULT_CHANNEL_TYPE
            this.changeWayType = MedalChangeMode.MANUAL
            this.description = vmr.desc
            this.memberId = memberId
            this.triggerId = fileName
            this.transferMedalType = TransferMedalTypeEnum.CHANGE
            this.overdueDate = vmr.overdueDate
        }
        val locker = ApplicationContextHolder.getBean(Locker::class.java)
        val gradeLock = locker.getLock("medal_calculate_${request.medalHierarchyId}-${request.memberId}")
        gradeLock.withLock {
            memberMedalTransferService.modifyMemberMedal(request, ForbiddenPort.MANUAL)
        }
    }


    private fun convertToDateTime(datetimeStr: String, patterns: List<DateTimeFormatter>): ZonedDateTime? {
        var datetime: LocalDateTime? = null
        for (pattern in patterns) {
            datetime = runCatching { LocalDateTime.parse(datetimeStr, pattern) }.getOrNull()
            if (datetime != null) {
                break
            }
        }
        return datetime?.let { ZonedDateTime.of(datetime, ZoneId.systemDefault()) }
    }


    private fun convertToDate(dateStr: String, patterns: List<DateTimeFormatter>): LocalDate? {
        var date: LocalDate? = null
        for (pattern in patterns) {
            date = runCatching { LocalDate.parse(dateStr, pattern) }.getOrNull()
            if (date != null) {
                break
            }
        }
        return date
    }


    private fun verifyLine(plan: Plan, importFileConfig: ImportFileConfig, importType: TypeEnum, line: List<String>, columnSize: Int, lineNumber: Int): VerifyResult {
        if (columnSize != line.size) {
            return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.LINE_CODE_MATCH))
        }
        val memberIds = LinkedHashSet<String>()
        var importFieldName = ""
        importFileConfig.importFieldConfig?.let {
            for (i in importFileConfig.importFieldConfig!!.indices) {
                val importFieldConfig = importFileConfig.importFieldConfig!![i]
                try {
                    importFieldName = importFieldConfig.field!!
                    val ids = TransferMemberIdUtil.transferMemberId(importFieldConfig.field!!, line[i].trim())
                    memberIds.addAll(ids)
                } catch (e: Exception) {
                    logger.warn("查询会员异常 field: {} id: {}", importFieldConfig.field!!, line[i].trim(), e)
                }
                if (memberIds.isNotEmpty()) break
            }
        }
        if (memberIds.isEmpty()) return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.MEMBER_NOT_FOUND))
        //验证重复性
        if (RepeatType.NOT_ALLOW == importFileConfig.repeatType && memberIds.size > 1) {
            return ErrorResult(
                I18nUtil.transferMessage(
                    LoyaltyExceptionCode.OPERATE_MEMBER_REPEAT,
                    importFieldName
                )
            )
        }
        val subject = plan.subjectList!!.first()
        val mainMemberIds = memberIds.map {
            val mainMemberId = MergeUtil.findMergePointMemberId(subject, it)
            mainMemberId ?: it
        }

        val patterns = listOf(
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm"),
            DateTimeFormatter.BASIC_ISO_DATE
        )
        return when (importType) {
            TypeEnum.POINT -> verifyPointLine(subject.pointAccountTypeList!!.first(), mainMemberIds, importFileConfig, line, lineNumber, patterns)
            TypeEnum.GRADE -> verifyGradeLine(subject.gradeHierarchyList!!.first(), mainMemberIds, importFileConfig, line, lineNumber, patterns)
            TypeEnum.MEDAL -> verifyMedalLine(subject.medalHierarchyList!!.first(), mainMemberIds, importFileConfig, line, lineNumber)
        }
    }


    private fun verifyPointLine(pointAccountType: PointAccountType, memberIds: List<String>, importFileConfig: ImportFileConfig, row: List<String>, lineNumber: Int, patterns: List<DateTimeFormatter>) : VerifyResult {
        // val memberId = line[0]
        val n = (importFileConfig.importFieldConfig?.size ?: 1) - 1
        val line = row.drop(n)
        val pointAccountTypeName = line[1]
        val point = line[2]
        val effectiveDate = line[3]
        val overdueDate = line[4]
        val desc = line[5]
        val shopId = if (importFileConfig.shopSelectorId.isNullOrEmpty()) null else  line[6]
        val kzzdOffset = if (importFileConfig.shopSelectorId.isNullOrEmpty()) 0 else 1
        var kzzd1: String? = null
        var kzzd2: String? = null
        var kzzd3: String? = null

        importFileConfig.extensionFieldSettings?.forEachIndexed { i, ext ->
            when (ext.fieldName) {
                "KZZD1" -> kzzd1 = line[6 + kzzdOffset + i]
                "KZZD2" -> kzzd2 = line[6 + kzzdOffset + i]
                "KZZD3" -> kzzd3 = line[6 + kzzdOffset + i]
                else -> {}
            }
        }
        if (pointAccountTypeName != pointAccountType.name) {
            return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.POINT_TYPE_NAME_MATCH))
        }
        val pointValue = point.toBigDecimalOrNull()
            ?: return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.POINT_FORMAT_ERROR))

        var effective: ZonedDateTime? = null
        var overdue: ZonedDateTime? = null
        if (pointValue >= BigDecimal.ZERO) {
            // 大于0 是发积分，发积分需要检查生效时间和过期时间
            if (effectiveDate.isBlank()) {
                return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.EFFECTIVE_TIME_EMPTY))
            }
            if (effectiveDate != I18nUtil.transferMessage(LoyaltyExceptionCode.START_EFFECTIVE)) {
                effective = convertToDateTime(effectiveDate, patterns)
                if (effective == null) {
                    return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.EFFECTIVE_TIME_ERROR))
                }
            } else {
                effective = ZonedDateTime.now().minusSeconds(1)
            }

            if (overdueDate.isBlank()) {
                return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.OVERATE_TIME_EMPTY))
            }
            if (overdueDate != I18nUtil.transferMessage(LoyaltyExceptionCode.PERMANENT_EFFECT)) {
                overdue = convertToDateTime(overdueDate, patterns)
                if (overdue == null) {
                    return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.OVERATE_TIME_ERROR))
                }
            } else {
                overdue = ConstantValue.LONG_TERM_OVERDUE_DATE
            }

            if (desc.length > 200) {
                return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.DESC_NOT_ALLOWED_GT_200))
            }
        }


        return VerifiedPointResult(
            memberIds.toSet(),
            shopId,
            pointAccountType.id!!,
            pointAccountTypeName,
            pointAccountType.calculateRoundingResult(pointValue),
            effective,
            overdue,
            kzzd1,
            kzzd2,
            kzzd3,
            desc,
            lineNumber
        )
    }


    private fun verifyGradeLine(
        gradeHierarchy: GradeHierarchy,
        memberIds: List<String>,
        importFileConfig: ImportFileConfig,
        line: List<String>,
        lineNumber: Int,
        patterns: List<DateTimeFormatter>
    ): VerifyResult {

        val memberIdOffset = importFileConfig.importFieldConfig?.size ?: 0
        val gradeHierarchyName = line[0 + memberIdOffset]
        val gradeDefinitionName = line[1 + memberIdOffset]
        val overdueDate = line[2 + memberIdOffset]
        val desc = line[3 + memberIdOffset]

        if (gradeHierarchyName != gradeHierarchy.name) {
            return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.GRADE_HIERARCHY_NAME_MATCH))
        }

        val gradeDefinitions = gradeHierarchy.gradeDefinitions ?: emptyList()
        val targetGradeDefinition = gradeDefinitions.find { it.name == gradeDefinitionName }
        if (targetGradeDefinition == null) {
            return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.GRADE_NAME_MATCH))
        }

        if (overdueDate.isBlank()) {
            return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.OVERATE_TIME_EMPTY))
        }

        var overdue: ZonedDateTime? = null
        if (overdueDate != I18nUtil.transferMessage(LoyaltyExceptionCode.PERMANENT_EFFECT)) {
            overdue = convertToDateTime(overdueDate, patterns)
            if (overdue == null) {
                return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.OVERATE_TIME_ERROR))
            }
            if (overdue < ZonedDateTime.now()) {
                return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.OVERATE_LET_NOW_TIME))
            }
        }

        if (desc.length > 200) {
            return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.DESC_NOT_ALLOWED_GT_200))
        }

        return VerifiedGradeResult(
            memberIds.toSet(),
            gradeHierarchy.id!!,
            gradeHierarchyName,
            targetGradeDefinition.id!!,
            targetGradeDefinition.name!!,
            overdue,
            desc,
            lineNumber
        )
    }


    private fun verifyMedalLine(
        medalHierarchy: MedalHierarchy,
        memberIds: List<String>,
        importFileConfig: ImportFileConfig,
        line: List<String>,
        lineNumber: Int
    ): VerifyResult {

        val memberIdOffset = importFileConfig.importFieldConfig?.size ?: 0
        val medalHierarchyName = line[0 + memberIdOffset]
        val medalDefinitionName = line[1 + memberIdOffset]
        val overdueDate = line[2 + memberIdOffset]
        val desc = line[3 + memberIdOffset]

        if (medalHierarchyName != medalHierarchy.name) {
            return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.MEDAL_HIERARCHY_NAME_MATCH))
        }

        val medalDefinitions = medalHierarchy.medalDefinitions ?: emptyList()
        val targetMedalDefinition = medalDefinitions.find { it.name == medalDefinitionName }
        if (targetMedalDefinition == null) {
            return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.MEDAL_NAME_MATCH))
        }

        if (overdueDate.isBlank()) {
            return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.OVERATE_TIME_EMPTY))
        }

        var overdue: ZonedDateTime? = null
        if (overdueDate != I18nUtil.transferMessage(LoyaltyExceptionCode.PERMANENT_EFFECT)) {
            val overDate = convertToDate(
                overdueDate,
                listOf(
                    DateTimeFormatter.ofPattern("yyyy/MM/dd"),
                    DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                    DateTimeFormatter.ofPattern("MM/dd/yyyy"),
                )
            )
            if (overDate == null) {
                return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.OVERATE_TIME_ERROR))
            }
            if (overDate < LocalDate.now()) {
                return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.OVERATE_LET_NOW_TIME))
            }
            overdue = ZonedDateTime.of(overDate, LocalTime.MIN, ZoneId.systemDefault())
        }

        if (desc.length > 200) {
            return ErrorResult(I18nUtil.transferMessage(LoyaltyExceptionCode.DESC_NOT_ALLOWED_GT_200))
        }

        return VerifiedMedalResult(
            memberIds.toSet(),
            medalHierarchy.id!!,
            medalHierarchyName,
            targetMedalDefinition.id!!,
            targetMedalDefinition.name!!,
            overdue,
            desc,
            lineNumber
        )
    }

}

sealed class VerifyResult {

    data class ErrorResult(val error: String) : VerifyResult()

    data class VerifiedPointResult(
        val memberIds: Set<String>,
        val shopId: String?,
        val pointAccountId: Long,
        val pointAccountName: String,
        val point: BigDecimal,
        val effectiveDate: ZonedDateTime?,
        val overdueDate: ZonedDateTime?,
        val kzzd1: String?,
        val kzzd2: String?,
        val kzzd3: String?,
        val desc: String?,
        val lineNumber: Int
    ) : VerifyResult()

    data class VerifiedGradeResult(
        val memberIds: Set<String>,
        val gradeHierarchyId: Long,
        val gradeHierarchyName: String,
        val targetGradeDefinitionId: Long,
        val targetGradeDefinitionName: String,
        val overdueDate: ZonedDateTime?,
        val desc: String?,
        val lineNumber: Int
    ) : VerifyResult()

    data class VerifiedMedalResult(
        val memberIds: Set<String>,
        val medalHierarchyId: Long,
        val medalHierarchyName: String,
        val targetMedalDefinitionId: Long,
        val targetMedalDefinitionName: String,
        val overdueDate: ZonedDateTime?,
        val desc: String?,
        val lineNumber: Int
    ) : VerifyResult()
}