package com.shuyun.loyalty.manager.vo

import com.shuyun.loyalty.entity.enums.ProcessStatusEnum
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.meta.RuleGroupStatusEnum
import com.shuyun.loyalty.service.meta.RuleSortTypeEnum
import com.shuyun.loyalty.service.meta.TimeTypeEnum
import com.shuyun.loyalty.service.model.PointDeductRule
import com.shuyun.loyalty.service.model.PointRuleGroup
import com.shuyun.loyalty.service.model.PointSendRule
import com.shuyun.loyalty.service.util.ModelInitUtil.copyPropertiesIgnoreNull
import com.shuyun.pip.component.json.JsonUtils
import io.swagger.v3.oas.annotations.media.Schema
import org.apache.logging.log4j.LogManager
import java.math.BigDecimal
import java.text.SimpleDateFormat
import java.util.*
import javax.persistence.Transient
import javax.validation.Valid

/**
 * 业务端 规则组
 * */
@Schema(title = "PointRuleGroupVo")
class PointRuleGroupVo {
    companion object {
        @Transient
        private val logger = LogManager.getLogger(PointRuleGroupVo::class.java)
    }

    @Schema(title = "规则组ID", type = "Long")
    var id: Long? = null

    @Schema(title = "计划ID", type = "Long")
    var planId: Long? = null

    @Schema(title = "账户ID", type = "Long")
    var pointAccountTypeId: Long? = null

    @Schema(title = "规则组名称", type = "String")
    var groupName: String? = null

    @Schema(title = "积分类型 冻结:FREEZE, 解冻:UNFREEZE, 丢弃:DISCARD, 重新计算:RECALCULATE, 发放:SEND", type = "String")
    var scoreType: EventOperationEnum? = null

    @Schema(title = "归档状态", type = "Boolean")
    var fileStatus: Boolean? = null

    @Schema(title = "规则组开始时间", type = "date")
    var startTime: Date? = null

    @Schema(title = "规则组结束时间", type = "date")
    var endTime: Date? = null

    @Schema(title = "事件Id", type = "Long")
    var eventTypeId: Long? = null

    @Schema(title = "事件名称", type = "String")
    var eventTypeName: String? = null

    @Schema(title = "规则组状态", type = "String")
    var groupStatus: RuleGroupStatusEnum? = null

    @Schema(title = "规则取值", type = "String")
    var ruleSortType: RuleSortTypeEnum? = null

    @Schema(title = "允许发放积分额度", type = "Long")
    var limitPoint: BigDecimal? = null

    @Valid
    @Schema(title = "发放积分规则列表", type = "object")
    var sendRuleList: List<PointSendRuleVo>? = null

    @Valid
    @Schema(title = "扣除积分规则列表", type = "object")
    var deductRuleList: List<PointDeductRuleVo>? = null

    @Schema(title = "审批流ID", type = "String")
    var processId: String? = null

    @Schema(title = "国际化信息")
    var _i18nPayload: com.shuyun.pip.i18n.I18nPayload? = null


    /** RuleGroup赋值 */
    fun initRuleGroupFromVo(pointRuleGroup: PointRuleGroup) {
        if (this.groupStatus != RuleGroupStatusEnum.RUNNING) {
            copyPropertiesIgnoreNull(this, pointRuleGroup)
        }
        //判断积分类型 发放/扣除
        when (pointRuleGroup.scoreType) {
            EventOperationEnum.SEND ->
                pointRuleGroup.sendRuleList = this.sendRuleList?.map { sendRuleVo ->
                    val sendRule = PointSendRule()
                    //开始和结束时间赋值
                    sendRuleVo.getStartorEnd()
                    //实体赋值
                    sendRuleVo.initSendRuleFromVo(sendRule)
                    logger.trace("发放积分对规则实体类进行赋值 {}", { JsonUtils.toJson(sendRuleVo) })
                    //规则组和规则时间对比
                    this.isRuleGroupTimeToRuleTime(sendRule)
                    sendRule
                }
            else ->
                pointRuleGroup.deductRuleList = this.deductRuleList?.map { deductVo ->
                    val deductRule = PointDeductRule()
                    deductVo.initDeductRuleFromVo(deductRule)
                    logger.trace("扣除积分对规则实体类进行赋值 {}", { JsonUtils.toJson(deductVo) })
                    deductRule
                }
        }
    }

    /** 判断规则组开始时间>当前时间 */
    @Transient
    fun compareStartTimeToLocalTime(): Boolean {
        //立即开始,赋值当前时间
        return if (this.startTime == null) {
            this.startTime = Date()
            compareEndTimeToStartTime()
            true
        } else {
            compareEndTimeToStartTime()
            //规则组，开始时间>当前时间
            if (this.startTime!!.compareTo(Date()) > -1) true else {
                logger.error("规则组不允许开始时间小于当前时间 {}", { JsonUtils.toJson(this) })
                throw LoyaltyException(LoyaltyExceptionCode.GROUP_LT_NOW_TIME)
            }
        }
    }

    /** 判断规则组开始时间<结束时间 */
    @Transient
    fun compareEndTimeToStartTime() {
        if (this.endTime == null) return
        //规则结束时间>规则开始时间
        if (this.endTime!! < this.startTime) {
            logger.error("规则组不允许开始时间大于结束时间 {}", { JsonUtils.toJson(this) })
            throw LoyaltyException(LoyaltyExceptionCode.GROUP_GT_END_TIME)
        }
    }

    /** 获取规则组判断状态后的实体*/
    fun getPointRuleGroupInsertOrUpdate(pointRuleGroup: PointRuleGroup): PointRuleGroup {
        when {
            //规则组ID为空，创建规则组
            pointRuleGroup.id == null -> {
                this.compareStartTimeToLocalTime()
                this.groupStatus = if(!this.processId.isNullOrEmpty()) RuleGroupStatusEnum.DESIGN else RuleGroupStatusEnum.WAIT_EFFECT
                this.initRuleGroupFromVo(pointRuleGroup)
                this.groupStatus = if(!this.processId.isNullOrEmpty()) RuleGroupStatusEnum.DESIGN else RuleGroupStatusEnum.WAIT_EFFECT
                if(this.groupStatus == RuleGroupStatusEnum.DESIGN) {
                    pointRuleGroup.processStatus = ProcessStatusEnum.DESIGN
                }
                return pointRuleGroup
            }
            //被驳回
            pointRuleGroup.isRuleGroupReject() -> {
                this.compareStartTimeToLocalTime()
                this.groupStatus = RuleGroupStatusEnum.REJECT
                this.initRuleGroupFromVo(pointRuleGroup)
                pointRuleGroup.limitPoint = this.limitPoint
                pointRuleGroup.endTime = this.endTime
                pointRuleGroup.groupStatus = RuleGroupStatusEnum.REJECT
                return pointRuleGroup
            }
            //待生效
            pointRuleGroup.isRuleGroupWaitEffect() -> {
                this.compareStartTimeToLocalTime()
                this.groupStatus = RuleGroupStatusEnum.WAIT_EFFECT
                this.initRuleGroupFromVo(pointRuleGroup)
                pointRuleGroup.endTime = this.endTime
                pointRuleGroup.limitPoint = this.limitPoint
                pointRuleGroup.groupStatus = RuleGroupStatusEnum.WAIT_EFFECT
                return pointRuleGroup
            }
            // 设计中
            pointRuleGroup.isRuleGroupDesign() -> {
                this.compareStartTimeToLocalTime()
                this.groupStatus = RuleGroupStatusEnum.DESIGN
                this.initRuleGroupFromVo(pointRuleGroup)
                pointRuleGroup.endTime = this.endTime
                pointRuleGroup.limitPoint = this.limitPoint
                pointRuleGroup.groupStatus = RuleGroupStatusEnum.DESIGN
                return pointRuleGroup
            }
            //规则组开始时间<当前时间<规则组结束时间(运行中)
            pointRuleGroup.isRuleGroupRunning() -> {
                this.startTime = pointRuleGroup.startTime
                this.groupStatus = RuleGroupStatusEnum.RUNNING
                this.initRuleGroupFromVo(pointRuleGroup)
                pointRuleGroup.startTime = this.startTime
                pointRuleGroup.endTime = this.endTime
                pointRuleGroup.limitPoint = this.limitPoint
                pointRuleGroup.groupStatus = RuleGroupStatusEnum.RUNNING
                return pointRuleGroup
            }
            else -> throw IllegalArgumentException("该规则组不可编辑")
        }
    }

    /** 规则组和规则时间对比*/
    fun isRuleGroupTimeToRuleTime(pointRule: PointSendRule) {
        when (TimeTypeEnum.RELATIVE_TIME) {
            pointRule.startYearType -> return
            pointRule.startMonthType -> return
            pointRule.startDayType -> return
            pointRule.endYearType -> return
            pointRule.endMonthType -> return
            pointRule.endDayType -> return
            else -> {}
        }

        val startCalendar = Calendar.getInstance()
        //立即开始
        if (!pointRule.startYearTime.isNullOrEmpty()) {
            //获取规则开始年份
            startCalendar.set(Calendar.YEAR, pointRule.startYearTime!!.toInt())
            //获取规则开始月份
            startCalendar.set(Calendar.MONTH, pointRule.startMonthTime!!.toInt() - 1)
            //获取规则开始日
            startCalendar.set(Calendar.DAY_OF_MONTH, pointRule.startDayTime!!.toInt())
            logger.trace("规则开始时间进行赋值 ${SimpleDateFormat("yyyy-MM-dd").format(startCalendar.time)}")
            if (this.dateCompare(this.startTime!!, startCalendar.time)) {
                logger.error("积分有效期不允许开始时间小于规则组时间 ${JsonUtils.toJson(this)}")
                throw LoyaltyException(LoyaltyExceptionCode.POINT_LT_GROUP_TIME)
            }
        }
        //永久结束，不判断
        if (pointRule.endYearTime == "") return
        if (this.endTime == null) {
            logger.error("积分有效期不允许结束时间小于规则组时间 {}", { JsonUtils.toJson(this) })
            throw LoyaltyException(LoyaltyExceptionCode.POINT_LT_GROUP_TIME)
        }
        if (this.endTime!! < this.startTime) {
            logger.error("规则组不允许开始时间大于结束时间 {}", { JsonUtils.toJson(this) })
            throw LoyaltyException(LoyaltyExceptionCode.GROUP_GT_END_TIME)
        }
        val endCalendar = Calendar.getInstance()
        //获取规则结束年份
        endCalendar.set(Calendar.YEAR, pointRule.endYearTime!!.toInt())
        //获取规则结束月
        endCalendar.set(Calendar.MONTH, pointRule.endMonthTime!!.toInt() - 1)
        //获取规则结束日
        endCalendar.set(Calendar.DAY_OF_MONTH, pointRule.endDayTime!!.toInt())

        if (!pointRule.startYearTime.isNullOrEmpty()) {
            if (this.dateCompare(startCalendar.time, endCalendar.time)) {
                logger.error("规则组不允许开始时间大于结束时间 {}", { JsonUtils.toJson(this) })
                throw LoyaltyException(LoyaltyExceptionCode.POINT_LT_END_TIME)
            }
        }
        if (this.dateCompare(this.endTime!!, endCalendar.time)) {
            logger.error("积分有效期不允许结束时间小于规则组时间 {}", { JsonUtils.toJson(this) })
            throw LoyaltyException(LoyaltyExceptionCode.POINT_LT_GROUP_TIME)
        }
    }

    /**比较年月日*/
    fun dateCompare(date1: Date, date2: Date): Boolean {
        val dateFormat = SimpleDateFormat("yyyyMMdd")
        return if (dateFormat.format(date1).toInt() > dateFormat.format(date2).toInt()) true else return false
    }
}