package com.shuyun.loyalty.manager.resource

import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.epassport.sdk.register.RequiresPermissions
import com.shuyun.loyalty.manager.repository.ImportPointOrGradeRepository
import com.shuyun.loyalty.manager.service.ModelInitService
import com.shuyun.loyalty.manager.service.ModelType
import com.shuyun.loyalty.manager.service.PlanToolService
import com.shuyun.loyalty.manager.service.SubjectService
import com.shuyun.loyalty.service.repository.*
import com.shuyun.loyalty.service.service.PlanBaseService
import com.shuyun.loyalty.service.service.SubjectBaseService
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import java.text.MessageFormat
import javax.validation.Valid

@RestController
@RequestMapping("/update/loyalty/version")
@RequiresPermissions(allowAuthenticated = true)
class UpdateVersionResource {

    private val logger = LogManager.getLogger(UpdateVersionResource::class.java)


    @Autowired
    private lateinit var planToolService: PlanToolService

    @Operation(summary = "模型拆分迁移旧有积分账户数据", tags = ["升级微服务 API"])
    @PostMapping("/transfer/point/data/{accountId}")
    fun transferPointData(@Parameter(description = "积分账户ID") @PathVariable("accountId") accountId: Long) {
        DmPoolFactory.execute { sdk ->
            sdk.execute(
                MessageFormat.format(
                    "insert into data.loyalty.member.account.Point$accountId (id, {0}, member) " +
                            "select id,{0},memberId from data.loyalty.member.Point where pointPlanId = $accountId",
                    "planId,pointPlanId,memberId,subjectFqn,version,point,modified,created"
                ),
                mutableMapOf()
            )
        }
        DmPoolFactory.execute { sdk ->
            sdk.execute(
                MessageFormat.format(
                    "insert into data.loyalty.member.account.point.Log$accountId (id, {0}, member) " +
                            "select id,{0},memberId from data.loyalty.member.point.Log where pointPlanId = $accountId",
                    "planId,pointPlanId,memberId,subjectFqn,point,created"
                ),
                mutableMapOf()
            )
        }
        DmPoolFactory.execute { sdk ->
            sdk.execute(
                MessageFormat.format(
                    "insert into data.loyalty.member.account.PointRecord$accountId (id, {0},memberPoint) " +
                            "select id,{0},memberPointId from data.loyalty.member.PointRecord where pointPlanId = $accountId",
                    "planId,pointPlanId,memberPointId,memberId,subjectFqn,point,recordType,recordSourceDetail,effectiveDate,overdueDate,extralInfo,recordDetail,`desc`,fromStatus,operator,operatorId,modified,created,channel,key,ruleGroup,changeMode,planName,pointPlanName,traceId"
                ),
                mutableMapOf()
            )
        }
        DmPoolFactory.execute { sdk ->
            sdk.execute(
                MessageFormat.format(
                    "insert into data.loyalty.member.account.FrozenPoint$accountId (id, {0},memberPoint) " +
                            "select id,{0},memberPointId from data.loyalty.member.FrozenPoint where pointPlanId = $accountId",
                    "planId,pointPlanId,memberPointId,memberId,subjectFqn,point,fromStatus,gainStatementId,created"
                ),
                mutableMapOf()
            )
        }
        DmPoolFactory.execute { sdk ->
            sdk.execute(
                MessageFormat.format(
                    "insert into data.loyalty.member.account.GainStatement$accountId (id, {0},memberPoint) " +
                            "select id,{0},memberPointId from data.loyalty.member.GainStatement where pointPlanId = $accountId",
                    "planId,pointPlanId,memberPointId,memberId,subjectFqn,point,`status`,eventTypeId,traceId,effectiveDate,overdueDate,modified,created,ruleGroupId,occurrenceTs"
                ),
                mutableMapOf()
            )
        }
        DmPoolFactory.execute { sdk ->
            sdk.execute(
                MessageFormat.format(
                    "insert into data.loyalty.member.account.ValidStatement$accountId (id, {0},memberPoint,gainStatement) " +
                            "select id,{0},memberPointId,gainStatementId from data.loyalty.member.ValidStatement where pointPlanId = $accountId",
                    "planId,pointPlanId,memberPointId,memberId,subjectFqn,point,gainStatementId,effectiveDate,overdueDate,fromStatus,modified,created"
                ),
                mutableMapOf()
            )
        }
        DmPoolFactory.execute { sdk ->
            sdk.execute(
                MessageFormat.format(
                    "insert into data.loyalty.member.account.FrozenStatement$accountId (id, {0},memberPoint,memberFrozenPoint) " +
                            "select id,{0},memberPointId,frozenId from data.loyalty.member.FrozenStatement where pointPlanId = $accountId",
                    "planId,pointPlanId,memberPointId,memberId,subjectFqn,point,gainStatementId,effectiveDate,overdueDate,fromStatus,frozenId,modified,created"
                ),
                mutableMapOf()
            )
        }

    }



    @Operation(summary = "彻底删除一个计划", tags = ["升级微服务 API"])
    @DeleteMapping("/plan/{planId}")
    fun deletePlan(
        @Parameter(description = "计划ID") @PathVariable("planId") planId: Long,
        @Parameter(description = "删除用户数据模型") @RequestParam("onlyDeleteUserModel") onlyDeleteUserModel: Boolean? = false) {
        planToolService.physicalDelete(planId, onlyDeleteUserModel ?: false)
    }



    data class PlanNameModel(var names: List<String>? = null)

    @Operation(summary = "清除无用计划(测试专用，不允许其他人员使用)", tags = ["升级微服务 API"])
    @PostMapping("/removeUselessPlan")
    fun removeUselessPlan(@Valid @RequestBody planNameModel: PlanNameModel) {

        val planBaseService = ApplicationContextHolder.getBean(PlanBaseService::class.java)
        val subjectService = ApplicationContextHolder.getBean(SubjectService::class.java)
        val subjectBaseService = ApplicationContextHolder.getBean(SubjectBaseService::class.java)
        val planRepository = ApplicationContextHolder.getBean(PlanRepository::class.java)
        val subjectRepository = ApplicationContextHolder.getBean(SubjectRepository::class.java)
        val gradeHierarchyRepository = ApplicationContextHolder.getBean(GradeHierarchyRepository::class.java)
        val gradeDefinitionRepository = ApplicationContextHolder.getBean(GradeDefinitionRepository::class.java)
        val eventTypeRepository = ApplicationContextHolder.getBean(EventTypeRepository::class.java)
        val customizedPropertyRepository = ApplicationContextHolder.getBean(CustomizedPropertyRepository::class.java)
        val propertyTemplateRepository = ApplicationContextHolder.getBean(PropertyTemplateRepository::class.java)
        val pointAccountTypeRepository = ApplicationContextHolder.getBean(PointAccountTypeRepository::class.java)
        val specialListConfigRepository = ApplicationContextHolder.getBean(SpecialListConfigRepository::class.java)
        val importFileConfigRepository = ApplicationContextHolder.getBean(ImportFileConfigRepository::class.java)
        val importPointOrGradeRepository = ApplicationContextHolder.getBean(ImportPointOrGradeRepository::class.java)
        val modelInitService = ApplicationContextHolder.getBean(ModelInitService::class.java)

        logger.debug("删除计划开始, ${JsonUtils.toJson(planNameModel)}")

        val pointAccountTypeIdList = ArrayList<Long>()
        val gradeHierarchyIdList = ArrayList<Long>()

        planNameModel.names?.forEach {
            planRepository.findByName(it).forEach { plan ->

                subjectBaseService.findDetailByPlanVersionId(plan.versionId!!).forEach {
                    logger.debug("删除模型和引用开始")
                    it.pointAccountTypeList!!.forEach { pointAccountType ->
                        if (!pointAccountTypeIdList.contains(pointAccountType.id)) {
                            pointAccountTypeIdList.add(pointAccountType.id!!)
                            modelInitService.planModelDelete(pointAccountType.id!!, ModelType.POINT, it.dataType!!)
                        }
                    }

                    it.gradeHierarchyList!!.forEach { gradeHierarchy ->
                        if (!gradeHierarchyIdList.contains(gradeHierarchy.id)) {
                            gradeHierarchyIdList.add(gradeHierarchy.id!!)
                            modelInitService.planModelDelete(gradeHierarchy.id!!, ModelType.GRADE, it.dataType!!)
                        }
                    }
                    logger.debug("删除模型和引用结束")

                    try {
                        modelInitService.deleteGroup(it.eventTypeList!!)
                    } catch (e: Throwable) {
                        logger.error("删除规则组异常", e)
                    }

                    subjectService.delete(it)
                }

                planBaseService.delete(plan)

                planRepository.deleteByFilter(JsonUtils.toJson(mapOf(Pair("disabled", true))))
                subjectRepository.deleteByFilter(JsonUtils.toJson(mapOf(Pair("disabled", true))))
                pointAccountTypeRepository.deleteByFilter(JsonUtils.toJson(mapOf(Pair("disabled", true))))
                gradeHierarchyRepository.deleteByFilter(JsonUtils.toJson(mapOf(Pair("disabled", true))))
                gradeDefinitionRepository.deleteByFilter(JsonUtils.toJson(mapOf(Pair("disabled", true))))
                customizedPropertyRepository.deleteByFilter(JsonUtils.toJson(mapOf(Pair("disabled", true))))
                eventTypeRepository.deleteByFilter(JsonUtils.toJson(mapOf(Pair("disabled", true))))
                propertyTemplateRepository.deleteByFilter(JsonUtils.toJson(mapOf(Pair("disabled", true))))

                specialListConfigRepository.deleteByFilter(JsonUtils.toJson(mapOf(Pair("planId", plan.id))))
                importPointOrGradeRepository.findAll().filter { it.planId == plan.id }
                    .forEach { importPointOrGradeRepository.deleteById(it.id!!) }
                importFileConfigRepository.deleteByPlanId(plan.id!!)
                logger.debug("删除计划结束, ${JsonUtils.toJson(planNameModel)}")
            }
        }

    }

}

