package com.shuyun.loyalty.manager.vo

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.CalcSortTypeEnum
import com.shuyun.loyalty.service.meta.EventOccasionEnum
import com.shuyun.loyalty.service.meta.FxReferenceTime
import com.shuyun.loyalty.service.model.I18nPayload
import com.shuyun.loyalty.service.model.Plan
import com.shuyun.loyalty.service.model.Subject
import com.shuyun.loyalty.service.util.ModelInitUtil.copyPropertiesIgnoreNull
import com.shuyun.loyalty.service.util.ValidatorUtils.nameListConflict
import com.shuyun.pip.component.json.JsonUtils
import io.swagger.v3.oas.annotations.media.Schema
import javax.persistence.EnumType
import javax.persistence.Enumerated
import javax.validation.Valid
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotNull

/**
 * 计划
 */
@Schema(title = "PlanVo")
data class PlanVo(

    @Schema(title = "计划ID")
    var id: Long? = null,
    @Schema(title = "计划名称", required = true)
    @field:NotBlank(message = "计划名称不能为空")
    var name: String? = null,
    @field:NotBlank(message = "计划描述不能为空")
    @Schema(title = "描述", required = true)
    var description: String? = null,
    @Schema(title = "执行排序")
    @field:NotNull(message = "计划执行排序不能为空")
    var sortType: CalcSortTypeEnum? = null,
    @Schema(title = "主体")
    @field:Valid
    var subjectList: List<SubjectVo>? = null, //主体

    @Schema(title = "特殊条件名称")
    var specialConditionName: String? = null,

    @Schema(title = "标签条件名称")
    var specialConditionTagName: String? = null,

    @Schema(title = "人群条件名称")
    var specialConditionCohortName: String? = null,

    @Schema(title = "状态")
    @Enumerated(EnumType.STRING)
    var specialConditionStatus: EnableStatusEnum? = null,

    @Schema(title = "适用场景")
    var specialConditionOccasion: List<EventOccasionEnum>? = null,

    @Schema(title = "等级参考时间")
    @Enumerated(EnumType.STRING)
    var gradeReferenceTime: FxReferenceTime? = null,

    @Schema(title = "积分参考时间")
    @Enumerated(EnumType.STRING)
    var pointReferenceTime: FxReferenceTime? = null,

    @Schema(title = "勋章参考时间")
    @Enumerated(EnumType.STRING)
    var medalReferenceTime: FxReferenceTime? = null,

    @Schema(title = "是否开启积分上限规则")
    var sendLimitRuleSwitch: Boolean? = null,

    ) : I18nPayload() {

    /**根据vo初始化model*/
    fun initPlanFromVo(plan: Plan) {
        copyPropertiesIgnoreNull(this, plan)
        if (!this.specialConditionOccasion.isNullOrEmpty()) {
            plan.specialConditionOccasionString = JsonUtils.toJson(this.specialConditionOccasion)
        }
        plan.subjectList = this.subjectList?.map { subjectVo ->
            val subject = Subject()
            subjectVo.initSubjectFromVo(subject)
            subject
        }
    }

    /**校验计划下各实体名称重复*/
    fun validateNameConflict() {
        var conflict = nameListConflict(this.subjectList?.map { it.name!!.trim() })
        if (conflict) {
            throw LoyaltyException(LoyaltyExceptionCode.SUBJECT_REPEATED, "主体已经存在，不允许重复添加")
        }

        conflict = nameListConflict(this.subjectList?.map { it.dataType!! })
        if (conflict) {
            throw LoyaltyException(LoyaltyExceptionCode.DATA_TYPE_REPEATED, "主体对应dataType重复")
        }

        this.subjectList?.forEach {
            it.validateNameConflict()
        }
    }

}