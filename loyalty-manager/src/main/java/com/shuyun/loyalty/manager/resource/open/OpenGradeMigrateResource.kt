package com.shuyun.loyalty.manager.resource.open

import com.shuyun.loyalty.entity.api.request.MemberGradeImportRequest
import com.shuyun.loyalty.service.util.MDCUtils
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.extensions.Extension
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty
import org.apache.logging.log4j.LogManager
import org.springframework.web.bind.annotation.*
import javax.annotation.Resource
import javax.validation.Valid
import javax.validation.constraints.NotBlank

@RestController
@RequestMapping("/open/grade/migration")
class OpenGradeMigrateResource {

    @Resource
    private lateinit var loyaltyFacadeFeignApi: LoyaltyFacadeFeignApi

    private val logger = LogManager.getLogger(OpenGradeMigrateResource::class.java)

    @Operation(summary = "导入等级变更元日志", tags = ["等级导入 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/importMetaLog/{migrationId}")
    fun import(@PathVariable("migrationId") migrationId: String,
               @Parameter(name = "planId", description = "忠诚度计划ID") @RequestParam("planId") planId: Long,
               @Parameter(name = "gradeHierarchyId", description = "等级体系ID") @RequestParam("gradeHierarchyId") gradeHierarchyId: Long,
               @RequestBody @Valid memberGradeImportRequests: List<MemberGradeImportRequest>) {

        val start = System.currentTimeMillis()
        logger.info("导入等级开始: {}", memberGradeImportRequests.size)
        loyaltyFacadeFeignApi.saveGradeRecord(MDCUtils.getTraceId(), migrationId, gradeHierarchyId, memberGradeImportRequests)
        logger.info("导入等级完成 耗时: {}ms", (System.currentTimeMillis() - start))
    }


    @Operation(summary = "应用等级变更原始日志", tags = ["等级导入 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/applyMetaLog/{migrationId}")
    fun applyMetaLog(@Valid @NotBlank @PathVariable("migrationId") migrationId: String,
                     @Parameter(name = "planId", description = "忠诚度计划ID") @RequestParam("planId") planId: Long,
                     @Parameter(name = "gradeHierarchyId", description = "等级体系ID") @RequestParam("gradeHierarchyId") gradeHierarchyId: Long): Map<String, Any> {
        return mapOf("status" to "等级操作无需调用此接口")
    }


    @Operation(summary = "查询导入执行结果（暂未实现）", tags = ["等级导入 OPEN API"])
    @GetMapping("/result/{migrationId}")
    fun getResult(@PathVariable("migrationId") migrationId: String,
                  @Parameter(name = "planId", description = "忠诚度计划ID") @RequestParam("planId") planId: Long,
                  @Parameter(name = "gradeHierarchyId", description = "等级体系ID") @RequestParam("gradeHierarchyId") gradeHierarchyId: Long): Map<String, Any> {
        return mapOf("status" to "暂未实现")
    }

}