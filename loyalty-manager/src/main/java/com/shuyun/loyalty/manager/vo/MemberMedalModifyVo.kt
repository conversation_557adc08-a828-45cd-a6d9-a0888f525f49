package com.shuyun.loyalty.manager.vo

import com.shuyun.loyalty.entity.api.request.MemberMedalModifyBaseStruct
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.extension.set
import com.shuyun.loyalty.service.meta.TimeTypeEnum
import io.swagger.v3.oas.annotations.media.Schema
import java.util.*

@Schema(title = "MemberMedalModifyVo")
class MemberMedalModifyVo: MemberMedalModifyBaseStruct() {

    @Schema(title = "结束年类型 RELATIVE_TIME:相对时间，ABSOLUTE_TIME:绝对时间", type = "String")
    var endYearType: TimeTypeEnum? = null

    @Schema(title = "结束年时间", type = "String")
    var endYearTime: Int? = null

    @Schema(title = "结束月类型 RELATIVE_TIME:相对时间，ABSOLUTE_TIME:绝对时间", type = "String")
    var endMonthType: TimeTypeEnum? = null

    @Schema(title = "结束月时间", type = "String")
    var endMonthTime: Int? = null

    @Schema(title = "结束日类型 RELATIVE_TIME:相对时间，ABSOLUTE_TIME:绝对时间", type = "String")
    var endDayType: TimeTypeEnum? = null

    @Schema(title = "结束日时间", type = "String")
    var endDayTime: Int? = null

    /**计算根据当前时间计算出来的勋章过期时间,如果永久有效，返回null，如果计算出来的时机已经过期，则报错*/
    fun calculateOverdueTime(): Date? {
        if(null == endDayTime) return null
        val calendar = Calendar.getInstance()
        val now = calendar.time
        calendar.set(endYearType!!, Calendar.YEAR, endYearTime!!)
        calendar.set(endMonthType!!, Calendar.MONTH, endMonthTime!!)
        if(endDayTime != -1) calendar.set(endDayType!!, Calendar.DATE, endDayTime!!)
        calendar.set(Calendar.HOUR_OF_DAY,23)
        calendar.set(Calendar.MINUTE,59)
        calendar.set(Calendar.SECOND,59)

        if(endDayTime == -1) {
            val last = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
            calendar.set(Calendar.DAY_OF_MONTH, last)
        }

        val overdue = calendar.time
        if(overdue.before(now)){
            throw LoyaltyException(LoyaltyExceptionCode.OVERATE_LE_NOW)
        }
        return overdue
    }

}