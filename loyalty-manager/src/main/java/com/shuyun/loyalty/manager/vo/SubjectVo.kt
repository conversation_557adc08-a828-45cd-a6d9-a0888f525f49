package com.shuyun.loyalty.manager.vo

import com.fasterxml.jackson.databind.node.ObjectNode
import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.model.*
import com.shuyun.loyalty.service.util.ModelInitUtil.copyPropertiesIgnoreNull
import com.shuyun.loyalty.service.util.ValidatorUtils.nameListConflict
import com.shuyun.pip.component.json.JsonUtils
import io.swagger.v3.oas.annotations.media.Schema
import org.apache.commons.lang.StringUtils
import org.apache.logging.log4j.LogManager
import javax.persistence.Transient
import javax.validation.Valid
import javax.validation.constraints.NotBlank

@Schema(title = "SubjectVo")
data class SubjectVo(

    @Schema(title = "名称")
        @field:NotBlank(message = "主体名称不能为空")
        var name: String? = null,//主体名称

    @Schema(title = "后端使用计划ID，前端勿用")
        var planVersionId: Long? = null,//主体归属计划的ID

    @Schema(title = "Data Type")
        @field:NotBlank(message = "主体Data Type不能为空")
        var dataType: String? = null,

    @Schema(title = "主体ID")
        var id: Long? = null,

    @Schema(title = "属性")
        @field:Valid
        var customizedPropertyList: List<CustomizedPropertyVo>? = null,//属性

    @Schema(title = "属性模板")
        @field:Valid
        var propertyTemplateList: List<PropertyTemplateVo>? = null,

    @Schema(title = "时机")
        @field:Valid
        var eventTypeList: List<EventTypeVo>? = null,//时机

    @Schema(title = "状态")
        var status: EnableStatusEnum? = null,

    @Schema(title = "前端排序号")
        var sort: Int? = null,

    @Schema(title = "账户类型")
        @field:Valid
        var pointAccountTypeList: List<PointAccountTypeVo>? = null,//账户类型

    @Schema(title = "等级体系")
        @field:Valid
        var gradeHierarchyList: List<GradeHierarchyVo>? = null,//等级体系

    @Schema(title = "勋章体系")
        @field:Valid
        var medalHierarchyList: List<MedalHierarchyVo>? = null,// 勋章体系

    @Schema(title = "合并模型")
        var mergeFqn: String? = null,

    @Schema(title = "是否合卡")
        var hasMerge: Boolean? = null

) : I18nPayload() {
    companion object {
        @Transient
        private val logger = LogManager.getLogger(SubjectVo::class.java)
    }

    /**根据vo初始化model*/
    fun initSubjectFromVo(subject: Subject) {
        copyPropertiesIgnoreNull(this, subject)
        subject.eventTypeList = this.eventTypeList?.map { eventTypeVo ->
            val eventType = EventType()
            eventTypeVo.initEventTypeFromVo(eventType)
            eventType.subjectFqn = subject.dataType
            eventType
        }
        var exception: Exception? = null
        subject.customizedPropertyList = this.customizedPropertyList?.map { customizedPropertyVo ->
            val customizedProperty = CustomizedProperty()
            try {
                customizedPropertyVo.initCustomizedPropertyFromVo(customizedProperty, subject.dataType!!)
            } catch (e: Exception) {
                exception = e
            }
            customizedProperty
        }
        exception?.let {
            throw it
        }
        subject.propertyTemplateList = this.propertyTemplateList?.map { propertyTemplateVo ->
            val propertyTemplate = PropertyTemplate()
            try {
                propertyTemplateVo.initPropertyTemplateFromVo(propertyTemplate)
            } catch (e: Exception) {
                exception = e
            }
            propertyTemplate
        }
        exception?.let {
            throw it
        }
        subject.pointAccountTypeList = this.pointAccountTypeList?.map { pointAccountTypeVo ->
            val pointAccountType = PointAccountType()
            pointAccountTypeVo.initPointAccountTypeFromVo(pointAccountType)

            pointAccountType.remindConfigList = pointAccountTypeVo.remindConfigList?.map { remindConfigVo ->
                val remindConfig = RemindConfig()
                remindConfigVo.initRemindConfigFromVo(remindConfig)
                remindConfig
            }

            pointAccountType
        }
        subject.gradeHierarchyList = this.gradeHierarchyList?.map { gradeHierarchyVo ->
            val gradeHierarchy = GradeHierarchy()
            gradeHierarchyVo.initGradeHierarchyFromVo(gradeHierarchy)

            gradeHierarchy.remindConfigList = gradeHierarchyVo.remindConfigList?.map { remindConfigVo ->
                val remindConfig = RemindConfig()
                remindConfigVo.initRemindConfigFromVo(remindConfig)
                remindConfig
            }

            gradeHierarchy
        }
        subject.medalHierarchyList = this.medalHierarchyList?.map { medalHierarchyVo ->
            val medalHierarchy = MedalHierarchy()
            medalHierarchyVo.initMedalHierarchyFromVo(medalHierarchy)

            medalHierarchy
        }
    }

    /**校验主体下的各实体名称重复*/
    fun validateNameConflict() {
        var conflict = nameListConflict(this.customizedPropertyList?.map { it.name!!.trim() })
        if (conflict) {
            throw LoyaltyException(LoyaltyExceptionCode.SUBJECT_REPEATED)
        }

        conflict = nameListConflict(this.propertyTemplateList?.map { it.name!!.trim() })
        if (conflict) {
            throw LoyaltyException(LoyaltyExceptionCode.SUBJECT_TMP_REPEATED)
        }

        conflict = nameListConflict(this.pointAccountTypeList?.map { it.name!!.trim() })
        if (conflict) {
            throw LoyaltyException(LoyaltyExceptionCode.POINT_ACCOUNT_TYPE_REPEATED)
        }

        conflict = nameListConflict(this.eventTypeList?.map { it.name!!.trim() })
        if (conflict) {
            throw LoyaltyException(LoyaltyExceptionCode.EVENT_TYPE_REPEATED)
        }

//        conflict = nameListConflict(this.eventTypeList?.map { it.eventStream!!.trim() })
//        if(conflict){
//            throw LoyaltyException(LoyaltyExceptionCode.EVENT_TYPE_TO_FQN_REPEATED)
//        }

        conflict = nameListConflict(this.gradeHierarchyList?.map { it.name!!.trim() })
        if (conflict) {
            throw LoyaltyException(LoyaltyExceptionCode.GRADE_HIERARCHY_REPEATED)
        }

        conflict = nameListConflict(this.medalHierarchyList?.map { it.name!!.trim() })
        if (conflict)
            throw LoyaltyException(LoyaltyExceptionCode.MEDAL_HIERARCHY_REPEATED)

        this.eventTypeList?.forEach {
            it.validateNameConflict()
        }
        this.gradeHierarchyList?.forEach {
            it.validateNameConflict()
        }
        this.medalHierarchyList?.forEach {
            it.validateNameConflict()
        }
    }

    /**校验事件流是否与此时机匹配*/
    fun validateEventStreamValid(eventStreamMetas: EventStreamMetas) {
        if (eventStreamMetas.fqn == "event.loyalty.grade.over.event"
            || eventStreamMetas.fqn == "event.loyalty.grade.merge.event"
            || eventStreamMetas.fqn == "event.loyalty.medal.over.event")
            return
        logger.debug("eventStreamMetas dataType:$dataType, schema:{}", {JsonUtils.toJson(eventStreamMetas)})
        val reference = eventStreamMetas.getReferencePathJson()
        if (StringUtils.isEmpty(reference)) {
            throw LoyaltyException(LoyaltyExceptionCode.EVENT_NOT_MATCH)
        }
        logger.debug("reference json :${JsonUtils.toJson(reference)}")
        val map = JsonUtils.parse(reference, ObjectNode::class.java)
        if (!map.has(dataType)) {
            throw LoyaltyException(LoyaltyExceptionCode.EVENT_NOT_MATCH)
        }
    }
}