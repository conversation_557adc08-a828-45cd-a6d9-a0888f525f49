package com.shuyun.loyalty.manager.migration

import com.shuyun.lite.client.PassportClientFactory
import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.sdk.Property
import org.springframework.boot.SpringApplication
import org.springframework.boot.SpringApplicationRunListener
import org.springframework.context.ConfigurableApplicationContext
import java.time.Duration

class MigrationRunListener(val application: SpringApplication, val args: Array<String?>): SpringApplicationRunListener {

    companion object {
        private const val DEV_MODE_KEY = "devMode"
        var MIGRATION_IS_OK = false
    }

    override fun contextLoaded(context: ConfigurableApplicationContext)  {
        registerService()
        if (!Property.getSysOrEnv(DEV_MODE_KEY, false)) {
            // 先执行
            IbatisMigration().execute()
            MetadataMigration().execute()
        }
    }

    private fun registerService() {
        val clientId = Property.getSysOrEnv("service.clientId", GlobalContext.serviceName())
        val passportBuilder = PassportClientFactory.passportBuilder().appKey(clientId)
        val passportClient = passportBuilder.build()
        passportClient.registerClient(clientId, GlobalContext.serviceName())
    }

    override fun ready(context: ConfigurableApplicationContext?, timeTaken: Duration?) {
        if (!Property.getSysOrEnv(DEV_MODE_KEY, false)) {
            // 后执行
            FlywayMigration().execute()
            MIGRATION_IS_OK = true
        } else {
            MIGRATION_IS_OK = true
        }
    }
}