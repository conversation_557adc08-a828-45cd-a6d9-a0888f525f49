package com.shuyun.loyalty.manager.resource.open

import com.shuyun.loyalty.entity.api.constants.OpenFSMPointEvent
import com.shuyun.loyalty.entity.api.request.MemberPointModifyRequest
import com.shuyun.loyalty.entity.api.request.MemberPointUpdateValidRequest
import com.shuyun.loyalty.entity.api.request.PointTransactionFeignRequest
import com.shuyun.loyalty.entity.dto.MemberPointDeductRequest
import com.shuyun.loyalty.entity.dto.MemberPointSendResponse
import com.shuyun.loyalty.service.util.MDCUtils
import com.shuyun.loyalty.service.util.ModelInitUtil
import com.shuyun.pip.component.json.JsonUtils
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.extensions.Extension
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty
import org.apache.logging.log4j.LogManager
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.math.BigDecimal
import javax.annotation.Resource
import javax.validation.Valid

@RestController
@RequestMapping("/open/pointTransaction")
class OpenPointTransactionResource {

    private val log = LogManager.getLogger(OpenPointTransactionResource::class.java)

    @Resource
    private lateinit var loyaltyFacadeFeignApi: LoyaltyFacadeFeignApi
    @Operation(summary = "同步变更会员积分", tags = ["事物-积分openApi"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/modify")
    fun modifyPoint(@Valid @RequestBody modify: MemberPointModifyRequest): MemberPointSendResponse? {
        val start = System.currentTimeMillis()
        log.info("事物接口调用同步变更有效积分开始:{}", { JsonUtils.toJson(modify) })

        val modifyPoint = when (modify.recordType) {
            OpenFSMPointEvent.SEND -> {
                val buildSendRequest = modify.buildSendRequest()
                buildSendRequest.tx = true
                ModelInitUtil.copyPropertiesIgnoreNull(modify, buildSendRequest)
                loyaltyFacadeFeignApi.send(MDCUtils.getTraceId(), buildSendRequest)
            }

            OpenFSMPointEvent.DEDUCT -> {
                val buildDeductRequest = modify.buildDeductRequest()
                buildDeductRequest.tx = true
                loyaltyFacadeFeignApi.deduct(MDCUtils.getTraceId(), buildDeductRequest)
                null
            }

            else -> { null
            }
        }

        val end = System.currentTimeMillis()
        log.info("耗时:${end - start}ms, 事物接口调用同步变更积分结束")
        return modifyPoint
    }


    @Operation(summary = "消耗冻结会员积分", tags = ["事物-积分openApi"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/updateValidFrozenPoint")
    fun updateValidRequest(@Valid @RequestBody updateValidMemberPoint: MemberPointUpdateValidRequest) {
        val start = System.currentTimeMillis()
        log.info("事物接口同步变更 冻结消耗 开始:{}", { JsonUtils.toJson(updateValidMemberPoint) })
        val buildUseFreezeFreezeRequest = buildUseFreezeFreezeRequest(updateValidMemberPoint)
        buildUseFreezeFreezeRequest.tx = true
        loyaltyFacadeFeignApi.frozenDeduct(MDCUtils.getTraceId(), buildUseFreezeFreezeRequest)
        val end = System.currentTimeMillis()
        log.info("耗时:${end - start}ms, 事物接口同步变更 冻结消耗 结束")
    }


    @Operation(summary = "同步反向操作积分", tags = ["事物-积分openApi"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/reverse")
    fun reversePoint(@Valid @RequestBody request: PointTransactionFeignRequest) {
        val start = System.currentTimeMillis()
        log.info("事物接口逆向调用同步变更有效积分开始:{}", { JsonUtils.toJson(request) })
        request.uniqueId = request.triggerId
        loyaltyFacadeFeignApi.pointRevert(MDCUtils.getTraceId(),request)
        val end = System.currentTimeMillis()
        log.info("耗时:${end - start}ms, 事物接口逆向调用同步变更有效积分结束")
    }

    fun buildUseFreezeFreezeRequest(modify: MemberPointUpdateValidRequest): MemberPointDeductRequest {
        val request = MemberPointDeductRequest()
        request.point = modify.point ?: BigDecimal.ZERO
        request.pointAccountId = modify.pointAccountId!!
        request.desc = modify.desc
        request.changeMode = modify.changeMode
        request.businessId = modify.openTraceId!!
        request.channelType = modify.channelType!!
        request.memberId = modify.memberId!!
        request.shopId = modify.shopId
        request.KZZD1 = modify.KZZD1
        request.KZZD2 = modify.KZZD2
        request.KZZD3 = modify.KZZD3
        request.actionId = modify.actionId
        request.actionName = modify.actionName
        request.actionNodeId = modify.actionNodeId
        request.actionNodeName = modify.actionNodeName
        request.lockWaitTime = modify.lockWaitTime
        request.autoFillShopId = modify.autoFillShopId
        return request
    }
}