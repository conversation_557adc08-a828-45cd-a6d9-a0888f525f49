package com.shuyun.loyalty.manager.service

import com.fasterxml.jackson.databind.node.ObjectNode
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.shuyun.fx.factory.DslEpp
import com.shuyun.fx.function.SelectorFunction
import com.shuyun.fx.function.SelectorValueFunction
import com.shuyun.lite.annotation.DbNodeSelector
import com.shuyun.loyalty.entity.api.request.BudgetGradeRequest
import com.shuyun.loyalty.entity.api.response.*
import com.shuyun.loyalty.manager.service.audit.AuditGradeRuleGroupService
import com.shuyun.loyalty.manager.util.Constants.keyId
import com.shuyun.loyalty.manager.util.ListCompareUtil.initModifyDataByCompareList
import com.shuyun.loyalty.manager.util.WAY_TO_HANDLE_DATA
import com.shuyun.loyalty.manager.vo.GradeRuleGroupVoList
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.fx.SimpleParseVisitor
import com.shuyun.loyalty.service.meta.GradeRuleGroupTypeEnum
import com.shuyun.loyalty.service.meta.ModeTypeEnum
import com.shuyun.loyalty.service.meta.RuleGroupStatusEnum
import com.shuyun.loyalty.service.meta.TypeEnum
import com.shuyun.loyalty.service.model.GradeRule
import com.shuyun.loyalty.service.model.GradeRuleGroup
import com.shuyun.loyalty.service.repository.GradeDefinitionRepository
import com.shuyun.loyalty.service.service.*
import com.shuyun.loyalty.service.util.DateUtils
import com.shuyun.loyalty.service.util.ExpressionIdentUtil
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.ZonedDateTime
import java.util.*
import kotlin.concurrent.thread

@Service
class GradeRuleGroupService {

    private val logger = LogManager.getLogger(GradeRuleGroupService::class.java)

    @Autowired
    private lateinit var gradeRuleGroupBaseService: GradeRuleGroupBaseService

    @Autowired
    private lateinit var gradeRuleService: GradeRuleService

    @Autowired
    private lateinit var gradeRuleBaseService: GradeRuleBaseService

    @Autowired
    private lateinit var gradeDefinitionBaseService: GradeDefinitionBaseService

    @Autowired
    private lateinit var auditGradeRuleGroupService: AuditGradeRuleGroupService

    @Autowired
    private lateinit var i18nModelService: I18nModelService



    /**等级概览*/
    fun findViewList(gradeHierarchyId: Long, groupName: String?, groupStatusSet: Set<RuleGroupStatusEnum>): List<GradeRuleGroup> {
        val groupList = ArrayList<GradeRuleGroup>()
        groupList.addAll(gradeRuleGroupBaseService.findByGradeHierarchyId(gradeHierarchyId, Date()))
        if (groupList.isEmpty()) return groupList
        val max = groupList.maxOf { it.createTime!! }
        val plan = LoyaltyPrograms.findPlanByGradeHierarchyId(gradeHierarchyId, DateUtils.date2DateTime(max))
        val ts = ArrayList<Thread>()
        for (it in groupList) {
            if (it.eventTypeId != -1L) {
                val eventType = plan?.subjectList?.firstOrNull()?.eventTypeList?.firstOrNull { x -> x.id == it.eventTypeId }
                it.eventTypeName = eventType?.name
            }
            val t = thread { it._i18nPayload = i18nModelService.findI18nPayload(GradeRuleGroup.I18N_GROUP__NAME, it.id) }
            ts.add(t)
        }
        ts.forEach { it.join() }
        return groupList.filter {
            groupStatusSet.contains(it.status) && if (!groupName.isNullOrBlank()) it.name == groupName else true
        }.sortedByDescending { it.createTime }
    }

    /**根据等级Id获取规则组列表*/
    fun findList(gradeDefinitionId: Long): List<GradeRuleGroup> {
        val groups = gradeRuleGroupBaseService.findByGradeDefinitionId(gradeDefinitionId, Date()).sortedBy {
            it.sort
        }
        for (group in groups) {
            group._i18nPayload = i18nModelService.findI18nPayload(GradeRuleGroup.I18N_GROUP__NAME, group.id)
        }
        return groups
    }

    fun findByIdAndDisabled(id: Long) = gradeRuleGroupBaseService.findByIdAndDisabled(id)

    /**保存一个等级下的规则组列表*/
    fun save(gradeRuleGroupVoList: GradeRuleGroupVoList, oldName: String?) {
        val gradeDefinitionId = gradeRuleGroupVoList.validate()

        val oldRuleGroupList = if (gradeDefinitionId == -1L) {
            val gradeHierarchyId = gradeRuleGroupVoList.fetchGradeHierarchyId()
            gradeRuleGroupBaseService.findByGradeDefinitionId(gradeHierarchyId, gradeDefinitionId, Date())
        } else {
            gradeRuleGroupBaseService.findByGradeDefinitionId(gradeDefinitionId, Date())
        }
        var i = 0
        val newRuleGroupList = gradeRuleGroupVoList.ruleGroupList!!.map {
            val group = it.initModelFromVo()
            group.sort = i++
            group
        }

        initModifyDataByCompareList(newRuleGroupList, oldRuleGroupList, true, keyId)
                .forEach { (ruleGroup, wayToHandle) ->
                    ruleGroup as GradeRuleGroup
                    when (wayToHandle) {
                        WAY_TO_HANDLE_DATA.TO_ADD -> {
                            gradeRuleGroupVoList.ruleGroupList!!.forEach {
                                val list = gradeRuleGroupBaseService.findIsGroupName(it.gradeHierarchyId!!, it.gradeDefinitionId!!, it.groupType!!, it.name!!)
                                if (list.isNotEmpty()) throw LoyaltyException(LoyaltyExceptionCode.GRADE_GROUP_REPEATED)
                            }
                            ApplicationContextHolder.getBean(GradeRuleGroupService::class.java).insert(ruleGroup)
                            i18nModelService.saveOrUpdate(GradeRuleGroup.I18N_GROUP__NAME, ruleGroup.id!!, ruleGroup._i18nPayload)
                        }
                        WAY_TO_HANDLE_DATA.TO_UPDATE -> {
                            gradeRuleGroupVoList.ruleGroupList!!.forEach {
                                val list = gradeRuleGroupBaseService.findIsGroupName(it.gradeHierarchyId!!, it.gradeDefinitionId!!, it.groupType!!, it.name!!).filter { f -> f.id != it.id }
                                if (list.isNotEmpty()) throw LoyaltyException(LoyaltyExceptionCode.GRADE_GROUP_REPEATED)
                            }
                            ApplicationContextHolder.getBean(GradeRuleGroupService::class.java).update(ruleGroup, oldName!!)
                            i18nModelService.saveOrUpdate(GradeRuleGroup.I18N_GROUP__NAME, ruleGroup.id!!, ruleGroup._i18nPayload)
                        }
                        else -> {}
                    }
                }
    }

    /**删除规则组列表*/
    fun delete(groupId: Long) {
        val gradeRuleGroup = gradeRuleGroupBaseService.findById(groupId, Date())
        auditGradeRuleGroupService.delete(gradeRuleGroup)
        gradeRuleGroup.ruleList?.forEach {
            gradeRuleBaseService.delete(it)
        }
        i18nModelService.delete(GradeRuleGroup.I18N_GROUP__NAME, gradeRuleGroup.id)
    }

    /**插入规则组列表*/
    @Transactional(rollbackFor = [Exception::class])
    fun insert(gradeRuleGroup: GradeRuleGroup) {
        auditGradeRuleGroupService.insert(gradeRuleGroup, gradeRuleGroup.name!!)
        gradeRuleService.insert(gradeRuleGroup)
    }

    /**归档规则组列表*/
    fun file(groupId: Long) {
        val gradeRuleGroup = gradeRuleGroupBaseService.findById(groupId, Date())
        gradeRuleGroup.status = RuleGroupStatusEnum.FILED
        gradeRuleGroupBaseService.file(gradeRuleGroup)
    }

    /**更新规则组列表*/
    @Transactional(rollbackFor = [Exception::class])
    fun update(gradeRuleGroup: GradeRuleGroup, oldName: String) {
        val newRuleList = gradeRuleGroup.ruleList!!.map {
            JsonUtils.parse(JsonUtils.toJson(it), GradeRule::class.java)
        }
        auditGradeRuleGroupService.update(gradeRuleGroup, oldName)
        val oldRuleList = gradeRuleGroupBaseService.findById(gradeRuleGroup.id!!, Date()).ruleList
        logger.debug("new:{}", { JsonUtils.toJson(newRuleList) })
        logger.debug("old:{}", { JsonUtils.toJson(oldRuleList) })
        initModifyDataByCompareList(newRuleList, oldRuleList!!, true, keyId)
                .forEach { (rule, wayToHandle) ->
                    rule as GradeRule
                    when (wayToHandle) {
                        WAY_TO_HANDLE_DATA.TO_ADD -> {
                            gradeRuleBaseService.insert(rule, gradeRuleGroup.gradeDefinitionId!!)
                        }

                        WAY_TO_HANDLE_DATA.TO_UPDATE -> {
                            logger.debug("ruleIs: {}", { JsonUtils.toJson(rule) })
                            gradeRuleBaseService.update(rule, gradeRuleGroup.gradeDefinitionId!!)
                        }
                        else -> {}
                    }
                }
    }


    @DbNodeSelector(DbNodeSelector.Hint.AUTO)
    fun findRunningAndUpgradeList(gradeHierarchyId: Long, modeType: ModeTypeEnum): GradeHierarchyRuleResponse {
        //查询运行中的升级规则组
        val groupList = gradeRuleGroupBaseService.findGradeHierarchyIdAndGroupType(gradeHierarchyId, GradeRuleGroupTypeEnum.UPGRADE).filter { it.startTime!! <= Date() && (it.endTime == null || it.endTime!! >= Date()) }
        val definitionGroupMap = groupList.groupBy { it.gradeDefinitionId }
        val gradeDefinitionList = gradeDefinitionBaseService.findByEnabledHierarchyByVersionId(gradeHierarchyId)

        val gradeRuleResponseList = ArrayList<GradeRuleResponse>()
        val gradeHierarchyRuleResponse = GradeHierarchyRuleResponse()

        definitionGroupMap.forEach { (gradeId, groupIdList) ->

            groupIdList.forEach { gid ->

                gid.ruleList?.filter { it.mode == modeType }?.forEach {

                    //保存规则当前等级
                    val gradeRule = GradeRuleResponse()
                    gradeRule.currentGradeId = gradeId
                    gradeRule.currentGradeName = gradeDefinitionList.find { x -> x.id == gradeId }?.name

                    //保存规则下一个等级
                    val nextGradeDefinition = gradeDefinitionList
                        .filter { x ->
                            x.sort!! > gradeDefinitionList.find { y -> y.id == gradeId }!!.sort!!
                        }
                        .minByOrNull { z -> z.sort!! }
                    gradeRule.nextGradeId = nextGradeDefinition?.id
                    gradeRule.nextGradeName = nextGradeDefinition?.name

                    when (it.mode) {
                        //条件条件模式下的表达式按照json存储
                        ModeTypeEnum.CONDITION -> {
                            val expressionList :List<ExpressionIdentResponse> = Gson().fromJson(it.expression, object : TypeToken<List<ExpressionIdentResponse>>() {}.type)
                            gradeRule.expressionIdentResponseList = expressionList
                        }

                        //高级表达式按照string存储，分为表达式是属性id组成 和 属性名称组成
                        ModeTypeEnum.SENIOR -> {
                            val expression: Map<String, String> = Gson().fromJson(it.expressionDisplayInfo!!, object : TypeToken<Map<String, String>>() {}.type)
                            val expressionStringResponse = ExpressionStringResponse()
                            expressionStringResponse.expressionToIds = expression["condition"].toString()
                            expressionStringResponse.expressionToNames = ExpressionIdentUtil.expressionToCommonName(expression["condition"].toString())
                            gradeRule.expressionStringResponse = expressionStringResponse
                        }
                        else -> {}
                    }
                    gradeRuleResponseList.add(gradeRule)
                }
            }
        }
        gradeHierarchyRuleResponse.data = gradeRuleResponseList
        return gradeHierarchyRuleResponse
    }


    fun budgetGrade(budgetGradeRequest: BudgetGradeRequest):ArrayList<BudgetGradeResponse> {
        val response = ArrayList<BudgetGradeResponse>()
        val subject = ApplicationContextHolder.getBean(SubjectBaseService::class.java)
            .findSubjectByAccountOrHierarchyId(budgetGradeRequest.gradeHierarchyId!!, TypeEnum.GRADE)?: throw LoyaltyException(
            LoyaltyExceptionCode.SUBJECT_NOT_FOUND
        )
        val fqn = subject.dataType
        val objectNode = JsonUtils.convert(mapOf("subject" to fqn, "modelRefKey" to mapOf(fqn to budgetGradeRequest.memberId)),ObjectNode::class.java)

        budgetGradeRequest.targetGradeIds?.forEach { targetGradeId ->
            val budgetGradeResponse = BudgetGradeResponse()
            response.add(budgetGradeResponse)
            budgetGradeResponse.targetGradeId = targetGradeId

            budgetGradeRequest.gradeRuleTypes?.forEach { gradeRecordType ->
                val enum = GradeRuleGroupTypeEnum.valueOf(gradeRecordType.name)
                val groupList = gradeRuleGroupBaseService.findByParams(budgetGradeRequest.gradeHierarchyId!!.toLong(), enum, targetGradeId.toLong()).filter { it.startTime!! <= Date() && (it.endTime == null || it.endTime!! >= Date()) }

                groupList.forEach{ group ->
                    val budgetGradeRuleResponse = BudgetGradeResponse.BudgetGradeRuleResponse()
                    budgetGradeResponse.gradeRuleList.add(budgetGradeRuleResponse)
                    budgetGradeRuleResponse.type = gradeRecordType.name

                    group.ruleList?.filter { it.mode == ModeTypeEnum.CONDITION }?.forEach { rule ->
                        try {
                            budgetGradeRuleResponse.gradeRule = BudgetGradeResponse.BudgetGradeRuleDetailResponse().apply {
                                this.ruleGroupId = group.id.toString()
                                this.ruleGroupName = group.name
                                this.ruleId = rule.id.toString()
                                this.ruleName = rule.name.toString()
                                this.gradeId = targetGradeId
                                if(targetGradeId == "-1" && GradeRuleGroupTypeEnum.GRADE_RECALCULATE == enum) {
                                    this.gradeId = rule.degradeId.toString()
                                }
                                if(this.gradeId != "-1") {
                                    //查询等级名称
                                    val gradeDefinition = ApplicationContextHolder.getBean(GradeDefinitionRepository::class.java).findByIdAndDate(this.gradeId!!.toLong(),ZonedDateTime.now())
                                    if(gradeDefinition.isPresent) {
                                        this.gradeName = gradeDefinition.get().name
                                    }
                                }
                                val expressionList :List<ExpressionIdentResponse> = Gson().fromJson(rule.expression, object : TypeToken<List<ExpressionIdentResponse>>() {}.type)
                                this.expressionIdentResponseList = expressionList
                                this.expressionDisplayInfo = rule.expressionDisplayInfo
                            }

                            // 计算出表达式中 fxId , 映射 属性ID: fxId , 才能计算某个属性是多少
                            val dsl = rule.expressionFx!!
                            val propertyList = ExpressionIdentUtil.parsePropertyList(rule.expressionTranslated!!)
                            val filterGroupList = parseDsl(dsl)

                            filterGroupList.forEach {  filterGroup ->
                                filterGroup.filterList.forEach{ filter ->
                                    parseDslProperty(filter, propertyList, objectNode)
                                }
                            }
                            budgetGradeRuleResponse.gradeRule!!.filterGroupList.addAll(filterGroupList)
                            // 计算表达式
                            budgetGradeRuleResponse.gradeRule!!.result = epp(rule.expressionFxId!!.toInt(), objectNode)
                        } catch (e: Exception) {
                            logger.error("规则: {} 计算失败", { JsonUtils.toJson(rule) }, e)
                        }
                    }
                }
            }
        }

        return response
    }

    private fun parseDsl(dsl: String): ArrayList<BudgetGradeResponse.BudgetGradeRuleFilterGroupResponse> {
        val filterGroupList = ArrayList<BudgetGradeResponse.BudgetGradeRuleFilterGroupResponse>()
        var visitor = SimpleParseVisitor()
        visitor.command = "OR"
        visitor.visit(DslEpp.dslParse(dsl))
        val or = visitor.result
        if (or.isEmpty()) {
            or.add(dsl)
        }

        visitor = SimpleParseVisitor()
        visitor.command = "AND"
        or.forEach {
            val filterGroup = BudgetGradeResponse.BudgetGradeRuleFilterGroupResponse()
            filterGroupList.add(filterGroup)
            visitor.visit(DslEpp.dslParse(it))
            val and = visitor.result
            if (and.isEmpty()) {
                and.add(it)
            }
            and.forEach { exp ->
                val filter = BudgetGradeResponse.BudgetGradeRuleFilterResponse()
                filter.expressionObj = exp
                filterGroup.filterList.add(filter)
            }
            visitor.result.clear()
        }
        return filterGroupList
    }

    private fun parseDslProperty(filter: BudgetGradeResponse.BudgetGradeRuleFilterResponse, propertyList: ArrayList<Pair<Long,String>>, objectNode :ObjectNode) {
        val visitor = SimpleParseVisitor()
        visitor.visit(DslEpp.dslParse(filter.expressionObj.toString()))
        val comparison = visitor.comparison
        comparison?.let {
            filter.comparisonOperators = it.second
            filter.value = it.third

            parseSelector(filter)

            filter.property = BudgetGradeResponse.BudgetGradeRulePropertyResponse().apply {
                val first = propertyList.removeFirst()
                this.propertyId = first.first.toString()
                this.propertyName = first.second
            }
            val fxId = it.first.replace("{", "").replace("}", "")
            filter.currentValue = epp(fxId.toInt(), objectNode)
        }
    }

    private fun parseSelector(filter: BudgetGradeResponse.BudgetGradeRuleFilterResponse) {
        try {
            val value = filter.value.toString()
            if (value.startsWith(SelectorValueFunction.funName)) {
                // 明细选择器
                val ary = value.replace(SelectorValueFunction.funName + "(", "").split(",")
                filter.valueDetail = mapOf(
                    "type" to "DetailSelector", "fqn" to ary[2],
                    "expressionDesc" to mapOf(
                        "ruleId" to "",
                        "selectItems" to ary[1].replace(ary[2], "").replace(")", ","),
                        "selectorId" to ary[0]
                    )
                )
            } else if (value.startsWith(SelectorFunction.funName)) {
                // 规则选择器
                val ary = value.replace(SelectorFunction.funName + "(", "").split(",")
                filter.valueDetail = mapOf(
                    "type" to "RuleSelector", "fqn" to ary[2],
                    "expressionDesc" to mapOf("ruleId" to ary[1], "selectItems" to "", "selectorId" to ary[0])
                )
            }
        } catch (e: Exception) {
            logger.error("解析选择器异常:{}", { filter.value }, e)
        }
    }

    private fun epp(fxId: Int, objectNode: ObjectNode, rule: GradeRule? = null):Any? {
        try {
            val event = Event()
            event.setOccurrenceTs(ZonedDateTime.now().toInstant().toEpochMilli())
            return ExpressionIdentUtil.eppById(fxId, event, objectNode)
        } catch (e: Exception) {
            logger.error("计算表达式异常 表达式ID:{} 规则:{} ", fxId, rule?.let { JsonUtils.toJson(rule) }, e)
        }
        return null
    }
}
