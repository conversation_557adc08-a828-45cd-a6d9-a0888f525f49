package com.shuyun.loyalty.manager.service

import com.pip.shuyun.pool.transaction.DmTransaction
import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.manager.vo.ImportFileConfigVo
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.ImportFileConfigEnum
import com.shuyun.loyalty.service.model.*
import com.shuyun.loyalty.service.service.ImportExtensionSettingBaseService
import com.shuyun.loyalty.service.service.ImportFieldConfigBaseService
import com.shuyun.loyalty.service.util.ModelInitUtil
import com.shuyun.pip.util.converter.String2ZonedDateTimeConverter
import com.shuyun.pip.util.converter.ZonedDateTime2StringConverter
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.util.ObjectUtils
import java.time.ZonedDateTime

/**
 *
 * 新建DRAFT -> 编辑 ，发布，删除
 * 启用ENABLE -> 编辑，禁用
 * 启用编辑ENABLE_DRAFT -> 编辑，发布，禁用
 * 禁用BANISH -> 编辑， 启用
 * 禁用编辑BANISH_BANISH -> 编辑,发布,启用
 *
 */
@Service
class ImportFileConfigService {

    private val logger = LogManager.getLogger(ImportFileConfigService::class.java)

    @Autowired
    private lateinit var importFieldConfigBaseService: ImportFieldConfigBaseService

    @Autowired
    private lateinit var importExtensionSettingBaseService: ImportExtensionSettingBaseService

    /**新建配置*/
    @Transactional(rollbackFor = [Exception::class])
    fun add(importFileConfigVo: ImportFileConfigVo): ImportFileConfig {
        if(!ObjectUtils.isEmpty(ImportFileConfig().findBySubjectIdAndDisabled(importFileConfigVo.subjectId!!))){
            throw LoyaltyException(LoyaltyExceptionCode.SUBJECT_REPEATED,"主体已经存在，不允许重复添加")
        }
        //保存主配置信息
        val importFileConfig = ImportFileConfig()
        importFileConfigVo.initImportFileConfigFromVo(importFileConfig)

        importFileConfig.apply {
            this.historyRecord = false
            this.parentId = "0"
            this.disabled = false
            this.status = ImportFileConfigEnum.DRAFT
            this.publishedTime = null
        }.save()

        //保存子配置信息
        importFieldConfigBaseService.insert(importFileConfig.importFieldConfig, importFileConfig.id!!)
        importExtensionSettingBaseService.insert(importFileConfig.extensionFieldSettings, importFileConfig.id!!)

        //再次初始化条数据用于草稿状态
        val initImportFileConfigChild = initImportFileConfigFromPo(importFileConfig)

        initImportFileConfigChild.apply {
            this.parentId =  importFileConfig.id!!
            this.status = ImportFileConfigEnum.DRAFT
            this.publishedTime = null
        }.save()

        //保存导入字段配置信息
        importFieldConfigBaseService.insert(initImportFileConfigChild.importFieldConfig, initImportFileConfigChild.id!!)
        //查询导入字段配置信息并赋值
        initImportFileConfigChild.importFieldConfig = importFieldConfigBaseService.listByImportFileConfigId(initImportFileConfigChild.id!!)

        //保存导入扩展字段配置信息
        importExtensionSettingBaseService.insert(initImportFileConfigChild.extensionFieldSettings, initImportFileConfigChild.id!!)
        //查询导入扩展字段配置信息并赋值
        initImportFileConfigChild.extensionFieldSettings = importExtensionSettingBaseService.listByImportFileConfigId(initImportFileConfigChild.id!!)

        return initImportFileConfigChild
    }

    /**修改配置 修改首先判断之前文件的状态， 发布：重新新建一个文件，未发布：编辑文件，不需要发布*/
    @DmTransaction
    fun update(importFileConfigVo: ImportFileConfigVo): ImportFileConfig {

        val importFileConfig = ImportFileConfig().findById(importFileConfigVo.id!!)!!
        if(ObjectUtils.isEmpty(importFileConfig)) {
            throw LoyaltyException(LoyaltyExceptionCode.VERSION_NOT_FOUND,"当前版本不存在，无法修改")
        }

        //判断修改状态是否会抛出异常
        importFileConfig.statusIsException(importFileConfigVo.status!!)

        val publishedTime = importFileConfig.publishedTime
        when (importFileConfig.status) {
            ImportFileConfigEnum.ENABLE, ImportFileConfigEnum.BANISH -> {
                importFileConfig.updateHistory() //把之前的isHistory置为true

                val importFileConfigChild = ImportFileConfig()
                importFileConfigVo.initImportFileConfigFromVo(importFileConfigChild)

                val status = if(importFileConfig.status == ImportFileConfigEnum.ENABLE) ImportFileConfigEnum.ENABLE_DRAFT else ImportFileConfigEnum.BANISH_DRAFT
                importFileConfigChild.status = status
                importFileConfigChild.parentId = importFileConfig.id
                importFileConfigChild.historyRecord = false
                importFileConfigChild.publishedTime = publishedTime
                importFileConfigChild.save()

                //保存导入配置字段信息
                importFieldConfigBaseService.insert(importFileConfigChild.importFieldConfig, importFileConfigChild.id!!)
                //查询导入字段配置信息并赋值
                importFileConfigChild.importFieldConfig = importFieldConfigBaseService.listByImportFileConfigId(importFileConfigChild.id!!)

                //保存导入扩展字段配置信息
                importExtensionSettingBaseService.insert(importFileConfigChild.extensionFieldSettings, importFileConfigChild.id!!)
                //查询导入扩展字段配置信息并赋值
                importFileConfigChild.extensionFieldSettings = importExtensionSettingBaseService.listByImportFileConfigId(importFileConfigChild.id!!)

                return importFileConfigChild

            }
            ImportFileConfigEnum.ENABLE_DRAFT, ImportFileConfigEnum.DRAFT, ImportFileConfigEnum.BANISH_DRAFT -> {

                //新建状态，可以修改主体和计划
                if(importFileConfig.status == ImportFileConfigEnum.DRAFT) {
                    if(importFileConfig.subjectId!=importFileConfigVo.subjectId || importFileConfig.parentId!=importFileConfigVo.parentId){
                        val importFileConfigParent = ImportFileConfig().findById(importFileConfig.parentId!!)!!
                        importFileConfigParent.subjectId = importFileConfigVo.subjectId
                        importFileConfigParent.subjectName = importFileConfigVo.subjectName
                        importFileConfigParent.planId = importFileConfigVo.planId
                        importFileConfigParent.planName = importFileConfigVo.planName
                        importFileConfigParent.update()
                    }
                }

                importFileConfigVo.initImportFileConfigFromVo(importFileConfig)
                importFileConfig.publishedTime = publishedTime
                importFileConfig.update()
                // 删导入配置字段 , 再添加
                importFieldConfigBaseService.deleteByImportFileConfigId(importFileConfig.id!!)
                importExtensionSettingBaseService.deleteByImportFileConfigId(importFileConfig.id!!)
                //保存子配置信息
                importFieldConfigBaseService.insert(importFileConfig.importFieldConfig, importFileConfig.id!!)
                importExtensionSettingBaseService.insert(importFileConfig.extensionFieldSettings, importFileConfig.id!!)
                //查询导入字段配置信息并赋值
                importFileConfig.importFieldConfig = importFieldConfigBaseService.listByImportFileConfigId(importFileConfig.id!!)
                importFileConfig.extensionFieldSettings = importExtensionSettingBaseService.listByImportFileConfigId(importFileConfig.id!!)

                return importFileConfig
            }
            else -> {
                throw LoyaltyException(LoyaltyExceptionCode.UNKNOWN_EXCEPTION,"未知状态${importFileConfig.status}")
            }
        }
    }

    /**删除配置 第一次编辑，未发布 可以进行删除*/
    @DmTransaction
    fun delete(id: String) {
        val importFileConfig = ImportFileConfig().findById(id)!!
        if(importFileConfig.disabled == true || importFileConfig.historyRecord == true) {
            throw IllegalArgumentException("配置已变为历史版本，或者已删除")
        }
        importFileConfig.apply {
            this.disabled = true
            this.historyRecord = true
        }.delete()

        val importFileConfigParent = ImportFileConfig().findById(importFileConfig.parentId!!)!!
        importFileConfigParent.apply {
            this.disabled = true
            this.historyRecord = true
        }.delete()
    }


    /**修改配置状态*/
    @DmTransaction
    fun updateStatus(id: String, updateStatus: ImportFileConfigEnum) {
        var importFileConfig = ImportFileConfig()
        importFileConfig = importFileConfig.findById(id) !!
        if(ObjectUtils.isEmpty(importFileConfig)) {
            throw LoyaltyException(LoyaltyExceptionCode.VERSION_NOT_FOUND,"当前版本不存在，无法修改")
        }
        if(importFileConfig.historyRecord!!){
            throw LoyaltyException(LoyaltyExceptionCode.HISTORY_PLAN_NOT_OPERATION_UPDATE,"历史版本不允许修改")
        }

        //判断修改状态是否会抛出异常
        importFileConfig.statusIsException(updateStatus)

        importFileConfig.status = updateStatus
        if(ImportFileConfigEnum.ENABLE == updateStatus) importFileConfig.publishedTime = ZonedDateTime2StringConverter().convert(ZonedDateTime.now())
        importFileConfig.update()
    }

    /**返回上一版本*/
    fun returnVersion(id: String): ImportFileConfig? {
        //查询当前版本
        val importFileConfig = ImportFileConfig().findById(id)!!
        //更改上一版本状态
        val importFileConfigList = ImportFileConfig().findByParentIdAndStatusAndDisabled(importFileConfig.parentId!!)
        val maxImportFileConfig =
            importFileConfigList.maxByOrNull { String2ZonedDateTimeConverter().convert(it.createTime!!)!!.toEpochSecond() } ?: throw LoyaltyException(LoyaltyExceptionCode.PREVIOUS_VERSION_NOT_FOUND,"找不到上一版本")
        //查询导入配置字段信息重新复制到配置信息中
        maxImportFileConfig.apply {
            this.importFieldConfig = importFieldConfigBaseService.listByImportFileConfigId(maxImportFileConfig.id!!)
            this.extensionFieldSettings = importExtensionSettingBaseService.listByImportFileConfigId(maxImportFileConfig.id!!)
            this.id = importFileConfig.id
            this.importFieldConfig?.forEach {
                it.importFileConfigId = importFileConfig.id
            }
            this.extensionFieldSettings?.forEach {
                it.importFileConfigId = importFileConfig.id
            }
        }
        return maxImportFileConfig
    }

    /**获取id对应配置*/
    fun findById(id: String): ImportFileConfig?{
        val importFieldConfig = ImportFileConfig().findById(id)
        importFieldConfig?.let {
            it.importFieldConfig = importFieldConfigBaseService.listByImportFileConfigId(it.id!!)
            it.extensionFieldSettings = importExtensionSettingBaseService.listByImportFileConfigId(it.id!!)
        }
        return importFieldConfig
    }

    /**查询 非历史, 非删除，非父 记录*/
    fun findListPage(pageable: Pageable): Page<ImportFileConfig> {
        val listPage = ImportFileConfig().findListPage(pageable)
        listPage.forEach {
            it.importFieldConfig = importFieldConfigBaseService.listByImportFileConfigId(it.id!!)
            it.extensionFieldSettings = importExtensionSettingBaseService.listByImportFileConfigId(it.id!!)
        }
        return listPage
    }


    /**查询 所有已发布配置*/
    fun findEnableAll(): List<ImportConfigPlanVo>{
        return ImportFileConfig().findEnableAll()
    }

    /**查询 所有已发布配置（过滤完权限重新组装数据）*/
    fun findPermissionConfigBox(): List<ImportConfigPlanVo>{
        return ImportFileConfig().findPermissionConfigBox()
    }

    /**实施端修改关联配置修改*/
    @DmTransaction
    fun updateSubjectList(subjectList: List<Subject>) {
        subjectList.forEach {
            if(it.id == null){
                logger.error("主体Id不存在")
                throw LoyaltyException(LoyaltyExceptionCode.SUBJECT_ID_NOT_FOUND,"主体不存在")
            }
            val importFileConfigList = ImportFileConfig().findBySubjectIdAndDisabled(it.id!!)
            importFileConfigList.forEach { config ->
                val importFileConfig: ImportFileConfig = config
                importFileConfig.subjectName = it.name
                if(it.status == EnableStatusEnum.DISABLED) {

                    if (importFileConfig.status == ImportFileConfigEnum.ENABLE_DRAFT) {
                        importFileConfig.status = ImportFileConfigEnum.BANISH_DRAFT
                    }

                    if(importFileConfig.status == ImportFileConfigEnum.ENABLE) {
                        importFileConfig.status = ImportFileConfigEnum.BANISH
                    }
                }
                importFileConfig.update()
            }
        }
    }

    /**计划归档关联配置也归档*/
    @DmTransaction
    fun planFiled(planId: Long){
        val importFileConfigList = ImportFileConfig().findByPlanId(planId)
        importFileConfigList.forEach {
            val importFileConfig: ImportFileConfig = it
            importFileConfig.status = ImportFileConfigEnum.FILED
            importFileConfig.update()
        }
    }


    /**获取主体对应已发布的配置*/
    fun findEnableBySubjectId(subjectId: Long): ImportFileConfig{
        val importFieldConfig = ImportFileConfig().findEnableBySubjectId(subjectId)
        importFieldConfig.let {
            it.importFieldConfig = importFieldConfigBaseService.listByImportFileConfigId(it.id!!)
            it.extensionFieldSettings = importExtensionSettingBaseService.listByImportFileConfigId(it.id!!)
        }
        return importFieldConfig
    }



    /**根据po初始化新po*/
    private fun initImportFileConfigFromPo(importFileConfigParent :ImportFileConfig): ImportFileConfig {
        val importFileConfigChild = ImportFileConfig()
        ModelInitUtil.copyPropertiesIgnoreNull(importFileConfigParent, importFileConfigChild)
        importFileConfigChild.importFieldConfig = importFileConfigParent.importFieldConfig?.map { importFieldConfigParent ->
            val importFieldConfig = ImportFieldConfig()
            ModelInitUtil.copyPropertiesIgnoreNull(importFieldConfigParent, importFieldConfig)
            importFieldConfig
        }
        importFileConfigChild.extensionFieldSettings = importFileConfigParent.extensionFieldSettings?.map { x ->
            val importExtensionSetting = ImportExtensionSetting()
            ModelInitUtil.copyPropertiesIgnoreNull(x, importExtensionSetting)
            importExtensionSetting
        }
        return importFileConfigChild
    }


    /**新建配置*/
    @Transactional(rollbackFor = [Exception::class])
    fun addHub(importFileConfig: ImportFileConfig) {
        if (!ObjectUtils.isEmpty(ImportFileConfig().findBySubjectIdAndDisabled(importFileConfig.subjectId!!))) {
            throw LoyaltyException(LoyaltyExceptionCode.SUBJECT_REPEATED, "主体已经存在，不允许重复添加")
        }

        importFileConfig.apply {
            this.parentId = "0"
            this.disabled = false
            this.status = ImportFileConfigEnum.DRAFT
            this.publishedTime = null
        }.save()

        //保存子配置信息
        importFieldConfigBaseService.insert(importFileConfig.importFieldConfig, importFileConfig.id!!)
        importExtensionSettingBaseService.insert(importFileConfig.extensionFieldSettings, importFileConfig.id!!)

        //再次初始化条数据用于草稿状态
        val initImportFileConfigChild = initImportFileConfigFromPo(importFileConfig)

        initImportFileConfigChild.apply {
            this.parentId = importFileConfig.id!!
            this.status = ImportFileConfigEnum.DRAFT
            this.publishedTime = null
        }.save()
        //保存导入字段配置信息
        importFieldConfigBaseService.insert(initImportFileConfigChild.importFieldConfig, initImportFileConfigChild.id!!)
        importExtensionSettingBaseService.insert(initImportFileConfigChild.extensionFieldSettings, initImportFileConfigChild.id!!)
    }

    /**新建配置*/
    @Transactional(rollbackFor = [Exception::class])
    fun upgrade(importFileConfig: ImportFileConfig) {
        importFileConfig.apply { this.publishedTime = null}.save()
        //保存子配置信息
        importFieldConfigBaseService.insert(importFileConfig.importFieldConfig, importFileConfig.id!!)
        importExtensionSettingBaseService.insert(importFileConfig.extensionFieldSettings, importFileConfig.id!!)
    }

    /**
     * 升级导入配置
     */
    @Transactional(rollbackFor = [Exception::class])
    fun upgrade(subject: Subject, plan: Plan) {
        if(subject.sourceImportFileConfig == null) {
            logger.info("来源主体导入配置项为空: {} ", {subject.id})
            return
        }
        if (subject.importFileConfig == null) { // 新增变更配置项
            subject.sourceImportFileConfig!!.apply {
                this.subjectId = subject.id
                this.subjectName = subject.name
                this.planId = plan.id
                this.planName = plan.name
            }
            this.addHub(subject.sourceImportFileConfig!!)
            return
        }

        // 下边是修改  变更配置项
        val importFileConfig = subject.importFileConfig!!
        val sourceImportFileConfig = subject.sourceImportFileConfig!!

        // 将旧数据的parentId复制过来,不能变  , 旧数据如果是 ENABLE , 则改为historyRecord=true
        // 如果是 非 enable状态, 则 disabled = true
        val parentId = importFileConfig.parentId

        if(importFileConfig.status == ImportFileConfigEnum.DRAFT || importFileConfig.status == ImportFileConfigEnum.ENABLE_DRAFT ||
            importFileConfig.status == ImportFileConfigEnum.BANISH_DRAFT) {
            // 删除旧数据
            importFileConfig.disabled = true
            importFileConfig.delete()

            // 草稿状态,直接使用当前数据的状态
            sourceImportFileConfig.status = importFileConfig.status

        }else if (importFileConfig.status == ImportFileConfigEnum.ENABLE || importFileConfig.status == ImportFileConfigEnum.BANISH) {
            // 归档旧数据
            importFileConfig.historyRecord = true
            importFileConfig.update()
            sourceImportFileConfig.status = ImportFileConfigEnum.ENABLE_DRAFT

            if (importFileConfig.status == ImportFileConfigEnum.BANISH) {
                sourceImportFileConfig.status = ImportFileConfigEnum.BANISH_DRAFT
            }
        }else {
            logger.info("导入变更配置不存在所属状态:{} ", importFileConfig.status)
            return
        }

        // 新增一条数据
        sourceImportFileConfig.apply {
            this.planName = plan.name
            this.planId = plan.id
            this.subjectId = subject.id
            this.subjectName = subject.name
            this.parentId = parentId
            this.historyRecord = false
            this.id = null
        }
        this.upgrade(sourceImportFileConfig)

    }
}