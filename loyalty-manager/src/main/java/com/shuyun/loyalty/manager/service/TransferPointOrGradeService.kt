package com.shuyun.loyalty.manager.service

import com.github.f4b6a3.tsid.Tsid
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.pip.shuyun.pool.transaction.DmTransaction
import com.pip.shuyun.pool.transaction.TransactionInfoHolder
import com.shuyun.lite.client.PassportClientFactory
import com.shuyun.loyalty.entity.api.constants.ChangeMode
import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.MedalChangeMode
import com.shuyun.loyalty.entity.api.constants.RequestType
import com.shuyun.loyalty.entity.api.request.MemberGradeModifyRequest
import com.shuyun.loyalty.entity.api.request.MemberMedalModifyRequest
import com.shuyun.loyalty.entity.dto.MemberPointMessage
import com.shuyun.loyalty.entity.dto.MemberRequest
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.entity.enums.NegativeStrategyEnum
import com.shuyun.loyalty.entity.enums.PointStateEnum
import com.shuyun.loyalty.manager.vo.TransferPointDetailVo
import com.shuyun.loyalty.manager.vo.TransferPointOrGradeVo
import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.service.datamodel.MemberPointValidStatement
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.exception.UsedPointException
import com.shuyun.loyalty.service.extension.shDate
import com.shuyun.loyalty.service.kafka.IPointSyncProducer
import com.shuyun.loyalty.service.meta.ChangeType
import com.shuyun.loyalty.service.meta.ComponentType
import com.shuyun.loyalty.service.meta.LimitActionEnum
import com.shuyun.loyalty.service.meta.PlanStatusEnum
import com.shuyun.loyalty.service.model.*
import com.shuyun.loyalty.service.service.*
import com.shuyun.loyalty.service.transfer.grade.MemberGradeTransferService
import com.shuyun.loyalty.service.transfer.medal.MemberMedalTransferService
import com.shuyun.loyalty.service.transfer.points.*
import com.shuyun.loyalty.service.util.*
import com.shuyun.loyalty.service.util.ConstantValue.LONG_TERM_OVERDUE_DATE
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.exception.AbstractPipException
import com.shuyun.pip.i18n.LocaleI18nContextHolder
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service
import org.springframework.util.ObjectUtils
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.concurrent.withLock


@Service
class TransferPointOrGradeService {

    private val logger = LogManager.getLogger(TransferPointOrGradeService::class.java)


    @Autowired
    private lateinit var subjectBaseService: SubjectBaseService

    @Autowired
    private lateinit var memberPointService: MemberPointService

    @Autowired
    private lateinit var transferPointOrGradeBaseService: TransferPointOrGradeBaseService

    @Autowired
    private lateinit var memberGradeTransferService: MemberGradeTransferService

    @Autowired
    private lateinit var importFileConfigBaseService:ImportFileConfigBaseService


    @Autowired
    private lateinit var locker: Locker

    @Autowired
    private lateinit var dataModelService: DataModelService

    @Autowired
    private lateinit var memberMedalTransferService: MemberMedalTransferService

    @Autowired
    private lateinit var gainService: MemberPointGainStatementService


    private val passportClient by lazy { PassportClientFactory.passportClient() }


    data class TransferPointResult(
        var memberId: String? = null,
        var dynamic: String? = null,
        var result: String? = null,
        var desc: String? = null,
        var point: BigDecimal? = null,
        var plusPoint: BigDecimal? = null
    )
    //变更积分
    fun transferPoint(transferPointVo: TransferPointOrGradeVo, checkLimit: Boolean = true): List<TransferPointResult> {
        val memberIdList: List<Map<String, String>> = Gson().fromJson(transferPointVo.memberIdsRecord, object : TypeToken<List<Map<String, String>>>() {}.type)
        //用于查询动态字段用
        val importFileConfig = importFileConfigBaseService.findEnableBySubjectId(transferPointVo.subjectId!!)
        val matchFiled = importFileConfig.matchFiled
        val userId = VisitorInfoUtil.userId.get()
        val roleIds = passportClient.users(listOf(userId.toLong())).firstOrNull()?.roles?.map { it.id.toString() }
        if (checkLimit) {
            transferPointVo.transferPointValue?.let {
                val changePoint = it * memberIdList.size.toBigDecimal()
                val action = LimitActionEnum.get(transferPointVo.transferPointType!!)
                if (roleIds != null) {
                    ManuallyPointLimitUtils.check(importFileConfig, roleIds.toMutableSet(), changePoint, action)
                }
            }
        }

        val plan = LoyaltyPrograms.findPlanByAccountTypeId(transferPointVo.accountOrHierarchyId!!) ?: throw LoyaltyException(LoyaltyExceptionCode.PLAN_NOT_FOUND, "- hierarchyId: ${transferPointVo.accountOrHierarchyId}")
        val pointAccount = plan.subjectList!!.first().pointAccountTypeList!!.first()
        if (plan.status == PlanStatusEnum.FILED) throw LoyaltyException(LoyaltyExceptionCode.PLAN_IS_FILE)

        if (transferPointVo.transferPointValue!! <= ConstantValue.defaultZeroLine) {
            throw LoyaltyException(LoyaltyExceptionCode.POINT_LESS_THAN_OR_EQUAL_ZERO)
        }
        if (transferPointVo.transferPointType != TransferPointOrGrade.PointTransferTypeEnum.ADD && NegativeStrategyEnum.NOT_ALLOWED == pointAccount.negativeStrategy) {
            throw UsedPointException(LoyaltyExceptionCode.NOT_DEDUCT_ALLOWED)
        }
        if (memberIdList.size > Property.getSysOrEnv("loyalty.transfer.point.member.count.limit", 20)) {
            throw IllegalArgumentException("单次变更积分会员数量超过限制")
        }
        val results = ArrayList<TransferPointResult>()
        var totalChangePoint = BigDecimal.ZERO
        for (member in memberIdList) {
            val inputMemberId = member["memberId"]!!
            val subject = plan.subjectList!!.first()
            var mainMemberId: String
            try {
                mainMemberId = MergeUtil.findMergePointMemberId(subject, inputMemberId) ?: inputMemberId
            } catch (_: Exception) {
                val dynamic = TransferMemberIdUtil.transferDynamic(matchFiled!!, inputMemberId)?.toString()
                results.add(TransferPointResult(memberId = inputMemberId, dynamic = dynamic, result = "ERROR", desc = I18nUtil.transferMessage(LoyaltyExceptionCode.MEMBER_NOT_FOUND)))
                continue
            }
            val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT, transferPointVo.accountOrHierarchyId, mainMemberId))
            try {
                lock.lock()
                val svc = ApplicationContextHolder.getBean(TransferPointOrGradeService::class.java)
                val accountTypeId = plan.subjectList!!.first().pointAccountTypeList!!.first().id!!
                val result = svc.transferPoint(accountTypeId, inputMemberId, transferPointVo, matchFiled)
                results.add(result)
                if (result.plusPoint != null) {
                    totalChangePoint += result.plusPoint!!
                }
            } catch (e: Exception) {
                logger.warn("手动变更错误", e)
                val desc = if (e is AbstractPipException && e.code != null) {
                    LocaleI18nContextHolder.getMessage(e.code!!.toString(), e.args)
                } else {
                    if (e.message?.isNotEmpty() == true) {
                        e.message!!
                    } else if (e.cause != null) {
                        e.cause!!.message!!
                    } else {
                        "手动变更错误"
                    }
                }
                val dynamic = TransferMemberIdUtil.transferDynamic(matchFiled!!, inputMemberId)!!.toString()
                results.add(TransferPointResult(memberId = inputMemberId, dynamic = dynamic, result = "ERROR", desc = desc))
            } finally {
                lock.unlock()
            }
        }
        val transferPoint = TransferPointOrGrade()
        transferPointVo.initTransferPointOrGrade(transferPoint)
        logger.debug("transfer point model :{}", {JsonUtils.toJson(transferPoint)})
        transferPointOrGradeBaseService.transferPoint(transferPoint)
        ManuallyPointRecord().apply {
            this.importFileConfigId = importFileConfig.id
            this.userId = userId
            this.roleId = roleIds.toString()
            this.changePoint = totalChangePoint
            this.action = LimitActionEnum.get(transferPointVo.transferPointType!!)!!.name
            this.changeDate = LocalDate.now()
        }.save()
        return results
    }


    @DmTransaction
    fun transferPoint(accountTypeId: Long, memberId: String, transferPointVo: TransferPointOrGradeVo, matchFiled: String?): TransferPointResult {
        val memberPoint = memberPointService.getOrCreate(accountTypeId, memberId)
        val traceId = Tsid.fast().toString()
        val lp = LoyaltyPoints.of(
            memberPoint = memberPoint,
            type = LoyaltyRequestType.MANUAL,
            point = transferPointVo.transferPointValue!!.abs(),
            changeMode = ChangeMode.MANUAL.name,
            channel = ConstantValue.DEFAULT_CHANNEL_TYPE,
            traceId = traceId,
            uniqueId = traceId,
            shopId = transferPointVo.shopId,
            kzzd1 = transferPointVo.KZZD1,
            kzzd2 = transferPointVo.KZZD2,
            kzzd3 = transferPointVo.KZZD3,
            desc = transferPointVo.transferDesc
        )

        val dynamic = TransferMemberIdUtil.transferDynamic(matchFiled!!, memberId)?.toString()
        var result = "SUCCESS"
        var desc: String?
        var changePoint: BigDecimal? = null
        when (transferPointVo.transferPointType) {
            TransferPointOrGrade.PointTransferTypeEnum.ADD -> {
                lp.attr.effectiveDate = transferPointVo.initEffectiveDateDateTime(lp.date)
                lp.attr.overdueDate = transferPointVo.initOverdueZoneDateTime(lp.attr.effectiveDate)
                val sendTransfer = ApplicationContextHolder.getBean(MemberPointSendTransfer::class.java)
                desc = sendTransfer.send(lp, lp.afterPoints, ForbiddenPort.MANUAL, lp.attr.uniqueId)
                if (lp.afterPoints > BigDecimal.ZERO) {
                    changePoint = lp.afterPoints
                } else {
                    result = "ERROR"
                }
            }
            TransferPointOrGrade.PointTransferTypeEnum.DEDUCT -> {
                val (result1, desc1) = checkAndDeduct(lp.hierarchy.negativeStrategy!!, lp.member.point, lp.afterPoints) {
                    val deductTransfer = ApplicationContextHolder.getBean(MemberPointDeductTransfer::class.java)
                    deductTransfer.deduct(lp, ForbiddenPort.MANUAL, ForbiddenOperation.POINT_DEDUCT_BY_USE, lp.attr.uniqueId)
                    lp.afterTotalPoints
                }
                result = result1
                desc = desc1
            }
            TransferPointOrGrade.PointTransferTypeEnum.MANUAL_ABOLISH -> {
                val (result1, desc1) = checkAndDeduct(lp.hierarchy.negativeStrategy!!, lp.member.point, lp.afterPoints) {
                    lp.attr.abolish = true
                    val deductTransfer = ApplicationContextHolder.getBean(MemberPointDeductTransfer::class.java)
                    deductTransfer.deduct(lp, ForbiddenPort.MANUAL, ForbiddenOperation.POINT_DEDUCT_BY_ABOLISH, lp.attr.uniqueId)
                    lp.afterTotalPoints
                }
                result = result1
                desc = desc1

            }
            null -> return TransferPointResult(memberId = memberId, dynamic = dynamic, result = "ERROR", desc = "不支持的类型")
        }
        logger.info(
            "积分处理完成 accountTypeId: {} memberId: {} points: {}",
            lp.hierarchy.id,
            lp.member.id,
            lp.afterPoints
        )
        TransactionInfoHolder.afterCommit {
            ApplicationContextHolder.getBean(IPointSyncProducer::class.java)
                .send(
                    MemberPointMessage(
                        lp.hierarchy.id,
                        lp.member.memberId,
                        lp.member.id
                    )
                )
        }

        return TransferPointResult(
            memberId = memberId,
            result = result,
            dynamic = dynamic,
            desc = desc,
            plusPoint = changePoint
        )
    }

    fun checkAndDeduct(negativeStrategy: NegativeStrategyEnum, beforeTotalPoints: BigDecimal, deductPoint: BigDecimal, block: () -> BigDecimal): Pair<String, String> {
        if (negativeStrategy == NegativeStrategyEnum.TO_ZERO && beforeTotalPoints <= BigDecimal.ZERO) {
            return Pair("ERROR", "扣除设置仅扣到0，当前余额小于或等于0")
        }
        val afterTotalPoints = block()
        val after = (beforeTotalPoints - afterTotalPoints).abs()
        if (deductPoint.compareTo(after) != 0) {
            // 仅扣除部分积分
            return if (negativeStrategy == NegativeStrategyEnum.TO_ZERO) {
                Pair("SUCCESS", "仅扣减部分积分，扣除设置仅扣到0")
            } else {
                Pair("SUCCESS", "仅扣减部分积分")
            }
        }
        return Pair("SUCCESS", "")
    }

    //变更等级
    fun transferGrade(transferGradeVo: TransferPointOrGradeVo): ArrayList<HashMap<String, String>> {

        val errorList = ArrayList<HashMap<String, String>>()

        val memberIdList: List<Map<String, String>> =
            Gson().fromJson(transferGradeVo.memberIdsRecord, object : TypeToken<List<Map<String, String>>>() {}.type)

        //用于查询动态字段用
        val matchFiled = ApplicationContextHolder.getBean(ImportFileConfigBaseService::class.java)
            .findEnableBySubjectId(transferGradeVo.subjectId!!).matchFiled

        if (memberIdList.size > Property.getSysOrEnv("loyalty.transfer.grade.member.count.limit", 20)) {
            throw IllegalArgumentException("单次变更等级会员数量超过限制")
        }

        memberIdList.forEach {

            val memberGradeModifyRequest = MemberGradeModifyRequest().apply {
                this.gradeHierarchyId = transferGradeVo.accountOrHierarchyId
                this.channelType = "loyalty"
                this.overdueDate = transferGradeVo.initOverdueZoneDateTime()
                this.gradeDefinitionId = transferGradeVo.transferGradeId
                this.changeWayType = ChangeMode.MANUAL
                this.description = transferGradeVo.transferDesc
            }

            val error = HashMap<String, String>()

            error["memberId"] = it["memberId"].toString()
            error["dynamic"] = TransferMemberIdUtil.transferDynamic(matchFiled!!, it["memberId"]!!)!!.toString()
            error["result"] = "SUCCESS"
            error["desc"] = ""

            memberGradeModifyRequest.memberId = it["memberId"] //会员ID
            try {
                memberGradeTransferService.modifyMemberGrade(memberGradeModifyRequest, RequestType.SYNC, ForbiddenPort.MANUAL)
            } catch (e: AbstractPipException) {
                logger.warn("手动变更错误", e)
                error["result"] = "ERROR"
                error["desc"] = LocaleI18nContextHolder.getMessage(e.code!!.toString(), e.args)

            } catch (e: Throwable) {
                logger.warn("手动变更错误", e)
                var calcMessage = LocaleI18nContextHolder.getMessage(LoyaltyExceptionCode.UNKNOWN_EXCEPTION.code).plus(" ")
                if (e.message?.isNotEmpty() == true) {
                    calcMessage = e.message!!
                } else if (e.cause != null) {
                    calcMessage = e.cause!!.message!!
                }
                error["result"] = "ERROR"
                error["desc"] = calcMessage
            }
            errorList.add(error)
        }

        val transferGrade = TransferPointOrGrade()
        transferGradeVo.initTransferPointOrGrade(transferGrade)
        logger.debug("transfer grade model :{}", {JsonUtils.toJson(transferGrade)})
        transferPointOrGradeBaseService.transferGrade(transferGrade)

        return errorList
    }


    //查询待生效积分特殊变更明细
    fun findPendingTransferPointDetails(planId: Long, @Suppress("unused") subjectId: Long, pointAccountId: Long, transferMemberId: List<TransferPointDetailVo.DynamicMemberIds>, pageable: Pageable): Page<TransferPointDetailVo> {

        val transferInfo = this.findTransferInfo(pointAccountId, transferMemberId)
        if (ObjectUtils.isEmpty(transferInfo.memberPointIds)) {
            return PageImpl(ArrayList<TransferPointDetailVo>(), pageable, 0)
        }

        val transferPointDetailedList = ArrayList<TransferPointDetailVo>()

        //根据三个字段，查询validStatement 查询 待生效

        val page = gainService.findPagePendingStatements(transferInfo.memberPointIds!!, pointAccountId, pageable)

        if (page.isEmpty) return PageImpl(ArrayList<TransferPointDetailVo>(), pageable, 0)

        val list = page.toList()
        list.forEach {
            val detailed = TransferPointDetailVo()
            detailed.id = it.id
            detailed.planId = planId
            detailed.planName = transferInfo.planName
            detailed.pointAccountId = pointAccountId
            detailed.pointAccountName = transferInfo.pointAccountName
            detailed.createTime = it.created
            detailed.effectiveDate = it.effectiveDate
            detailed.overdueDate = if (it.overdueDate.shDate().isEqual(LONG_TERM_OVERDUE_DATE.shDate())) null else it.overdueDate
            detailed.status = if (it.status == FSMPointEvent.DELAY_SEND) "DELAY" else "DELAY_FROZEN"
            detailed.pointValue = it.point
            detailed.memberId = it.memberId
            transferPointDetailedList.add(detailed)
        }
        return PageImpl(transferPointDetailedList, pageable, page.totalElements)
    }

    //查询积分特殊变更明细
    fun findTransferPointDetails(planId: Long, subjectId: Long, pointAccountId: Long, transferMemberId: List<TransferPointDetailVo.DynamicMemberIds>, pageable: Pageable): Page<TransferPointDetailVo> {

        //三个值必须不为空
        if (ObjectUtils.isEmpty(planId) || ObjectUtils.isEmpty(pointAccountId) || ObjectUtils.isEmpty(subjectId) || ObjectUtils.isEmpty(transferMemberId)) {
            throw IllegalArgumentException("参数必填")
        }

        val transferInfo = this.findTransferInfo(pointAccountId, transferMemberId)
        if (ObjectUtils.isEmpty(transferInfo.memberPointIds)) {
            return PageImpl(ArrayList<TransferPointDetailVo>(), pageable, 0)
        }

        val transferPointDetailedList = ArrayList<TransferPointDetailVo>()

        //根据三个字段，查询validStatemenet 查询 生效 待生效 冻结 积分
        val page = MemberPointValidStatement().findPageValidStatementList(transferInfo.memberPointIds!!, pointAccountId, pageable)
        if (page.isEmpty) return PageImpl(ArrayList<TransferPointDetailVo>(), pageable, 0)

        val list = page.toList()
        list.forEach {
            val detailed = TransferPointDetailVo()
            detailed.id = it.id
            detailed.planId = planId
            detailed.planName = transferInfo.planName
            detailed.pointAccountId = pointAccountId
            detailed.pointAccountName = transferInfo.pointAccountName
            detailed.createTime = it.created
            detailed.effectiveDate = it.effectiveDate
            detailed.overdueDate = if (it.overdueDate.shDate().isEqual(LONG_TERM_OVERDUE_DATE.shDate())) null else it.overdueDate
            detailed.status =
                if (PointStateEnum.SPECIAL_FROZE == it.fromStatus) FSMPointEvent.SPECIAL_FREEZE.name else FSMPointEvent.SEND.name
            detailed.pointValue = it.point
            detailed.memberId = it.memberId
            transferPointDetailedList.add(detailed)
        }
        return PageImpl(transferPointDetailedList, pageable, page.totalElements)
    }

    /**冻结积分*/
    fun manualToFrozenPoint(manualMemberPoint: ManualMemberPoint) {
        val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT,manualMemberPoint.pointAccountId,manualMemberPoint.memberId))
        lock.withLock {
            ApplicationContextHolder.getBean(TransferPointOrGradeService::class.java).manualPoint(manualMemberPoint, LimitActionEnum.FROZEN)
        }
    }

    /**解冻积分*/
    fun manualFromFrozenPoint(manualMemberPoint: ManualMemberPoint) {
        val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT,manualMemberPoint.pointAccountId,manualMemberPoint.memberId))
        lock.withLock {
            ApplicationContextHolder.getBean(TransferPointOrGradeService::class.java).manualPoint(manualMemberPoint, LimitActionEnum.UNFROZEN)
        }
    }

    /**废弃积分*/
    fun manualToAbolishPoint(manualMemberPoint: ManualMemberPoint) {
        val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT,manualMemberPoint.pointAccountId,manualMemberPoint.memberId))
        lock.withLock {
            ApplicationContextHolder.getBean(TransferPointOrGradeService::class.java).manualPoint(manualMemberPoint, LimitActionEnum.ABOLISH)
        }
    }

    /**使用积分*/
    fun manualToUsedPoint(manualMemberPoint: ManualMemberPoint) {
        val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT,manualMemberPoint.pointAccountId,manualMemberPoint.memberId))
        lock.withLock {
            ApplicationContextHolder.getBean(TransferPointOrGradeService::class.java).manualPoint(manualMemberPoint, LimitActionEnum.DEDUCT)
        }
    }


    @DmTransaction
    fun manualPoint(manualMemberPoint: ManualMemberPoint, action: LimitActionEnum, checkLimit: Boolean = true) {
        val subjectId = subjectBaseService.findSubject(manualMemberPoint.pointAccountId!!).id
        val importFileConfig = importFileConfigBaseService.findEnableBySubjectId(subjectId!!)
        val userId = VisitorInfoUtil.userId.get()
        val roleIds = passportClient.users(listOf(userId.toLong())).firstOrNull()?.roles?.map { it.id.toString() }
        if (checkLimit && roleIds != null) {
            ManuallyPointLimitUtils.check(importFileConfig, roleIds.toMutableSet(), manualMemberPoint.pointValue, action)
        }
        val traceId = Tsid.fast().toString()
        val memberPoint = memberPointService.getOrCreate(manualMemberPoint.pointAccountId!!, manualMemberPoint.memberId!!)
        val lp = LoyaltyPoints.of(
            memberPoint = memberPoint,
            type = LoyaltyRequestType.MANUAL,
            point = (manualMemberPoint.pointValue ?: BigDecimal.ZERO).abs(),
            changeMode = ChangeMode.MANUAL.name,
            channel = ConstantValue.DEFAULT_CHANNEL_TYPE,
            traceId = traceId,
            uniqueId = traceId,
            shopId = manualMemberPoint.shopId,
            kzzd1 = manualMemberPoint.KZZD1,
            kzzd2 = manualMemberPoint.KZZD2,
            kzzd3 = manualMemberPoint.KZZD3,
            desc = manualMemberPoint.desc,
        ).apply {
            this.attr.businessId = manualMemberPoint.id
            this.attr.pendingPoints = manualMemberPoint.isNotValidPoint
        }

        when (action) {
            LimitActionEnum.FROZEN -> {
                val freezeTransfer = ApplicationContextHolder.getBean(MemberPointFreezeTransfer::class.java)
                freezeTransfer.freeze(lp, lp.afterPoints, ForbiddenPort.MANUAL, ForbiddenOperation.POINT_FREEZE, lp.attr.uniqueId)
            }
            LimitActionEnum.UNFROZEN -> {
                val unfreezeTransfer = ApplicationContextHolder.getBean(MemberPointUnfreezeTransfer::class.java)
                unfreezeTransfer.unfreeze(lp, lp.afterPoints, ForbiddenPort.MANUAL, ForbiddenOperation.POINT_UNFREEZE, lp.attr.uniqueId)
            }
            LimitActionEnum.ABOLISH -> {
                lp.attr.abolish = true
                val deductTransfer = ApplicationContextHolder.getBean(MemberPointDeductTransfer::class.java)
                deductTransfer.deduct(lp, ForbiddenPort.MANUAL, ForbiddenOperation.POINT_DEDUCT_BY_ABOLISH, lp.attr.uniqueId)
            }
            LimitActionEnum.DEDUCT -> {
                lp.attr.abolish = false
                val deductTransfer = ApplicationContextHolder.getBean(MemberPointDeductTransfer::class.java)
                deductTransfer.deduct(lp, ForbiddenPort.MANUAL, ForbiddenOperation.POINT_DEDUCT_BY_USE, lp.attr.uniqueId)
            }
            else -> return
        }

        TransactionInfoHolder.afterCommit {
            ApplicationContextHolder.getBean(IPointSyncProducer::class.java)
                .send(
                    MemberPointMessage(
                        lp.hierarchy.id,
                        lp.member.memberId,
                        lp.member.id
                    )
                )
        }
    }

    fun findMatchFieldValue(filter:String, fields:String, subjectId: String,changeType: ChangeType):Any? {
        val importFieldConfig = ImportFileConfig().findEnableBySubjectId(subjectId.toLong())
        val fqn = if(ComponentType.SELECTOR != importFieldConfig.componentType) {
            importFieldConfig.matchFiled!!.substringBeforeLast(".")
        }else {
            if(changeType == ChangeType.MANUAL) {
                importFieldConfig.oneSelectorReturnField!!.substringBeforeLast(".")
            }else {
                importFieldConfig.batchSelectorReturnField!!.substringBeforeLast(".")
            }
        }
        return dataModelService.query(fqn, fields,filter)
    }

    /**
     * 查询
     */
    private fun findTransferInfo(pointAccountId: Long, memberIdList: List<TransferPointDetailVo.DynamicMemberIds>): TransferPointDetailVo.TransferInfo {

        val plan = LoyaltyPrograms.findPlanByAccountTypeId(pointAccountId)
            ?: throw LoyaltyException(LoyaltyExceptionCode.PLAN_NOT_FOUND, "- hierarchyId: $pointAccountId")
        val subject = plan.subjectList!!.first()
        val pointAccountType = subject.pointAccountTypeList!!.first()

        val list = ArrayList<String>()
        for (it in memberIdList) {
            //首先查询数据模型账号对应都账户模型id
            val memberPoint = memberPointService.getByMemberId(pointAccountId, it.dynamicMemberId!!, refreshSegmentPoints = false)
            if (ObjectUtils.isEmpty(memberPoint)) continue
            list.add(memberPoint!!.id!!)
        }
        val transferInfo = TransferPointDetailVo.TransferInfo().apply {
            this.memberPointIds = list
            this.planName = plan.name
            this.pointAccountName = pointAccountType.name
        }
        logger.debug("transfer info member point id, list :{}", { JsonUtils.toJson(transferInfo) })
        return transferInfo
    }

    // 变更勋章
    fun transferMedal(transferPointVo: TransferPointOrGradeVo): List<Map<String, String>> {
        val errorList = mutableListOf<MutableMap<String, String>>()
        val memberIdList: List<Map<String, String>> =
            Gson().fromJson(transferPointVo.memberIdsRecord, object : TypeToken<List<Map<String, String>>>() {}.type)
        val matchField = importFileConfigBaseService.findEnableBySubjectId(transferPointVo.subjectId!!).matchFiled

        memberIdList.forEach {
            val memberId = it["memberId"]
            val memberMedalModifyRequest = MemberMedalModifyRequest().apply {
                this.medalHierarchyId = transferPointVo.accountOrHierarchyId
                this.channelType = "loyalty"
                this.overdueDate = transferPointVo.initOverdueZoneDateTime()
                this.medalDefinitionId = transferPointVo.transferMedalId
                this.changeWayType = MedalChangeMode.MANUAL
                this.description = transferPointVo.transferDesc
                this.memberId = memberId
                this.transferMedalType = transferPointVo.transferMedalType
            }

            val error = mutableMapOf<String, String>()
            error["memberId"] = memberId.toString()
            error["dynamic"] = TransferMemberIdUtil.transferDynamic(matchField!!, memberId!!)!!.toString()
            error["result"] = "SUCCESS"
            error["desc"] = ""

            try {
                val locker = ApplicationContextHolder.getBean(Locker::class.java)
                val gradeLock = locker.getLock("medal_calculate_${memberMedalModifyRequest.medalHierarchyId}-${memberId}")
                gradeLock.withLock {
                    memberMedalTransferService.modifyMemberMedal(memberMedalModifyRequest, ForbiddenPort.MANUAL)
                }

            } catch (e: AbstractPipException) {
                logger.warn("手动变更错误", e)
                error["result"] = "ERROR"
                error["desc"] = LocaleI18nContextHolder.getMessage(e.code!!.toString(), e.args)
            } catch (e: Throwable) {
                logger.warn("手动变更错误", e)
                var calcMessage = LocaleI18nContextHolder.getMessage(LoyaltyExceptionCode.UNKNOWN_EXCEPTION.code).plus("")
                if (!e.message.isNullOrEmpty())
                    calcMessage = e.message!!
                else if (e.cause != null)
                    calcMessage = e.cause!!.message!!
                error["result"] = "ERROR"
                error["desc"] = calcMessage
            }
            errorList.add(error)
        }

        val transferMedal = TransferPointOrGrade()
        transferPointVo.initTransferPointOrGrade(transferMedal)
        logger.debug("transfer medal model:{}", JsonUtils.toJson(transferMedal))
        transferPointOrGradeBaseService.transferMedal(transferMedal)
        return errorList
    }
}
