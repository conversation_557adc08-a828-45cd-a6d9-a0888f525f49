package com.shuyun.loyalty.manager.migration

import com.shuyun.loyalty.sdk.Property
import org.flywaydb.core.Flyway
import org.flywaydb.core.api.pattern.ValidatePattern
import org.slf4j.LoggerFactory

class FlywayMigration {

    companion object {
        private val log = LoggerFactory.getLogger(FlywayMigration::class.java)
        const val SKIP_MIGRATION_FLYWAY_KEY = "flyway.disabled"
    }

    fun execute() {
        if (Property.getSysOrEnv(SKIP_MIGRATION_FLYWAY_KEY, false)) {
            log.info("flyway.disabled=true，将跳过执行")
            return
        }
        val url = Property.getSysOrEnv("database.url")
        val user = Property.getSysOrEnv("database.username")
        val password = Property.getSysOrEnv("database.password")
        val driverClass = Property.getSysOrEnv("database.driverClass","com.mysql.cj.jdbc.Driver")
        val locations = Property.getSysOrEnv("flyway.locations", "db/migration")
        val baselineOnMigrate = Property.getSysOrEnv("flyway.baseline-on-migrate", "true")
        val validateOnMigrate = Property.getSysOrEnv("flyway.validate-on-migrate", "true")
        val cleanDisable = Property.getSysOrEnv("flyway.clean-disabled", "true")
        val ignoreMissingMigration = Property.getSysOrEnv("flyway.ignore-missing-migrations", "false")
        log.info("scripts:{}, dataSource.url:{}", locations, url)
        if (null == url || locations.isEmpty()) {
            log.info("migration execute skip")
            return
        }
        val flyway: Flyway = Flyway.configure()
            .dataSource(url, user, password)
            .driver(driverClass)
            .locations(locations)
            .baselineOnMigrate("true" == baselineOnMigrate)
            .validateOnMigrate("true" == validateOnMigrate)
            .cleanDisabled("true" == cleanDisable)
            .apply {
                if ("true" == ignoreMissingMigration) {
                    this.ignoreMigrationPatterns(ValidatePattern.fromPattern("*:missing"))
                }
            }
            .load()

        // 增加repair方法，解决因外部因素导致脚本初始化失败，再次重试flyway校验Detected failed migration
        flyway.repair()
        flyway.migrate()
    }
}