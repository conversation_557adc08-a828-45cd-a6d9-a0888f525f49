package com.shuyun.loyalty.manager.resource.open

import com.shuyun.loyalty.entity.api.constants.*
import com.shuyun.loyalty.entity.api.request.*
import com.shuyun.loyalty.entity.api.response.*
import com.shuyun.loyalty.entity.dto.*
import com.shuyun.loyalty.entity.dto.MemberPointRequest
import com.shuyun.loyalty.manager.service.OpenMemberPointService
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.service.service.PointDelayRemindRecordService
import com.shuyun.loyalty.service.service.PointExpireRemindRecordService
import com.shuyun.loyalty.service.util.MDCUtils
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.pip.component.json.JsonUtils
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.extensions.Extension
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty
import org.apache.logging.log4j.LogManager
import org.springframework.beans.BeanUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.web.bind.annotation.*
import javax.annotation.Resource
import javax.validation.Valid
import kotlin.concurrent.withLock

@RestController
@RequestMapping("/open/point")
class OpenPointResource {

    private val log = LogManager.getLogger(OpenPointResource::class.java)

    @Autowired
    private lateinit var openMemberPointService: OpenMemberPointService


    @Resource
    private lateinit var loyaltyFacadeFeignApi: LoyaltyFacadeFeignApi

    @Autowired
    private lateinit var pointExpireRemindRecordService: PointExpireRemindRecordService

    @Autowired
    private lateinit var pointDelayRemindRecordService: PointDelayRemindRecordService

    @Autowired
    private lateinit var locker: Locker
    @Operation(summary = "获取会员积分", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member")
    fun findMemberPointList(@Parameter(description = "当前页,从0开始", example = "0") @RequestParam(value = "number", defaultValue = "0") number: Int?,
                            @Parameter(description = "每页记录数 ", example = "20") @RequestParam(value = "pageSize", defaultValue = "20") pageSize: Int?,
                            @Parameter(description = "积分账户id", required = true) @RequestParam(value = "pointAccountId", required = true) pointAccountId: Long,
                            @Parameter(description = "会员ID") @RequestParam(value = "memberId",required = false) memberId: String?): List<MemberPointResponse> {
        val start = System.currentTimeMillis()
        log.info("接口获取会员积分开始，$number-$pointAccountId-$memberId")
        val list = loyaltyFacadeFeignApi.findPoint(MDCUtils.getTraceId(),pointAccountId,memberId,number,pageSize)
        val end = System.currentTimeMillis()
        log.info("耗时:${end - start}ms, 接口获取会员积分结束，$number-$pointAccountId-$memberId")
        return list

    }



    @Operation(summary = "获取会员积分记录", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member/record")
    fun findMemberPointRecordList(@Parameter(description = "当前页,从0开始", example = "0") @RequestParam(value = "number", defaultValue = "0") number: Int?,
                                  @Parameter(description = "每页记录数 ", example = "20") @RequestParam(value = "pageSize", defaultValue = "20") pageSize: Int?,
                                  @Parameter(description = "积分账户id", required = true) @RequestParam(value = "pointAccountId", required = true) pointAccountId: Long,
                                  @Parameter(description = "触发动作") @RequestParam(value = "recordType", required = false) recordType: FSMPointEvent?,
                                  @Parameter(description = "批量触发动作") @RequestParam(value = "recordTypes", required = false) recordTypes: List<FSMPointEvent>?,
                                  @Parameter(description = "状态") @RequestParam(value = "status", required = false) status: List<PCStatus>?,
                                  @Parameter(description = "会员ID") @RequestParam(value = "memberId",required = false) memberId: String?,
                                  @Parameter(description = "店铺ID") @RequestParam(value = "shopId",required = false) shopId: String?,
                                  @Parameter(description = "渠道类型") @RequestParam(value = "channelType",required = false) channelType:String?,
                                  @Parameter(description = "变更筛选开始时间,ISO8601格式") @RequestParam(value = "startTime",required = false) startTime: String?,
                                  @Parameter(description = "变更筛选结束时间,ISO8601格式") @RequestParam(value = "endTime",required = false) endTime: String?,
                                  @Parameter(description = "原单ID") @RequestParam(value = "traceId",required = false) traceId: String?,
                                  @Parameter(description = "排序") @RequestParam(value = "sortType",required = false) sortType: PointSortType?,
                                  @Parameter(description = "变更方式") @RequestParam(value = "changeMode",required = false) changeMode: String?): List<MemberPointRecordResponse> {
        val start = System.currentTimeMillis()
        log.info("接口获取会员积分记录开始，$number-$pointAccountId-$memberId-$changeMode")
        val list = loyaltyFacadeFeignApi.findMemberPointRecordList(
            MDCUtils.getTraceId(),
            number, pageSize, pointAccountId, recordType, recordTypes,
            status, memberId, shopId, channelType, startTime, endTime,
            traceId, sortType, changeMode
        ) ?: emptyList()
        val end = System.currentTimeMillis()
        log.info("接口获取会员积分记录结束 耗时:${end - start}ms, $number-$pointAccountId-$memberId")
        return list
    }


    @Operation(summary = "分页获取会员积分记录", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member/pageRecord")
    fun pageMemberPointRecordList(@Parameter(description = "当前页,从0开始", example = "0") @RequestParam(value = "number", defaultValue = "0") number: Int?,
                                  @Parameter(description = "每页记录数 ", example = "20") @RequestParam(value = "pageSize", defaultValue = "20") pageSize: Int?,
                                  @Parameter(description = "积分账户id", required = true) @RequestParam(value = "pointAccountId", required = true) pointAccountId: Long,
                                  @Parameter(description = "触发动作") @RequestParam(value = "recordType", required = false) recordType: FSMPointEvent?,
                                  @Parameter(description = "批量触发动作") @RequestParam(value = "recordTypes", required = false) recordTypes: List<FSMPointEvent>?,
                                  @Parameter(description = "状态") @RequestParam(value = "status", required = false) status: List<PCStatus>?,
                                  @Parameter(description = "会员ID") @RequestParam(value = "memberId",required = false) memberId: String?,
                                  @Parameter(description = "店铺ID") @RequestParam(value = "shopId",required = false) shopId: String?,
                                  @Parameter(description = "渠道类型") @RequestParam(value = "channelType",required = false) channelType:String?,
                                  @Parameter(description = "变更筛选开始时间,ISO8601格式") @RequestParam(value = "startTime",required = false) startTime: String?,
                                  @Parameter(description = "变更筛选结束时间,ISO8601格式") @RequestParam(value = "endTime",required = false) endTime: String?,
                                  @Parameter(description = "原单ID") @RequestParam(value = "traceId",required = false) traceId: String?,
                                  @Parameter(description = "排序") @RequestParam(value = "sortType",required = false) sortType: PointSortType?,
                                  @Parameter(description = "变更方式") @RequestParam(value = "changeMode",required = false) changeMode: String?): PageImpl<MemberPointRecordResponse> {
        val pageMemberPointRecordList = loyaltyFacadeFeignApi.pageMemberPointRecordList(
            MDCUtils.getTraceId(),
            number, pageSize, pointAccountId, recordType, recordTypes,
            status, memberId, shopId, channelType, startTime, endTime,
            traceId, sortType, changeMode
        )
        val pageImpl = PageImpl(pageMemberPointRecordList.content!!.map {
            JsonUtils.parse(
                JsonUtils.toJson(it),
                MemberPointRecordResponse::class.java
            )
        }, PageRequest.of(number!!, pageSize!!), pageMemberPointRecordList.totalElements ?: 0L)
        return pageImpl
    }


    @Operation(summary = "获取会员积分记录总数", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member/recordCount")
    fun findMemberPointRecordCount(@Parameter(description = "积分账户id", required = true) @RequestParam(value = "pointAccountId", required = true) pointAccountId: Long,
                                   @Parameter(description = "触发动作") @RequestParam(value = "recordType", required = false) recordType: FSMPointEvent?,
                                   @Parameter(description = "批量触发动作") @RequestParam(value = "recordTypes", required = false) recordTypes: List<FSMPointEvent>?,
                                   @Parameter(description = "状态") @RequestParam(value = "status", required = false) status: List<PCStatus>?,
                                   @Parameter(description = "会员ID") @RequestParam(value = "memberId",required = false) memberId: String?,
                                   @Parameter(description = "店铺ID") @RequestParam(value = "shopId",required = false) shopId: String?,
                                   @Parameter(description = "渠道类型") @RequestParam(value = "channelType",required = false) channelType:String?,
                                   @Parameter(description = "变更筛选开始时间,ISO8601格式") @RequestParam(value = "startTime",required = false) startTime: String?,
                                   @Parameter(description = "变更筛选结束时间,ISO8601格式") @RequestParam(value = "endTime",required = false) endTime: String?,
                                   @Parameter(description = "原单ID") @RequestParam(value = "traceId",required = false) traceId: String?,
                                   @Parameter(description = "变更方式") @RequestParam(value = "changeMode",required = false) changeMode: String?): Int {
            val start = System.currentTimeMillis()
        log.info("接口获取会员积分记录总数开始，-$pointAccountId-$memberId-$changeMode")
        val count = loyaltyFacadeFeignApi.findMemberPointRecordCount(
            MDCUtils.getTraceId(), pointAccountId, recordType, recordTypes, status,
            memberId, shopId, channelType, startTime, endTime, traceId, changeMode
        )?.get("totalElements") ?: 0

        val end = System.currentTimeMillis()
        log.info("接口获取会员积分记录总数结束 耗时:${end - start}ms, $pointAccountId-$memberId")
        return count
    }


    @Operation(summary = "获取会员积分记录详情", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member/record/detail")
    fun findPointRecordById(
            @Parameter(description = "积分账户id" ,required = true) @RequestParam(value = "pointAccountId",required = true) pointAccountId:Long,
            @Parameter(description = "积分记录ID" ,required = true) @RequestParam(value = "recordId",required = true) recordId:String
    ): PointRecordDTO {
        return loyaltyFacadeFeignApi.findPointRecordById(MDCUtils.getTraceId(),pointAccountId,recordId)
    }


    /**
     * timeValue：1 timeUnit：DAY 查询今天即将过期的积分
     * timeValue：2 timeUnit：DAY 查询明天即将过期的积分
     */
    @Operation(summary = "获取即将到期积分", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member/valid")
    fun findMemberValidPointList(@Parameter(description = "当前页,从0开始", example = "0") @RequestParam(value = "number", defaultValue = "0") number: Int?,
                                 @Parameter(description = "每页记录数 ", example = "100") @RequestParam(value = "pageSize", defaultValue = "100") pageSize: Int?,
                                 @Parameter(description = "积分账户id", required = true) @RequestParam(value = "pointAccountId", required = true) pointAccountId: Long,
                                 @Parameter(description = "会员ID", required = true) @RequestParam(value = "memberId",required = true) memberId: String,
                                 @Parameter(description = "单位:YEAR,MONTH,DAY", required = true) @RequestParam(value = "timeUnit",required = true) timeUnit: TimeUnit,
                                 @Parameter(description = "值", required = true) @RequestParam(value = "timeValue",required = true) timeValue: Int): List<MemberValidPointResponse> {
        return loyaltyFacadeFeignApi.findValidPoint(
            MDCUtils.getTraceId(), number, pageSize, pointAccountId, memberId, timeUnit, timeValue)
    }


    @Operation(summary = "批量变更会员积分", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/asyncBatchModify")
    fun modifyMemberPointAsyncBatch(@Valid @RequestBody openPointBatch: OpenPointBatch){
        val start = System.currentTimeMillis()
        log.info("批量变更会员积分开始，唯一标示:${openPointBatch.triggerId} : {}", { JsonUtils.toJson(openPointBatch) })
        openPointBatch.openPointBatchItems = openPointBatch.openPointBatchItem
        loyaltyFacadeFeignApi.modifyMemberPointWithRecordBatch(MDCUtils.getTraceId(), openPointBatch)
        log.info("批量变更会员积分结束 耗时:${System.currentTimeMillis() - start}ms, 批次号:${openPointBatch.triggerId}")
    }


    @Operation(summary = "批量变更会员积分查询", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member/batchModifyLog")
    fun queryMemberPointAsyncBatchLog(@Parameter(description = "追溯id") @RequestParam(value = "triggerId") triggerId: String): MemberPointModifyBatchResponse? {
        return loyaltyFacadeFeignApi.findModifyMemberPointLog(MDCUtils.getTraceId(), triggerId)
    }


    @Operation(summary = "查询积分明细记录", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member/pointRecordItem")
    fun queryPointRecordItem(@Parameter(description = "追溯id") @RequestParam(value = "triggerId", required = true) triggerId: String,
                             @Parameter(description = "积分账户id", required = true) @RequestParam(value = "pointAccountId", required = true) pointAccountId: Long): List<MemberPointRecordItemResponse> {
        return loyaltyFacadeFeignApi.findByTraceId(MDCUtils.getTraceId(),triggerId, pointAccountId)
    }


    @Operation(summary = "同步变更会员积分", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/modify")
    fun modifyMemberPoint(@Valid @RequestBody modify: MemberPointModifyRequest): MemberPointSendResponse? {
        val start = System.currentTimeMillis()
        val requestJson = JsonUtils.toJson(modify)
        log.info("同步变更会员积分开始，$requestJson")
        var result: MemberPointSendResponse? = null
        when(modify.recordType){
            OpenFSMPointEvent.SEND -> {
                result = loyaltyFacadeFeignApi.send(MDCUtils.getTraceId(),modify.buildSendRequest())
            }
            OpenFSMPointEvent.DEDUCT -> {
                loyaltyFacadeFeignApi.deduct(MDCUtils.getTraceId(),modify.buildDeductRequest())
            }
            else -> {}
        }
        val end = System.currentTimeMillis()
        log.info("同步变更会员积分结束 耗时: ${end - start}ms,  $requestJson")
        return result
    }


    @Operation(summary = "同步变更冻结,解冻,冻结消耗", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/updateValidFrozenPoint")
    fun updateValidRequest(@Valid @RequestBody updateValidMemberPoint: MemberPointUpdateValidRequest) {
        val start = System.currentTimeMillis()
        val requestJson = JsonUtils.toJson(updateValidMemberPoint)
        log.info("同步变更冻结,解冻,冻结消耗开始，$requestJson")
        when (updateValidMemberPoint.recordType) {
            UpdateValidPointEvent.FREEZE -> loyaltyFacadeFeignApi.frozen(MDCUtils.getTraceId(),buildFrozenRequest(updateValidMemberPoint))
            UpdateValidPointEvent.UNFREEZE -> loyaltyFacadeFeignApi.unfrozen(MDCUtils.getTraceId(),buildCommonRequest(updateValidMemberPoint))
            UpdateValidPointEvent.USE_FREEZE -> loyaltyFacadeFeignApi.frozenDeduct(MDCUtils.getTraceId(),buildCommonRequest(updateValidMemberPoint))
            else -> {}
        }
        val end = System.currentTimeMillis()
        log.info("同步变更冻结,解冻,冻结消耗结束 耗时:${end - start}ms, $requestJson")
    }


    @Operation(summary = "查询即将过期的提醒积分", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member/remind/expire")
    fun findRemindExpire(@Parameter(description = "当前页,从0开始", example = "0") @RequestParam(value = "number", defaultValue = "0") number: Int?,
                         @Parameter(description = "每页记录数 ", example = "100") @RequestParam(value = "pageSize", defaultValue = "100") pageSize: Int?,
                         @Parameter(description = "积分账户id", required = true) @RequestParam(value = "pointAccountId", required = true) pointAccountId: Long,
                         @Parameter(description = "会员ID集合", required = true) @RequestParam(value = "memberIds",required = true) memberIds: List<String>,
                         @Parameter(description = "提醒配置ID", required = false) @RequestParam(value = "remindId", required = false) remindId: Long? = null
    ): MemberRemindPointPage {
        val start = System.currentTimeMillis()
        log.info("查询即将过期的提醒积分开始，{} - {}", memberIds, pointAccountId)
        val responsePage =  pointExpireRemindRecordService.findByMemberIdsPage(memberIds, pointAccountId, PageRequest.of(number!!, pageSize!!), remindId)

        val memberRemindPointPage = MemberRemindPointPage()
        memberRemindPointPage.totalElements = responsePage.totalElements
        memberRemindPointPage.totalPages = responsePage.totalPages.toLong()

        val memberRemindPointResponseList = ArrayList<MemberRemindPointResponse>()
        responsePage.forEach {
            val response = MemberRemindPointResponse()
            BeanUtils.copyProperties(it,response)
            response.id = it.id
            response.memberId = it.memberId
            response.planId = it.planId
            response.pointPlanId = it.pointPlanId
            response.pointPlanName = it.pointPlanName
            response.subjectId = it.subjectId
            response.subjectFqn = it.subjectFqn
            response.effectiveDate  =it.effectiveDate
            response.overdueDate  = it.overdueDate
            response.point = it.point
            response.created = it.created
            response.name = it.name
            response.remindId = it.remindId
            response.desc = it.desc
            memberRemindPointResponseList.add(response)
        }
        memberRemindPointPage.remindPointResponse = memberRemindPointResponseList
        val end = System.currentTimeMillis()
        log.info("查询即将过期的提醒积分结束, 耗时:${end - start}ms, $memberIds - $pointAccountId")
        return memberRemindPointPage
    }

    @Operation(summary = "查询即将生效的提醒积分", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member/remind/delay")
    fun findRemindDelay(@Parameter(description = "当前页,从0开始", example = "0") @RequestParam(value = "number", defaultValue = "0") number: Int?,
                         @Parameter(description = "每页记录数 ", example = "100") @RequestParam(value = "pageSize", defaultValue = "100") pageSize: Int?,
                         @Parameter(description = "积分账户id", required = true) @RequestParam(value = "pointAccountId", required = true) pointAccountId: Long,
                         @Parameter(description = "会员ID集合", required = true) @RequestParam(value = "memberIds",required = true) memberIds: List<String>,
                        @Parameter(description = "提醒配置ID", required = false) @RequestParam(value = "remindId", required = false) remindId: Long? = null
    ): MemberRemindPointPage {
        val start = System.currentTimeMillis()
        log.info("查询即将生效的提醒积分开始，{} - {}", memberIds, pointAccountId)
        val responsePage =  pointDelayRemindRecordService.findByMemberIdsPage(memberIds, pointAccountId, PageRequest.of(number!!, pageSize!!), remindId)

        val memberRemindPointPage = MemberRemindPointPage()
        memberRemindPointPage.totalElements = responsePage.totalElements
        memberRemindPointPage.totalPages = responsePage.totalPages.toLong()

        val memberRemindPointResponseList = ArrayList<MemberRemindPointResponse>()
        responsePage.forEach {
            val response = MemberRemindPointResponse()
            BeanUtils.copyProperties(it,response)
            response.id = it.id
            response.memberId = it.memberId
            response.planId = it.planId
            response.pointPlanId = it.pointPlanId
            response.pointPlanName = it.pointPlanName
            response.subjectId = it.subjectId
            response.subjectFqn = it.subjectFqn
            response.effectiveDate  =it.effectiveDate
            response.overdueDate  = it.overdueDate
            response.point = it.point
            response.created = it.created
            response.name = it.name
            response.remindId = it.remindId
            response.desc = it.desc
            memberRemindPointResponseList.add(response)
        }
        memberRemindPointPage.remindPointResponse = memberRemindPointResponseList
        val end = System.currentTimeMillis()
        log.info("查询即将生效的提醒积分结束, 耗时:${end - start}ms, $memberIds - $pointAccountId")
        return memberRemindPointPage
    }

    @Operation(summary = "积分预算", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/budget")
    fun budget(@Valid @RequestBody budget: MemberPointBudgetBatchRequest):List<MemberPointBudgetBatchResponse> {
        log.info("积分预算接口调用开始 events : {}", Json.toJson(budget.event))
        return openMemberPointService.budget(budget)
    }

    @Operation(summary = "积分账户初始化", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/init")
    fun initMemberPoint(@Valid @RequestBody request: InitMemberPointRequest): MemberPointInitResponse {
        return loyaltyFacadeFeignApi.init(MDCUtils.getTraceId(), MemberPointInitRequest().apply {
            this.memberId = request.memberId
            this.pointAccountIds = request.pointAccountIds
            this.channelType = request.channelType
            this.businessId = request.triggerId
        })
    }

    @Operation(summary = "查询待生效积分", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member/notValidPoint")
    fun findMemberPendingPoints(@Parameter(description = "积分账户ID", required = true) @RequestParam pointAccountId: Long,
                                @Parameter(description = "会员ID", required = true) @RequestParam memberId: String): PendingPointResponse {
        val response = loyaltyFacadeFeignApi.findMemberPendingPoints(MDCUtils.getTraceId(),pointAccountId, memberId)
        return PendingPointResponse().apply {
            this.notValidPoint = response.points
        }
    }

    @Operation(summary = "延长积分过期时间", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/extend")
    fun extendMemberPoint(@Valid @RequestBody request: ExtendMemberPointRequest) {
        val lockStr = String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT, request.pointPlanId, request.memberId)
        val lock = locker.getLock(lockStr)
        lock.withLock {
            openMemberPointService.extend(request)
        }
    }


    @Operation(summary = "作废积分", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/invalidating")
    fun invalidatingMemberPoint(@Valid @RequestBody request: InvalidatingMemberPointRequest) : InvalidatingResponse {
       return loyaltyFacadeFeignApi.invalidating(MDCUtils.getTraceId(), request)
    }

    @Operation(summary = "重新迁移会员积分块数据", tags = ["积分 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/migrateMemberPointSegment")
    fun migrateMemberPointSegment(
        @Parameter(description = "积分账户ID", required = true) @RequestParam pointAccountId: Long,
        @Parameter(description = "会员ID", required = true) @RequestParam memberId: String,
        @Parameter(description = "迁移类型") @RequestParam reference: Int? = 1
    ) {
        val ref = if (reference == null || (reference != 0 && reference != 1)) 1 else reference // 手动迁移默认以有效积分记录为准
        loyaltyFacadeFeignApi.migrateMemberPointSegment(
            MDCUtils.getTraceId(),
            mapOf(
                "pointAccountId" to pointAccountId,
                "memberId" to memberId,
                "reference" to ref
            )
        )
    }

    fun buildFrozenRequest(modify: MemberPointUpdateValidRequest): MemberPointDeductRequest {
        val request = MemberPointDeductRequest()
        request.point =  modify.point!!
        request.pointAccountId = modify.pointAccountId!!
        request.desc = modify.desc
        request.changeMode = modify.changeMode
        request.businessId = modify.openTraceId!!
        request.channelType = modify.channelType!!
        request.memberId = modify.memberId!!
        request.shopId = modify.shopId
        request.KZZD1 = modify.KZZD1
        request.KZZD2 = modify.KZZD2
        request.KZZD3 = modify.KZZD3
        request.actionId = modify.actionId
        request.actionName = modify.actionName
        request.actionNodeId = modify.actionNodeId
        request.actionNodeName = modify.actionNodeName
        request.lockWaitTime = modify.lockWaitTime
        return request
    }

    private fun buildCommonRequest(modify: MemberPointUpdateValidRequest): MemberPointRequest {
        val request = MemberPointDeductRequest()
        request.point =  modify.point!!
        request.pointAccountId = modify.pointAccountId!!
        request.desc = modify.desc
        request.changeMode = modify.changeMode
        request.businessId = modify.openTraceId!!
        request.channelType = modify.channelType!!
        request.memberId = modify.memberId!!
        request.shopId = modify.shopId
        request.KZZD1 = modify.KZZD1
        request.KZZD2 = modify.KZZD2
        request.KZZD3 = modify.KZZD3
        request.actionId = modify.actionId
        request.actionName = modify.actionName
        request.actionNodeId = modify.actionNodeId
        request.actionNodeName = modify.actionNodeName
        request.lockWaitTime = modify.lockWaitTime
        request.autoFillShopId = modify.autoFillShopId
        return request
    }
}