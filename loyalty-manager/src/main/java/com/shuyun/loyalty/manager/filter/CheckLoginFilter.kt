package com.shuyun.loyalty.manager.filter

import com.fasterxml.jackson.databind.node.ObjectNode
import com.shuyun.kylin.i18n.LocaleContext
import com.shuyun.lite.util.Common
import com.shuyun.loyalty.service.util.VisitorInfoUtil
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import com.shuyun.pip.frameworkext.filter.VisitTenantInfoHolder
import com.shuyun.pip.frameworkext.filter.VisitTokenInfoHolder
import com.shuyun.pip.i18n.LocaleI18nContextHolder
import com.shuyun.pip.i18n.LocaleResolver
import com.shuyun.support.common.context.VisitInfoHolder
import org.apache.commons.lang3.StringUtils
import org.apache.logging.log4j.LogManager
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter
import java.io.IOException
import java.util.*
import javax.servlet.FilterChain
import javax.servlet.ServletException
import javax.servlet.annotation.WebFilter
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse


@Component
@WebFilter(urlPatterns = ["/*"], filterName = "authFilter")
class CheckLoginFilter : OncePerRequestFilter() {

    private val log = LogManager.getLogger(CheckLoginFilter::class.java)
    private val whiteUrl = listOf(
        "/loyalty-manager/v1/system",
        "/loyalty-manager/v1/open",
        "/loyalty-manager/v1/basic",
        "/loyalty-manager/v1/fx",
        "/loyalty-manager/v1/v2/api-docs",
        "/loyalty-manager/v1/v3/api-docs"
    )


    @Throws(IOException::class, ServletException::class)
    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain
    ) {
        try {

            val language = request.getHeader("Accept-Language") ?: LocaleResolver.DEFAULT_LANGUAGE
            LocaleI18nContextHolder.setLocale(language)
            LocaleContext.setLocale(LocaleI18nContextHolder.getLocale())

            val withI18nPayload = request.getHeader("WITH_ALL_I18N_HEADER")?.toBoolean()
                ?: request.getParameter(LocaleI18nContextHolder.WITH_I18N_PAYLOAD)?.toBoolean()
                ?: false
            LocaleContext.setWithAllI18n(withI18nPayload)
            LocaleI18nContextHolder.setWithAllI18n(withI18nPayload)

            val uri = request.requestURI
            log.debug("current request uri: $uri, method: ${request.method}")
            if (request.method == HttpMethod.OPTIONS.toString() || whiteUrl.any { uri.startsWith(it) }) {
                log.trace("current request uri in white list")
                UserContextThreadSafe.setWithUserContext(false)
                filterChain.doFilter(request, response)
                return
            }
            val authorization = request.getHeader("Authorization")
            if (!authorization.isNullOrEmpty()) {
                VisitTokenInfoHolder.setToken(authorization)
                UserContextThreadSafe.setWithUserContext(
                    "true" == Common.getSysOrEnv(
                        "dataapi.request.default.use.user.context",
                        "false"
                    )
                )
                VisitorInfoUtil.userId.set(parserAuth(authorization, "sub"))
                VisitorInfoUtil.tenantId.set(parserAuth(authorization, "tenant"))
                VisitTenantInfoHolder.setTenantId(VisitorInfoUtil.tenantId.get())
                VisitorInfoUtil.username.set(parserAuth(authorization, "name"))
                if (StringUtils.isEmpty(VisitorInfoUtil.userId.get()) ||
                    StringUtils.isEmpty(VisitorInfoUtil.tenantId.get()) ||
                    StringUtils.isEmpty(VisitorInfoUtil.username.get())
                ) {
                    log.warn("invalid authorization:{}", authorization)
                    response.sendError(HttpServletResponse.SC_UNAUTHORIZED)
                } else {
                    log.trace("current login user auth passed:{}", authorization)
                    filterChain.doFilter(request, response)
                }
            } else if (request.requestURL.contains("swagger") || Common.getBoolean("devMode")) {
                log.warn("current running devMode, no authorized login user!")
                VisitorInfoUtil.mockAnonymous()
                filterChain.doFilter(request, response)
            } else {
                log.trace("current user unauthorized request {} by type {}", uri, request.method)
                response.sendError(HttpServletResponse.SC_UNAUTHORIZED)
            }
        } finally {
            VisitorInfoUtil.clearLoginInfo()
            VisitTokenInfoHolder.setToken(null)
            VisitInfoHolder.setUserId(null)
            LocaleI18nContextHolder.removeLocale()
            LocaleContext.clear()
        }
    }

    private fun parserAuth(authorization: String, field: String): String? {
        //解析租户
        try {
            val payload = authorization.split("\\.".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()[1]
            val json = String(Base64.getUrlDecoder().decode(payload)).replace("/".toRegex(), "_")
            val jsonObject = JsonUtils.parse(json, ObjectNode::class.java)
            return jsonObject.get(field).asText()
        } catch (e: Exception) {
            log.warn("解析token属性失败：{}-{}", field, authorization, e)
        }
        return null
    }

}
