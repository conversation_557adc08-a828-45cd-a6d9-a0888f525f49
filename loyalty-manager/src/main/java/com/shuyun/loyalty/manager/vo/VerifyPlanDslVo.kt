package com.shuyun.loyalty.manager.vo

import io.swagger.v3.oas.annotations.media.Schema

@Schema(title = "VerifyPlanDslVo")
class VerifyPlanDslVo {

    @Schema(title = "校验结果", type = "Boolean")
    var verifyResult: Boolean = false

    @Schema(title = "错误主体集合", type = "List")
    var errorSubjectList: List<ErrorSubject>? = null

}


@Schema(title = "ErrorSubject")
class ErrorSubject {

    @Schema(title = "主体名称", type = "String")
    var subjectName: String? = null

    @Schema(title = "错误属性集合", type = "List")
    var errorPropertyList: List<ErrorProperty>? = null

    @Schema(title = "错误时机集合", type = "List")
    var errorEventList: List<ErrorEvent>? = null
}

@Schema(title = "ErrorEvent")
class ErrorEvent {

    @Schema(title = "时机名称", type = "Long")
    var eventName: String? = null

    @Schema(title = "错误属性集合", type = "List")
    var errorPropertyList: List<ErrorProperty>? = null

}

@Schema(title = "ErrorProperty")
class ErrorProperty {


    var propertyName: String? = null

    var errorDesc: String? = null

}