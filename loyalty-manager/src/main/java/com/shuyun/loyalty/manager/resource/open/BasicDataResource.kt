package com.shuyun.loyalty.manager.resource.open

import com.shuyun.loyalty.service.service.GradeDefinitionBaseService
import com.shuyun.loyalty.service.service.GradeHierarchyBaseService
import com.shuyun.loyalty.service.service.PlanBaseService
import com.shuyun.loyalty.service.service.PointAccountTypeBaseService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.extensions.Extension
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.Instant
import java.time.ZoneId
import java.time.ZonedDateTime

/**
 */

@RestController
@RequestMapping("/basic/data")
class BasicDataResource {

    @Autowired
    private lateinit var planBaseService: PlanBaseService

    @Autowired
    private lateinit var pointAccountTypeBaseService: PointAccountTypeBaseService

    @Autowired
    private lateinit var gradeHierarchyBaseService: GradeHierarchyBaseService

    @Autowired
    private lateinit var gradeDefinitionBaseService: GradeDefinitionBaseService

    @Operation(summary = "获取所有计划信息", tags = ["获取基础数据,提供给其他微服务使用"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/findPlanList")
    fun findPlanList() = planBaseService.findAll().toList()

    @Operation(summary = "获取所有积分账户信息", tags = ["获取基础数据,提供给其他微服务使用"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/findPointAccountTypeList")
    fun findPointAccountTypeList() = pointAccountTypeBaseService.findAll().toList()

    @Operation(summary = "查询等级体系对应最小等级", tags = ["获取基础数据,提供给其他微服务使用"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/findMinGradeDefinition")
    fun findMinGradeDefinition(@Parameter(description = "等级体系",required = true) @RequestParam(value = "gradeHierarchyId")gradeHierarchyId: Long,
                               @Parameter(description = "事件时间",required = false) @RequestParam(value = "occurrenceTs") occurrenceTs: Long?): Long{
        val zonedDateTime = if(occurrenceTs == null) ZonedDateTime.now() else ZonedDateTime.ofInstant(Instant.ofEpochMilli(occurrenceTs), ZoneId.systemDefault())
        val gradeHierarchy = gradeHierarchyBaseService.getEffectiveOne(gradeHierarchyId, zonedDateTime)
        val gradeDefinition = gradeDefinitionBaseService.findByEnabledHierarchyByVersionId(gradeHierarchy.versionId!!).sortedBy { it.sort }[0]
        return gradeDefinition.id!!
    }

}
