package com.shuyun.loyalty.manager.config

import com.shuyun.easy.filestorage.FileClient
import com.shuyun.easy.filestorage.FileClientFactory
import com.shuyun.easy.filestorage.helper.UploadResponse
import com.shuyun.lite.context.GlobalContext
import com.shuyun.pip.frameworkext.filter.VisitTenantInfoHolder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.io.InputStream

@Configuration
class StorageConfiguration {

    @Bean
    fun fileClient(): FileClient {
        return FileClientFactory().build()
    }

}

fun FileClient.uploadFile(fileExtName: String, inputStream: InputStream, length: Long): UploadResponse {
    return this.uploadFile(
        fileExtName,
        VisitTenantInfoHolder.getTenantId(),
        GlobalContext.serviceName(),
        inputStream,
        length,
        hashMapOf()
    )
}