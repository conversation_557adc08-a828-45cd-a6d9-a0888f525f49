package com.shuyun.loyalty.manager.service

import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.github.benmanes.caffeine.cache.Caffeine
import com.pip.shuyun.pool.transaction.DmTransaction
import com.pip.shuyun.pool.transaction.Propagation
import com.pip.shuyun.pool.transaction.TransactionInfoHolder
import com.shuyun.loyalty.entity.api.*
import com.shuyun.loyalty.entity.api.constants.ChangeMode
import com.shuyun.loyalty.entity.api.constants.ChangeMode.TX_INTERFACE
import com.shuyun.loyalty.entity.api.constants.FSMPointEvent
import com.shuyun.loyalty.entity.api.constants.GradeRecordType
import com.shuyun.loyalty.entity.api.util.Uuid
import com.shuyun.loyalty.entity.dto.MemberPointMessage
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.entity.enums.ForbiddenPort.INTERFACE
import com.shuyun.loyalty.entity.enums.ProcessRecordTypeEnum
import com.shuyun.loyalty.sdk.localDate
import com.shuyun.loyalty.sdk.toEpochMilli
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.service.datamodel.MemberGrade
import com.shuyun.loyalty.service.datamodel.MemberGradeRecord
import com.shuyun.loyalty.service.datamodel.MemberPoint
import com.shuyun.loyalty.service.datamodel.PointTransaction
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.fx.function.CurrentFuncTLK
import com.shuyun.loyalty.service.meta.EventOccasionEnum.CALC_GRADE
import com.shuyun.loyalty.service.meta.EventOccasionEnum.CALC_POINT
import com.shuyun.loyalty.service.meta.EventOperationEnum
import com.shuyun.loyalty.service.meta.GradeRuleGroupTypeEnum
import com.shuyun.loyalty.service.meta.GradeRuleGroupTypeEnum.UPGRADE
import com.shuyun.loyalty.service.meta.PropertyBelongerTypeEnum.EVENT
import com.shuyun.loyalty.service.meta.PropertyBelongerTypeEnum.SUBJECT
import com.shuyun.loyalty.service.meta.RuleSortTypeEnum.*
import com.shuyun.loyalty.service.meta.TimeTypeEnum
import com.shuyun.loyalty.service.meta.TypeEnum
import com.shuyun.loyalty.service.meta.TypeEnum.GRADE
import com.shuyun.loyalty.service.meta.TypeEnum.POINT
import com.shuyun.loyalty.service.model.*
import com.shuyun.loyalty.service.model.ForbiddenOperation.POINT_DEDUCT
import com.shuyun.loyalty.service.model.ForbiddenOperation.POINT_SEND
import com.shuyun.loyalty.service.repository.MemberGradeRecordRepository
import com.shuyun.loyalty.service.repository.MemberGradeRepository
import com.shuyun.loyalty.service.service.*
import com.shuyun.loyalty.service.transfer.points.*
import com.shuyun.loyalty.service.transfer.points.LoyaltyRequestType.TX_API
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.DateUtils
import com.shuyun.loyalty.service.util.ExpressionIdentUtil
import com.shuyun.loyalty.service.util.ProcessRecordUtil
import com.shuyun.pip.ApplicationContextHolder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.*
import java.time.temporal.ChronoUnit
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.jvm.optionals.getOrNull

@Service
class PrecomputeService {

    private val logger = LoggerFactory.getLogger(PrecomputeService::class.java)

    @Autowired
    private lateinit var pointRuleGroupService: PointRuleGroupBaseService

    @Autowired
    private lateinit var pointSendRuleService: PointSendRuleBaseService

    @Autowired
    private lateinit var gradeRuleGroupService: GradeRuleGroupBaseService

    @Autowired
    private lateinit var memberPointService: MemberPointService

    @Autowired
    private lateinit var memberPointSendTransfer: MemberPointSendTransfer

    @Autowired
    private lateinit var memberGradeRepository: MemberGradeRepository

    @Autowired
    private lateinit var notifyBaseService: NotifyBaseService

    @Autowired
    private lateinit var memberGradeRecordRepository: MemberGradeRecordRepository

    @Autowired
    private lateinit var specialListService: SpecialListService

    @Autowired
    private lateinit var memberPointDeductTransfer: MemberPointDeductTransfer
    
    @Autowired
    private lateinit var pointSynchProducer: PointSyncProducer

    @Autowired
    private lateinit var propertyBaseService: CustomizedPropertyBaseService

    private data class ForbiddenConfigKey(val subjectId: Long, val accountTypeId: Long, val type: TypeEnum)
    private data class BlockListKey(val subjectId: Long, val subjectUid: String, val accountTypeId: Long, val type: TypeEnum, val forbiddenOperation: ForbiddenOperation)
    private data class PointCacheKey(val planId: Long, val accountTypeId: Long, val eventTypeId: Long, val operation: EventOperationEnum)
    private data class GradeCacheKey(val gradeHierarchyId: Long, val groupType: GradeRuleGroupTypeEnum, val eventTypeId: Long)


    companion object {
        // 使用最低的等级
        private val useMinGradeAsDefault = Property.getSysOrEnv("current.grade.min.enabled", true)
    }

    private val pointRuleCache = Caffeine.newBuilder()
        .refreshAfterWrite(Duration.ofSeconds(30))
        .maximumSize(50000)
        .expireAfterWrite(30, TimeUnit.DAYS)
        .build<PointCacheKey, List<PointRuleGroup>> { key ->
            val pointRuleGroups = pointRuleGroupService.findPointRuleGroup(key.planId, key.accountTypeId, key.eventTypeId, key.operation)
            pointSendRuleService.findByRuleGroupIds(pointRuleGroups.map { it.id!! }).filter { it.disabled == false }
                .groupBy { it.ruleGroupId }
                .forEach { (k, v) -> pointRuleGroups.find { it.id == k }?.sendRuleList = v.sortedBy { it.sort } }
            pointRuleGroups
        }


    private val gradeRuleCache = Caffeine.newBuilder()
        .refreshAfterWrite(Duration.ofSeconds(30))
        .maximumSize(50000)
        .expireAfterWrite(30, TimeUnit.DAYS)
        .build<GradeCacheKey, List<GradeRuleGroup>> { k ->
            val groups =
                gradeRuleGroupService.findGradeHierarchyIdAndGroupType(k.gradeHierarchyId, k.groupType)
                    .filter {
                        it.eventTypeId == k.eventTypeId
                    }
            groups
        }


    private val forbiddenConfigCache = Caffeine.newBuilder()
        .refreshAfterWrite(Duration.ofSeconds(30))
        .maximumSize(50000)
        .expireAfterWrite(30, TimeUnit.DAYS)
        .build<ForbiddenConfigKey, List<PointForbiddenConfig>> { k ->
            specialListService.find(k.subjectId, k.accountTypeId, k.type)
        }


    fun getPointRuleGroups(planId: Long, pointAccountTypeId: Long, eventTypeId: Long, scoreType: EventOperationEnum, date: Date): List<PointRuleGroup> {
        return pointRuleCache.get(PointCacheKey(planId, pointAccountTypeId, eventTypeId, scoreType))
            ?.filter { it.startTime!!.before(date) && (it.endTime == null || it.endTime!!.after(date)) } ?: emptyList()
    }

    fun getPointRuleGroup(planId: Long, pointAccountTypeId: Long, eventTypeId: Long, scoreType: EventOperationEnum, groupId: Long): PointRuleGroup? {
        return pointRuleCache.get(PointCacheKey(planId, pointAccountTypeId, eventTypeId, scoreType))
            ?.firstOrNull { it.id == groupId }
    }


    fun getGradeRuleGroups(gradeHierarchyId: Long, eventTypeId: Long, date: Date): List<GradeRuleGroup> {
        return gradeRuleCache.get(GradeCacheKey(gradeHierarchyId, UPGRADE, eventTypeId))
            ?.filter { it.startTime!!.before(date) && (it.endTime == null || it.endTime!!.after(date)) } ?: emptyList()
    }


    fun getPlanSubjectEventT(fqn: String, accountTypeIds: Map<TypeEnum, List<Long>>, date: ZonedDateTime): Triple<Plan, Subject, EventType> {
        if (accountTypeIds.isEmpty()) {
            throw LoyaltyException(LoyaltyExceptionCode.REQUEST_EXCEPTION)
        }
        val plans = LoyaltyPrograms.findPlansByFqn(fqn, date)
        var plan: Plan? = null
        accountTypeIds.forEach { (k, ids) ->
            if (ids.isEmpty()) {
                return@forEach
            }
            if (k == POINT) {
                ids.forEach { id ->
                    val plan0 = LoyaltyPrograms.findPlanByAccountTypeId(id, date)
                    if (plan0 == null) {
                        logger.error("通过积分账户类型ID(${id})没有匹配到任何忠诚度计划方案")
                        throw LoyaltyException(LoyaltyExceptionCode.REQUEST_EXCEPTION)
                    }
                    if (plan == null) {
                        plan = plan0
                    } else {
                        if (plan0.id != plan!!.id) {
                            logger.error("积分账户类型参数匹配到多个忠诚度计划方案 {}", Json.toJson(accountTypeIds[POINT]))
                            throw LoyaltyException(LoyaltyExceptionCode.REQUEST_EXCEPTION)
                        }
                    }
                }
            } else if (k == GRADE) {
                ids.forEach { id ->
                    val plan0 = LoyaltyPrograms.findPlanByGradeHierarchyId(id, date)
                    if (plan0 == null) {
                        logger.error("通过等级体系ID(${id})没有匹配到任何忠诚度计划方案")
                        throw LoyaltyException(LoyaltyExceptionCode.REQUEST_EXCEPTION)
                    }
                    if (plan == null) {
                        plan = plan0
                    } else {
                        if (plan0.id != plan!!.id) {
                            logger.error("等级体系参数匹配到多个忠诚度计划方案 {}", Json.toJson(accountTypeIds[GRADE]))
                            throw LoyaltyException(LoyaltyExceptionCode.REQUEST_EXCEPTION)
                        }
                    }
                }
            }
        }
        if (plan == null) {
            logger.error("积分试算 无效的账户类型参数 {}", Json.toJson(accountTypeIds))
            throw LoyaltyException(LoyaltyExceptionCode.REQUEST_EXCEPTION)
        }
        val pl = plans.find { it.id == plan.id }
        if (pl == null) {
            logger.error("找不到忠诚度计划方案 {}", plan.id)
            throw LoyaltyException(LoyaltyExceptionCode.REQUEST_EXCEPTION)
        }
        var subject0: Subject? = null
        var eventType0: EventType? = null
        for (sb in pl.subjectList!!) {
            sb.eventTypeList?.forEach {
                if (it.eventStream == fqn) {
                    subject0 = sb
                    eventType0 = it
                }
            }
        }
        if (eventType0 == null) {
            logger.error("找不到时机类型 {}", fqn)
            throw LoyaltyException(LoyaltyExceptionCode.REQUEST_EXCEPTION)
        }
        val subject = subject0!!
        val eventType = eventType0
        return Triple(pl, subject, eventType)
    }


    fun buildPrecomputeTasks(req: PrecomputeRequest): PrecomputeTuple6<Plan, Subject, EventType, TreeMap<Int, PrecomputeTask>, String, ZonedDateTime> {
        val date = ZonedDateTime.now()
        val (plan, subject, eventType) = getPlanSubjectEventT(req.event.getFqn(), req.accountTypeIds, date)
        val subjectUid: String
        val pe = createPrecomputeEvent(plan, subject, eventType, req.event, req, date)
        val memberId = eventType.referencePath?.let {
            val k = Json.parse<HashMap<String, String>>(it)[subject.dataType]
            k?.let { req.event.get(k)?.toString() }
        }
        subjectUid = memberId ?: throw LoyaltyException(LoyaltyExceptionCode.MEMBER_NOT_FOUND)
        if (req.lastResponse != null && req.lastResponse!!.subjectUid != subjectUid) {
            throw LoyaltyException(LoyaltyExceptionCode.POINT_TRIAL_SIGN_FAILED)
        }
        val tasks = TreeMap<Int, PrecomputeTask>(Comparator.reverseOrder())
        if (eventType.occasion?.contains(CALC_POINT) == true && !subject.pointAccountTypeList.isNullOrEmpty()) {
            createPointTask(pe, date).forEach {
                tasks[it.executeOrder] = it
            }
        }
        if (eventType.occasion?.contains(CALC_GRADE) == true && !subject.gradeHierarchyList.isNullOrEmpty()) {
            createGradeTask(pe, date).forEach {
                tasks[it.executeOrder] = it
            }
        }
        return PrecomputeTuple6(plan, subject, eventType, tasks, subjectUid, date)
    }


    @DmTransaction
    fun precompute(tuple6: PrecomputeTuple6<Plan, Subject, EventType, TreeMap<Int, PrecomputeTask>, String, ZonedDateTime>, req: PrecomputeRequest): PrecomputeResponse {
        val (plan, subject, _, tasks, subjectUid, date) = tuple6
        val currentPoints = HashMap<Long, BigDecimal>()
        val currentGrades = HashMap<Long, Long>()
        val blockList = HashMap<BlockListKey, Boolean>()
        val xs = ArrayList<PrecomputeResult>()
        try {
            for ((_, task) in tasks) {
                val accountTypeId = task.accountTypeId
                if (task.eoe == CALC_POINT) {
                    if (currentPoints[accountTypeId] == null) {
                        currentPoints[accountTypeId] = calculateCurrentPoints(accountTypeId, subjectUid, req)
                    }
                    CurrentFuncTLK.setCurrentPoint(accountTypeId, currentPoints[accountTypeId]!!)
                }
                if (task.eoe == CALC_GRADE) {
                    if (currentGrades[accountTypeId] == null) {
                        var minGradeDefinitionId = -1L
                        if (useMinGradeAsDefault) {
                            plan.subjectList
                                ?.flatMap { s -> s.gradeHierarchyList.orEmpty() }
                                ?.find { it.id == accountTypeId }
                                ?.gradeDefinitions
                                ?.minByOrNull { it.sort!! }
                                ?.id
                                ?.let { id -> minGradeDefinitionId = id }
                        }
                        currentGrades[accountTypeId] = calculateCurrentGrade(accountTypeId, subjectUid, req, minGradeDefinitionId)
                    }
                    CurrentFuncTLK.setCurrentGrade(accountTypeId, currentGrades[accountTypeId]!!)
                }
            }
            for ((_, task) in tasks) {
                val accountTypeId = task.accountTypeId
                if (task.eoe == CALC_POINT) {
                    logger.info("计算积分 accountTypeId: {} executeOrder: {}", accountTypeId, task.executeOrder)
                    val results = task.func()
                    if (req.checkPointsQuotaAndBlockList) {
                        // 检查扣减黑名单
                        req.deductions?.forEach { x ->
                            checkBlockList(
                                POINT,
                                x.accountTypeId,
                                POINT_DEDUCT,
                                INTERFACE,
                                subject.id!!,
                                subject.dataType!!,
                                subjectUid,
                                blockList
                            )
                        }

                        results.forEach {
                            if (it is PrecomputeRulePoint) {
                                // 检查发放黑名单
                                checkBlockList(
                                    POINT,
                                    it.accountTypeId,
                                    POINT_SEND,
                                    ForbiddenPort.CALC_EVENT,
                                    subject.id!!,
                                    subject.dataType!!,
                                    subjectUid,
                                    blockList
                                )
                                try {
                                    // 检查积分上限
                                    ApplicationContextHolder.getBean(PrecomputeService::class.java).checkPointQuota(
                                        planId = plan.id!!,
                                        subjectId = subject.id!!,
                                        subjectFqn = subject.dataType!!,
                                        memberId = subjectUid,
                                        beforePoints = currentPoints[accountTypeId]!!,
                                        rulePoint = it,
                                        date = date
                                    )
                                } catch (e: Exception) {
                                    if (e !is ExceptionCC) {
                                        throw e
                                    }
                                }
                            }
                        }
                    }
                    results.forEach {
                        if (it is PrecomputeRulePoint) {
                            if (date.isAfter(it.effectiveDate)) {
                                currentPoints[accountTypeId] = currentPoints[accountTypeId]!!.add(it.points)
                            }
                            xs.add(it)
                        }
                    }
                    CurrentFuncTLK.setCurrentPoint(accountTypeId, currentPoints[accountTypeId]!!)
                }
                if (task.eoe == CALC_GRADE) {
                    logger.info("计算等级 accountTypeId: {} executeOrder: {}", accountTypeId, task.executeOrder)
                    val results = task.func()
                    results.forEach {
                        if (it is PrecomputeRuleGrade) {
                            if (req.checkPointsQuotaAndBlockList) {
                                // 检查等级升级黑名单
                                checkBlockList(
                                    GRADE,
                                    it.gradeHierarchyId,
                                    ForbiddenOperation.GRADE_UPGRADE,
                                    ForbiddenPort.CALC_EVENT,
                                    subject.id!!,
                                    subject.dataType!!,
                                    subjectUid,
                                    blockList
                                )
                            }
                            val before = it.gradeDefinitionIdSort[currentGrades[accountTypeId]!!]
                            val after = it.gradeDefinitionIdSort[it.gradeDefinitionId]!!
                            if (before == null || after > before) {
                                currentGrades[accountTypeId] = it.gradeDefinitionId
                                xs.add(it)
                            }
                        }
                    }
                    CurrentFuncTLK.setCurrentGrade(accountTypeId, currentGrades[accountTypeId]!!)
                }
            }
        } finally {
            CurrentFuncTLK.clear()
        }
        val response = PrecomputeResponse(
            planId = plan.id!!,
            planName = plan.name!!,
            subjectId = subject.id!!,
            subjectName = subject.name!!,
            subjectFqn = subject.dataType!!,
            subjectUid = subjectUid,
            contents = xs,
            deductions = req.deductions,
            lastResponse = req.lastResponse,
            dateTime = date
        )
        return response
    }



    @DmTransaction
    fun confirm(response: PrecomputeResponse) {
        val currentPoints = HashMap<Long, MemberPoint>()
        val currentPointsLastModified = HashMap<Long, ZonedDateTime>()
        val currentGrades = HashMap<Long, MemberGrade>()
        val currentGradeLastModified = HashMap<Long, ZonedDateTime>()
        val gradeRecords = mutableListOf<MemberGradeRecord>()
        val blockList = HashMap<BlockListKey, Boolean>()
        val pointAccountTypeIds = extractPointAccountTypeIds(response)
        for (pointAccountTypeId in pointAccountTypeIds) {
            val mp = memberPointService.getByMemberId(pointAccountTypeId, response.subjectUid)
            if (mp == null) {
                val memberPoint = MemberPoint()
                memberPoint.planId = response.planId
                memberPoint.pointPlanId = pointAccountTypeId
                memberPoint.memberId = response.subjectUid
                memberPoint.subjectFqn = response.subjectFqn
                memberPoint.point = BigDecimal.ZERO
                memberPoint.version = 0
                memberPoint.openSegmentFlag = true
                memberPointService.saveOrUpdate(memberPoint, replacePattern = pointAccountTypeId.toString())
                currentPoints[pointAccountTypeId] = memberPoint
                currentPointsLastModified[pointAccountTypeId] = ZonedDateTime.now().minusYears(1)
            } else {
                currentPoints[pointAccountTypeId] = mp
                currentPointsLastModified[pointAccountTypeId] = mp.modified
            }
        }
        val gradeHierarchyIds = extractPointGradeHierarchyIds(response)
        for (gradeHierarchyId in gradeHierarchyIds) {
            val mg = memberGradeRepository.findMemberGrade(response.subjectUid, gradeHierarchyId.toString()).getOrNull()
            val latestCreated = memberGradeRecordRepository.findLatestRecordCreate(gradeHierarchyId, response.subjectUid)
            if (mg == null) {
                val memberGrade = MemberGrade()
                memberGrade.planId = response.planId
                memberGrade.gradeHierarchyId = gradeHierarchyId
                memberGrade.memberId = response.subjectUid
                memberGrade.currentGradeDefinitionId = -1
                memberGrade.currentGradeName = "无等级"
                memberGrade.effectDate = response.dateTime!!
                memberGradeRepository.save(memberGrade, gradeHierarchyId.toString())
                currentGrades[gradeHierarchyId] = memberGrade
            } else {
                currentGrades[gradeHierarchyId] = mg
            }
            currentGradeLastModified[gradeHierarchyId] = latestCreated ?: ZonedDateTime.now().minusYears(1)
        }

        val now = ZonedDateTime.now()
        var i = 0
        fun date(): ZonedDateTime = now.plus((i++).toLong(), ChronoUnit.MILLIS)
        fun confirm0(response: PrecomputeResponse) {
            if (response.lastResponse != null) {
                confirm0(response.lastResponse!!)
            }
            response.deductions?.forEach { x ->
                checkBlockList(POINT, x.accountTypeId, POINT_DEDUCT, INTERFACE, response.subjectId, response.subjectFqn, response.subjectUid, blockList)
                val mp = currentPoints[x.accountTypeId]!!
                val date = date()
                LoyaltyPoints.of(
                    memberPoint = mp,
                    type = TX_API,
                    point = x.point,
                    changeMode = TX_INTERFACE.name,
                    channel = x.channelType,
                    traceId = x.key!!,
                    uniqueId = x.key!!,
                    shopId = x.shopId,
                    kzzd1 = x.kzzd1,
                    kzzd2 = x.kzzd2,
                    kzzd3 = x.kzzd3,
                    desc = x.desc,
                    date = date
                ).also {
                    it.attr.actionId = x.actionId
                    it.attr.actionName = x.actionName
                    it.attr.actionNodeId = x.actionNodeId
                    it.attr.actionNodeName = x.actionNodeName
                }.also {
                    memberPointDeductTransfer.deduct(
                        lp = it,
                        journalKey = x.key!!,
                        forbiddenPort = INTERFACE,
                        operation = null
                    )
                    mp.point = it.afterTotalPoints
                    PointTransaction().apply {
                        this.id = Uuid.uuid
                        this.businessId = x.key!!
                        this.memberId = response.subjectUid
                        this.point = x.point
                        this.created = date
                        this.modified = date
                        this.recordType = FSMPointEvent.DEDUCT.name
                    }.insert(x.accountTypeId.toString())
                }
            }
            for (content in response.contents) {
                val c = content as Map<*, *>
                val date = date()
                when (c["type"]) {
                    POINT.name -> {
                        val rulePoint = Json.convert<PrecomputeRulePoint>(c)
                        // 查询当前积分
                        val mp = currentPoints[rulePoint.accountTypeId]!!
                        if (currentPointsLastModified[rulePoint.accountTypeId]!!.isAfter(response.dateTime)) {
                            throw LoyaltyException(LoyaltyExceptionCode.DATA_CHANGED)
                        }
                        checkBlockList(POINT, rulePoint.accountTypeId, POINT_SEND, ForbiddenPort.CALC_EVENT, response.subjectId, response.subjectFqn, response.subjectUid, blockList)
                        val id = Uuid.uuid
                        val lp = loyaltyPoints(mp, rulePoint, response.subjectId, date, id)
                        val action = if (rulePoint.effectiveDate.isAfter(date)) FSMPointEvent.DELAY_SEND else FSMPointEvent.SEND
                        val gs = memberPointSendTransfer.initGainPoints(lp, id, action, rulePoint.points, rulePoint.effectiveDate, lp.attr.overdueDate!!).save(lp.hierarchy.id)
                        val journalKey = lp.attr.uniqueId + "-"  + gs.id
                        val msg = memberPointSendTransfer.send(lp, gs.point, null, journalKey)
                        if (gs.point != lp.afterPoints || !lp.hierarchy.sendLimitResults.isNullOrEmpty()) {
                            logger.info("积分被发放上限过滤 之前分值：{} 过滤后分值：{} {}", gs.point, lp.afterPoints, msg)
                            throw LoyaltyException(LoyaltyExceptionCode.POINT_TRIAL_PLUS_FAILED)
                        }
                        mp.point = lp.afterTotalPoints
                    }
                    GRADE.name -> {
                        val ruleGrade = Json.convert<PrecomputeRuleGrade>(c)
                        checkBlockList(GRADE, ruleGrade.gradeHierarchyId, ForbiddenOperation.GRADE_UPGRADE, ForbiddenPort.CALC_EVENT, response.subjectId, response.subjectFqn, response.subjectUid, blockList)
                        val streamProcessed = ProcessRecordUtil.isProcessRecord(ruleGrade.gradeHierarchyId.toString(),ruleGrade.uniqueId, ProcessRecordTypeEnum.GRADE)
                        if(streamProcessed){
                            logger.info("等级体系下已处理过该事件，X-Business-Token: ${ruleGrade.uniqueId},等级体系ID: ${ruleGrade.gradeHierarchyId}")
                            throw LoyaltyException(LoyaltyExceptionCode.TOKEN_REPEATED)
                        }
                        val memberGrade = currentGrades[ruleGrade.gradeHierarchyId]!!
                        if (currentGradeLastModified[ruleGrade.gradeHierarchyId]!!.isAfter(response.dateTime)) {
                            throw LoyaltyException(LoyaltyExceptionCode.DATA_CHANGED)
                        }
                        val originGrade = Json.copy(memberGrade)
                        memberGrade.currentGradeDefinitionId = ruleGrade.gradeDefinitionId
                        memberGrade.currentGradeName = ruleGrade.gradeDefinitionName
                        memberGrade.effectDate = date
                        memberGrade.overdueDate = ruleGrade.overdueDate
                        memberGradeRepository.saveOrUpdate(memberGrade, ruleGrade.gradeHierarchyId.toString())

                        val memberGradeRecord = MemberGradeRecord.init(
                            originGrade,
                            memberGrade,
                            GradeRecordType.UPGRADE,
                            ruleGrade.uniqueId,
                            ruleGrade.ruleGroupName,
                            (ruleGrade.channel ?: ConstantValue.DEFAULT_CHANNEL_TYPE),
                            ChangeMode.AUTO_CALC,
                            ruleGrade.traceId,
                            ruleGrade.eventTypeName,
                            ruleGrade.ruleId,
                            ruleGrade.ruleGroupName,
                        ).apply {
                            id = Uuid.uuid
                            created = date
                        }
                        memberGradeRecordRepository.save(memberGradeRecord, ruleGrade.gradeHierarchyId.toString())
                        gradeRecords.add(memberGradeRecord)
                    }
                }
            }
        }
        confirm0(response)
        TransactionInfoHolder.afterCommit {
            for ((accountTypeId, mp) in currentPoints) {
                pointSynchProducer.send(MemberPointMessage(accountTypeId, mp.memberId, mp.id!!))
            }
            for (gradeRecord in gradeRecords) {
                notifyBaseService.postIntoBus(gradeRecord)
            }
        }
    }


    private fun extractPointAccountTypeIds(response: PrecomputeResponse): Set<Long> {
        val ids = mutableSetOf<Long>()
        for (content in response.contents) {
            val c = content as Map<*, *>
            val type = c["type"] as? String ?: continue
            if (type == POINT.name) {
                ids.add(c["accountTypeId"].toString().toLong())
            }
        }
        response.lastResponse?.let {
            ids.addAll(extractPointAccountTypeIds(it))
        }
        return ids
    }


    private fun extractPointGradeHierarchyIds(response: PrecomputeResponse): Set<Long> {
        val ids = mutableSetOf<Long>()
        for (content in response.contents) {
            val c = content as Map<*, *>
            val type = c["type"] as? String ?: continue
            if (type == GRADE.name) {
                ids.add(c["gradeHierarchyId"].toString().toLong())
            }
        }
        response.lastResponse?.let { ids.addAll(extractPointGradeHierarchyIds(it)) }
        return ids
    }


    private fun calculateCurrentPoints(accountTypeId: Long, subjectUid: String, req: PrecomputeRequest): BigDecimal {
        val mp = memberPointService.getByMemberId(accountTypeId, subjectUid)
        var points = mp?.point ?: BigDecimal.ZERO
        fun calculateCurrentPoints(resp: PrecomputeResponse?) {
            if (resp == null) return
            if (mp != null && mp.modified.isAfter(resp.dateTime!!)) {
                logger.error("账户积分已经发生了变更")
                throw LoyaltyException(LoyaltyExceptionCode.DATA_CHANGED)
            }
            calculateCurrentPoints(resp.lastResponse)
            resp.deductions?.filter { it.accountTypeId == accountTypeId }?.forEach {
                points -= it.point
            }
            resp.contents.forEach {
                val m = it as Map<*, *>
                if (m["type"] == POINT.name && m["accountTypeId"].toString().toLong() == accountTypeId) {
                    val pointValue = m["points"].toString().toBigDecimal()
                    points += pointValue
                }
            }
        }
        calculateCurrentPoints(req.lastResponse)
        val beforePoints = points
        val deductions = req.deductions?.filter { it.accountTypeId == accountTypeId }
        deductions?.forEach { points -= it.point }
        if (!deductions.isNullOrEmpty() && points < BigDecimal.ZERO) {
            logger.error("试算扣减失败，账户积分不足 accountTypeId: $accountTypeId, memberId: $subjectUid beforePoints: $beforePoints, afterPoints: $points")
            throw LoyaltyException(LoyaltyExceptionCode.POINT_TRIAL_DEDUCT_FAILED)
        }
        return points
    }


    private fun calculateCurrentGrade(gradeHierarchyId: Long, subjectUid: String, req: PrecomputeRequest, minGradeDefinition: Long): Long {
        val currentGrade = memberGradeRepository.findMemberGrade(subjectUid, gradeHierarchyId.toString()).getOrNull()
        var gradeDefinitionId = currentGrade?.currentGradeDefinitionId ?: minGradeDefinition
        val latestCreated = memberGradeRecordRepository.findLatestRecordCreate(gradeHierarchyId, subjectUid)
        fun calculateCurrentGrade(resp: PrecomputeResponse?) {
            if (resp == null) return
            if (latestCreated != null && latestCreated.isAfter(resp.dateTime!!)) {
                throw LoyaltyException(LoyaltyExceptionCode.DATA_CHANGED)
            }
            calculateCurrentGrade(resp.lastResponse)
            resp.contents.forEach {
                val m = it as Map<*, *>
                if (m["type"] == GRADE.name && m["gradeHierarchyId"].toString().toLong() == gradeHierarchyId) {
                    val gradeDefinitionId0 = m["gradeDefinitionId"].toString().toLong()
                    if (gradeDefinitionId0 > gradeDefinitionId) {
                        gradeDefinitionId = gradeDefinitionId0
                    }
                }
            }
        }
        calculateCurrentGrade(req.lastResponse)
        return gradeDefinitionId
    }


    private fun createPrecomputeEvent(plan: Plan, subject: Subject, eventType: EventType, e: Event, req: PrecomputeRequest, date: ZonedDateTime): PrecomputeEvent {
        e.setDetectionTs(date.toInstant().toEpochMilli())
        e.setOccurrenceTs(date.toInstant().toEpochMilli())
        return PrecomputeEvent(plan, subject, eventType, e, req, date)
    }


    private fun createPointTask(pe: PrecomputeEvent, date: ZonedDateTime): List<PrecomputeTask> {
        val tasks = ArrayList<PrecomputeTask>()
        for (accountType in pe.subject.pointAccountTypeList!!) {
            if (pe.req.accountTypeIds[POINT] != null && pe.req.accountTypeIds[POINT]!!.none { it == accountType.id }) {
                continue
            }
            // 查询积分规则组
            val groups = getPointRuleGroups(
                pe.plan.id!!,
                accountType.id!!,
                pe.eventType.id!!,
                pe.eventType.operation!!,
                Date.from(date.toInstant())
            )
            if (groups.isEmpty()) {
                continue
            }
            val func = x@ {
                val totalRulePoints = ArrayList<PrecomputeRulePoint>()
                for (group in groups) {
                    val rulePoints = ArrayList<PrecomputeRulePoint>()
                    for (rule in group.sendRuleList!!) {
                        if (rule.fileStatus == true) {
                            continue
                        }
                        try {
                            val conditionResult = ExpressionIdentUtil.eppById(
                                rule.conditionExpressionFxId!!,
                                pe.event,
                                JsonNodeFactory.instance.objectNode().put("subject", pe.subject.dataType)
                            )
                            val conditionBooleanResult = conditionResult?.toString()?.toBoolean() ?: false
                            logger.debug(
                                "规则表达式条件过滤 规则组信息：规则组ID：{}, 规则名称：{}, 规则Id：{} 规则名称：{} fxId: {} 结果：{}",
                                rule.ruleGroupId,
                                rule.ruleName,
                                rule.id,
                                rule.ruleName,
                                rule.conditionExpressionFxId,
                                conditionResult
                            )
                            if (!conditionBooleanResult) {
                                continue
                            }
                        } catch (e: Exception) {
                            logger.error(
                                "表达式计算错误：规则组ID：{}, 规则组名称：{}, 规则ID：{} 规则名称：{}, 表达式：{}",
                                group.id, group.groupName, rule.id, rule.ruleName, rule.conditionExpression, e
                            )
                            throw e
                        }

                        // 条件已经满足
                        // 计算该规则的积分值
                        //积分值
                        val pointValue = ExpressionIdentUtil.eppById(
                            rule.actionExpressionFxId!!,
                            pe.event,
                            JsonNodeFactory.instance.objectNode().put("subject", pe.subject.dataType)
                        )?.toString()?.toBigDecimalOrNull()
                        if (pointValue == null) {
                            logger.error(
                                "计算出的积分值不是一个数值：规则组ID：{}, 规则组名称：{}, 规则ID：{} 规则名称：{}, 表达式：{}",
                                group.id, group.groupName, rule.id, rule.ruleName, rule.actionExpression
                            )
                            throw IllegalArgumentException("计算出的积分值不是一个数值")
                        }


                        val point = BigDecimal(pointValue.toString()).setScale(accountType.precision!!, accountType.rounding)
                        if (point < BigDecimal.ZERO) {
                            logger.error(
                                "计算出的积分值({})小于0：规则组ID：{}, 规则组名称：{}, 规则ID：{} 规则名称：{}, 表达式：{}",
                                group.id, group.groupName, rule.id, rule.ruleName, rule.actionExpression, point
                            )
                            throw IllegalArgumentException("计算出的积分值(${point})小于0")
                        }
                        // 生效时间和失效时间
                        val (effectiveDateTime, overdueDateTime) = rule.calcPointDateTime(pe.subject.dataType!!, pe.event, date, pe.eventType.pointSendCycleTimePath)
                        if (overdueDateTime != null) {
                            if (overdueDateTime.isBefore(effectiveDateTime)) {
                                logger.error("规则组信息：规则组ID：{}, 规则名称：{}, 规则Id：{} 规则名称：{} fxId: {} 结果：{}",
                                    rule.ruleGroupId,
                                    rule.ruleName,
                                    rule.id,
                                    rule.ruleName,
                                    rule.actionExpressionFxId,
                                    "积分过期时间小于积分生效时间 $overdueDateTime")
                                throw IllegalArgumentException("积分过期时间小于积分生效时间(${overdueDateTime})")
                            }
                            if (overdueDateTime.isBefore(date)) {
                                logger.warn("规则组信息：规则组ID：{}, 规则名称：{}, 规则Id：{} 规则名称：{} fxId: {} 结果：{}",
                                    rule.ruleGroupId,
                                    rule.ruleName,
                                    rule.id,
                                    rule.ruleName,
                                    rule.actionExpressionFxId,
                                    "积分过期时间已过期 $overdueDateTime")
                                throw IllegalArgumentException("积分过期时间已过期(${overdueDateTime})")
                            }
                        }

                        PrecomputeRulePoint(
                            type = POINT,
                            eventTypeId = pe.eventType.id!!,
                            eventTypeName = pe.eventType.name!!,
                            eventFQN = pe.eventType.eventStream!!,
                            accountTypeId = accountType.id!!,
                            accountTypeName = pe.eventType.name!!,
                            traceId = pe.eventType.originalOrderPath!!.let { pe.event.get(it)!!.toString() },
                            uniqueId = pe.event.getKey(),
                            ruleGroupId = group.id!!,
                            ruleGroupName = group.groupName!!,
                            ruleId = rule.id!!,
                            ruleName = rule.ruleName!!,
                            points = point,
                            effectiveDate = effectiveDateTime,
                            overdueDate = overdueDateTime ?: ConstantValue.LONG_TERM_OVERDUE_DATE,
                            desc = rule.remark ?: rule.ruleName ?: group.groupName ?: "",
                            channel = pe.eventType.channelTypePath?.let { pe.event.get(it)?.toString() },
                            shopId = pe.eventType.shopIdPath?.let { pe.event.get(it)?.toString() },
                            kzzd1 = pe.eventType.KZZD1Path?.let { pe.event.get(it)?.toString() },
                            kzzd2 = pe.eventType.KZZD2Path?.let { pe.event.get(it)?.toString() },
                            kzzd3 = pe.eventType.KZZD3Path?.let { pe.event.get(it)?.toString() },
                            sort = rule.sort ?: rule.id!!.toInt(),
                        ).also { rulePoints.add(it) }
                    }
                    if (rulePoints.isEmpty()) {
                        continue
                    }
                    // 规则组计算的积分取值策略，默认取累计值
                    val sortType = group.ruleSortType ?: TOTAL_VALUE
                    when (sortType) {
                        MAX_VALUE -> {
                            // 取最大积分
                            // 如果等于最大值的元素存在多个，则优先取永久有效的积分，否则取失效时间最大的
                            val max = rulePoints.maxOf { it.points }
                            val rp = rulePoints.sortedByDescending { it.overdueDate }.first { it.points.compareTo(max) == 0 }
                            totalRulePoints.add(rp)
                        }
                        MIN_VALUE -> {
                            // 取最小积分
                            // 如果等于最小值的元素存在多个，则优先取永久有效的积分，否则取失效时间最大的
                            val min = rulePoints.minOf { it.points }
                            val rp = rulePoints.sortedByDescending { it.overdueDate }.first { it.points.compareTo(min) == 0 }
                            totalRulePoints.add(rp)
                        }
                        MAX_LEVEL -> {
                            // 按照规则顺序，取优先级高的 sort值越小有优先级越高
                            val gs = rulePoints.minBy { it.sort }
                            totalRulePoints.add(gs)
                        }
                        NO, TOTAL_VALUE -> {
                            totalRulePoints.addAll(rulePoints)
                        }
                    }
                }
//                for (trp in totalRulePoints) {
//                    if (!trp.effectiveDate.isAfter(date)) {
//                        currentPoints[accountType.id!!] = currentPoints[accountType.id!!]!!.add(trp.points)
//                        continue
//                    }
//                }
                totalRulePoints
            }
            tasks.add(PrecomputeTask(accountType.executeOrder!!, accountType.id!!, CALC_POINT, func))
        }
        return tasks
    }


    private fun createGradeTask(pe: PrecomputeEvent, date: ZonedDateTime): List<PrecomputeTask> {
        val tasks = ArrayList<PrecomputeTask>()
        for (gradeHierarchy in pe.subject.gradeHierarchyList!!) {
            if (pe.req.accountTypeIds[GRADE] != null && pe.req.accountTypeIds[GRADE]!!.none { it == gradeHierarchy.id }) {
                continue
            }
            val groups = getGradeRuleGroups(gradeHierarchy.id!!, pe.eventType.id!!, Date.from(date.toInstant())).sortedByDescending { it.gradeDefinitionId }
            if (groups.isEmpty()) {
                continue
            }
            val func = x@ {
                val prgs = ArrayList<PrecomputeRuleGrade>()
                for (group in groups) {
                    if (group.ruleList.isNullOrEmpty()) continue
                    if (prgs.isNotEmpty()) break
                    for (rule in group.ruleList!!) {
                        val b = ExpressionIdentUtil.eppById(
                            rule.expressionFxId!!,
                            pe.event,
                            JsonNodeFactory.instance.objectNode().put("subject", pe.subject.dataType)
                        )?.toString()?.toBoolean()
                        if (b != true) {
                            continue
                        }
                        //  通过
                        // 获取目标等级
                        val targetTierId = group.gradeDefinitionId!!
                        var targetLevelOverdueDate: ZonedDateTime? = null

                        if (rule.gradeEffectForever != true) {
                            val localTime = LocalTime.of(23, 59, 59, 999999999)
                            val zoneId = ZoneId.of("Asia/Shanghai")
                            if (rule.endYearType != TimeTypeEnum.ABSOLUTE_TIME) {
                                // 自定义时间
                                if (rule.endTimePropertyId != null && rule.endTimePropertyId != -1L) {
                                    val property = propertyBaseService.findBy(rule.endTimePropertyId!!, EVENT, pe.eventType.versionId!!)
                                        ?: propertyBaseService.findBy(rule.endTimePropertyId!!, SUBJECT, pe.subject.versionId!!)
                                        ?: throw IllegalArgumentException("找不到规则对应的属性 id: ${rule.endTimePropertyId}")

                                    val d = ExpressionIdentUtil.eppById(
                                        property.fxId!!,
                                        pe.event,
                                        JsonNodeFactory.instance.objectNode().put("subject", pe.subject.dataType)
                                    )
                                    val localDate = DateUtils.stringToLocalDate(d.toString())
                                    if (localDate == null) {
                                        logger.error(
                                            "等级过期时间函数返回日期错误，规则组信息：规则组ID：{}, 规则名称：{}, 规则Id：{} propertyId: {}",
                                            group.id,
                                            group.name,
                                            rule.id,
                                            rule.endTimePropertyId
                                        )
                                        throw IllegalArgumentException("等级过期时间函数返回日期错误")
                                    }
                                    targetLevelOverdueDate = ZonedDateTime.of(localDate, localTime, zoneId)
                                } else {
                                    // 相对时间
                                    var base = date.localDate()
                                    if (rule.refCustomizedPropertyFxId != null && rule.refCustomizedPropertyId != "-1" &&
                                        rule.refCustomizedPropertyId != null && rule.refCustomizedPropertyFxId != "-1") {
                                        val d = ExpressionIdentUtil.eppById(
                                            rule.refCustomizedPropertyFxId!!.toInt(),
                                            pe.event,
                                            JsonNodeFactory.instance.objectNode().put("subject", pe.subject.dataType)
                                        )
                                        DateUtils.stringToLocalDate(d.toString())?.let { base = it }
                                    }
                                    if (rule.endYearTime != null && rule.endYearTime != 0) {
                                        base = base.plusYears(rule.endYearTime!!.toLong())
                                    }
                                    if (rule.endMonthTime != null && rule.endMonthTime != 0) {
                                        base = base.plusMonths(rule.endMonthTime!!.toLong())
                                    }
                                    if (rule.endDayTime != null && rule.endDayTime != 0) {
                                        base = if (rule.endDayTime == -1) base.withDayOfMonth(base.lengthOfMonth()) else base.withDayOfMonth(rule.endDayTime!!)
                                    }
                                    targetLevelOverdueDate = base.atTime(localTime).atZone(zoneId)
                                }
                            } else {
                                val localDate = LocalDate.of(rule.endYearTime!!, rule.endMonthTime!!, rule.endDayTime!!)
                                targetLevelOverdueDate = ZonedDateTime.of(localDate, localTime, zoneId)
                            }
                        }

                        val prg = PrecomputeRuleGrade(
                            type = GRADE,
                            eventTypeId = pe.eventType.id!!,
                            eventTypeName = pe.eventType.name!!,
                            eventFQN = pe.eventType.eventStream!!,
                            gradeHierarchyId = gradeHierarchy.id!!,
                            gradeHierarchyName = gradeHierarchy.name!!,
                            traceId = pe.eventType.originalOrderPath!!.let { pe.event.get(it)!!.toString() },
                            uniqueId = pe.event.getKey(),
                            ruleGroupId = group.id!!,
                            ruleGroupName = group.name!!,
                            ruleId = rule.id!!,
                            ruleName = rule.name!!,
                            gradeDefinitionId = targetTierId,
                            gradeDefinitionName = gradeHierarchy.gradeDefinitions?.find { it.id == targetTierId }?.name ?: "",
                            gradeDefinitionIdSort = gradeHierarchy.gradeDefinitions?.associateBy({ it.id!! }, { it.sort!! }) ?: emptyMap(),
                            overdueDate = targetLevelOverdueDate,
                            desc = group.name ?: "",
                            channel = pe.eventType.channelTypePath?.let { pe.event.get(it)?.toString() },
                            shopId = pe.eventType.shopIdPath?.let { pe.event.get(it)?.toString() },
                            kzzd1 = pe.eventType.KZZD1Path?.let { pe.event.get(it)?.toString() },
                            kzzd2 = pe.eventType.KZZD2Path?.let { pe.event.get(it)?.toString() },
                            kzzd3 = pe.eventType.KZZD3Path?.let { pe.event.get(it)?.toString() },
                        )
                        prgs.add(prg)
                    }
                }
                prgs
            }
            tasks.add(PrecomputeTask(gradeHierarchy.executeOrder!!, gradeHierarchy.id!!, CALC_GRADE, func))
        }
        return tasks
    }


    private fun loyaltyPoints(memberPoint: MemberPoint, rulePoint: PrecomputeRulePoint, subjectId: Long, dateTime: ZonedDateTime, id: String): LoyaltyPoints {
        val g = getPointRuleGroup(
            memberPoint.planId!!,
            rulePoint.accountTypeId,
            rulePoint.eventTypeId,
            EventOperationEnum.SEND,
            rulePoint.ruleGroupId
        )
        val lp = LoyaltyPoints.of(
            memberPoint = memberPoint,
            type = LoyaltyRequestType.EVENT,
            point = rulePoint.points,
            changeMode = ChangeMode.AUTO_CALC.name,
            channel = rulePoint.channel ?: ConstantValue.DEFAULT_CHANNEL_TYPE,
            traceId = rulePoint.traceId,
            uniqueId = rulePoint.uniqueId,
            date = dateTime,
            shopId = rulePoint.shopId,
            kzzd1 = rulePoint.kzzd1,
            kzzd2 = rulePoint.kzzd2,
            kzzd3 = rulePoint.kzzd3,
            desc = rulePoint.desc,
            checkSpecialList = false
        )
        lp.attr.effectiveDate = rulePoint.effectiveDate
        lp.attr.overdueDate = rulePoint.overdueDate ?: ConstantValue.LONG_TERM_OVERDUE_DATE
        lp.pointRuleGroup = PointGroup(rulePoint.ruleGroupId, rulePoint.ruleGroupName, limit = g?.limitPoint != null)
        lp.sendRule = PointIssuanceRule(rulePoint.ruleId, rulePoint.ruleName)
        lp.event = PointEvent(rulePoint.eventFQN, rulePoint.uniqueId, dateTime.toEpochMilli(), dateTime.toEpochMilli())
        lp.eventType = PointEventType(
            rulePoint.eventTypeId,
            rulePoint.eventTypeName,
            dateTime.toInstant().toEpochMilli(),
            EventOperationEnum.SEND
        )
        lp.forbiddenConfigs = forbiddenConfigCache.get(ForbiddenConfigKey(subjectId, rulePoint.accountTypeId, POINT))
        lp.attr.businessId = id
        return lp
    }

    private class ExceptionCC : RuntimeException()
    @DmTransaction(propagation = Propagation.REQUIRES_NEW)
    fun checkPointQuota(planId: Long, subjectId: Long, subjectFqn: String, memberId: String, beforePoints: BigDecimal, rulePoint: PrecomputeRulePoint, date: ZonedDateTime) {
        // 检查积分上限
        val mp = MemberPoint().apply {
            this.id = Uuid.uuid
            this.planId = planId
            this.pointPlanId = rulePoint.accountTypeId
            this.memberId = memberId
            this.subjectFqn = subjectFqn
            this.point = beforePoints
            this.version = 0
            this.openSegmentFlag = true
        }
        val lp = loyaltyPoints(mp, rulePoint, subjectId, date, rulePoint.traceId)
        memberPointSendTransfer.filterLimit(lp, rulePoint.points, FSMPointEvent.SEND, lp.eventType?.id, lp.pointRuleGroup?.id)
        if (rulePoint.points != lp.afterPoints || !lp.hierarchy.sendLimitResults.isNullOrEmpty()) {
            logger.info("积分被发放上限过滤 之前分值：{} 过滤后分值：{}", rulePoint.points, lp.afterPoints)
            throw LoyaltyException(LoyaltyExceptionCode.POINT_TRIAL_PLUS_FAILED)
        } else {
            throw ExceptionCC()
        }
    }


    private fun checkBlockList(
        type: TypeEnum,
        accountTypeId: Long,
        forbiddenOperation: ForbiddenOperation,
        forbiddenPort: ForbiddenPort,
        subjectId: Long, subjectFqn: String, subjectUid: String, blockList: MutableMap<BlockListKey, Boolean>
    ) {
        val blockKey = BlockListKey(subjectId, subjectUid, accountTypeId, type, forbiddenOperation)
        val blockValue = blockList[blockKey]
        if (blockValue == null) {
            val forbiddenConfigs = forbiddenConfigCache.get(ForbiddenConfigKey(subjectId, accountTypeId, type))
            if (!forbiddenConfigs.isNullOrEmpty()) {
                val isBlackList = specialListService.check(
                    subjectFqn,
                    subjectUid,
                    forbiddenOperation,
                    forbiddenPort,
                    forbiddenConfigs
                )
                if (isBlackList) {
                    logger.error("客户在${blockKey.forbiddenOperation}黑名单中! subjectUid:${subjectUid}")
                    throw LoyaltyException(LoyaltyExceptionCode.BLACKLIST_EXIST, UPGRADE.name)
                } else {
                    blockList[blockKey] = false
                }
            } else {
                blockList[blockKey] = false
            }
        }
    }

}