package com.shuyun.loyalty.manager.util

import com.shuyun.loyalty.service.model.BaseDataServiceModel
import com.shuyun.loyalty.service.model.BaseModel

enum class WAY_TO_HANDLE_DATA{
    TO_ADD,
    TO_UPDATE,
    TO_DELETE
}

object ListCompareUtil {

    /**将新的BaseModel的列表与数据库原有的列表进行比较，backup为计划是否发布过，compareKeyName为比较两个BaseModel是否是同一个BaseModel所用到的属性名，返回map，key值为所有的BASEModel,value值是这个BaseModel是要新增还是更新到数据库中，还是要从数据库中删除*/
    fun initModifyDataByCompareList(newList:List<BaseModel>?,oldList:List<BaseModel>,backup:Boolean,compareKeyName:String):HashMap<BaseModel,WAY_TO_HANDLE_DATA>{
        val resultMap = HashMap<BaseModel,WAY_TO_HANDLE_DATA>()
        val toDeleteMap = HashMap<Long, BaseModel>()
        oldList.forEach {
            val key = it.javaClass.getDeclaredField(compareKeyName)
            key.isAccessible = true
            val id = key.get(it) as Long
            toDeleteMap.put(id, it)
        }
        newList?.forEach {
            val key = it.javaClass.getDeclaredField(compareKeyName)
            key.isAccessible = true
            val id = key.get(it) as Long?
            if (toDeleteMap.containsKey(id)) {
                val oldOne = toDeleteMap.get(id)
                it.copyToOldOne(oldOne!!,backup)
                resultMap[oldOne] = WAY_TO_HANDLE_DATA.TO_UPDATE
                toDeleteMap.remove(id)
            } else {
                resultMap[it] = WAY_TO_HANDLE_DATA.TO_ADD
            }
        }
        toDeleteMap.values.forEach {
            resultMap[it] = WAY_TO_HANDLE_DATA.TO_DELETE
        }
        return resultMap
    }


    /**将新的BaseModel的列表与数据库原有的列表进行比较，backup为计划是否发布过，compareKeyName为比较两个BaseModel是否是同一个BaseModel所用到的属性名，返回map，key值为所有的BASEModel,value值是这个BaseModel是要新增还是更新到数据库中，还是要从数据库中删除*/
    fun initModifyDataServiceByCompareList(newList:List<BaseDataServiceModel>?, oldList:List<BaseDataServiceModel>, backup:Boolean, compareKeyName:String):HashMap<BaseDataServiceModel,WAY_TO_HANDLE_DATA>{
        val resultMap = HashMap<BaseDataServiceModel,WAY_TO_HANDLE_DATA>()
        val toDeleteMap = HashMap<Long, BaseDataServiceModel>()
        oldList.forEach {
            val key = it.javaClass.getDeclaredField(compareKeyName)
            key.isAccessible = true
            val id = key.get(it) as Long
            toDeleteMap.put(id, it)
        }
        newList?.forEach {
            val key = it.javaClass.getDeclaredField(compareKeyName)
            key.isAccessible = true
            val id = key.get(it) as Long?
            if (toDeleteMap.containsKey(id)) {
                val oldOne = toDeleteMap.get(id)
                it.copyToOldOne(oldOne!!,backup)
                resultMap[oldOne] = WAY_TO_HANDLE_DATA.TO_UPDATE
                toDeleteMap.remove(id)
            } else {
                resultMap[it] = WAY_TO_HANDLE_DATA.TO_ADD
            }
        }
        toDeleteMap.values.forEach {
            resultMap[it] = WAY_TO_HANDLE_DATA.TO_DELETE
        }
        return resultMap
    }
}