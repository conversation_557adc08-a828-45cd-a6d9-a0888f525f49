package com.shuyun.loyalty.manager.service

import com.shuyun.loyalty.service.model.PointDeductRule
import com.shuyun.loyalty.service.model.PointDeductRuleOperate
import com.shuyun.loyalty.service.service.PointDeductRuleOperateBaseService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class PointDeductRuleOperateService{

    @Autowired
    private lateinit var pointDeductRuleOperateBaseService: PointDeductRuleOperateBaseService

    fun insertPointDeductRuleOperate(pointDeductRule: PointDeductRule){
        //遍历插入积分操作信息
        pointDeductRule.pointDeductRuleOperateList?.forEach {
            it.ruleGroupId=pointDeductRule.ruleGroupId
            it.ruleId=pointDeductRule.id
            it.fileStatus = false
            pointDeductRuleOperateBaseService.insertPointRuleOperate(it)
        }
    }

    fun findPointDeductRuleOperateList(ruleId: Long):List<PointDeductRuleOperate>{
        return  pointDeductRuleOperateBaseService.findPointRuleOperateList(ruleId)
    }


    fun deletePointDeductOperateRule(ruleId: Long){
        val ruleList = pointDeductRuleOperateBaseService.findPointRuleOperateList(ruleId)
        ruleList.forEach {
            pointDeductRuleOperateBaseService.deletePointRuleOperate(it)
        }
    }


    fun updatePointDeductOperateRule(ruleId: Long){
        val ruleList = pointDeductRuleOperateBaseService.findPointRuleOperateList(ruleId)
        ruleList.forEach {
            pointDeductRuleOperateBaseService.updatePointRuleOperate(it)
        }
    }
}