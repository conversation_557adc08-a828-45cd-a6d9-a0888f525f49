package com.shuyun.loyalty.manager.vo

import com.fasterxml.jackson.annotation.JsonProperty
import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.entity.api.constants.EventLimitType
import com.shuyun.loyalty.service.meta.*
import com.shuyun.loyalty.service.model.CustomizedProperty
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.loyalty.service.model.I18nPayload
import com.shuyun.loyalty.service.model.PropertyTemplate
import com.shuyun.loyalty.service.util.ModelInitUtil.copyPropertiesIgnoreNull
import com.shuyun.loyalty.service.util.ValidatorUtils.nameListConflict
import com.shuyun.pip.component.json.JsonUtils
import io.swagger.v3.oas.annotations.media.Schema
import javax.validation.Valid
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotNull

@Schema(title = "EventTypeVo")
data class EventTypeVo(

    @Schema(title = "名称")
    @field:NotBlank(message = "时机名称不能为空")
    var name: String? = null,

    @Schema(title = "事件流")
    @field:NotBlank(message = "时机事件流不能为空")
    var eventStream: String? = null,

    @Schema(title = "事件流来源")
    val eventStreamSource: String = "loyalty",

    @Schema(title = "适用场景")
    @field:NotNull(message = "时机适用场景不能为空")
    var occasion: List<EventOccasionEnum>? = null, //积分规则  积分规则 勋章规则

    @Schema(title = "积分触发动作")
    var operation: EventOperationEnum? = null,

    @Schema(title = "等级触发动作")
    var operationGrade: EventOperationGradeEnum? = null,

    @Schema(title = "勋章触发动作")
    var operationMedal: EventOperationMedalEnum? = null,

    @Schema(title = "原单路径")
    var originalOrderPath: String? = null,

    @Schema(title = "渠道路径")
    var channelTypePath: String? = null,

    @Schema(title = "积分发放时间周期")
    var pointSendCycleTimePath: String? = null,

    @Schema(title = "店铺字段路径")
    var shopIdPath: String? = null,

    @Schema(title = "扩展字段1路径")
    @set:JsonProperty("KZZD1Path")
    @get:JsonProperty("KZZD1Path")
    var KZZD1Path: String? = null,

    @Schema(title = "扩展字段2路径")
    @set:JsonProperty("KZZD2Path")
    @get:JsonProperty("KZZD2Path")
    var KZZD2Path: String? = null,

    @Schema(title = "扩展字段3路径")
    @set:JsonProperty("KZZD3Path")
    @get:JsonProperty("KZZD3Path")
    var KZZD3Path: String? = null,

    @Schema(title = "关联时机")
    var relatedEventTypeIds: String? = null,

    @Schema(title = "限制类型")
    var limitType: EventLimitType? = null,

    @Schema(title = "限制时间", type = "Int")
    var limitTime: Int? = null,

    @Schema(title = "主体ID")
    var subjectVersionId: Long? = null,

    @Schema(title = "时机ID")
    @field:NotNull(message = "时机ID不能为空")
    var id: Long? = null,

    @Schema(title = "状态")
    var status: EnableStatusEnum? = null,

    @Schema(title = "等级规则匹配时间路径")
    var matchingTimePathForGradeRule: String? = null,

    @Schema(title = "积分规则匹配时间路径")
    var matchingTimePathForPointsRule: String? = null,

    @Schema(title = "勋章规则匹配时间路径")
    var matchingTimePathForMedalRule: String? = null,

    @Schema(title = "延迟时机处理策略")
    var delayedEventStrategy: DelayedEventStrategy = DelayedEventStrategy.NONE,

    @Schema(title = "延迟时机参照字段路径", description = "当delayedEventStrategy=DISCARD_AFTER时必填")
    var delayedEventPath: String? = null,

    @Schema(
        title = "时机事件延迟多久不再参与计算",
        description = "当delayedEventStrategy=DISCARD_AFTER时必填 ISO8601持续时间格式 例如: P1D 代表1天 PT1H 代表1小时 PT1M 代表1分钟 P1DT1H1M 代表1天1小时1分钟"
    )
    var discardDelayedEventAfter: String? = null,

    @Schema(title = "前端排序号")
    var sort: Int? = null,

    @Schema(title = "属性")
    @field:Valid
    var customizedPropertyList: List<CustomizedPropertyVo>? = null,

    @Schema(title = "属性模板")
    @field:Valid
    var propertyTemplateList: List<PropertyTemplateVo>? = null
) : I18nPayload() {
    /**根据vo初始化model*/
    fun initEventTypeFromVo(eventType: EventType) {
        copyPropertiesIgnoreNull(this, eventType)
        eventType.occasionString = JsonUtils.toJson(eventType.occasion)
        var exception: Exception? = null
        eventType.customizedPropertyList = this.customizedPropertyList?.map { customizedPropertyVo ->
            val customizedProperty = CustomizedProperty()
            try {
                customizedPropertyVo.initCustomizedPropertyFromVo(customizedProperty, eventType.eventStream!!)
            } catch (e: Exception) {
                exception = e
            }
            customizedProperty
        }
        exception?.let {
            throw it
        }
        eventType.propertyTemplateList = this.propertyTemplateList?.map { propertyTemplateVo ->
            val propertyTemplate = PropertyTemplate()
            try {
                propertyTemplateVo.initPropertyTemplateFromVo(propertyTemplate)
            } catch (e: Exception) {
                exception = e
            }
            propertyTemplate
        }
        exception?.let {
            throw it
        }
    }

    /**校验属性名称重复*/
    fun validateNameConflict() {
        var conflict = nameListConflict(this.customizedPropertyList?.map { it.name!! })
        if (conflict) {
            throw IllegalArgumentException("时机属性名称重复")
        }
        conflict = nameListConflict(this.propertyTemplateList?.map { it.name!! })
        if (conflict) {
            throw IllegalArgumentException("时机属性模板名称重复")
        }
    }
}