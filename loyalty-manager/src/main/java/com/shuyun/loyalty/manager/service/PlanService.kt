package com.shuyun.loyalty.manager.service

import com.pip.mybatisplus.toolkit.MetadataFactory
import com.pip.shuyun.pool.transaction.DmTransaction
import com.pip.shuyun.pool.transaction.TransactionInfoHolder
import com.shuyun.dm.api.domain.DataModel
import com.shuyun.dm.api.domain.ModelRelation
import com.shuyun.easy.filestorage.FileClient
import com.shuyun.kylin.es.sdk.meta.EventModel
import com.shuyun.lite.util.Common
import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.entity.api.constants.PublishStatusEnum
import com.shuyun.loyalty.manager.config.uploadFile
import com.shuyun.loyalty.manager.vo.*
import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.service.exception.FileException
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.infrastructure.es.EventClient
import com.shuyun.loyalty.service.meta.EntityType
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.meta.PlanStatusEnum
import com.shuyun.loyalty.service.model.DerivationEventMetas
import com.shuyun.loyalty.service.model.DerivationEventType
import com.shuyun.loyalty.service.model.LoyaltyEventBusMessage
import com.shuyun.loyalty.service.model.Plan
import com.shuyun.loyalty.service.model.Plan.Companion.checkOperationValid
import com.shuyun.loyalty.service.repository.PlanRepository
import com.shuyun.loyalty.service.service.*
import com.shuyun.loyalty.service.util.ConstantValue
import com.shuyun.loyalty.service.util.ExpressionIdentUtil
import com.shuyun.loyalty.service.util.ModelInitUtil.copyPropertiesIgnoreNull
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.json.JsonUtils
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import com.shuyun.pip.util.converter.ZonedDateTime2StringConverter
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.Resource
import org.springframework.core.io.support.PathMatchingResourcePatternResolver
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import java.time.ZonedDateTime
import java.util.*
import java.util.concurrent.CopyOnWriteArrayList
import java.util.concurrent.atomic.AtomicInteger
import java.util.regex.Pattern
import javax.servlet.http.HttpServletResponse
import kotlin.concurrent.thread

@Service
class PlanService {

    @Autowired
    private lateinit var planBaseService: PlanBaseService

    @Autowired
    private lateinit var subjectService: SubjectService

    @Autowired
    private lateinit var planRepository: PlanRepository

    @Autowired
    private lateinit var eventStreamMetasBaseService: EventStreamMetasBaseService

    @Autowired
    private lateinit var validateService: ValidateService

    @Autowired
    private lateinit var notifyBaseService: NotifyBaseService

    @Autowired
    private lateinit var modelInitService: ModelInitService

    @Autowired
    private lateinit var importFileConfigService: ImportFileConfigService

    @Autowired
    private lateinit var specialListConfigService: SpecialListConfigService

    @Autowired
    private lateinit var medalDefinitionBaseService: MedalDefinitionBaseService

    @Autowired
    private lateinit var derivationEventMetasBaseService: DerivationEventMetasBaseService


    @Autowired(required = false)
    private lateinit var fileClient: FileClient

    private val logger = LogManager.getLogger(PlanService::class.java)

    fun insert(plan: Plan) {
        planBaseService.insert(plan)
        subjectService.insert(plan)
    }

    fun update(plan: Plan) {
        planBaseService.update(plan)
        subjectService.update(plan)
    }

    @DmTransaction
    fun save(planVo: PlanVo): Long {
        planVo.validateNameConflict()
        validateEventTypeMatchSubject(planVo)
        var plan = Plan()
        if (null != planVo.id) {
            val existPlanList: List<Plan> = planBaseService.getPlanList(planVo.id!!)
            if (existPlanList.isNotEmpty()) {
                plan = checkOperationValid(existPlanList, OperationType.UPDATE)!!
            } else {
                planVo.id = null
            }
        }
        planVo.initPlanFromVo(plan)
        if (null == plan.versionId) {
            insert(plan)
        } else {
            plan.status = when (plan.status) {
                PlanStatusEnum.DRAFT -> PlanStatusEnum.DRAFT
                else -> PlanStatusEnum.PUBLISHED_DRAFT
            }
            update(plan)
            plan = planBaseService.findPlanDetailByPlanIdAndStatus(plan.id!!, PlanStatusEnum.DRAFT)
            checkDisabledReferenced(plan)
        }
        return plan.id!!
    }

    private fun validateEventTypeMatchSubject(planVo: PlanVo) {
        planVo.subjectList?.forEach { subjectVo ->
            subjectVo.eventTypeList?.forEach { eventTypeVo ->
                val eventStreamMetas = if (eventTypeVo.eventStreamSource == ConstantValue.ES_SOURCE) {
                    eventStreamMetasBaseService.findESStreamMetas(eventTypeVo.eventStream!!, subjectVo.dataType!!)
                } else {
                    eventStreamMetasBaseService.findByFqn(eventTypeVo.eventStream!!)
                }
                subjectVo.validateEventStreamValid(eventStreamMetas!!)
            }
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun delete(id: Long) {
        val existPlanList: List<Plan> = planBaseService.getPlanList(id)
        checkOperationValid(existPlanList, OperationType.DELETE)
        existPlanList.forEach { existPlan ->
            planBaseService.delete(existPlan)
            existPlan.subjectList?.forEach {
                subjectService.delete(it)
            }
        }
    }

    @Transactional(rollbackFor = [Exception::class])
    fun file(id: Long) {
        val existPlanList: List<Plan> = planBaseService.getPlanList(id)
        checkOperationValid(existPlanList, OperationType.FILE)
        existPlanList.forEach {
            if (validateService.checkEntityReferenced(it.id!!, EntityType.PLAN)) {
                throw LoyaltyException(LoyaltyExceptionCode.PLAN_NOT_OPERATION_FILE)
            }
            if (PlanStatusEnum.FILED != it.status) {
                planBaseService.file(it)
            }
        }
        importFileConfigService.planFiled(id)
        specialListConfigService.planFiled(id)
    }

    @DmTransaction
    fun publish(id: Long) {
        val existPlanList: List<Plan> = planBaseService.getPlanList(id)
        checkOperationValid(existPlanList, OperationType.PUBLISH)
        existPlanList.find {
            it.status in listOf(PlanStatusEnum.DRAFT, PlanStatusEnum.PUBLISHED_DRAFT, PlanStatusEnum.PUBLISHED_BACKUP)
        }!!.let {
            val plan = planBaseService.findPlanDetailByPlanIdAndStatus(it.id!!, PlanStatusEnum.DRAFT)
            checkDisabledReferenced(plan)
            plan.validateMultiEntryExist()
            plan.status = PlanStatusEnum.PUBLISHING
            planBaseService.update(plan)
        }
    }


    @DmTransaction
    fun published(existPlanList: List<Plan>, originalPlan: Plan?) {
        val plans = ArrayList<Plan>()
        existPlanList.forEach { plan ->
            when (plan.status) {
                PlanStatusEnum.PUBLISHED -> {
                    plan.subjectList = planBaseService.findPlanDetailByPlanIdAndStatus(plan.id!!, PlanStatusEnum.PUBLISHED).subjectList

                    planBaseService.file(plan)
                }
                PlanStatusEnum.PUBLISHING, PlanStatusEnum.PUBLISH_FAILED -> {
                    plan.subjectList = planBaseService.findPlanDetailByPlanIdAndStatus(plan.id!!, PlanStatusEnum.DRAFT).subjectList
                    //添加表达式
                    plan.subjectList!!.forEach {
                        it.eventTypeList!!.forEach { x ->
                            x.customizedPropertyList!!.forEach { c ->
                                c.fxId = ExpressionIdentUtil.addDsl(c.name!!, c.dsl!!)
                            }
                        }
                        it.customizedPropertyList!!.forEach { x ->
                            x.fxId = ExpressionIdentUtil.addDsl(x.name!!, x.dsl!!)
                        }
                    }
                    checkDisabledReferenced(plan)
                    plan.validateMultiEntryExist()
                    subjectService.publish(plan)
                    val planCopy = Plan()
                    copyPropertiesIgnoreNull(plan, planCopy)
                    planCopy.versionId = null
                    planCopy.backup = true
                    planCopy.status = PlanStatusEnum.PUBLISHED_BACKUP
                    planCopy.publishedTime = ZonedDateTime2StringConverter().convert(ZonedDateTime.now())
                    planRepository.save(planCopy)
                    subjectService.copy(planCopy)
                    plan.status = PlanStatusEnum.PUBLISHED
                    plan.publishedTime = ZonedDateTime2StringConverter().convert(ZonedDateTime.now())
                    planBaseService.update(plan)

                    importFileConfigService.updateSubjectList(plan.subjectList!!)
                    specialListConfigService.updateWhenPlanPublished(plan.subjectList!!)
                    plans.add(plan)
                }
                else -> {}
            }
        }
        TransactionInfoHolder.afterCommit {
            plans.forEach {notifyBaseService.postIntoBus(PublishedPlanChanged(it, originalPlan))}
        }
    }



    fun createdModelAndReference(originalPlan: Plan?, currentPlan: Plan) {
        val initGradeHierarchies = HashMap<String, String>()
        val initPointAccounts = HashMap<String, String>()
        val initMedalHierarchies = HashMap<String, String>()

        val derivationEventsOfGrade = HashMap<String, String>()
        val derivationEventsOfPoint = HashMap<String, String>()
        val derivationEventsOfMedal = HashMap<String, String>()

        val initGradeHierarchiesIds = HashSet<Long>()
        val initMedalHierarchiesIds = HashSet<Long>()
        val initPointAccountsIds = HashSet<Long>()
        val checkModel = Common.getBoolean("always.check.model.when.publish.plan")
        if (!checkModel) {
            originalPlan?.subjectList?.forEach { subject ->
                subject.gradeHierarchyList?.forEach { initGradeHierarchiesIds += it.id!! }
                subject.medalHierarchyList?.forEach { initMedalHierarchiesIds += it.id!! }
                subject.pointAccountTypeList?.forEach { initPointAccountsIds += it.id!! }
            }
        }

        currentPlan.subjectList?.forEach { subject ->
            subject.gradeHierarchyList?.forEach {
                if (!initGradeHierarchiesIds.contains(it.id!!)) {
                    initGradeHierarchies[it.id!!.toString()] = subject.dataType!!
                    derivationEventsOfGrade[it.id!!.toString()] = subject.dataType!!
                }
            }
            subject.medalHierarchyList?.forEach {
                if (!initMedalHierarchiesIds.contains(it.id!!)) {
                    initMedalHierarchies[it.id!!.toString()] = subject.dataType!!
                    derivationEventsOfMedal[it.id!!.toString()] = subject.dataType!!
                }
            }
            subject.pointAccountTypeList?.forEach {
                if (!initPointAccountsIds.contains(it.id!!)) {
                    initPointAccounts[it.id!!.toString().plus("&|^").plus(it.precision)] = subject.dataType!!
                    derivationEventsOfPoint[it.id!!.toString()] = subject.dataType!!
                }
            }
        }

        val subjectFqnSet = HashSet<String>()

        val gradeInitModelTasks = ArrayList<ModelTask>()
        val gradeFqnRelations = HashMap<String, MutableList<ModelRelation>>()

        val pointInitModelTasks = ArrayList<ModelTask>()
        val pointFqnRelations = HashMap<String, MutableList<ModelRelation>>()

        val medalInitModelTasks = ArrayList<ModelTask>()
        val medalFqnRelations = HashMap<String, MutableList<ModelRelation>>()

        fun collect(id: String, subjectFqn: String,
                    modelTasks: ArrayList<ModelTask>,
                    fqnRelations: HashMap<String, MutableList<ModelRelation>>,
                    modelResourceList: Array<Resource>,
                    referenceResourceList: Array<Resource>) {
            val tasks = modelInitService.buildPlanModelTask(modelResourceList, id, subjectFqn)
            modelTasks.addAll(tasks)
            val relationMap = modelInitService.buildModelReference(referenceResourceList, id, subjectFqn)
            for ((fqn, relations) in relationMap) {
                if (!fqnRelations.containsKey(fqn)) {
                    fqnRelations[fqn] = ArrayList()
                }
                fqnRelations[fqn]!!.addAll(relations)
            }
            subjectFqnSet.add(subjectFqn)
        }

        fun getResource(type: ModelType): Pair<Array<Resource>, Array<Resource>> {
            val modelList = PathMatchingResourcePatternResolver().getResources("classpath:planPublishInit/${type.type}/*.json")
            val referenceList = PathMatchingResourcePatternResolver().getResources("classpath:planPublishInit/${type.type}/reference/*.json")
            return Pair(modelList, referenceList)
        }

        fun runOnThread(message: String, tasks: List<ModelTask>, chunkSize: Int = 100) {
            logger.info("{} - 开始", message)
            val chunkedThreads = tasks.chunked(chunkSize)
            val total = tasks.size
            val c = AtomicInteger(0)
            for (ct in chunkedThreads) {
                val exceptions = CopyOnWriteArrayList<Throwable>()
                val ts = ct.map {
                    thread {
                        try {
                            it.block()
                        } catch (e: Throwable) {
                            exceptions.add(e)
                        } finally {
                            logger.info("{} name: {} id: {} 进度: {}/{}", message, it.name, it.id.split("&|^").firstOrNull(), c.incrementAndGet(), total)
                        }
                    }
                }
                for (t in ts) t.join()
                if (exceptions.isNotEmpty()) {
                    throw exceptions.first()
                }
            }
            logger.info("{} - 结束", message)
        }

        if (initGradeHierarchies.isNotEmpty()) {
            val (modelList, referenceList) = getResource(ModelType.GRADE)
            initGradeHierarchies.forEach { (id, subjectFqn) ->
                collect(id, subjectFqn, gradeInitModelTasks, gradeFqnRelations, modelList, referenceList)
            }
        }

        if (initPointAccounts.isNotEmpty()) {
            val (modelList, referenceList) = getResource(ModelType.POINT)
            initPointAccounts.forEach { (id, subjectFqn) ->
                collect(id, subjectFqn, pointInitModelTasks, pointFqnRelations, modelList, referenceList)
            }
        }

        if (initMedalHierarchies.isNotEmpty()) {
            val (modelList, referenceList) = getResource(ModelType.MEDAL)
            initMedalHierarchies.forEach { (id, subjectFqn) ->
                collect(id, subjectFqn, medalInitModelTasks, medalFqnRelations, modelList, referenceList)
            }
        }

        runOnThread("==== 等级模型初始化", gradeInitModelTasks)
        runOnThread("==== 积分模型初始化", pointInitModelTasks)
        runOnThread("==== 勋章模型初始化", medalInitModelTasks)

        val subjectRelations = HashMap<String,  MutableList<ModelRelation>>()
        val groupedRelations = HashMap<String,  MutableList<ModelRelation>>()
        val pattern = Pattern.compile(".+?([0-9])$")
        fun groupRelations(relationMap: Map<String, List<ModelRelation>>) {
            for ((mainFQN, relations) in relationMap) {
                val matcher = pattern.matcher(mainFQN)
                if (matcher.matches() && mainFQN !in subjectFqnSet) {
                    val id = matcher.group(1)
                    if (groupedRelations[id] == null) {
                        groupedRelations[id] = ArrayList()
                    }
                    groupedRelations[id]!!.addAll(relations)
                } else {
                    if (subjectRelations[mainFQN] == null) {
                        subjectRelations[mainFQN] = ArrayList()
                    }
                    subjectRelations[mainFQN]!!.addAll(relations)
                }
            }
        }
        groupRelations(gradeFqnRelations)
        groupRelations(pointFqnRelations)
        groupRelations(medalFqnRelations)

        subjectRelations.map { (fqn, relations) ->
            ModelTask(fqn,"-") {
                for (relation in relations) {
                    modelInitService.addRelation(fqn, relation)
                }
            }
        }.run {
            runOnThread("==== 主体模型添加关系", this)
        }

        groupedRelations.map { (id, relations) ->
            ModelTask(id, id) {
                for (relation in relations) {
                    modelInitService.addRelation(relation.mainModelFqn, relation)
                }
            }
        }.run {
            runOnThread("==== 其他模型添加关系", this)
        }

        val skipped = UserContextThreadSafe.isMetadataRefScanSkipped()
        try {
            logger.info("提交模型关系开始")
            UserContextThreadSafe.setMetadataRefScanSkipped(true)
            for (it in subjectFqnSet) {
                logger.info("准备提交模型:{}", it)
                MetadataFactory.sdk().updateModel(it, DataModel().apply { fqn = it })
            }
            for ((fqn, _) in gradeFqnRelations) {
                if (fqn in subjectFqnSet) {
                    continue
                }
                logger.info("准备提交等级模型:{}", fqn)
                MetadataFactory.sdk().updateModel(fqn, DataModel().apply { this.fqn = fqn })
            }
            for ((fqn, _) in pointFqnRelations) {
                if (fqn in subjectFqnSet) {
                    continue
                }
                logger.info("准备提交积分模型:{}", fqn)
                MetadataFactory.sdk().updateModel(fqn, DataModel().apply { this.fqn = fqn })
            }
            for ((fqn, _) in medalFqnRelations) {
                if (fqn in subjectFqnSet) {
                    continue
                }
                logger.info("准备提交勋章模型:{}", fqn)
                MetadataFactory.sdk().updateModel(fqn, DataModel().apply { this.fqn = fqn })
            }
            logger.info("提交模型关系结束")
        } finally {
            UserContextThreadSafe.setMetadataRefScanSkipped(skipped)
        }

        val derivationBoolean = Common.getBoolean("event.service.derivation.enabled")
        if (derivationBoolean) {
            // 等级的
            val requiredHierarchyCodes = HashMap<String, List<String>>()
            derivationEventsOfGrade.forEach { hierarchy ->
                val list = ArrayList<String>()
                for (entry in DerivationEventType.entries) {
                    if (entry == DerivationEventType.GRADE_EXPIRE_REMIND || entry == DerivationEventType.GRADE_RECORD) {
                        list.add(entry.code.plus("_").plus(hierarchy.key))
                    }
                }
                requiredHierarchyCodes[hierarchy.key] = list
            }
            val hierarchyDerivationEventMetas = CopyOnWriteArrayList<DerivationEventMetas>()
            val hierarchyChunked = requiredHierarchyCodes.values.flatten().chunked(100)
            for (cs in hierarchyChunked) {
                hierarchyDerivationEventMetas.addAll(derivationEventMetasBaseService.findByCodeIn(cs))
            }
            // 积分的
            val requiredPointAccountCodes = HashMap<String, List<String>>()
            derivationEventsOfPoint.forEach { hierarchy ->
                val list = ArrayList<String>()
                for (entry in DerivationEventType.entries) {
                    if (entry != DerivationEventType.GRADE_EXPIRE_REMIND && entry != DerivationEventType.GRADE_RECORD) {
                        list.add(entry.code.plus("_").plus(hierarchy.key))
                    }
                }
                requiredPointAccountCodes[hierarchy.key] = list
            }
            val pointAccountDerivationEventMetas = CopyOnWriteArrayList<DerivationEventMetas>()
            val pointAccountChunked = requiredPointAccountCodes.values.flatten().chunked(100)
            for (cs in pointAccountChunked) {
                pointAccountDerivationEventMetas.addAll(derivationEventMetasBaseService.findByCodeIn(cs))
            }

            fun initDerivation(derivationEventMetas: List<DerivationEventMetas>, eventType: DerivationEventType, id: String) {
                val metas = derivationEventMetas.find { meta -> meta.code == eventType.code.plus("_").plus(id) }
                if (metas != null && !Property.getSysOrEnv("event.stream.open.disabled", false)) {
                    val eventModel = JsonUtils.parse(metas.schema!!, EventModel::class.java).apply {
                        this.fqn = metas.fqn!!.replaceFirst("data", "event")
                        this.originEventFqn = metas.fqn!!.replaceFirst("data", "event") + ".raw"
                    }
                    logger.info("初始化衍生事件开始 id: $id, fqn: ${metas.fqn}")
                    EventClient.upsertModel(eventModel)
                    logger.info("初始化衍生事件完成 id: $id, fqn: ${metas.fqn}")
                }
            }

            derivationEventsOfGrade.map { (id, subjectFqn) ->
                ModelTask(subjectFqn, id) {
                    initDerivation(hierarchyDerivationEventMetas, DerivationEventType.GRADE_RECORD, id)
                    initDerivation(hierarchyDerivationEventMetas, DerivationEventType.GRADE_EXPIRE_REMIND, id)
                }
            }.run {
                runOnThread("创建等级衍生事件模型", this, chunkSize = 5)
            }

            derivationEventsOfPoint.map { (id, subjectFqn) ->
                ModelTask(subjectFqn, id) {
                    initDerivation(pointAccountDerivationEventMetas, DerivationEventType.POINT_RECORD, id)
                    initDerivation(pointAccountDerivationEventMetas, DerivationEventType.POINT_EXPIRE_REMIND, id)
                    initDerivation(pointAccountDerivationEventMetas, DerivationEventType.POINT_DELAY_REMIND, id)
                    initDerivation(pointAccountDerivationEventMetas, DerivationEventType.POINT_RECORD_ITEM, id)
                    initDerivation(pointAccountDerivationEventMetas, DerivationEventType.BEYOND_POINT_RECORD, id)
                    initDerivation(pointAccountDerivationEventMetas, DerivationEventType.LIMIT_POINT_RECORD, id)
                }
            }.run {
                runOnThread("创建积分衍生事件模型", this, chunkSize = 5)
            }
        }
    }


    fun publishFail(existPlanList: List<Plan>) {
        existPlanList.find {
            it.status == PlanStatusEnum.PUBLISHING
        }?.let {
            it.status = PlanStatusEnum.PUBLISH_FAILED
            planBaseService.update(it)
        }
    }

    private fun getOriginalAndCurrentPlan(existPlanList: List<Plan>): Pair<Plan?, Plan> {
        var originalPlan: Plan? = null
        var currentPlan: Plan? = null
        existPlanList.forEach { plan ->
            when (plan.status) {
                PlanStatusEnum.PUBLISHED -> {
                    plan.subjectList = planBaseService.findPlanDetailByPlanIdAndStatus(plan.id!!, PlanStatusEnum.PUBLISHED).subjectList
                    originalPlan = JsonUtils.parse(JsonUtils.toJson(plan), Plan::class.java)
                }

                PlanStatusEnum.PUBLISHING, PlanStatusEnum.PUBLISH_FAILED -> {
                    plan.subjectList = planBaseService.findPlanDetailByPlanIdAndStatus(plan.id!!, PlanStatusEnum.DRAFT).subjectList
                    checkDisabledReferenced(plan)
                    plan.validateMultiEntryExist()
                    currentPlan = JsonUtils.parse(JsonUtils.toJson(plan), Plan::class.java)
                }
                else -> {}
            }
        }
        if (currentPlan == null) throw LoyaltyException(LoyaltyExceptionCode.NOT_OPERATION_AFRESH_PUBLISH)
        return Pair(originalPlan,  currentPlan)
    }

    fun publishedWithCheck(id: Long) {
        val existPlanList: List<Plan> = planBaseService.getPlanList(id, listOf(PlanStatusEnum.PUBLISHING, PlanStatusEnum.PUBLISHED, PlanStatusEnum.PUBLISH_FAILED))
        checkOperationValid(existPlanList, OperationType.PUBLISHED)
        existPlanList.find { it.status == PlanStatusEnum.PUBLISH_FAILED }?.let {
            it.status = PlanStatusEnum.PUBLISHING
            planBaseService.update(it)
        }
        try {
            val (originalPlan, currentPlan) = getOriginalAndCurrentPlan(existPlanList)
            createdModelAndReference(originalPlan, currentPlan)
            ApplicationContextHolder.getBean(PlanService::class.java).published(existPlanList, originalPlan)
            logger.info("计划发布成功 id: $id")
        } catch (e: Exception) {
            logger.error("发布失败", e)
            val planList: List<Plan> = planBaseService.getPlanList(id)
            publishFail(planList)
        }
    }


    @DmTransaction
    fun backToPublished(id: Long) {
        val existPlanList: List<Plan> = planBaseService.getPlanList(id)
        checkOperationValid(existPlanList, OperationType.BACK_TO_PUBLISH)
        existPlanList.forEach { plan ->
            when (plan.status) {
                PlanStatusEnum.PUBLISHED -> {
                    val planCopy = Plan()
                    plan.subjectList = planBaseService.findPlanDetailByVersionId(plan.versionId!!).subjectList
                    copyPropertiesIgnoreNull(plan, planCopy)
                    planCopy.versionId = null
                    planCopy.backup = true
                    planCopy.status = PlanStatusEnum.PUBLISHED_BACKUP
                    planRepository.save(planCopy)
                    subjectService.copy(planCopy)
                }
                PlanStatusEnum.PUBLISHED_DRAFT -> {
                    plan.subjectList = planBaseService.findPlanDetailByVersionId(plan.versionId!!).subjectList
                    planBaseService.delete(plan)
                    subjectService.delete(plan)
                }
                else -> {}
            }
        }
    }

    fun checkDisabledReferenced(plan: Plan) {
        plan.subjectList?.forEach { subject ->
            if (subject.status == EnableStatusEnum.DISABLED) {
                if (validateService.checkEntityReferenced(subject.id!!, EntityType.SUBJECT)) {
                    throw LoyaltyException(LoyaltyExceptionCode.SUBJECT_NOT_DISABLED, subject.name!!)
                }
            }
            subject.eventTypeList?.forEach { eventType ->
                if (eventType.status == EnableStatusEnum.DISABLED) {
                    if (validateService.checkEntityReferenced(eventType.id!!, EntityType.EVENT_TYPE)) {
                        throw LoyaltyException(LoyaltyExceptionCode.EVENT_TYPE_NOT_DISABLED, eventType.name!!)
                    }
                }
                eventType.customizedPropertyList?.forEach { customizedProperty ->
                    if (customizedProperty.status == EnableStatusEnum.DISABLED) {
                        if (validateService.checkEntityReferenced(
                                customizedProperty.id!!,
                                EntityType.CUSTOMIZED_PROPERTY
                            )
                        ) {
                            throw LoyaltyException(
                                LoyaltyExceptionCode.EVENT_TYPE_CUSTOMIZED_PROPERTY_NOT_DISABLED,
                                customizedProperty.name!!
                            )
                        }
                    }
                }
            }
            subject.customizedPropertyList?.forEach { customizedProperty ->
                if (customizedProperty.status == EnableStatusEnum.DISABLED) {
                    if (validateService.checkEntityReferenced(
                            customizedProperty.id!!,
                            EntityType.CUSTOMIZED_PROPERTY
                        )
                    ) {
                        throw LoyaltyException(
                            LoyaltyExceptionCode.SUBJECT_CUSTOMIZED_PROPERTY_NOT_DISABLED,
                            customizedProperty.name!!
                        )
                    }
                }
            }
            subject.pointAccountTypeList?.forEach { pointAccountType ->
                if (pointAccountType.status == PublishStatusEnum.FILED) {
                    if (validateService.checkEntityReferenced(pointAccountType.id!!, EntityType.POINT_ACCOUNT_TYPE)) {
                        throw LoyaltyException(LoyaltyExceptionCode.ACCOUNT_TYPE_NOT_DISABLED, pointAccountType.name!!)
                    }
                }
            }

        }
    }

    fun uploadMedalIcon(files: List<MultipartFile>): List<String> {
        return files.map {
            if (it.size > 1024 * 1024) throw IllegalArgumentException("勋章图标大小不能超过1M")
            val uploadResponse =  ApplicationContextHolder.getBean(FileClient::class.java).uploadFile(
                UUID.randomUUID().toString(),
                it.inputStream,
                it.size
            )
            "${uploadResponse.groupName}&${uploadResponse.fileName}"
        }
    }

    fun downloadMedalIcon(medalDefinitionVersionId: Long, response: HttpServletResponse) {
        val medalDefinition = medalDefinitionBaseService.findByVersionId(medalDefinitionVersionId)
        if (medalDefinition.icon == null || medalDefinition.icon!!.indexOf("&") < 0) {
            throw FileException(LoyaltyExceptionCode.NOT_FOUND)
        }
        val groupName = medalDefinition.icon!!.split("&")[0]
        val fileName = medalDefinition.icon!!.split("&")[1]
        response.contentType = "image/jpeg"
        try {
            fileClient.downloadToStream(groupName, fileName).use {
                it.copyTo(response.outputStream)
            }
        } catch (e: Exception) {
            logger.error("勋章图标读取失败", e)
            throw FileException(LoyaltyExceptionCode.FILE_WRITE_ERROR,e)
        }
    }

    fun verifyPlanDsl(planVo: PlanVo): VerifyPlanDslVo {
        val errorSubjectList = planVo.subjectList?.mapNotNull {
            val errorPropertyList = it.customizedPropertyList?.mapNotNull { customizedProperty ->
                try {
                    ExpressionIdentUtil.validation(initDsl(customizedProperty.expression!!, it.dataType!!))
                    null
                } catch (e: Exception) {
                    logger.warn("验证主体属性表达式失败", e)
                    ErrorProperty().apply {
                        this.propertyName = customizedProperty.name
                        this.errorDesc = e.message
                    }
                }
            }

            val errorEventList = it.eventTypeList?.mapNotNull { eventTypeVo ->
                if (eventTypeVo.eventStreamSource == ConstantValue.ES_SOURCE) {
                    LoyaltyPrograms.reloadEsMetas(listOf(eventTypeVo.eventStream!!))
                }
                val errorEventPropertyList = eventTypeVo.customizedPropertyList?.mapNotNull { customizedProperty ->
                    try {
                        ExpressionIdentUtil.validation(initDsl(customizedProperty.expression!!, eventTypeVo.eventStream!!))
                        null
                    } catch (e: Exception) {
                        logger.warn("验证时机属性表达式失败", e)
                        ErrorProperty().apply {
                            this.propertyName = customizedProperty.name
                            this.errorDesc = e.message
                        }
                    }
                }
                if (errorEventPropertyList.isNullOrEmpty()) {
                    null
                } else {
                    ErrorEvent().apply {
                        this.eventName = eventTypeVo.name
                        this.errorPropertyList = errorEventPropertyList
                    }
                }
            }

            if (errorEventList.isNullOrEmpty() && errorPropertyList.isNullOrEmpty()) {
                null
            } else {
                ErrorSubject().apply {
                    this.subjectName = it.name
                    this.errorEventList = errorEventList
                    this.errorPropertyList = errorPropertyList
                }
            }
        }

        return VerifyPlanDslVo().apply {
            if (errorSubjectList.isNullOrEmpty()) this.verifyResult = true
            else this.errorSubjectList = errorSubjectList
        }
    }

    private fun initDsl(expression: String, fqn: String): String {
        var dsl = expression
        while(dsl.contains("this.")){
            val regex = Regex("this\\.[a-zA-Z][a-zA-Z0-9_\\[\\].]*")
            val result = regex.find(dsl,0)
            val resultString = result!!.value
            dsl = dsl.replaceFirst(resultString,"$fqn(${resultString.replace("this.","")})")
        }
        return dsl
    }

}

class PublishedPlanChanged(val currentPlan: Plan, val originalPlan: Plan?) : LoyaltyEventBusMessage


data class ModelTask(val name: String, val id: String, val block: () -> Unit)