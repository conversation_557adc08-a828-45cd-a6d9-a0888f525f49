package com.shuyun.loyalty.manager.resource.open

import com.shuyun.loyalty.entity.api.constants.MedalRecordType
import com.shuyun.loyalty.entity.api.request.BatchMemberMedalRecycleRequest
import com.shuyun.loyalty.entity.api.request.MemberMedalObtainRequest
import com.shuyun.loyalty.entity.api.request.MemberMedalRecycleRequest
import com.shuyun.loyalty.entity.api.request.MemberMedalRestRequest
import com.shuyun.loyalty.entity.api.response.MemberMedalRecordResponse
import com.shuyun.loyalty.entity.api.response.MemberMedalResponse
import com.shuyun.loyalty.manager.service.MedalRuleGroupService
import com.shuyun.loyalty.manager.vo.MedalRuleGroupApiVo
import com.shuyun.loyalty.service.meta.MedalRuleGroupTypeEnum
import com.shuyun.loyalty.service.meta.RuleGroupStatusEnum
import com.shuyun.loyalty.service.util.MDCUtils
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.extensions.Extension
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import javax.annotation.Resource
import javax.validation.Valid

@RestController
@RequestMapping("/open/medal")
class OpenMedalResource {

    @Resource
    private lateinit var loyaltyFacadeFeignApi: LoyaltyFacadeFeignApi
    @Autowired
    private lateinit var medalRuleGroupService: MedalRuleGroupService


    @Operation(summary = "查询会员勋章", tags = ["勋章 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member")
    fun findMemberMedalList(@Parameter(description = "当前页,从0开始", example = "0") @RequestParam(value = "number", defaultValue = "0") number: Int,
                            @Parameter(description = "每页记录数", example = "20") @RequestParam(value = "pageSize", defaultValue = "20") pageSize: Int,
                            @Parameter(description = "会员ID", required = true) @RequestParam memberId: String,
                            @Parameter(description = "计划ID", required = true) @RequestParam planId: Long,
                            @Parameter(description = "勋章体系ID", required = true) @RequestParam medalHierarchyId: Long): List<MemberMedalResponse> {
        return loyaltyFacadeFeignApi.findMemberMedalList(MDCUtils.getTraceId(),memberId, planId, medalHierarchyId, number, pageSize)
    }

    @Operation(summary = "颁发会员勋章", tags = ["勋章 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/obtain")
    fun obtainMemberMedal(@Valid @RequestBody request: MemberMedalObtainRequest) {
        loyaltyFacadeFeignApi.obtainMemberMedal(MDCUtils.getTraceId(),request)
    }

    @Operation(summary = "回收会员勋章", tags = ["勋章 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/recycle")
    fun recycleMemberMedal(@Valid @RequestBody request: MemberMedalRecycleRequest) {
        loyaltyFacadeFeignApi.recycleMemberMedal(MDCUtils.getTraceId(),request)
    }

    @Operation(summary = "重置会员有效期", tags = ["勋章 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/reset")
    fun resetMemberMedal(@Valid @RequestBody request: MemberMedalRestRequest) {
        loyaltyFacadeFeignApi.resetMemberMedal(MDCUtils.getTraceId(),request)
    }

    @Operation(summary = "查询勋章变更记录", tags = ["勋章 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("member/record")
    fun findRecord(@Parameter(description = "当前页,从0开始", example = "0") @RequestParam(value = "number", defaultValue = "0") number: Int,
                   @Parameter(description = "每页记录数 ", example = "20") @RequestParam(value = "pageSize", defaultValue = "20") pageSize: Int,
                   @Parameter(description = "计划ID", required = true) @RequestParam(value = "planId") planId: Long,
                   @Parameter(description = "勋章体系ID" ,required = true) @RequestParam(value = "medalHierarchyId") medalHierarchyId: Long,
                   @Parameter(description = "勋章ID") @RequestParam(value = "medalDefinitionId", required = false) medalDefinitionId: String?,
                   @Parameter(description = "会员ID(为空查询所有)") @RequestParam(value = "memberId", required = false) memberId: String?,
                   @Parameter(description = "变更筛选开始时间,格式ZonedDateTime") @RequestParam(value = "startTime", required = false) startTime: String?,
                   @Parameter(description = "变更筛选结束时间,格式ZonedDateTime") @RequestParam(value = "endTime", required = false) endTime: String?,
                   @Parameter(description = "渠道") @RequestParam(value = "channelType", required = false) channelType: String?,
                   @Parameter(description = "变更类型") @RequestParam(value = "recordType",required = false) recordType: MedalRecordType?): List<MemberMedalRecordResponse> {
        val list = loyaltyFacadeFeignApi.findMedalRecord(
            MDCUtils.getTraceId(), number, pageSize, planId, medalHierarchyId, medalDefinitionId,
            memberId, startTime, endTime, channelType, recordType
        )
        list.forEach { it.descpription = it.description }
        return list
    }

    @Operation(summary = "批量回收会员勋章", tags = ["勋章 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/batchRecycle")
    fun batchRecycleMemberMedal(@Valid @RequestBody request: BatchMemberMedalRecycleRequest) {
        loyaltyFacadeFeignApi.batchMemberMedal(MDCUtils.getTraceId(),request)
    }

    @Operation(summary = "获取勋章体系下勋章规则组列表", tags = ["勋章 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/medalRuleGroup/list/{medalHierarchyId}/{medalDefinitionId}/{type}")
    fun listMedalRuleGroup(
        @Parameter(description = "勋章体系ID") @PathVariable(value = "medalHierarchyId") medalHierarchyId: Long,
        @Parameter(description = "勋章ID") @PathVariable(value = "medalDefinitionId") medalDefinitionId: Long,
        @Parameter(description = "规则组类别") @PathVariable(value = "type") type: MedalRuleGroupTypeEnum
    ): List<MedalRuleGroupApiVo> {

        val medalRuleGroupList = medalRuleGroupService.listMedalRuleGroup(medalHierarchyId, medalDefinitionId, type, null,
            RuleGroupStatusEnum.RUNNING )
        return MedalRuleGroupApiVo().build(medalRuleGroupList)
    }

    @Operation(summary = "获取勋章体系下规则组列表", tags = ["勋章 OPEN API"],extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/medalRuleGroup/list/{medalHierarchyId}")
    fun listMedalRuleGroupView(
        @Parameter(description = "勋章体系ID") @PathVariable(value = "medalHierarchyId") medalHierarchyId: Long
    ): List<MedalRuleGroupApiVo> {
        val medalRuleGroupList = medalRuleGroupService.listMedalRuleGroupView(medalHierarchyId, null, "WAIT_EFFECT,RUNNING")
        return MedalRuleGroupApiVo().build(medalRuleGroupList)
    }

}