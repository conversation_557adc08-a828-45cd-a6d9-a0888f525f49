package com.shuyun.loyalty.manager.migration

import com.shuyun.dm.metadata.sdk.MetadataSdkFactory
import com.shuyun.kylin.es.sdk.meta.EventModel
import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.service.infrastructure.es.EventClient
import com.shuyun.pip.component.json.JsonUtils
import org.apache.commons.io.IOUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.InitializingBean
import org.springframework.core.io.support.PathMatchingResourcePatternResolver
import org.springframework.stereotype.Component
import java.nio.charset.StandardCharsets
import kotlin.concurrent.thread

@Component
class DerivationEventMigration : InitializingBean {
    private val log = LogManager.getLogger(DerivationEventMigration::class.java)

    private data class EventConfig(
        val fileName: String,
        val fqn: String,
        val enableFlag: Boolean
    ) {
        companion object {
            fun create(fileName: String, fqn: String, propertyKey: String, defaultValue: Boolean): EventConfig =
                EventConfig(
                    fileName,
                    fqn,
                    Property.getSysOrEnv(propertyKey, defaultValue)
                )
        }
    }

    fun run() {
        val eventConfigs = listOf(
            EventConfig.create(
                fileName = "derivation.event.mergeRecord",
                fqn = "data.loyalty.manager.mergeRecord",
                propertyKey = "event.open.mergeRecord",
                defaultValue = true
            ),
            EventConfig.create(
                fileName = "derivation.event.returnOrder",
                fqn = "data.loyalty.manager.returnOrder",
                propertyKey = "event.open.returnOrder",
                defaultValue = false
            ),
            EventConfig.create(
                fileName = "derivation.event.alert",
                fqn = "data.loyalty.manager.alert",
                propertyKey = "event.open.alert",
                defaultValue = false
            )
        )

        thread {
            log.info("开始注册迁移衍生事件，共计${eventConfigs.size}个事件配置")
            val configs = eventConfigs.filter { it.enableFlag }
            configs.forEach {
                subscribe(it)
            }
            configs.forEach { config ->
                retryOperation(config)
            }
            log.info("所有迁移衍生事件注册完成")
        }

    }

    private fun subscribe(config: EventConfig) {
        val sdk = MetadataSdkFactory.createMetadataHttpSdk()
        val rawFqn = config.fqn.replaceFirst("data", "event") + ".raw"
        log.info("开始订阅模型事件: filename: {} fqn: {} rawFqn: {}", config.fileName, config.fqn, rawFqn)
        sdk.subscribe(config.fqn, rawFqn)
    }

    private fun retryOperation(config: EventConfig) {
        PathMatchingResourcePatternResolver().getResource("classpath:META-INF/migrations/event/${config.fileName}.json")
            .inputStream.use { stream ->
                val jsonEvent = IOUtils.toString(stream, StandardCharsets.UTF_8)
                val eventModel = JsonUtils.parse(jsonEvent, EventModel::class.java).apply {
                    this.fqn = config.fqn.replaceFirst("data", "event")
                    this.originEventFqn = config.fqn.replaceFirst("data", "event") + ".raw"
                }
                log.info("开始创建衍生事件: filename: {} fqn: {} originEventFqn: {}", config.fileName, eventModel.fqn, eventModel.originEventFqn)
                EventClient.upsertModel(eventModel)
            }
    }

    override fun afterPropertiesSet() {
        run()
    }
}