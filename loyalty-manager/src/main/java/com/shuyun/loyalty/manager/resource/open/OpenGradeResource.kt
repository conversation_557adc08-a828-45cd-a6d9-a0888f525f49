package com.shuyun.loyalty.manager.resource.open

import com.github.f4b6a3.tsid.Tsid
import com.shuyun.loyalty.entity.api.constants.GradeRecordType
import com.shuyun.loyalty.entity.api.constants.SortType
import com.shuyun.loyalty.entity.api.request.BatchMemberGradeModifyRequest
import com.shuyun.loyalty.entity.api.request.BudgetGradeRequest
import com.shuyun.loyalty.entity.api.request.MemberGradeModifyRequest
import com.shuyun.loyalty.entity.api.response.*
import com.shuyun.loyalty.manager.service.GradeRuleGroupService
import com.shuyun.loyalty.manager.vo.GradeRuleGroupApiVo
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.service.meta.ModeTypeEnum
import com.shuyun.loyalty.service.meta.RuleGroupStatusEnum
import com.shuyun.loyalty.service.util.MDCUtils
import com.shuyun.pip.component.json.JsonUtils
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.extensions.Extension
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.web.bind.annotation.*
import javax.annotation.Resource
import javax.validation.Valid

@RestController
@RequestMapping("/open/grade")
class OpenGradeResource {

    private val log = LogManager.getLogger(OpenGradeResource::class.java)

    @Autowired
    private lateinit var gradeRuleGroupService: GradeRuleGroupService


    @Resource
    private lateinit var loyaltyFacadeFeignApi: LoyaltyFacadeFeignApi
    @Operation(summary = "获取会员等级", tags = ["等级 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member")
    fun findMemberGradeList(
        @Parameter(description = "当前页,从0开始") @RequestParam(value = "number", defaultValue = "0") number: Int?,
        @Parameter(description = "每页条数，默认20") @RequestParam(value = "pageSize", defaultValue = "20") pageSize: Int?,
        @Parameter(description = "等级体系id", required = true) @RequestParam(value = "gradeHierarchyId", required = true) gradeHierarchyId: Long,
        @Parameter(description = "会员ID") @RequestParam(value = "memberId", required = false) memberId: String?,
        @Parameter(description = "当前等级ID") @RequestParam(value = "currentGradeDefinitionId", required = false) currentGradeDefinitionId: Long?
    ): List<MemberGradeResponse>? {
        val start = System.currentTimeMillis()
        log.info("接口获取会员等级开始，$number-$currentGradeDefinitionId-$memberId")
        val list = loyaltyFacadeFeignApi.findMemberGradeList(
            MDCUtils.getTraceId(),
            number,
            pageSize,
            gradeHierarchyId,
            memberId,
            currentGradeDefinitionId
        )
        val end = System.currentTimeMillis()
        log.info("耗时:${end - start}ms, 接口获取会员等级结束，$number-$currentGradeDefinitionId-$memberId")
        return list
    }

    @Operation(summary = "获取指定会员等级", tags = ["等级 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member-current-grade")
    fun findMemberGrad(
        @Parameter(description = "等级体系id", required = true) @RequestParam(value = "gradeHierarchyId", required = true) gradeHierarchyId: Long,
        @Parameter(description = "会员ID") @RequestParam(value = "memberId", required = true) memberId: String,
    ): MemberGradeResponse? {
        val start = System.currentTimeMillis()
        log.info("接口获取指定会员等级开始，-$memberId")
        val memberGrade = loyaltyFacadeFeignApi.findMemberGrade(MDCUtils.getTraceId(),gradeHierarchyId,memberId)
        log.info("耗时:${System.currentTimeMillis() - start}ms, 接口获取指定会员等级开始，-$memberId")
        return memberGrade
    }

    @Operation(summary = "同步变更会员等级", tags = ["等级 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/modify")
    fun modifyMemberGrade(@Valid @RequestBody memberGradeModifyRequest: MemberGradeModifyRequest) {
        val start = System.currentTimeMillis()
        log.info("接口同步调用变更等级开始，{}", { JsonUtils.toJson(memberGradeModifyRequest) })
        if (memberGradeModifyRequest.triggerId == null || memberGradeModifyRequest.triggerId!!.isEmpty())
            memberGradeModifyRequest.triggerId = Tsid.fast().toLowerCase()
        loyaltyFacadeFeignApi.modifyMemberGrade(MDCUtils.getTraceId(),memberGradeModifyRequest)
        val end = System.currentTimeMillis()
        log.info("耗时:${end - start}ms, 接口同步调用变更等级结束，{}", { JsonUtils.toJson(memberGradeModifyRequest) })
    }

    @Operation(summary = "异步变更会员等级", tags = ["等级 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/asyncModify")
    fun modifyMemberGradeAsync(@Valid @RequestBody memberGradeModifyRequest: MemberGradeModifyRequest) {
        val start = System.currentTimeMillis()
        log.info("接口调用异步变更等级开始，{}", { JsonUtils.toJson(memberGradeModifyRequest) })
        if (memberGradeModifyRequest.triggerId == null || memberGradeModifyRequest.triggerId!!.isEmpty())
            memberGradeModifyRequest.triggerId = Tsid.fast().toLowerCase()
        memberGradeModifyRequest.async = true
        loyaltyFacadeFeignApi.modifyMemberGrade(MDCUtils.getTraceId(),memberGradeModifyRequest)
        val end = System.currentTimeMillis()
        log.info("耗时:${end - start}ms, 接口异步调用变更等级结束，{}", { JsonUtils.toJson(memberGradeModifyRequest) })
    }


    @Operation(summary = "获取会员等级记录", tags = ["等级 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member/record")
    fun findMemberGradeRecordList(
        @Parameter(description = "当前页,从0开始", example = "0") @RequestParam(value = "number", defaultValue = "0") number: Int?,
        @Parameter(description = "每页记录数 ", example = "20") @RequestParam(value = "pageSize", defaultValue = "20") pageSize: Int?,
        @Parameter(description = "等级体系id", required = true) @RequestParam(value = "gradeHierarchyId", required = true) gradeHierarchyId: Long,
        @Parameter(description = "会员ID") @RequestParam(value = "memberId", required = false) memberId: String?,
        @Parameter(description = "渠道类型") @RequestParam(value = "channelType", required = false) channelType: String?,
        @Parameter(description = "排序") @RequestParam(value = "sortType", required = false) sortType: SortType?,
        @Parameter(description = "变更类型") @RequestParam(value = "recordType", required = false) recordType: GradeRecordType?,
        @Parameter(description = "变更前等级ID") @RequestParam(value = "orignalGradeId", required = false) orignalGradeId: Long?,
        @Parameter(description = "变更后等级ID") @RequestParam(value = "currentGradeId", required = false) currentGradeId: Long?,
        @Parameter(description = "变更筛选开始时间,ISO8601格式") @RequestParam(value = "startTime", required = false) startTime: String?,
        @Parameter(description = "变更筛选结束时间,ISO8601格式") @RequestParam(value = "endTime", required = false) endTime: String?
    ): List<MemberGradeRecordResponse> {
        val start = System.currentTimeMillis()
        log.info("接口获取会员等级开始，$number-$orignalGradeId-$memberId")
        val list =  loyaltyFacadeFeignApi.findMemberGradeRecordList(
                MDCUtils.getTraceId(), number, pageSize, gradeHierarchyId,
                memberId, channelType, sortType, recordType, orignalGradeId, currentGradeId,
                startTime,endTime )
        val end = System.currentTimeMillis()
        log.info("耗时:${end - start}ms, 接口获取会员等级结束，$number-$orignalGradeId-$memberId")
        return list
    }

    @Operation(summary = "分页获取会员等级记录", tags = ["等级 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member/pageRecord")
    fun pageMemberGradeRecordList(
        @Parameter(description = "当前页,从0开始", example = "0") @RequestParam(value = "number", defaultValue = "0") number: Int?,
        @Parameter(description = "每页记录数 ", example = "20") @RequestParam(value = "pageSize", defaultValue = "20") pageSize: Int?,
        @Parameter(description = "等级体系id", required = true) @RequestParam(value = "gradeHierarchyId", required = true) gradeHierarchyId: Long,
        @Parameter(description = "会员ID") @RequestParam(value = "memberId", required = false) memberId: String?,
        @Parameter(description = "渠道类型") @RequestParam(value = "channelType", required = false) channelType: String?,
        @Parameter(description = "排序") @RequestParam(value = "sortType", required = false) sortType: SortType?,
        @Parameter(description = "变更类型") @RequestParam(value = "recordType", required = false) recordType: GradeRecordType?,
        @Parameter(description = "变更前等级ID") @RequestParam(value = "orignalGradeId", required = false) orignalGradeId: Long?,
        @Parameter(description = "变更后等级ID") @RequestParam(value = "currentGradeId", required = false) currentGradeId: Long?,
        @Parameter(description = "变更筛选开始时间,ISO8601格式") @RequestParam(value = "startTime", required = false) startTime: String?,
        @Parameter(description = "变更筛选结束时间,ISO8601格式") @RequestParam(value = "endTime", required = false) endTime: String?
    ): PageImpl<MemberGradeRecordResponse> {
        val start = System.currentTimeMillis()
        log.info("接口分页获取会员等级开始，$number-$orignalGradeId-$memberId")
        val pageMemberGradeRecordList = loyaltyFacadeFeignApi.pageMemberGradeRecordList(
            MDCUtils.getTraceId(), number, pageSize, gradeHierarchyId,
            memberId, channelType, sortType, recordType, orignalGradeId, currentGradeId,
            startTime, endTime
        )
        val pageImpl = PageImpl(pageMemberGradeRecordList.content!!.map {
            JsonUtils.parse(
                JsonUtils.toJson(it),
                MemberGradeRecordResponse::class.java
            )
        }, PageRequest.of(number!!, pageSize!!), pageMemberGradeRecordList.totalElements ?: 0L)
        val end = System.currentTimeMillis()
        log.info("耗时:${end - start}ms, 接口分页获取会员等级结束，$number-$orignalGradeId-$memberId")
        return pageImpl

    }

    @Operation(summary = "获取会员等级记录详情", tags = ["等级 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/member/record/detail")
    fun findGradeRecordById(
        @Parameter(description = "等级体系id" ,required = true) @RequestParam(value = "gradeHierarchyId",required = true) gradeHierarchyId:Long,
        @Parameter(description = "等级记录ID" ,required = true) @RequestParam(value = "recordId",required = true) recordId:String
    ): GradeRecordDTO {
        return loyaltyFacadeFeignApi.findGradeRecordById(MDCUtils.getTraceId(),gradeHierarchyId, recordId)
    }

    @Operation(summary = "升级简单规则表达式", tags = ["等级 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/rule/upgrade/condition")
    fun findUpGradeRuleBySimple(@Parameter(description = "等级体系ID") gradeHierarchyId: Long): GradeHierarchyRuleResponse {
        return gradeRuleGroupService.findRunningAndUpgradeList(gradeHierarchyId, ModeTypeEnum.CONDITION)
    }

    @Operation(summary = "升级高级规则表达式", tags = ["等级 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/rule/upgrade/senior")
    fun findUpGradeRuleBySenior(@Parameter(description = "等级体系ID") gradeHierarchyId: Long): GradeHierarchyRuleResponse {
        return gradeRuleGroupService.findRunningAndUpgradeList(gradeHierarchyId, ModeTypeEnum.SENIOR)
    }

    /**
     *
     */
    @Operation(summary = "预算等级", tags = ["等级 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/budget")
    fun budgetGrade(@RequestBody @Valid budgetGradeRequest: BudgetGradeRequest): ArrayList<BudgetGradeResponse> {
        log.info("预算等级接口调用开始 events : {}", Json.toJson(budgetGradeRequest))
        return gradeRuleGroupService.budgetGrade(budgetGradeRequest)
    }


    @Operation(summary = "同步批量变更会员等级", tags = ["等级 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/member/batchGradeHierarchy/modify")
    fun batchModifyMemberGrade(@Valid @RequestBody batchMemberGradeModifyRequest: BatchMemberGradeModifyRequest) {
        val start = System.currentTimeMillis()
        log.info("接口同步调用变更等级开始，{}", { JsonUtils.toJson(batchMemberGradeModifyRequest) })
        batchMemberGradeModifyRequest.gradeHierarchyIds = batchMemberGradeModifyRequest.gradeHierarchys
        loyaltyFacadeFeignApi.batchModifyMemberGradeByHierarchy(  MDCUtils.getTraceId(), batchMemberGradeModifyRequest )
        val end = System.currentTimeMillis()
        log.info(
            "耗时:${end - start}ms, 接口同步调用批量变更等级结束，{}",
            { JsonUtils.toJson(batchMemberGradeModifyRequest) })

    }

    @Operation(summary = "获取等级体系下等级规则组列表", tags = ["等级 OPEN API"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @GetMapping("/gradeRuleGroup/list/{gradeHierarchyId}")
    fun findGradeRuleGroupViewList(
        @Parameter(description = "等级体系ID") @PathVariable("gradeHierarchyId")gradeHierarchyId:Long
    ): List<GradeRuleGroupApiVo> {
        val status = HashSet<RuleGroupStatusEnum>()
        status.add(RuleGroupStatusEnum.RUNNING)
        status.add(RuleGroupStatusEnum.WAIT_EFFECT)
        val gradeRuleGroupList = gradeRuleGroupService.findViewList(gradeHierarchyId, null, status)
        return GradeRuleGroupApiVo().build(gradeRuleGroupList)
    }
}