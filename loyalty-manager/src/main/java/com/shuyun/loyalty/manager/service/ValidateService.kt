package com.shuyun.loyalty.manager.service

import com.shuyun.loyalty.service.meta.EntityType
import com.shuyun.loyalty.service.repository.MemberGradeRepository
import com.shuyun.loyalty.service.service.*
import com.shuyun.pip.component.json.JsonUtils
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime

@Service
class ValidateService{

    private val logger = LogManager.getLogger(ValidateService::class.java)

    @Autowired
    private lateinit var validateBaseService: ValidateBaseService

    @Autowired
    private lateinit var memberGradeRepository: MemberGradeRepository

    @Autowired
    private lateinit var gradeDefinitionBaseService: GradeDefinitionBaseService

    @Autowired
    private lateinit var gradeHierarchyBaseService: GradeHierarchyBaseService

    @Autowired
    private lateinit var memberMedalBaseService: MemberMedalBaseService

    @Autowired
    private lateinit var medalHierarchyBaseService: MedalHierarchyBaseService

    @Autowired
    private lateinit var medalDefinitionBaseService: MedalDefinitionBaseService

    fun checkEntityReferenced(id: Long, type: EntityType):Boolean{
        return if(id < 0) false
        else when(type){
            EntityType.PLAN -> isExistPlanId(id)//计划
            EntityType.POINT_ACCOUNT_TYPE -> isExistAccountTypeId(id) //积分账户
            EntityType.SUBJECT -> isExistSubjectId(id) //主体
            EntityType.EVENT_TYPE -> isExistEventType(id) //时机
            EntityType.CUSTOMIZED_PROPERTY -> isExistPropertyToExpreesionList(id) //属性
            EntityType.PROPERTY_TEMPLATE -> isExistPropertyTemplateToExpreesionList(id)
            EntityType.GRADE_DEFINITION -> isExistGradeDefinitionId(id) //等级
            EntityType.GRADE_HIERARCHY -> isExistGradeHierarchyId(id)  //等级体系
            EntityType.GRADE_MEMBER -> isExistGradeMemberNotForeverId(id)  //等级下非永久生效人数
            EntityType.GRADE_MEMBER_ALL -> isExistGradeMemberId(id) || isExistGradeDefinitionId(id) //等级下人数
            EntityType.MEDAL_HIERARCHY -> isExistMedalHierarchyId(id)// 勋章体系
            EntityType.MEDAL_DEFINITION -> isExistMemberDefinitionId(id)// 勋章
            EntityType.MEDAL_MEMBER -> isExistMedalMemberNotForeverId(id)// 勋章下非永久生效
            EntityType.MEDAL_MEMBER_ALL -> isExistMedalMemberId(id) || isExistMemberDefinitionId(id)// 勋章下生效
        }
    }

    private fun isExistMemberDefinitionId(medalDefinitionId: Long): Boolean {
        return validateBaseService.findMedalDefinitionId(medalDefinitionId) > 0
    }

    private fun isExistMedalMemberId(medalDefinitionId: Long): Boolean {
        return try {
            val medalDefinition = medalDefinitionBaseService.getEffectiveOne(medalDefinitionId, ZonedDateTime.now())
            val medalHierarchy = medalHierarchyBaseService.findById(medalDefinition.medalHierarchyVersionId!!).get()
            val filter = "{\"\$and\":[{\"medalDefinitionId\": $medalDefinitionId}, {\"disabled\": 0}]}"
            memberMedalBaseService.findOneByFilter(filter, medalHierarchy.id!!).isPresent
        } catch (e: IllegalArgumentException) {
            logger.warn("判断勋章下人是否存在", e)
            false
        }
    }

    private fun isExistMedalMemberNotForeverId(medalDefinitionId: Long): Boolean {
        val medalDefinition = medalDefinitionBaseService.getEffectiveOne(medalDefinitionId, ZonedDateTime.now())
        val medalHierarchy = medalHierarchyBaseService.findById(medalDefinition.medalHierarchyVersionId!!).get()
        val filter = "{\"\$and\":[{\"medalDefinitionId\":$medalDefinitionId\"}, " +
                        "{\"overdueDate\":{\"\$is_null\":false}}, {\"disabled\": 0}]}"
        return memberMedalBaseService.findOneByFilter(filter, medalHierarchy.id!!).isPresent
    }

    private fun isExistMedalHierarchyId(medalHierarchyId: Long): Boolean {
        return validateBaseService.findMedalHierarchy(medalHierarchyId) > 0
    }

    /**判断等级*/
    private fun isExistGradeDefinitionId(gradeDefinitionId:Long):Boolean{
        return if(validateBaseService.findGradeDefinitionId(gradeDefinitionId)>0) true else return false
    }

    /**判断等级下人是否存在*/
    private fun isExistGradeMemberId(gradeDefinitionId:Long):Boolean {
        return try{
            val grade = gradeDefinitionBaseService.getEffectiveOne(gradeDefinitionId, ZonedDateTime.now())
            val hierarchy = gradeHierarchyBaseService.findById(grade.gradeHierarchyVersionId!!.toString())
            memberGradeRepository.findOneByFilter("{\"currentGradeDefinitionId\": $gradeDefinitionId }",hierarchy.get().id.toString()).isPresent
        }catch (e : IllegalArgumentException) {
            logger.warn("判断等级下人是否存在 ", e)
            false
        }
    }

    /**判断等级下非永久生效人是否存在*/
    private fun isExistGradeMemberNotForeverId(gradeDefinitionId:Long):Boolean{
        val grade = gradeDefinitionBaseService.getEffectiveOne(gradeDefinitionId, ZonedDateTime.now())
        val hierarchy = gradeHierarchyBaseService.findById(grade.gradeHierarchyVersionId!!.toString())
        val forever = ZonedDateTime.of(LocalDate.of(1970, 12, 31), LocalTime.MIN, ZoneId.systemDefault())
        return memberGradeRepository.findOneByFilter("{\"\$and\":[{\"currentGradeDefinitionId\": " + gradeDefinitionId + " }, {\"overdueDate\": {\"\$eq\": "+ JsonUtils.toJson(forever)+"} }]}",hierarchy.get().id.toString()).isPresent
    }

    /**判断计划是否存在*/
    private fun isExistPlanId(planId:Long):Boolean{
        return if(validateBaseService.findPlanId(planId)>0) true else return false
    }

    /**判断主体是否存在*/
    private fun isExistSubjectId(subjectId:Long):Boolean{
        return if(validateBaseService.findSubjectId(subjectId)>0||isExistSubjectToExpreesionList(subjectId)) true else return false
    }

    /**判断时机是否存在*/
    private fun isExistEventType(eventTypeId:Long):Boolean{
        return if(validateBaseService.findEventTypeId(eventTypeId)>0) true else return false
    }

    /**判断积分账户是否存在*/
    private fun isExistAccountTypeId(accountTypeId:Long):Boolean{
        return if(validateBaseService.findPointAccountTypeId(accountTypeId)>0) true else return false
    }

    /**判断等级体系*/
    private fun isExistGradeHierarchyId(gradeHierarchyId:Long):Boolean{
        return if(validateBaseService.findGradeHierarchyId(gradeHierarchyId)>0) true else return false
    }

    /**判断属性是否存在*/
    private fun isExistPropertyToExpreesionList(propertyId:Long):Boolean{
        //查询所有表达式
        return validateBaseService.findPropertyToExpreesionList(propertyId,EntityType.CUSTOMIZED_PROPERTY).isNotEmpty()
    }

    /**判断属性模板是否存在*/
    private fun isExistPropertyTemplateToExpreesionList(templateId:Long):Boolean{
        //查询所有表达式
        return validateBaseService.findPropertyToExpreesionList(templateId,EntityType.PROPERTY_TEMPLATE).isNotEmpty()
    }

    /**判断主体是否存在*/
    private fun isExistSubjectToExpreesionList(subjectId:Long):Boolean{
        //查询所有表达式
        val properties = ArrayList<Long>()
        validateBaseService.findSubjectToExpreesionList(subjectId).forEach {
            if(it.isNotEmpty()) {
                JsonUtils.parse2List(it,Map::class.java).forEach { map ->
                    properties.add(map["id"].toString().toLong())
                }
            }
        }
        if(properties.size > 0) {
            if(validateBaseService.findPropertyToSubject(subjectId,properties)>0) return true
        }
        return false
    }

}