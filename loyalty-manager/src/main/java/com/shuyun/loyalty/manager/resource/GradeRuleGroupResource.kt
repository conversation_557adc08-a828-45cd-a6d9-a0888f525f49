package com.shuyun.loyalty.manager.resource

import com.shuyun.epassport.sdk.register.RequiresPermissions
import com.shuyun.loyalty.manager.service.GradeRuleGroupService
import com.shuyun.loyalty.manager.service.audit.AuditGradeRuleGroupService
import com.shuyun.loyalty.manager.vo.GradeRuleGroupVoList
import com.shuyun.loyalty.service.meta.GradeRuleGroupTypeEnum
import com.shuyun.loyalty.service.meta.RuleGroupStatusEnum
import com.shuyun.loyalty.service.model.GradeRuleGroup
import com.shuyun.loyalty.service.service.GradeRuleGroupBaseService
import com.shuyun.pip.util.ObjectUtils
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import java.util.*
import javax.validation.Valid

@RestController
@RequestMapping("/gradeRuleGroup")
class GradeRuleGroupResource {

    @Autowired
    private lateinit var gradeRuleGroupService: GradeRuleGroupService

    @Autowired
    private lateinit var gradeRuleGroupBaseService: GradeRuleGroupBaseService

    @Autowired
    private lateinit var auditGradeRuleGroupService: AuditGradeRuleGroupService

    @Operation(summary = "获取等级体系下等级规则组列表", tags = ["等级规则组 API"])
    @GetMapping("/list/{gradeHierarchyId}/{gradeDefinitionId}/{groupType}")
    @RequiresPermissions(value = ["loyalty4.manage.grade.rule.view","loyalty4.manage.grade.rule.edit","loyalty4.manage.grade.rule.file"])
    fun findGradeRuleGroupList(@Parameter(description = "等级体系ID") @PathVariable("gradeHierarchyId")gradeHierarchyId:Long,
                               @Parameter(description = "等级id") @PathVariable("gradeDefinitionId")gradeDefinitionId:Long,
                               @Parameter(description = "规则组类别") @PathVariable("groupType")groupType: GradeRuleGroupTypeEnum,
                               @Parameter(description = "规则组名称") @RequestParam("groupName", required = false) groupName: String?,
                               @Parameter(description = "规则状态") @RequestParam("groupStatus", required = false) groupStatus: RuleGroupStatusEnum?): List<GradeRuleGroup> {
        return auditGradeRuleGroupService.findList(
            gradeHierarchyId,
            gradeDefinitionId,
            groupType,
            groupName,
            groupStatus
        )
    }

    @Operation(summary = "获取等级体系下等级规则组列表", tags = ["等级规则组 API"])
    @GetMapping("/list/{gradeHierarchyId}")
    @RequiresPermissions(value = ["loyalty4.manage.grade.rule.view","loyalty4.manage.grade.rule.edit","loyalty4.manage.grade.rule.file"])
    fun findGradeRuleGroupViewList(
        @Parameter(description = "等级体系ID") @PathVariable("gradeHierarchyId")gradeHierarchyId:Long,
        @Parameter(description = "规则组名称") @RequestParam(value = "groupName", required = false) groupName:String?,
        @Parameter(description = "规则组状态", example = "DESIGN, END, FILED, PROCESS, REJECT, RUNNING, WAIT_EFFECT") @RequestParam(value = "groupStatus", defaultValue = "WAIT_EFFECT,RUNNING", required = false)groupStatus:String?
    ): List<GradeRuleGroup> {
        val status = HashSet<RuleGroupStatusEnum>()
        if (groupStatus.isNullOrBlank()) {
            status.add(RuleGroupStatusEnum.RUNNING)
            status.add(RuleGroupStatusEnum.WAIT_EFFECT)
        } else {
            groupStatus.split(",").forEach {
                status.add(RuleGroupStatusEnum.valueOf(it))
            }
        }
        return gradeRuleGroupService.findViewList(gradeHierarchyId, groupName, status)
    }

    @Operation(summary = "获取等级规则组", tags = ["等级规则组 API"])
    @GetMapping("/{groupId}")
    @RequiresPermissions(value = ["loyalty4.manage.grade.rule.view","loyalty4.manage.grade.rule.edit","loyalty4.manage.grade.rule.file"])
    fun findByGroupId(@Parameter(description = "规则组ID") @PathVariable("groupId") groupId: Long): GradeRuleGroup {
        return gradeRuleGroupBaseService.findById(groupId, Date())
    }

    @Operation(summary = "获取等级定义下的等级规则组列表", tags = ["等级规则组 API"])
    @GetMapping("/grade/{gradeDefinitionId}")
    @RequiresPermissions(value = ["loyalty4.manage.grade.rule.view","loyalty4.manage.grade.rule.edit","loyalty4.manage.grade.rule.file"])
    fun findByGradeDefinitionId(@Parameter(description = "等级id") @PathVariable("gradeDefinitionId")gradeDefinitionId:Long): List<GradeRuleGroup> {
        return gradeRuleGroupService.findList(gradeDefinitionId)
    }


    @Operation(summary = "保存规则组信息")
    @PostMapping("")
    @RequiresPermissions(value = ["loyalty4.manage.grade.rule.edit","loyalty4.manage.grade.rule.file"])
    fun saveOrUpdate(@Valid @RequestBody gradeRuleGroupVoList: GradeRuleGroupVoList){
        var oldName: String? = null
        if (!ObjectUtils.isEmpty(gradeRuleGroupVoList.ruleGroupList!![0].id)) {
            oldName = gradeRuleGroupService.findByIdAndDisabled(gradeRuleGroupVoList.ruleGroupList!![0].id!!)
        }
        gradeRuleGroupService.save(gradeRuleGroupVoList, oldName)
    }

    @Operation(summary = "删除规则组信息")
    @DeleteMapping("/{groupId}")
    @RequiresPermissions(value = ["loyalty4.manage.grade.rule.edit"])
    fun delete(@Parameter(description = "规则组ID") @PathVariable("groupId") groupId: Long){
        gradeRuleGroupService.delete(groupId)
    }

    @Operation(summary = "归档规则组信息")
    @PostMapping("/file/{groupId}")
    @RequiresPermissions(value = ["loyalty4.manage.grade.rule.file"])
    fun file(@Parameter(description = "规则组ID") @PathVariable("groupId") groupId: Long){
        gradeRuleGroupService.file(groupId)
    }

}