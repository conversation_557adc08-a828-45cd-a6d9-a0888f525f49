package com.shuyun.loyalty.manager.service

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.entity.api.constants.PublishStatusEnum
import com.shuyun.loyalty.manager.util.Constants
import com.shuyun.loyalty.manager.util.ListCompareUtil
import com.shuyun.loyalty.manager.util.WAY_TO_HANDLE_DATA
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.EntityType
import com.shuyun.loyalty.service.model.MedalHierarchy
import com.shuyun.loyalty.service.model.Subject
import com.shuyun.loyalty.service.service.MedalHierarchyBaseService
import com.shuyun.loyalty.service.util.ModelInitUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class MedalHierarchyService {

    @Autowired
    private lateinit var medalDefinitionService: MedalDefinitionService

    @Autowired
    private lateinit var medalHierarchyBaseService: MedalHierarchyBaseService

    @Autowired
    private lateinit var validateService: ValidateService

    fun insert(subject: Subject) {
        subject.medalHierarchyList?.forEach { medalHierarchy ->
            medalHierarchy.subjectVersionId = subject.versionId
            insert(medalHierarchy)
            medalDefinitionService.insert(medalHierarchy)
        }
    }

    fun delete(subject: Subject) {
        subject.medalHierarchyList?.forEach { delete(it) }
    }

    fun copy(subjectCopy: Subject) {
        subjectCopy.medalHierarchyList?.forEach { medalHierarchy ->
            val medalHierarchyCopy = MedalHierarchy()
            ModelInitUtil.copyPropertiesIgnoreNull(medalHierarchy, medalHierarchyCopy)
            medalHierarchyBaseService.save(medalHierarchyCopy.apply {
                this.versionId = null
                this.subjectVersionId = subjectCopy.versionId
            })
            medalDefinitionService.copy(medalHierarchyCopy)
        }
    }

    fun update(subject: Subject, backup: Boolean) {
        val newMedalHierarchyList = subject.medalHierarchyList
        val oldMedalHierarchyList = medalHierarchyBaseService.findDetailBySubjectVersionId(subject.versionId!!)

        ListCompareUtil.initModifyDataServiceByCompareList(newMedalHierarchyList, oldMedalHierarchyList, backup, Constants.keyId)
            .forEach { (medalHierarchy, wayToHandle) ->
                medalHierarchy as MedalHierarchy
                when (wayToHandle) {
                    WAY_TO_HANDLE_DATA.TO_ADD -> {
                        medalHierarchy.subjectVersionId = subject.versionId
                        insert(medalHierarchy)
                    }
                    WAY_TO_HANDLE_DATA.TO_UPDATE -> {
                        if (medalHierarchy.status == PublishStatusEnum.FILED
                            && validateService.checkEntityReferenced(medalHierarchy.id!!, EntityType.MEDAL_HIERARCHY)) {
                            throw LoyaltyException(LoyaltyExceptionCode.MEDAL_NOT_OPERATION_FILE)
                        }
                        update(medalHierarchy, backup)
                    }
                    WAY_TO_HANDLE_DATA.TO_DELETE -> {
                        if (backup && medalHierarchy.status != PublishStatusEnum.DRAFT)
                            throw LoyaltyException(LoyaltyExceptionCode.NOT_OPERATION_DELETE_MEDAL)
                        delete(medalHierarchy)
                    }
                }
            }
    }

    fun delete(medalHierarchy: MedalHierarchy) {
        medalHierarchyBaseService.delete(medalHierarchy)
        medalDefinitionService.delete(medalHierarchy)
    }

    fun update(medalHierarchy: MedalHierarchy, backup: Boolean) {
        medalHierarchyBaseService.update(medalHierarchy)
        medalDefinitionService.update(medalHierarchy, backup)
    }

    private fun insert(medalHierarchy: MedalHierarchy) {
        medalHierarchyBaseService.insert(medalHierarchy)
        medalDefinitionService.insert(medalHierarchy)
    }

    fun publish(subject: Subject) {
        subject.medalHierarchyList?.forEach { medalHierarchy ->
            medalHierarchy.status = if (subject.status == EnableStatusEnum.ENABLED) {
                if (medalHierarchy.status == PublishStatusEnum.DRAFT) PublishStatusEnum.PUBLISHED else medalHierarchy.status
            } else {
                PublishStatusEnum.FILED
            }

            if (medalHierarchy.status == PublishStatusEnum.FILED) {
                medalHierarchy.sort = medalHierarchy.sort!! - 100000
            } else {
                while (medalHierarchy.sort!! < 0)
                    medalHierarchy.sort = medalHierarchy.sort!! + 100000
            }

            medalHierarchyBaseService.update(medalHierarchy)
            if (medalHierarchy.status == PublishStatusEnum.PUBLISHED) {
                medalDefinitionService.publish(medalHierarchy)
            }
        }
    }


    fun upgrade(subject: Subject) {
        subject.medalHierarchyList?.forEach { medalHierarchy ->
            medalHierarchy.subjectVersionId = subject.versionId
            medalHierarchyBaseService.save(medalHierarchy)

            if (medalHierarchy.sourceId != null && medalHierarchy.id == null) {
                medalHierarchy.id = medalHierarchy.versionId
                medalHierarchy.status = PublishStatusEnum.DRAFT
                medalHierarchyBaseService.save(medalHierarchy)
            }

            medalDefinitionService.upgrade(medalHierarchy)
        }
    }

}