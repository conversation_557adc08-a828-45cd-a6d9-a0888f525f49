package com.shuyun.loyalty.manager.resource

import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.shuyun.epassport.sdk.register.RequiresPermissions
import com.shuyun.loyalty.service.event.Event
import com.shuyun.loyalty.service.util.ExpressionIdentUtil
import org.apache.logging.log4j.LogManager
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/fx")
class FxResource {

    private val log = LogManager.getLogger(FxResource::class.java)
    data class Input(val dsl: String, val subjectFqn: String, val event: Event)

    @PostMapping("/epp")
    @RequiresPermissions(allowAuthenticated = true)
    fun epp(@RequestBody input: Input): Any? {
        return try {
            ExpressionIdentUtil.epp(
                input.dsl,
                input.event,
                JsonNodeFactory.instance.objectNode().put("subject", input.subjectFqn)
            )
        } catch (e: Exception) {
            log.warn("表达式计算错误", e)
            mapOf("error" to e.message)
        }
    }

    @PostMapping("/validate")
    @RequiresPermissions(allowAuthenticated = true)
    fun validate(@RequestBody input: Input): Any? {
        return try {
            ExpressionIdentUtil.validation(
                input.dsl
            )
        } catch (e: Exception) {
            log.warn("表达式计算错误", e)
            mapOf("error" to e.message)
        }
    }
}