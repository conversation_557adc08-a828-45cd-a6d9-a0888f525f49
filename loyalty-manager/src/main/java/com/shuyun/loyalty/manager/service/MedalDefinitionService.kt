package com.shuyun.loyalty.manager.service

import com.shuyun.loyalty.manager.util.Constants
import com.shuyun.loyalty.manager.util.ListCompareUtil
import com.shuyun.loyalty.manager.util.WAY_TO_HANDLE_DATA
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.EntityType
import com.shuyun.loyalty.service.model.MedalDefinition
import com.shuyun.loyalty.service.model.MedalHierarchy
import com.shuyun.loyalty.service.service.MedalDefinitionBaseService
import com.shuyun.loyalty.service.util.ModelInitUtil.copyPropertiesIgnoreNull
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class MedalDefinitionService {

    @Autowired
    private lateinit var medalDefinitionBaseService: MedalDefinitionBaseService

    @Autowired
    private lateinit var validateService: ValidateService


    fun insert(medalHierarchy: MedalHierarchy) {
        medalHierarchy.medalDefinitions?.forEach { medalDefinition ->
            medalDefinition.medalHierarchyVersionId = medalHierarchy.versionId
            medalDefinitionBaseService.insert(medalDefinition)
        }
    }

    fun delete(medalHierarchy: MedalHierarchy) {
        medalHierarchy.medalDefinitions?.forEach { medalDefinition ->
            medalDefinitionBaseService.delete(medalDefinition)
        }
    }

    fun copy(medalHierarchyCopy: MedalHierarchy) {
        medalHierarchyCopy.medalDefinitions?.forEach {
            val medalDefinitions = MedalDefinition()
            copyPropertiesIgnoreNull(it, medalDefinitions)
            medalDefinitionBaseService.save(medalDefinitions.apply {
                this.versionId = null
                this.medalHierarchyVersionId = medalHierarchyCopy.versionId
            })
        }
    }

    fun update(medalHierarchy: MedalHierarchy, backup: Boolean) {
        val newMedalDefinitionList = medalHierarchy.medalDefinitions
        val oldMedalDefinitionList = medalDefinitionBaseService.findEnabledMedalHierarchyByVersionId(medalHierarchy.versionId!!)
        ListCompareUtil.initModifyDataServiceByCompareList(newMedalDefinitionList, oldMedalDefinitionList, backup, Constants.keyId)
            .forEach { (medalDefinition, wayToHandle) ->
                medalDefinition as MedalDefinition
                when (wayToHandle) {
                    WAY_TO_HANDLE_DATA.TO_ADD -> {
                        medalDefinition.medalHierarchyVersionId = medalHierarchy.versionId
                        medalDefinitionBaseService.insert(medalDefinition)
                    }
                    WAY_TO_HANDLE_DATA.TO_UPDATE -> medalDefinitionBaseService.update(medalDefinition)
                    WAY_TO_HANDLE_DATA.TO_DELETE -> {
                        if (validateService.checkEntityReferenced(medalDefinition.id!!, EntityType.MEDAL_DEFINITION))
                            throw LoyaltyException(LoyaltyExceptionCode.NOT_OPERATION_DELETE_CURRENT_MEDAL, "当前勋章正在运行，不可进行该操作，如需删除，请先到业务端终止相关规则后再操作")
                        if (validateService.checkEntityReferenced(medalDefinition.id!!, EntityType.MEDAL_MEMBER_ALL))
                            throw LoyaltyException(LoyaltyExceptionCode.NOT_OPERATION_DELETE_CURRENT_MEMBER, "当前勋章下存在用户，不可删除")
                        medalDefinitionBaseService.delete(medalDefinition)
                    }
                }
            }
    }

    fun publish(medalHierarchy: MedalHierarchy) {
        medalHierarchy.medalDefinitions?.forEach { medalDefinitionBaseService.update(it) }
    }


    fun upgrade(medalHierarchy: MedalHierarchy) {
        medalHierarchy.medalDefinitions?.forEach {
            it.medalHierarchyVersionId = medalHierarchy.versionId
            medalDefinitionBaseService.save(it)

            if (it.sourceId != null && it.id == null) {
                it.id = it.versionId
                medalDefinitionBaseService.save(it)
            }
        }
    }

}