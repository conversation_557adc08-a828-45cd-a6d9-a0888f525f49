package com.shuyun.loyalty.manager.resource

import com.shuyun.epassport.sdk.register.RequiresPermissions
import com.shuyun.loyalty.entity.api.constants.MedalChangeMode
import com.shuyun.loyalty.entity.api.constants.TransferMedalTypeEnum
import com.shuyun.loyalty.entity.api.request.MemberMedalModifyRequest
import com.shuyun.loyalty.entity.enums.ForbiddenPort
import com.shuyun.loyalty.manager.service.DataModelService
import com.shuyun.loyalty.manager.vo.MemberMedalModifyVo
import com.shuyun.loyalty.service.extension.toZonedDateTime
import com.shuyun.loyalty.service.transfer.medal.MemberMedalTransferService
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import com.shuyun.pip.component.json.JsonUtils
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import javax.validation.Valid
import kotlin.concurrent.withLock

@RestController
@RequestMapping("/member/medal")
@RequiresPermissions(allowAuthenticated = true)
class MemberMedalResource {

    @Autowired
    private lateinit var dataModelService: DataModelService

    @Autowired
    private lateinit var memberMedalTransferService: MemberMedalTransferService

    @Operation(summary = "查询会员勋章详情", tags = ["会员勋章 API"])
    @GetMapping("/findMemberMedalDetail")
    @RequiresPermissions(value = ["loyalty4.manage.mofify.manual.viewMedal"])
    fun findMemberMedalDetail(@RequestParam(value = "hierarchyId") hierarchyId: String,
                              @RequestParam(value = "fields") fields: String,
                              @RequestParam(value = "filter") filter: String,
                              @RequestParam(value = "offset") offset: Int,
                              @RequestParam(value = "limit") limit: Int,
                              @RequestParam(value = "withTotals", defaultValue = "false") withTotals: Boolean): Any? {

        return dataModelService.queryMedalDetail(hierarchyId, fields, filter, offset, limit, withTotals)
    }

    @Operation(summary = "变更会员勋章,overdueDate不为空时,过期时间为overdueDate,否则将根据过期日期各项定义计算overdueDate", tags = ["会员勋章 API"])
    @PostMapping("/modify")
    @RequiresPermissions(value = ["loyalty4.manage.mofify.manual.editMedal"])
    fun modifyMemberMedal(@Valid @RequestBody memberMedalModifyVo: MemberMedalModifyVo) {
        memberMedalModifyVo.transferMedalType = TransferMedalTypeEnum.CHANGE
        memberMedalModifyVo.overdueDate ?: memberMedalModifyVo.calculateOverdueTime()?.let {
            memberMedalModifyVo.overdueDate = it.toZonedDateTime()
        }
        val request = JsonUtils.convert(memberMedalModifyVo, MemberMedalModifyRequest::class.java).apply {
            this.changeWayType = MedalChangeMode.MANUAL
            this.channelType = "loyalty"
            this.transferMedalType = TransferMedalTypeEnum.CHANGE
        }
        val locker = ApplicationContextHolder.getBean(Locker::class.java)
        val gradeLock = locker.getLock("medal_calculate_${request.medalHierarchyId}-${request.memberId}")
        gradeLock.withLock {
            memberMedalTransferService.modifyMemberMedal(request, ForbiddenPort.MANUAL)
        }
    }

    @Operation(summary = "回收会员勋章", tags = ["会员勋章 API"])
    @PostMapping("/recycle")
    @RequiresPermissions(value = ["loyalty4.manage.mofify.manual.recycleMedal"])
    fun recycleMemberMedal(@Valid @RequestBody memberMedalModifyVo: MemberMedalModifyVo) {
        val request = JsonUtils.convert(memberMedalModifyVo, MemberMedalModifyRequest::class.java).apply {
            this.changeWayType = MedalChangeMode.MANUAL
            this.channelType = "loyalty"
            this.transferMedalType = TransferMedalTypeEnum.RECYCLE
        }
        val locker = ApplicationContextHolder.getBean(Locker::class.java)
        val gradeLock = locker.getLock("medal_calculate_${request.medalHierarchyId}-${request.memberId}")
        gradeLock.withLock {
            memberMedalTransferService.modifyMemberMedal(request, ForbiddenPort.MANUAL)
        }
    }
}