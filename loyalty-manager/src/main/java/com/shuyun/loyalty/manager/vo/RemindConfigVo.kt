package com.shuyun.loyalty.manager.vo

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.entity.api.constants.IntervalUnit
import com.shuyun.loyalty.service.meta.RemindType
import com.shuyun.loyalty.service.meta.TimeTypeEnum
import com.shuyun.loyalty.service.model.I18nPayload
import com.shuyun.loyalty.service.model.RemindConfig
import com.shuyun.loyalty.service.util.ModelInitUtil.copyPropertiesIgnoreNull
import io.swagger.v3.oas.annotations.media.Schema
import java.time.ZonedDateTime
import javax.validation.constraints.NotBlank

/**
 * 过期配置
 */
@Schema(title = "RemindConfigVo")
class RemindConfigVo : I18nPayload() {

    @Schema(title = "名称")
    @NotBlank(message = "名称")
    var name: String? = null

    @Schema(title = "计划ID")
    var planVersionId: Long? = null

    @Schema(title = "主体ID")
    var subjectVersionId: Long? = null

    @Schema(title = "过期ID")
    var id: Long? = null

    @Schema(title = "积分账户id或者等级体系id")
    var parentId: Long? = null

    @Schema(title = "积分账户id或者等级体系id")
    var parentVersionId: Long? = null

    @Schema(title = "到期类型")
    var remindType: RemindType? = null

    @Schema(title = "时间类型，相对/绝对")
    var timeType: TimeTypeEnum? = null

    @Schema(title = "天数")
    var timeDay: Int? = null

    @Schema(title = "月")
    var timeMonth: Int? = null

    @Schema(title = "年")
    var timeYear: Int? = null

    @Schema(title = "符号")
    var timeSign: String? = null

    @Schema(title = "账户类型描述")
    var timeDate: ZonedDateTime? = null

    @Schema(title = "发送时间")
    var cycleDay: Int? = null

    @Schema(title = "提醒间隔单位")
    var intervalUnit: IntervalUnit? = null
        get() {
            return field ?: IntervalUnit.DAY
        }

    @Schema(title = "提醒间隔值")
    var intervalValue: Int? = null
        get() {
            return field ?: when {
                cycleDay == null || cycleDay!! <= 0 -> 0
                else -> cycleDay
            }
        }

    @Schema(title = "前端排序")
    var sort: Int? = null

    @Schema(title = "记录表名")
    var remindTableName: String? = null

    @Schema(title = "状态")
    var status: EnableStatusEnum? = null


    /**根据vo初始化model*/
    fun initRemindConfigFromVo(remindConfig: RemindConfig) {
        copyPropertiesIgnoreNull(this, remindConfig)
    }
}