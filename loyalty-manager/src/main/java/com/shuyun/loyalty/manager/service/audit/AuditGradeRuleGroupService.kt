package com.shuyun.loyalty.manager.service.audit

import com.shuyun.loyalty.manager.audit.annotation.CreateLogAnnotation
import com.shuyun.loyalty.manager.audit.annotation.DeleteLogAnnotation
import com.shuyun.loyalty.manager.audit.annotation.QueryLogAnnotation
import com.shuyun.loyalty.manager.audit.annotation.UpdateLogAnnotation
import com.shuyun.loyalty.manager.audit.enums.BehaviorEnum
import com.shuyun.loyalty.manager.audit.enums.ModuleEnum
import com.shuyun.loyalty.service.annotation.InsertOrUpdateAnnotation
import com.shuyun.loyalty.service.meta.GradeRuleGroupTypeEnum
import com.shuyun.loyalty.service.meta.OperationType
import com.shuyun.loyalty.service.meta.RuleGroupStatusEnum
import com.shuyun.loyalty.service.model.GradeRuleGroup
import com.shuyun.loyalty.service.service.GradeRuleGroupBaseService
import com.shuyun.loyalty.service.service.I18nModelService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.*
import kotlin.concurrent.thread

@Service
class AuditGradeRuleGroupService {

    @Autowired
    private lateinit var gradeRuleGroupBaseService: GradeRuleGroupBaseService

    @Autowired
    private lateinit var i18nModelService: I18nModelService

    /**插入规则组本身*/
    @InsertOrUpdateAnnotation(operationType = OperationType.ADD)
    @CreateLogAnnotation(
        `object` = "等级规则名称",
        behaviorCode = BehaviorEnum.UPDATE,
        module = ModuleEnum.GRADE_RULE,
        fields = ["name"]
    )
    fun insert(gradeRuleGroup: GradeRuleGroup, @Suppress("unused") gradeName: String) {
        gradeRuleGroup.name = gradeRuleGroup.name!!.trim()
        gradeRuleGroup.id = null
        gradeRuleGroup.status = gradeRuleGroup.status ?: RuleGroupStatusEnum.WAIT_EFFECT
        gradeRuleGroupBaseService.saveOrUpdate(gradeRuleGroup)
    }


    /**更新规则组本身*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    @UpdateLogAnnotation(
        `object` = "等级规则名称",
        behaviorCode = BehaviorEnum.UPDATE,
        module = ModuleEnum.GRADE_RULE,
        fields = ["name"]
    )
    fun update(gradeRuleGroup: GradeRuleGroup, @Suppress("unused") oldName: String) {
        gradeRuleGroup.name = gradeRuleGroup.name!!.trim()
        gradeRuleGroupBaseService.saveOrUpdate(gradeRuleGroup)
    }


    /**删除规则组本身*/
    @InsertOrUpdateAnnotation(operationType = OperationType.UPDATE)
    @DeleteLogAnnotation(
        `object` = "等级规则名称",
        behaviorCode = BehaviorEnum.DELETE,
        module = ModuleEnum.GRADE_RULE,
        fields = ["name"]
    )
    fun delete(gradeRuleGroup: GradeRuleGroup) {
        gradeRuleGroup.disabled = true
        gradeRuleGroupBaseService.saveOrUpdate(gradeRuleGroup)
    }


    /**根据等级体系Id和规则组名称获取规则组列表*/
    @QueryLogAnnotation(behaviorCode = BehaviorEnum.QUERY, module = ModuleEnum.GRADE_RULE)
    fun findList(
        gradeHierarchyId: Long,
        gradeDefinitionId: Long,
        groupType: GradeRuleGroupTypeEnum,
        groupName: String?,
        groupStatus: RuleGroupStatusEnum?
    ): List<GradeRuleGroup> {

        val groupList = ArrayList<GradeRuleGroup>()

        if (StringUtils.isEmpty(groupName)) {
            groupList.addAll(
                gradeRuleGroupBaseService.findByGradeHierarchyId(
                    gradeHierarchyId,
                    gradeDefinitionId,
                    groupType,
                    Date()
                )
            )
        } else {
            groupList.addAll(
                gradeRuleGroupBaseService.findByGradeHierarchyIdAndGroupName(
                    gradeHierarchyId,
                    gradeDefinitionId,
                    groupType,
                    groupName!!,
                    Date()
                )
            )
        }
        val ts = ArrayList<Thread>()
        groupList.forEach {
            val t = thread {
                it.transferEventTypeName()
                it._i18nPayload = i18nModelService.findI18nPayload(GradeRuleGroup.I18N_GROUP__NAME, it.id)
            }
            ts.add(t)
        }
        ts.forEach { it.join() }
        return groupList.filter {
            groupStatus?.equals(it.status) ?: true
        }.sortedByDescending {
            it.createTime
        }
    }
}