package com.shuyun.loyalty.manager.migration

import com.shuyun.dm.api.domain.DataModel
import com.shuyun.dm.api.domain.FieldConstraint
import com.shuyun.dm.api.domain.ModelField
import com.shuyun.dm.metadata.sdk.MetadataSdkFactory.createMetadataHttpSdk
import com.shuyun.dm.metadata.sdk.MetadataSdkFactory.userContextSupplier
import com.shuyun.dm.metadata.sdk.client.MetadataHttpSdk
import com.shuyun.dm.sdk.Options.Companion.newBuilder
import com.shuyun.epassport.sdk.register.RegisterClientException
import com.shuyun.lite.client.PassportClientFactory
import com.shuyun.lite.context.ThreadLocalUserContext
import com.shuyun.loyalty.sdk.Property
import com.shuyun.pip.component.exception.StartupException
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe.Companion.setWithUserContext
import com.shuyun.pip.frameworkext.filter.VisitTenantInfoHolder.Companion.setTenantId
import com.shuyun.pip.frameworkext.filter.VisitTokenInfoHolder
import com.shuyun.pip.util.EnvUtils
import org.slf4j.LoggerFactory
import org.springframework.core.io.support.PathMatchingResourcePatternResolver
import org.springframework.core.io.support.ResourcePatternResolver
import org.yaml.snakeyaml.Yaml
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStreamReader
import java.io.Reader
import java.nio.charset.StandardCharsets
import java.util.*
import java.util.function.Consumer
import java.util.function.Function

/**
 * 数据服务标准migration支持
 *
 * <AUTHOR>
 */
class MetadataMigration {

    companion object {

        private val log = LoggerFactory.getLogger(MetadataMigration::class.java)
        private const val LOCATION_PATTERN = "classpath*:META-INF/migrations/metadata/\${migration.profile}/scripts/*.json"
        const val SKIP_MIGRATION_METADATA_KEY = "skip.migration.metadata"

        private var tokenSupplier = Function<String, String> { _ -> genToken() }

        fun migration(profile: String) {
            val tenantId = ThreadLocalUserContext.currentTenantId()
            val scriptsPath = LOCATION_PATTERN.replace("\${migration.profile}", profile)
            log.info("current metadata migration full path:{}", scriptsPath)
            val resourceResolver: ResourcePatternResolver = PathMatchingResourcePatternResolver()
            val metadataService = Property.getSysOrEnv("metadata/v1.client.targetServer")
            userContextSupplier(UserContextThreadSafe.getUserContextThreadSafeSupplier())
            val metadataHttpSdk: MetadataHttpSdk = try {
                if (metadataService.isNullOrEmpty()) {
                    createMetadataHttpSdk()
                } else {
                    val options =
                        newBuilder()
                            .enableSign(true)
                            .caller(Property.getSysOrEnv("metadata/v1.client.name", ""))
                            .secret(Property.getSysOrEnv("metadata/v1.client.sign.secret", "")
                        ).version("v1").build()
                    createMetadataHttpSdk(metadataService, options, "metaMigration")
                }
            } catch (e: StartupException) {
                throw StartupException("connection metadata failed", e)
            } catch (e: Exception) {
                throw RuntimeException("execute migration failed", e)
            }
            setWithUserContext(true)
            setTenantId(tenantId)
            VisitTokenInfoHolder.setToken(tokenSupplier.apply(tenantId))
            try {
                val resources = resourceResolver.getResources(scriptsPath).sortedBy { it.filename }
                for (r in resources) {
                    log.info("metadata script:{}", r.filename)
                    if (r.filename == "20220913150060000_update_account_type.json") {
                        metadataHttpSdk.updateField("data.loyalty.manager.pointAccountType", "priorityDeduction", ModelField().apply {
                            name = "priorityDeduction"
                            custom = true
                            description = "扣减顺序配置"
                            title = "扣减顺序配置"
                            indexed = false
                            fieldType = DataModel().apply { this.fqn = "system.lang.String" }
                            constraint = FieldConstraint()
                        })
                        continue
                    }
                    metadataHttpSdk.migrate(listOf(r.inputStream))
                }
            } catch (e: IOException) {
                throw RuntimeException("read metadata model file failed", e)
            }
            VisitTokenInfoHolder.setToken(null)
            setTenantId(null)
            log.info("metadata执行migration结束，租户：{}", tenantId)
        }

        private fun genToken(): String {
            // 从ThreadLocal获取token
            var token = VisitTokenInfoHolder.getToken()
            if (token == null) {
                // 客户端还未注册，需要token场景，生成客户端token
                token = PassportClientFactory.instance().virtualToken(getClientId())
            }
            return token
        }

        private fun getClientId(): String? {
            val resource = Thread.currentThread().contextClassLoader.getResourceAsStream("register.yml")
                ?: throw RegisterClientException("register.yml文件不存在!")
            val reader: Reader = BufferedReader(InputStreamReader(resource, StandardCharsets.UTF_8))
            val config = Yaml().loadAs(reader, com.shuyun.epassport.sdk.register.Configuration::class.java)
            return config.clientId
        }
    }

    fun execute() {
        if (Property.getSysOrEnv(SKIP_MIGRATION_METADATA_KEY, false)) {
            log.info("skip.migration.metadata=true，将跳过metadata的migration处理")
            return
        }
        val profiles = Property.getSysOrEnv("migration.profile")
        Objects.requireNonNull(profiles, "migration.profile not setting, metadata migration unkown how to do")
        if (EnvUtils.isDevMode) {
            log.warn("devMode skip register metadata:{}", profiles)
            return
        }
        listOf(*profiles!!.split(",".toRegex()).toTypedArray()).forEach(Consumer { profile: String -> migration(profile) })
    }

}
