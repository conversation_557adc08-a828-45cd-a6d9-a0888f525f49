package com.shuyun.loyalty.manager.service

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.entity.api.constants.PublishStatusEnum
import com.shuyun.loyalty.manager.util.Constants.keyId
import com.shuyun.loyalty.manager.util.ListCompareUtil.initModifyDataServiceByCompareList
import com.shuyun.loyalty.manager.util.WAY_TO_HANDLE_DATA
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.EntityType
import com.shuyun.loyalty.service.meta.EventStreamMetasStatusEnum
import com.shuyun.loyalty.service.model.*
import com.shuyun.loyalty.service.repository.GradeHierarchyRepository
import com.shuyun.loyalty.service.repository.RemindConfigRepository
import com.shuyun.loyalty.service.repository.SubjectRepository
import com.shuyun.loyalty.service.service.DerivationEventMetasBaseService
import com.shuyun.loyalty.service.service.GradeHierarchyBaseService
import com.shuyun.loyalty.service.service.RemindConfigBaseService
import com.shuyun.loyalty.service.util.ModelInitUtil.copyPropertiesIgnoreNull
import org.apache.commons.io.IOUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.support.PathMatchingResourcePatternResolver
import org.springframework.stereotype.Service
import java.util.*

@Service
class GradeHierarchyService {
    @Autowired
    private lateinit var gradeHierarchyBaseService: GradeHierarchyBaseService

    @Autowired
    private lateinit var gradeHierarchyRepository: GradeHierarchyRepository

    @Autowired
    private lateinit var gradeDefinitionService: GradeDefinitionService

    @Autowired
    private lateinit var validateService: ValidateService

    @Autowired
    private lateinit var remindConfigBaseService: RemindConfigBaseService

    @Autowired
    private lateinit var remindConfigService: RemindConfigService

    @Autowired
    private lateinit var remindConfigRepository: RemindConfigRepository

    @Autowired
    private lateinit var derivationEventMetasBaseService: DerivationEventMetasBaseService

    @Autowired
    private lateinit var subjectRepository: SubjectRepository


    fun insert(subject: Subject, gradeHierarchy: GradeHierarchy){
        gradeHierarchyBaseService.insert(gradeHierarchy)
        gradeDefinitionService.insert(gradeHierarchy)
        insertRemindConfigList(subject, gradeHierarchy)
    }

    fun update(subject: Subject, gradeHierarchy: GradeHierarchy,backup:Boolean){
        gradeHierarchyBaseService.update(gradeHierarchy)
        gradeDefinitionService.update(gradeHierarchy,backup)
        updateRemindConfigList(subject, gradeHierarchy, backup)
    }

    fun delete(gradeHierarchy: GradeHierarchy){
        gradeHierarchyBaseService.delete(gradeHierarchy)
        gradeDefinitionService.delete(gradeHierarchy)
        gradeHierarchy.remindConfigList?.forEach {
            remindConfigBaseService.delete(it)
        }
    }

    fun insert(subject: Subject) {
        subject.gradeHierarchyList?.forEach { gradeHierarchy ->
            gradeHierarchy.subjectVersionId = subject.versionId
            insert(subject,gradeHierarchy)
        }
    }

    fun insertRemindConfigList(subject: Subject, gradeHierarchy: GradeHierarchy){
        gradeHierarchy.remindConfigList?.forEach {
            remindConfigService.insert(it,subject, gradeHierarchy)
        }
    }

    fun updateRemindConfigList(subject: Subject, gradeHierarchy: GradeHierarchy, backup: Boolean){
        remindConfigService.update(subject,gradeHierarchy,backup)
    }

    fun update(subject: Subject, backup: Boolean) {
        val newGradeHierarchyList = subject.gradeHierarchyList
        val oldGradeHierarchyList = gradeHierarchyBaseService.findDetailBySubjectVersionId(subject.versionId!!)

        initModifyDataServiceByCompareList(newGradeHierarchyList, oldGradeHierarchyList, backup, keyId)
                .forEach { gradeHierarchy, wayToHandle ->
                    gradeHierarchy as GradeHierarchy
                    when (wayToHandle) {
                        WAY_TO_HANDLE_DATA.TO_ADD -> {
                            gradeHierarchy.subjectVersionId = subject.versionId
                            insert(subject,gradeHierarchy)
                        }
                        WAY_TO_HANDLE_DATA.TO_UPDATE -> {
                            if(gradeHierarchy.status == PublishStatusEnum.FILED){
                                if(validateService.checkEntityReferenced(gradeHierarchy.id!!, EntityType.GRADE_HIERARCHY)){
                                    throw LoyaltyException(LoyaltyExceptionCode.GRADE_NOT_OPERATION_FILE)
                                }
                            }
                            update(subject,gradeHierarchy,backup)
                        }
                        WAY_TO_HANDLE_DATA.TO_DELETE -> {
                            if (backup) {
                                if (!PublishStatusEnum.DRAFT.equals(gradeHierarchy.status)) {
                                    throw LoyaltyException(LoyaltyExceptionCode.NOT_OPERATION_DELETE_GRADE)
                                }
                            }
                            delete(gradeHierarchy)
                        }
                    }
                }
    }

    fun delete(subject: Subject) {
        subject.gradeHierarchyList?.forEach {
            delete(it)
        }
    }

    fun copy(subjectCopy: Subject) {
        subjectCopy.gradeHierarchyList?.forEach { g ->
            val gradeHierarchyCopy = GradeHierarchy()
            copyPropertiesIgnoreNull(g, gradeHierarchyCopy)
            gradeHierarchyCopy.versionId = null
            gradeHierarchyCopy.subjectVersionId = subjectCopy.versionId
            //initModelInfo(gradeHierarchyCopy, OperationType.ADD, userId.get(), username.get())
            gradeHierarchyRepository.save(gradeHierarchyCopy)

            g.remindConfigList?.forEach {
                val remindConfigCopy = RemindConfig()
                copyPropertiesIgnoreNull(it,remindConfigCopy)
                remindConfigCopy.versionId = null
                remindConfigCopy.name = it.name!!.trim()
                remindConfigCopy.parentId = gradeHierarchyCopy.id
                remindConfigCopy.parentVersionId = gradeHierarchyCopy.versionId
                remindConfigCopy.subjectVersionId = gradeHierarchyCopy.subjectVersionId
                remindConfigCopy.planVersionId = subjectCopy.planVersionId
                remindConfigRepository.save(remindConfigCopy)
            }

            gradeDefinitionService.copy(gradeHierarchyCopy)
        }
    }

    fun publish(subject: Subject) {

        val requiredCodes = HashMap<Long, List<String>>()
        subject.gradeHierarchyList?.map { gradeHierarchy ->
            val list = ArrayList<String>()
            for (entry in DerivationEventType.entries) {
                if (entry == DerivationEventType.GRADE_EXPIRE_REMIND || entry == DerivationEventType.GRADE_RECORD) {
                    list.add(entry.code.plus("_").plus(gradeHierarchy.id))
                }
            }
            requiredCodes[gradeHierarchy.id!!] = list
        }
        val derivationEventMetaCodes = HashSet<String>()
        val chunked = requiredCodes.values.flatten().chunked(100)
        for (cs in chunked) {
            derivationEventMetaCodes.addAll(derivationEventMetasBaseService.findByCodeIn(cs).map { it.code!! })
        }

        subject.gradeHierarchyList?.forEach { gradeHierarchy ->
            gradeHierarchy.status = when (subject.status) {
                EnableStatusEnum.ENABLED -> when (gradeHierarchy.status) {
                    PublishStatusEnum.DRAFT -> PublishStatusEnum.PUBLISHED
                    else -> gradeHierarchy.status
                }
                else -> PublishStatusEnum.FILED
            }
            if (PublishStatusEnum.FILED == gradeHierarchy.status) {
                gradeHierarchy.sort = gradeHierarchy.sort!! - 100000
            } else {
                while (gradeHierarchy.sort!! < 0) {
                    gradeHierarchy.sort = gradeHierarchy.sort!! + 100000
                }
            }
            val currentCodes = derivationEventMetaCodes.filter { it.endsWith("_${gradeHierarchy.id}") }
            val allCodes = requiredCodes[gradeHierarchy.id!!]!!

            val toSaveDerivationEventTypes = ArrayList<DerivationEventType>()
            allCodes.forEach {
                if (it !in currentCodes) {
                    DerivationEventType.fromCode(it)?.let { toSaveDerivationEventTypes.add(it) }
                }
            }

            gradeHierarchyBaseService.update(gradeHierarchy, toSaveDerivationEventTypes)

            if (PublishStatusEnum.PUBLISHED == gradeHierarchy.status) {
                gradeDefinitionService.publish(gradeHierarchy)
            }
            gradeHierarchy.remindConfigList?.forEach { remindConfig ->
                remindConfig.status = when (subject.status) {
                    EnableStatusEnum.ENABLED -> when (remindConfig.status) {
                        EnableStatusEnum.DRAFT -> EnableStatusEnum.ENABLED
                        else -> remindConfig.status
                    }
                    else -> EnableStatusEnum.DISABLED
                }
                if (EnableStatusEnum.DISABLED.equals(remindConfig.status)) {
                    remindConfig.sort = remindConfig.sort!! - 100000
                } else {
                    while (remindConfig.sort!! < 0) {
                        remindConfig.sort = remindConfig.sort!! + 100000
                    }
                }
                remindConfigBaseService.update(remindConfig)
            }
        }
    }

    /**
     * 追加 , 数据库旧数据 sourceId = null , 等级体系关联的下降最低等级不能变
     * 替换:  sourceId != null && id=null 替换新增, 等级体系关联等级要用sourceId匹配替换
     *      替换旧数据, lowestDegrade = sourceId , 等级还是以sourceId判断
     */
    fun upgrade(subject: Subject) {
        subject.gradeHierarchyList?.forEach { g ->
            g.subjectVersionId = subject.versionId
            gradeHierarchyRepository.save(g)

            // 新增数据,需要更新id
            if(g.sourceId != null && g.id == null) {
                g.id = g.versionId
                g.status = PublishStatusEnum.DRAFT
                gradeHierarchyRepository.save(g)
                derivationEventMetasBaseService.save(initGradeHierarchyDerivation(g, DerivationEventType.GRADE_EXPIRE_REMIND))
                derivationEventMetasBaseService.save(initGradeHierarchyDerivation(g, DerivationEventType.GRADE_RECORD))
            }

            g.remindConfigList?.forEach {
                it.name = it.name!!.trim()
                it.parentId = g.id
                it.parentVersionId = g.versionId
                it.subjectVersionId = g.subjectVersionId
                it.planVersionId = subject.planVersionId
                remindConfigRepository.save(it)
                // 新增数据,需要更新id
                if(it.sourceId != null && it.id == null) {
                    it.id = it.versionId
                    it.status = EnableStatusEnum.DRAFT
                    remindConfigRepository.save(it)
                }
            }
            gradeDefinitionService.upgrade(g)
        }
    }



    fun initGradeHierarchyDerivation(gradeHierarchy: GradeHierarchy, derivationEventType: DerivationEventType): DerivationEventMetas {
        val subject = subjectRepository.findByVersionId(gradeHierarchy.subjectVersionId!!)
        val resource = PathMatchingResourcePatternResolver().getResource("classpath:planPublishInit/derivation.event.${derivationEventType.fileName}.json")
        var jsonEvent = IOUtils.toString(resource.inputStream,"UTF-8")!!
        jsonEvent = jsonEvent.replace("{id}",gradeHierarchy.id.toString()).replace("{dataModelFqn}",subject.get().dataType!!)
        val derivationEventMetas = DerivationEventMetas().apply {
            this.code = derivationEventType.code.plus("_").plus(gradeHierarchy.id)
            this.schema = jsonEvent
            this.fqn = "data.loyalty.member.hierarchy.${derivationEventType.fileName}".plus(gradeHierarchy.id)
            this.name = "等级体系衍生事件,${derivationEventType.fileName}: ".plus(gradeHierarchy.id)
            this.status = EventStreamMetasStatusEnum.VALID
            this.created = Date()
            this.modified = Date()
        }
        return derivationEventMetas
    }
}