package com.shuyun.loyalty.manager.migration

import com.google.common.base.Joiner
import com.shuyun.loyalty.manager.migration.ibatis.MigrationSetup
import com.shuyun.loyalty.manager.migration.ibatis.MultiMigrationSetupFactory
import com.shuyun.loyalty.sdk.Property
import org.apache.commons.lang3.StringUtils
import org.apache.ibatis.datasource.pooled.PooledDataSource
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class IbatisMigration {
    fun execute() {
        if (Property.getSysOrEnv(SKIP_MIGRATION_MYSQL_KEY, false)) {
            log.info("$SKIP_MIGRATION_MYSQL_KEY=true，将跳过mybatis migration执行")
        }
        val driver = Property.getSysOrEnv("database.driverClass")
        val url = Property.getSysOrEnv("database.url")
        val password = Property.getSysOrEnv("database.password")
        val user = Property.getSysOrEnv("database.username")
        val defaultProfile = Property.getSysOrEnv("migration.profile", "")
        //执行特定部署模式下的脚本
        var scripts = Property.getSysOrEnv("migrations.scripts", "")
        scripts = if (StringUtils.isEmpty(scripts)) defaultProfile else Joiner.on(",").join(defaultProfile, scripts)
        log.info("dataSource.url:{}", url)

        if (null == url || null == driver || StringUtils.isEmpty(scripts)) {
            log.info("migration execute skip")
            return
        }
        val dataSource = PooledDataSource(driver, url, user, password)
        dataSource.setDefaultAutoCommit(true)

        try {
            val migrationSetup: MigrationSetup = MultiMigrationSetupFactory.defaultMultiMigrationSetup

            log.info("migrations.scripts:{}", scripts)
            migrationSetup.execute(dataSource, scripts)
        } catch (e: Exception) {
            if (e is RuntimeException) {
                throw e
            } else {
                throw RuntimeException(e)
            }
        } finally {
            dataSource.forceCloseAll()
        }
    }

    companion object {
        private val log: Logger = LoggerFactory.getLogger(IbatisMigration::class.java)
        const val SKIP_MIGRATION_MYSQL_KEY: String = "skip.migration.mysql"
    }
}
