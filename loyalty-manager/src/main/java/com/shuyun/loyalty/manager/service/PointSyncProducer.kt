package com.shuyun.loyalty.manager.service

import com.shuyun.loyalty.entity.dto.MemberMessage
import com.shuyun.loyalty.entity.dto.MemberPointMessage
import com.shuyun.loyalty.manager.kafka.sink.KafkaSource
import com.shuyun.loyalty.service.kafka.IPointSyncProducer
import com.shuyun.loyalty.service.util.MDCUtils
import com.shuyun.pip.component.json.JsonUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.messaging.support.MessageBuilder
import org.springframework.stereotype.Component

@Component
class PointSyncProducer : IPointSyncProducer {

    @Autowired
    private lateinit var kafkaSource: KafkaSource

    override fun send(memberMessage: MemberMessage): Boolean {
        if (memberMessage is MemberPointMessage) {
            val message = MessageBuilder.withPayload(JsonUtils.toJson(memberMessage))
                .setHeader(IPointSyncProducer.MEMBER_ID, memberMessage.memberId)
                .setHeader(MDCUtils.LOYALTY_TRACE_ID, MDCUtils.getTraceId()).build()
            return kafkaSource.requestSyncProcessOutput().send(message)
        }
        return  false
    }
}