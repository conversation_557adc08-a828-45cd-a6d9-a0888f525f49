package com.shuyun.loyalty.manager.resource.open

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.module.kotlin.readValue
import com.shuyun.lite.context.GlobalContext
import com.shuyun.loyalty.entity.api.PrecomputeRequest
import com.shuyun.loyalty.entity.api.PrecomputeResponse
import com.shuyun.loyalty.entity.api.PrecomputeTask
import com.shuyun.loyalty.entity.dto.MemberRequest.Companion.MEMBER_OPERATOR_LOCK_KEY_FORMAT
import com.shuyun.loyalty.manager.service.PrecomputeService
import com.shuyun.loyalty.sdk.Json
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.EventOccasionEnum.CALC_POINT
import com.shuyun.loyalty.service.meta.TypeEnum.GRADE
import com.shuyun.loyalty.service.meta.TypeEnum.POINT
import com.shuyun.pip.component.concurrent.lock.Locker
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.extensions.Extension
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty
import org.apache.commons.codec.digest.DigestUtils
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*


@RestController
@RequestMapping("/open/precompute")
class PrecomputeResource {

    @Autowired
    private lateinit var precomputeService: PrecomputeService

    @Autowired
    private lateinit var locker: Locker

    companion object {
        private val mapper by lazy {
            val mapper = ObjectMapper()
            Json.setDefault(mapper)
            mapper.configure(SerializationFeature.ORDER_MAP_ENTRIES_BY_KEYS, true) // 按照key排序
            mapper.configure(SerializationFeature.INDENT_OUTPUT, false) // 去除格式化空白
            mapper
        }

        private val logger = LoggerFactory.getLogger(PrecomputeResource::class.java)
    }


    @Operation(summary = "积分等级试算接口", tags = ["积分等级试算接口"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/execute")
    fun execute(@RequestBody req: PrecomputeRequest): PrecomputeResponse {
        logger.info("precompute request: {}", Json.toJson(req))
        checkHash(req.lastResponse)
        val tuple6 = precomputeService.buildPrecomputeTasks(req)
        val tasks = tuple6.fourth
        val subjectUid = tuple6.fifth
        val ks = lockKeys(tasks, subjectUid)
        val lks = ks.map { locker.getLock(it) }
        try {
            lks.forEach { it.lock() }
            return precomputeService.precompute(tuple6, req).also { it.hash = hash(it) }
        } finally {
            lks.forEach { it.unlock() }
        }
    }


    @Operation(summary = "确认积分等级试算结果", tags = ["积分等级试算接口"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/confirm")
    fun confirm(@RequestBody response: PrecomputeResponse) {
        logger.info("precompute confirm: {}", Json.toJson(response))
        checkHash(response)
        val ks = lockKeys(response)
        val lks = ks.map { locker.getLock(it) }
        try {
            lks.forEach { it.lock() }
            precomputeService.confirm(response)
        } finally {
            lks.forEach { it.unlock() }
        }
    }


    private fun lockKeys(response: PrecomputeResponse): Set<String> {
        val keys = mutableSetOf<String>()
        for (content in response.contents) {
            val c = content as Map<*, *>
            val type = c["type"] as? String ?: continue
            when (type) {
                POINT.name -> {
                    keys.add(String.format(MEMBER_OPERATOR_LOCK_KEY_FORMAT, c["accountTypeId"], response.subjectUid))
                }
                GRADE.name -> {
                    keys.add("grade_calculate_${c["gradeHierarchyId"]}-${response.subjectUid}")
                }
            }
        }
        response.lastResponse?.let { keys.addAll(lockKeys(it)) }
        return keys
    }


    private fun lockKeys(tasks: TreeMap<Int, PrecomputeTask>, subjectUid: String): Set<String> {
        val keys = mutableSetOf<String>()
        for ((_, task) in tasks) {
            val accountTypeId = task.accountTypeId
            if (task.eoe == CALC_POINT) {
                keys.add(String.format(MEMBER_OPERATOR_LOCK_KEY_FORMAT, accountTypeId, subjectUid))
            }
        }
        return keys
    }


    private fun checkHash(resp: PrecomputeResponse?) {
        if (resp != null && hash(resp) != resp.hash) {
            throw LoyaltyException(LoyaltyExceptionCode.POINT_TRIAL_SIGN_FAILED)
        }
    }

    private fun hash(response: PrecomputeResponse): String {
        val res: PrecomputeResponse = mapper.readValue(mapper.writeValueAsString(response))
        val d = res.deductions?.let { mapper.writeValueAsString(it) } ?: ""
        val c = mapper.writeValueAsString(res.contents)
        val l = res.lastResponse?.let { mapper.writeValueAsString(it) } ?: ""
        val t = GlobalContext.defTenantId()
        val str = res.subjectUid + d + c + l + t
        return DigestUtils.sha256Hex(str)
    }
}