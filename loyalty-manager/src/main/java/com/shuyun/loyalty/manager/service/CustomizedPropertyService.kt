package com.shuyun.loyalty.manager.service

import com.shuyun.loyalty.entity.api.constants.EnableStatusEnum
import com.shuyun.loyalty.manager.util.Constants.keyId
import com.shuyun.loyalty.manager.util.ListCompareUtil.initModifyDataServiceByCompareList
import com.shuyun.loyalty.manager.util.WAY_TO_HANDLE_DATA
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.loyalty.service.meta.EntityType
import com.shuyun.loyalty.service.meta.PropertyBelongerTypeEnum
import com.shuyun.loyalty.service.model.CustomizedProperty
import com.shuyun.loyalty.service.model.EventType
import com.shuyun.loyalty.service.model.Subject
import com.shuyun.loyalty.service.repository.CustomizedPropertyRepository
import com.shuyun.loyalty.service.service.CustomizedPropertyBaseService
import com.shuyun.loyalty.service.util.ModelInitUtil.copyPropertiesIgnoreNull
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class CustomizedPropertyService {

    @Autowired
    private lateinit var customizedPropertyBaseService: CustomizedPropertyBaseService

    @Autowired
    private lateinit var customizedPropertyRepository: CustomizedPropertyRepository

    @Autowired
    private lateinit var validateService: ValidateService

    fun insert(customizedProperty: CustomizedProperty){
        customizedProperty.name = customizedProperty.name!!.trim()
        customizedPropertyBaseService.insert(customizedProperty)
    }

    fun update(customizedProperty: CustomizedProperty){
        customizedProperty.name = customizedProperty.name!!.trim()
        customizedPropertyBaseService.update(customizedProperty)
    }

    fun delete(customizedProperty: CustomizedProperty){
        customizedPropertyBaseService.delete(customizedProperty)
    }

    fun insert(customizedPropertyList: List<CustomizedProperty>?, belongerVersionId: Long, belongerType: PropertyBelongerTypeEnum) {
        customizedPropertyList?.forEach {
            it.belongerVersionId = belongerVersionId
            it.belongerType = belongerType
            insert(it)
        }
    }

    fun update(newCustomizedPropertyList: List<CustomizedProperty>?, belongerVersionId: Long, belongerType: PropertyBelongerTypeEnum, backup: Boolean) {
        val oldCustomizedPropertyList = customizedPropertyBaseService.findDetailByBelongerVersionIdAndBelongerType(belongerVersionId, belongerType)
        initModifyDataServiceByCompareList(newCustomizedPropertyList, oldCustomizedPropertyList, backup, keyId)
                .forEach { customizedProperty, wayToHandle ->
                    customizedProperty as CustomizedProperty
                    when (wayToHandle) {
                        WAY_TO_HANDLE_DATA.TO_ADD -> {
                            customizedProperty.belongerVersionId = belongerVersionId
                            customizedProperty.belongerType = belongerType
                            insert(customizedProperty)
                        }
                        WAY_TO_HANDLE_DATA.TO_UPDATE -> {
                            if(customizedProperty.status == EnableStatusEnum.DISABLED){
                                if(validateService.checkEntityReferenced(customizedProperty.id!!, EntityType.CUSTOMIZED_PROPERTY)){
                                    throw LoyaltyException(LoyaltyExceptionCode.CURRENT_CUSTOMIZED_PROPERTY_NOT_DISABLED,"当前属性正在被业务端引用，不可禁用。如需禁用，请先到业务端终止相关规则")
                                }
                            }
                            update(customizedProperty)
                        }
                        WAY_TO_HANDLE_DATA.TO_DELETE -> {
                            if (backup) {
                                if (!EnableStatusEnum.DRAFT.equals(customizedProperty.status)) {
                                    throw LoyaltyException(LoyaltyExceptionCode.NOT_OPERATION_DELETE_PROPERTY_TEMPLATE,"已发布的计划不允许删除非草稿状态的属性")
                                }
                            }
                            delete(customizedProperty)
                        }
                    }
                }
    }


    fun delete(customizedPropertyList: List<CustomizedProperty>?) {
        customizedPropertyList?.forEach {
            delete(it)
        }
    }

    fun copy(customizedPropertyCopyList: List<CustomizedProperty>?, belongerVersionId: Long) {
        customizedPropertyCopyList?.forEach {
            val customizedPropertyCopy = CustomizedProperty()
            copyPropertiesIgnoreNull(it, customizedPropertyCopy)
            customizedPropertyCopy.versionId = null
            customizedPropertyCopy.belongerVersionId = belongerVersionId
            customizedPropertyRepository.save(customizedPropertyCopy)
        }
    }

    fun upgrade(customizedPropertyList: List<CustomizedProperty>?, belongerVersionId: Long) {
        customizedPropertyList?.forEach {
            it.belongerVersionId = belongerVersionId
            customizedPropertyRepository.save(it)
            // 新增数据,需要更新id
            if(it.sourceId != null && it.id == null) {
                it.id = it.versionId
                it.status = EnableStatusEnum.DRAFT
                customizedPropertyRepository.save(it)
            }
        }
    }

    fun publish(subject: Subject) {
        subject.customizedPropertyList?.forEach { customizedProperty ->
            customizedProperty.status = when (subject.status) {
                EnableStatusEnum.ENABLED -> when (customizedProperty.status) {
                    EnableStatusEnum.DRAFT -> EnableStatusEnum.ENABLED
                    else -> customizedProperty.status
                }
                else -> EnableStatusEnum.DISABLED
            }
            if (EnableStatusEnum.DISABLED == customizedProperty.status) {
                customizedProperty.sort = customizedProperty.sort!! - 100000
            }
            customizedPropertyBaseService.update(customizedProperty)
        }
    }

    fun publish(eventType: EventType) {
        eventType.customizedPropertyList?.forEach { customizedProperty ->
            customizedProperty.status = when(eventType.status) {
                EnableStatusEnum.ENABLED->when(customizedProperty.status){
                    EnableStatusEnum.DRAFT -> EnableStatusEnum.ENABLED
                    else->customizedProperty.status
                }
                else-> EnableStatusEnum.DISABLED
            }
            if(EnableStatusEnum.DISABLED.equals(customizedProperty.status)){
                customizedProperty.sort = customizedProperty.sort!!-100000
            }else{
                while(customizedProperty.sort!! < 0){
                    customizedProperty.sort = customizedProperty.sort!!+100000
                }
            }
            customizedPropertyBaseService.update(customizedProperty)
        }
    }
}