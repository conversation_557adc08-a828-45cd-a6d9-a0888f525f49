package com.shuyun.loyalty.manager.service

import com.pip.mybatisplus.pools.DmPoolFactory
import com.shuyun.dm.api.dataapi.request.QueryDataRequest
import com.shuyun.loyalty.service.datamodel.MemberGrade
import com.shuyun.loyalty.service.datamodel.MemberMedal
import com.shuyun.pip.frameworkext.filter.UserContextThreadSafe
import org.apache.logging.log4j.LogManager
import org.springframework.stereotype.Service

@Service
class DataModelService{
    private val logger = LogManager.getLogger(DataModelService::class)

    /**
     * 查询数据服务
     */
    fun query(fqn: String, fields: String, filter: String, offset: Int? = null, limit: Int? = null, withTotals: Boolean = false): Any? {
        val queryDataRequest = QueryDataRequest().apply {
            this.fqn = fqn
            this.fields = fields
            this.filter = filter
            this.sort = sort
            this.withTotals = withTotals
            this.offset = offset
            this.limit = limit
        }

        logger.debug("find values of fqn {} fields {} by filter {}", fqn, queryDataRequest.fields, filter)

        return DmPoolFactory.execute(UserContextThreadSafe.isWithUserContext()) { sdk ->
            sdk.queryObjects(
                queryDataRequest
            )
        }
    }

    /**
     * 获取等级详情
     */
    fun queryGradeDetail(hierarchyId: String,fields: String, filter: String, offset: Int, limit: Int, withTotals: Boolean = false):Any? {
       return query(MemberGrade.dataModelFqnPrefix+hierarchyId,fields,filter,offset,limit,withTotals)
    }

    /**
     * 获取勋章详情
     */
    fun queryMedalDetail(hierarchyId: String, fields: String, filter: String, offset: Int, limit: Int, withTotals: Boolean): Any? {
        return query(MemberMedal.DATA_MODEL_FQN_PREFIX + hierarchyId, fields, filter, offset, limit, withTotals)
    }
}