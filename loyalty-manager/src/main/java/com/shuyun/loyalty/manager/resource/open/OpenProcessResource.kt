package com.shuyun.loyalty.manager.resource.open

import com.shuyun.eawf.enums.ActionEnum
import com.shuyun.eawf.enums.InstanceStatus
import com.shuyun.eawf.spi.EawfSpi
import com.shuyun.eawf.spi.InstanceStatusChangeEvent
import com.shuyun.loyalty.entity.api.constants.ApproveFlowRouter
import com.shuyun.loyalty.entity.enums.ProcessStatusEnum
import com.shuyun.loyalty.manager.service.PointRuleGroupService
import com.shuyun.loyalty.manager.service.TransferApproveService
import com.shuyun.loyalty.manager.vo.UpdatePointRuleGroupProcessVo
import com.shuyun.loyalty.service.exception.LoyaltyException
import com.shuyun.loyalty.service.exception.LoyaltyExceptionCode
import com.shuyun.pip.component.json.JsonUtils
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.extensions.Extension
import io.swagger.v3.oas.annotations.extensions.ExtensionProperty
import org.apache.logging.log4j.LogManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import javax.validation.Valid

/**
 * 审批流回调接口
 */
@RestController
@RequestMapping("/eawf/instance")
class OpenProcessResource : EawfSpi {

    private val log = LogManager.getLogger(OpenProcessResource::class.java)

    @Autowired
    private lateinit var pointRuleGroupService: PointRuleGroupService

    @Autowired
    private lateinit var transferApproveService: TransferApproveService

    @Operation(summary = "审批流回调接口", tags = ["回调接口"], extensions = [Extension(name = "x-visibility", properties = [ExtensionProperty(name = "value", value = "Service")])])
    @PostMapping("/onChange")
    override fun onChange(@Valid @RequestBody event: InstanceStatusChangeEvent) {
        log.debug("审批流回调入参: {}", { JsonUtils.toJson(event) })
        val router = event.params.map { it.get("router")?.asText("") }.orElseGet { "" }
        if (ApproveFlowRouter.TRANSFER_POINT.name == router) {
            transferApproveService.approve(event)
            return
        }
        val request = UpdatePointRuleGroupProcessVo().apply {
            this.processId = event.processId.toString()
            this.pointRuleGroupId = event.params.map { it.get("ruleGroupId")?.asLong() }
                .orElseThrow { throw LoyaltyException(LoyaltyExceptionCode.VIOLATION_PARAMS) }
            this.processInstanceId = event.instanceId.toString()
            this.processStatus = convertStatus(event.status, event.action)
        }
        if (request.pointRuleGroupId == null || request.processInstanceId.isNullOrBlank() || request.processId.isNullOrBlank()) {
            throw LoyaltyException(LoyaltyExceptionCode.VIOLATION_PARAMS)
        }
        if (request.processStatus == null) {
            throw LoyaltyException(LoyaltyExceptionCode.POINT_PROCESS_STATUS_NOT_FOUND)
        }
        pointRuleGroupService.updateRuleGroupProcess(request)
        log.debug("审批流回调更新状态结束")
    }

    private fun convertStatus(status: InstanceStatus, action: ActionEnum): ProcessStatusEnum {
        if (action == ActionEnum.RECALL) {
            return ProcessStatusEnum.DESIGN
        } else if (action == ActionEnum.REJECT && status == InstanceStatus.INIT) {
            return ProcessStatusEnum.REJECT
        }
        return when (status) {
            InstanceStatus.INIT -> ProcessStatusEnum.DESIGN
            InstanceStatus.TERMINATION -> ProcessStatusEnum.REJECT
            InstanceStatus.REJECT -> ProcessStatusEnum.REJECT
            InstanceStatus.PASS -> ProcessStatusEnum.PASS
            InstanceStatus.PROCESS -> ProcessStatusEnum.PROCESS
            InstanceStatus.TERMINATION_BY_SUBMITTER -> ProcessStatusEnum.REJECT
        }
    }
}