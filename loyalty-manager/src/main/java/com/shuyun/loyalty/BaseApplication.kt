package com.shuyun.loyalty

import com.fasterxml.jackson.core.json.JsonReadFeature
import com.google.common.base.Joiner
import com.shuyun.lite.client.ConfigurationManagerHolder
import com.shuyun.loyalty.manager.util.ApiMgmtSupport
import com.shuyun.loyalty.sdk.Property
import com.shuyun.loyalty.service.infrastructure.idGenerator.IdService
import com.shuyun.pip.component.json.JsonUtils
import org.springframework.boot.SpringApplication
import org.springframework.context.ConfigurableApplicationContext
import kotlin.system.exitProcess

/**
 * @Author: wayne
 * @Description:
 * @Date create in 2018/10/30
 * @Modified by:
 */
open class BaseApplication(primarySources: Class<*>?) : SpringApplication(primarySources) {

    companion object {
        private const val LANGUAGE_KEY = "system.support.languages"
        private const val DEV_MODE_KEY = "devMode"
    }

    private fun beforeStart() {
        ConfigurationManagerHolder.init()
        // 解决json中有换行符的问题
        val mapper = JsonUtils.objectMapper()
        mapper.enable(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature())
        System.setProperty("tenant.tables", "derivation_event_metas,error_event,grade_rule,grade_rule_group,grade_transfer_task,import_point_or_grade,open_batch_log,point_deduct_rule,point_deduct_rule_operate,point_rule_group,point_send_rule,t_s_sequence_event,tmp_table,transfer_point_or_grade,point_send_limit_rule")
        System.setProperty("database.driverClass", Property.getSysOrEnv("database.driverClass","com.mysql.cj.jdbc.Driver"))
        System.setProperty("spring.datasource.driver-class-name", Property.getSysOrEnv("database.driverClass","com.mysql.cj.jdbc.Driver"))
    }

    override fun run(vararg args: String): ConfigurableApplicationContext {
        var run: ConfigurableApplicationContext? = null
        try {
            System.setProperty("CryptTools.crypt.not", "true")
            System.setProperty("hystrix.command.default.requestLog.enabled", "false")
            System.setProperty("log.warn.time", "500")
            System.setProperty("druid.mysql.usePingMethod", "false")
            presetSupportedLanguages()
            beforeStart()
            run = super.run(*args)
            afterConfiguration()
            started()
        } catch (e: Throwable) {
            if (run != null && run.isRunning) {
                run.stop()
                run.close()
            }
            e.printStackTrace()
            exitProcess(-1)
        }
        return run
    }

    private fun afterConfiguration() {
        IdService.init()
    }
    private fun started() {
        // 非开发模式
        if (Property.getSysOrEnv(DEV_MODE_KEY, false)) {
            ApiMgmtSupport.registerHttpApi()
        }
    }


    private fun presetSupportedLanguages() {
        val languages: MutableSet<String> = mutableSetOf("zh", "zh-HK", "zh-CN", "en-US")
        val langStr = Property.getSysOrEnv(LANGUAGE_KEY)
        if (langStr != null) {
            languages.addAll(listOf(*langStr.split("\\s*,\\s*".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()))
        }
        System.setProperty(LANGUAGE_KEY, Joiner.on(",").join(languages))
    }
}
