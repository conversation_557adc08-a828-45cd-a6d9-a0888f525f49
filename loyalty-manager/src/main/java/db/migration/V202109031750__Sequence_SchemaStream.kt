package db.migration

import org.apache.logging.log4j.LogManager
import org.flywaydb.core.api.migration.BaseJavaMigration
import org.flywaydb.core.api.migration.Context

/**
 * 231版本
 * 1. 时序事件 等级过期 枚举更改
 * 2. 时序事件 积分延迟发放 memberIdTx 改为 memberId
 * 3. schema stream 等级与合卡 overdueMemberId 改为 memberId
 */
class V202109031750__Sequence_SchemaStream : BaseJavaMigration() {
    companion object{
        private val log = LogManager.getLogger(V202109031750__Sequence_SchemaStream::class.java)
        private const val sequenceGrade = "update t_s_sequence_event set event_content = REPLACE(event_content,\"GRADE_OVER_EVENT_DISPATCH_HANDLER\",\"GRADE_OVER_DISPATCH_HANDLER\") , event_type_name='GRADE_OVER_DISPATCH_HANDLER' where reverse_source='DegradeDispatchHandler' and status = 'CREATED' and event_type_name='GRADE_OVER_EVENT_DISPATCH_HANDLER' "
        private const val sequenceDelaySend = "update t_s_sequence_event SET event_content=REPLACE (event_content,\"memberIdTx\",\"memberId\") WHERE reverse_source='DELAY_SEND' AND STATUS='CREATED' "
        private const val eventStream = "update event_stream_metas set `schema`=REPLACE(`schema`,\"overdueMemberId\",\"memberId\"), `references`=REPLACE(`references`,\"overdueMemberId\",\"memberId\") where fqn in (\"event.loyalty.grade.merge.event\",\"event.loyalty.grade.over.event\") "
    }

    override fun migrate(context: Context) {
        log.info("更新时序表等级过期任务开始")
        context.connection.also { conn ->
            conn.prepareStatement(sequenceGrade).use { stmt ->
                stmt.execute()
            }
        }
        log.info("更新时序表等级过期任务结束")

        log.info("更新时序表延迟发放积分任务开始")
        context.connection.also { conn ->
            conn.prepareStatement(sequenceDelaySend).use { stmt ->
                stmt.execute()
            }
        }
        log.info("更新时序表延迟发放积分任务结束")

        log.info("更新事件schema开始")
        context.connection.also { conn ->
            conn.prepareStatement(eventStream).use { stmt ->
                stmt.execute()
            }
        }
        log.info("更新事件schema结束")
        log.info("v231执行flyway--更改时序表等完成")
    }
}