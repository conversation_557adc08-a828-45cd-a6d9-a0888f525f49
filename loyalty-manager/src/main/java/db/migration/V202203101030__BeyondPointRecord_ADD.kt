package db.migration

import com.shuyun.dm.api.domain.DataModel
import com.shuyun.dm.api.domain.FieldConstraint
import com.shuyun.dm.api.domain.ModelField
import db.migration.Base.addField
import db.migration.Base.pointAccountTypeInfos
import org.apache.logging.log4j.LogManager
import org.flywaydb.core.api.migration.BaseJavaMigration
import org.flywaydb.core.api.migration.Context

/**
 * 不够扣除积分记录
 */
class V202203101030__BeyondPointRecord_ADD : BaseJavaMigration() {
    companion object{
        private val log = LogManager.getLogger(V202203101030__BeyondPointRecord_ADD::class.java)
    }

    override fun migrate(context: Context) {
        log.info("执行Flyway---积分超额记录表新增字段pointRecordId")
        withI18NTenantContext {
            log.info("执行Flyway---积分超额记录表新增字段pointRecordId----租户$it 开始执行")
            pointAccountTypeInfos().forEach { info ->
                val fqn = "data.loyalty.member.account.BeyondPointRecord${info.id}"
                val fieldType = DataModel()
                fieldType.fqn = "system.lang.String"
                val modelField = ModelField()
                modelField.constraint = FieldConstraint().apply { this.stringLength = 100 }

                modelField.name = "pointRecordId"
                modelField.custom = true
                modelField.description = "关联积分变更记录ID"
                modelField.title = "关联积分变更记录ID"
                modelField.fieldType = fieldType
                modelField.indexed = true
                addField(fqn, modelField)
            }
        }
        log.info("执行Flyway---积分超额记录表新增字段pointRecordId完成")
    }
}