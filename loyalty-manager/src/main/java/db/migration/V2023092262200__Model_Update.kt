package db.migration

import com.pip.mybatisplus.toolkit.DataapiHttpFactory
import com.shuyun.dm.api.vo.FetchStartRequest
import com.shuyun.dm.sdk.exception.SdkException
import com.shuyun.loyalty.entity.dto.MemberRequest
import com.shuyun.loyalty.manager.service.OpenMemberPointService
import com.shuyun.pip.ApplicationContextHolder
import com.shuyun.pip.component.concurrent.lock.Locker
import org.apache.logging.log4j.LogManager
import org.flywaydb.core.api.migration.BaseJavaMigration
import org.flywaydb.core.api.migration.Context
import kotlin.concurrent.thread
import kotlin.concurrent.withLock


class V2023092262200__Model_Update : BaseJavaMigration() {

    companion object {
        private val log = LogManager.getLogger(V2023092262200__Model_Update::class.java)
    }

    override fun migrate(context: Context) {
        withI18NTenantContext {
            val infos = Base.pointAccountTypeInfos()
            val chunk = infos.chunked(20)
            for (ck in chunk) {
                val ts = ArrayList<Thread>()
                for (info in ck) {
                    val t = thread {
                        try {
                            val fqn = "data.loyalty.member.account.Point${info.id}"
                            val fetchSql = "select memberId from data.loyalty.member.account.Point${info.id} group by memberId having count(memberId) > 1"
                            // 查询出重复的会员字段
                            try {
                                log.info("查询重复的会员字段 {}", fqn)
                                DataapiHttpFactory.httpSdk().fetch(FetchStartRequest(fetchSql, mapOf(), 1000)).use { fetchData ->
                                    try {
                                        var data: List<Map<String, Any>>?
                                        do {
                                            data = fetchData.next()?.data
                                            if (data.isNullOrEmpty()) break
                                            val memberIds = data.map { it["memberId"] as String }
                                            fixData(info.id.toLong(), memberIds)
                                        } while (data.isNotEmpty())
                                    } catch (e: SdkException){
                                        log.error("处理异常", e)
                                    }
                                }
                            } catch (e: SdkException) {
                                if (e.error_code == "152201" || e.error_code == "151147") {
                                    log.error("模型不存在 {} {}", e.message, fqn)
                                    return@thread
                                }
                                log.warn(e.message)
                            }
                        } catch (e: Exception) {
                            log.warn(e.message)
                        }
                    }
                    ts.add(t)
                }
                for (t in ts) {
                    t.join()
                }
            }
        }
    }

    // 修复重复的会员积分账户数据
    private fun fixData(pointPlanId: Long, memberIds: List<String>) {
        if (memberIds.isEmpty()) return
        val locker = ApplicationContextHolder.getBean(Locker::class.java)
        val openMemberPointService = ApplicationContextHolder.getBean(OpenMemberPointService::class.java)
        val chunk = memberIds.chunked(50)
        for (list in chunk) {
            val threads = ArrayList<Thread>()
            for (memberId in list) {
                val sdk = DataapiHttpFactory.httpSdk()
                val t = thread {
                    val sql = "select id from data.loyalty.member.account.Point${pointPlanId} where memberId = :memberId limit 1"
                    val response = sdk.execute(sql, mapOf("memberId" to memberId))
                    val map = response.data.firstOrNull() as? Map<*, *>
                    val memberPointId = map?.get("id") as? String ?: return@thread
                    val delSql = "delete from data.loyalty.member.account.Point${pointPlanId} where memberId = :memberId and id != :memberPointId"
                    sdk.execute(delSql, mapOf("memberId" to memberId, "memberPointId" to memberPointId))
                    val updateSql0 = "update data.loyalty.member.account.Point${pointPlanId} set openSegmentFlag = 0 where id = :memberPointId"
                    val updateSql1 = "update data.loyalty.member.account.PointRecord${pointPlanId} set memberPointId = :memberPointId where memberId = :memberId"
                    val updateSql2 = "update data.loyalty.member.account.GainStatement${pointPlanId} set memberPointId = :memberPointId where memberId = :memberId"
                    val updateSql3 = "update data.loyalty.member.account.ValidStatement${pointPlanId} set memberPointId = :memberPointId where memberId = :memberId"
                    val updateSql4 = "update data.loyalty.member.account.NegativeStatement${pointPlanId} set memberPointId = :memberPointId where memberId = :memberId"
                    val updateSql5 = "update data.loyalty.member.account.FrozenStatement${pointPlanId} set memberPointId = :memberPointId where memberId = :memberId"
                    val updateSql6 = "update data.loyalty.member.account.FrozenPoint${pointPlanId} set memberPointId = :memberPointId where memberId = :memberId"
                    val updateSql7 = "update data.loyalty.member.account.LimitPointRecord${pointPlanId} set memberPointId = :memberPointId where memberId = :memberId"
                    val params = mapOf("memberId" to memberId, "memberPointId" to memberPointId)
                    runCatching { sdk.execute(updateSql0, params) }.onFailure { log.warn(it.message) }
                    runCatching { sdk.execute(updateSql1, params) }.onFailure { log.warn(it.message) }
                    runCatching { sdk.execute(updateSql2, params) }.onFailure { log.warn(it.message) }
                    runCatching { sdk.execute(updateSql3, params) }.onFailure { log.warn(it.message) }
                    runCatching { sdk.execute(updateSql4, params) }.onFailure { log.warn(it.message) }
                    runCatching { sdk.execute(updateSql5, params) }.onFailure { log.warn(it.message) }
                    runCatching { sdk.execute(updateSql6, params) }.onFailure { log.warn(it.message) }
                    runCatching { sdk.execute(updateSql7, params) }.onFailure { log.warn(it.message) }
                    val lock = locker.getLock(String.format(MemberRequest.MEMBER_OPERATOR_LOCK_KEY_FORMAT, pointPlanId, memberId))
                    lock.withLock { openMemberPointService.migrateMemberPointSegment(pointPlanId, memberId) }
                }
                threads.add(t)
            }
            for (thread in threads) {
                thread.join()
            }
        }
    }
}