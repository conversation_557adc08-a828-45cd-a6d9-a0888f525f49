package db.migration

import com.shuyun.lite.util.Common
import migration.util.AlterTenantTableColumn
import org.flywaydb.core.api.migration.BaseJavaMigration
import org.flywaydb.core.api.migration.Context
import org.slf4j.LoggerFactory
import java.sql.SQLSyntaxErrorException

/**
 * sass化,自定义version
 */
class V202109011750__alter_tenant_table : BaseJavaMigration() {

    private val log = LoggerFactory.getLogger(AlterTenantTableColumn::class.java)

    private val alterSql = "alter table %s add tenant_id varchar(64)"
    private val updateSql = "update %s set tenant_id=?"
    override fun migrate(context: Context) {
        log.warn("此次服务启动需要进行多租户表结构处理，会锁表且不可回退！！！！，不可以中止！！")
        val tenantTables = System.getProperty("tenant.migration.tables", System.getProperty("tenant.tables", ""))
        if (tenantTables.isEmpty()) {
            return
        }
        tenantTables.split(",").forEach {
            val ddl = alterSql.format(it)
            log.info("开始执行ddl:{}", ddl)
            context.connection.also { conn ->
                conn.prepareStatement(ddl).use { stmt ->
                    try {
                        stmt.execute()
                    } catch (e: SQLSyntaxErrorException) {
                        if (e.sqlState != "42S21") {
                            throw e
                        } else {
                            false
                        }
                    }
                }

                Common.getSysOrEnv("system.tenant")?.apply {
                    conn.prepareStatement(updateSql.format(it)).use { stmt ->
                        stmt.setString(1, this)
                        stmt.execute()
                    }
                }
            }
        }
        log.info("表结构处理完成！")
    }
}